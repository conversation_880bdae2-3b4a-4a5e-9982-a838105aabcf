import { combineReducers } from "redux";
import currenciesReducer from "./currenciesReducer";
import loginReducer from "./loginReducer";
import systemLabelReducer from "./systemLabelReducer";

import { ActionTypes } from "../actions/ActionsTypes";
import aboutReducer from "./aboutReducer";
import collaborationReducer, {
  createCollaborationWrapperReducer
} from "./collaborationReducer";
import companyReducer from "./companyReducer";
import createCollaborationReducer from "./createCollaborationReducer";
import createNewOpportunityReducer from "./createNewOpportunityReducer";
import feedbackReducer from "./feedbackReducer";
import iproOpportunityReducer, {
  createOpportunityWrapperReducer
} from "./iproOpportunityReducer";
import landingReducer from "./landingReducer";
import messageInboxReducer from "./messageInboxReducer";
import messageNewReducer from "./messageNewReducer";
import messageSentReducer from "./messageSentReducer";
import navigationReducer from "./navigationReducer";
import newSavedSearchReducers from "./newSavedSearchReducers";
import newSearchReducer from "./newSearchReducer";
import newShortListReducer from "./newShortlsitReducer";
import notificationReducer from "./notificationReducer";
import phillipSearcherModalReducer from "./phillipSearcherModalReducer";
import presentReducer from "./presentReducer";
import resumeEditReducer from "./resumeEditReducer";
import resumeReducer from "./resumeReducer";
import savedSearchReducer from "./savedSearchReducer";
import searcherOpportunitiesDraftReducer from "./searcherOpportunitiesDraftReducer";
import searcherOpportunitiesSentReducer from "./searcherOpportunitiesSentReducer";
import searchReducer, { searchWrapperReducer } from "./searchReducer";
import settingReducer from "./settingReducer";
import shortlistReducer from "./shortlistReducer";
const appReducer = combineReducers({
  systemLabel: systemLabelReducer,
  currency: currenciesReducer,
  searcherModal: phillipSearcherModalReducer,
  userInfo: loginReducer,
  notification: notificationReducer,
  navigation: navigationReducer,
  resume: resumeReducer,
  present: presentReducer,
  company: companyReducer,
  newSearch: newSearchReducer,
  newSavedSearch: newSavedSearchReducers,
  newShortlist: newShortListReducer,
  search: searchWrapperReducer(searchReducer, "search"),
  savedSearchRed: savedSearchReducer,
  savedSearch: searchWrapperReducer(searchReducer, "savedSearch"),
  shortlist: shortlistReducer,
  messageNew: messageNewReducer,
  messageInbox: messageInboxReducer,
  messageSent: messageSentReducer,
  createNewOpportunity: createNewOpportunityReducer,
  searcherOpportunitySent: searcherOpportunitiesSentReducer,
  searcherOpportunityDraft: searcherOpportunitiesDraftReducer,
  resumeEdit: resumeEditReducer,
  landing: landingReducer,
  iproOpportunityAccepted: createOpportunityWrapperReducer(
    iproOpportunityReducer,
    "accepted"
  ),
  iproOpportunityDeclined: createOpportunityWrapperReducer(
    iproOpportunityReducer,
    "declined"
  ),
  iproOpportunityNew: createOpportunityWrapperReducer(
    iproOpportunityReducer,
    "new"
  ),
  iproCollaborationNew: createCollaborationWrapperReducer(
    collaborationReducer,
    "iproNew"
  ),
  iproCollaborationInActive: createCollaborationWrapperReducer(
    collaborationReducer,
    "iproInActive"
  ),
  iproCollaborationActive: createCollaborationWrapperReducer(
    collaborationReducer,
    "iproActive"
  ),
  searcherCollaborationPending: createCollaborationWrapperReducer(
    collaborationReducer,
    "searcherPending"
  ),
  searcherCollaborationAccepted: createCollaborationWrapperReducer(
    collaborationReducer,
    "searcherAccepted"
  ),
  searcherCollaborationInActive: createCollaborationWrapperReducer(
    collaborationReducer,
    "searcherInActive"
  ),
  searcherCollaborationDeclined: createCollaborationWrapperReducer(
    collaborationReducer,
    "searcherDeclined"
  ),
  createCollaboration: createCollaborationReducer,
  feedback: feedbackReducer,
  about: aboutReducer,
  setting: settingReducer
});

const rootReducer = (state, action) => {
  if (action.type === ActionTypes.LOGOUT) {
    state = { systemLabel: state.systemLabel, landing: state.landing };
  }
  return appReducer(state, action);
};

export default rootReducer;
