import moment from "moment";
import { useState, useEffect } from "react";
import { connect } from "react-redux";
import { useNavigate, useLocation } from "react-router-dom";
import { notificationAction } from "../../../../actions/notification";
import { StorageService } from "../../../../api/storage";
import ImgSrc from "../../../../assets/images/present.svg";
import Button from "../../../../common/Button/Button";
import Column from "../../../../common/Column/Column";
import Select from "../../../../common/Select/Select";
import { isValidEmail } from "../../../../utilities/helpers";
import Presentations from "../../components/PresentationDetails";
import ResumeDetail from "../../components/ResumeDetail";
import {
  DeleteShareCollaboration<PERSON>pi,
  getAllCollaborationsApi,
  GetCollaborationExpenseDetailApi,
  GetCollaborationTimeDetailApi,
  GetResumesByResumeId,
  GetSharedCollaborationInfo,
  GetUserDetailByResumeAndRequestId,
  ShareCollaborationApi,
  UpdateCollaborationEndDate,
  UpdateCollaborationHourlyRate
} from "../../snapshotApi";
import "./collaborations.scss";
import CollaborationDetail from "./components/CollaborationDetail";
import TimeAndExpenses from "./components/TimeAndExpenses";

const Collaborations = ({
  labels,
  isHelpActive,
  notificationAction,
  UserId,
  Currencies,
  token,
  ...props
}) => {
  const [state, setState] = useState({
    options: [
      { label: "", value: 1 },
      { label: "", value: 2 }
    ],
    acceptedCollaborations: [],
    selectedUser: [],
    selectedResume: [],
    collaborationDataChange1: false,
    allCollaborations: [],
    isCollapsed1: false,
    isCollapsed2: false,
    isCollapsed3: true,
    isCollapsed4: true,
    isCollapsed5: true,
    allTimeSheets: [],
    weekTimelogsList: [],
    weekExpenselogsList: [],
    currentExpenselog: {},
    currentTimeReport: {
      StatusId: 1,
      isEmpty: true,
      Amount: "",
      Time: ""
    },
    currentTimelog: {},
    TimeSheetStatusId: 2,
    ExpenseSheetStatusId: 2,
    isLoading: false,
    selectedCollaboration: {},
    durationOptions: [
      { label: "Current Month", value: 1 },
      { label: "Last Month", value: 2 },
      { label: "Year to date", value: 3 }
    ],
    StartDate: moment().startOf("months"),
    EndDate: moment(),
    TimeDetail: {},
    ExpenseDetail: {},
    selectedDuration: {},
    isEndDateCalendarOpen: false
  });

  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const {
      MY_ACTIVE_COLLAB,
      SHARED_ACTIVE_COLLAB,
      MY_INACTIVE_COLLAB,
      ALL_ACTIVE_COLLAB
    } = labels;
    setState(prevState => ({
      ...prevState,
      options: [
        { label: ALL_ACTIVE_COLLAB, value: 1 },
        { label: MY_INACTIVE_COLLAB, value: 2 }
      ],
      selectedOption: { value: 1, label: ALL_ACTIVE_COLLAB }
    }));
    GetAllColaborations(1);
    handleDurationOptionChange({ value: 1, label: "Current Month" });
  }, []);

  useEffect(() => {
    if (
      state.selectedCollaboration.CollaborationId &&
      state.selectedCollaboration.CollaborationId !==
        state.selectedCollaboration.CollaborationId
    ) {
      GetCollaborationTimeAndExpenseDetails(
        state.selectedCollaboration,
        state.StartDate,
        state.EndDate
      );
    }
  }, [state.selectedCollaboration]);

  const GetAllColaborations = val => {
    getAllCollaborationsApi(val, token).then(res => {
      if (res.items.length > 0) {
        const allCollaborations = res.items.map(coll => {
          return {
            ...coll,
            IsShared: coll.UserId != UserId,
            HourlyRateType: isNaN(coll.HourlyRateType)
              ? Currencies.find(a => a.Name == coll.HourlyRateType).CurrencyId
              : coll.HourlyRateType
          };
        });
        setState(prevState => ({
          ...prevState,
          allCollaborations
        }));
        const spliturl = location.pathname.match(/\d+/g);
        console.log("allCollaborations", allCollaborations);

        if (spliturl != null) {
          let filteredCollab = allCollaborations.filter(
            coll => coll.CollaborationId == spliturl[0]
          );
          console.log("filteredCollab", filteredCollab);
          handleCollaborationClick(filteredCollab[0]);
        } else {
          console.log("allCollaborations", allCollaborations[0]);

          handleCollaborationClick(allCollaborations[0]);
        }
      }
    });
  };

  const handleOptionChange = option => {
    setState(prevState => ({
      ...prevState,
      selectedOption: option
    }));
    GetAllColaborations(option.value);
  };

  const renderOptionClass = option => {
    return "";
    // if (!option) {
    //   return "";
    // }
    // switch (option.value) {
    //   case 1:
    //     return "yellow";
    //   case 2:
    //     return "green";
    //   case 3:
    //     return "red";
    //   default:
    //     return "";
    // }
  };

  const GetCollaborationTimeAndExpenseDetails = (
    collab,
    StartDate,
    EndDate
  ) => {
    GetCollaborationTimeDetail(
      moment(StartDate).format("DD/MM/YYYY"),
      moment(EndDate).format("DD/MM/YYYY"),
      collab
    );
    GetCollaborationExpenseDetail(
      moment(StartDate).format("DD/MM/YYYY"),
      moment(EndDate).format("DD/MM/YYYY"),
      collab
    );
  };

  const handleCollabHourlyFeeChange = e => {
    const { selectedCollaboration } = state;
    const { name, value, type } = e.target;
    setState(prevState => ({
      ...prevState,
      selectedCollaboration: {
        ...selectedCollaboration,
        HourlyRate: value
      }
    }));
  };

  const handleCollabCurrencyChange = option => {
    const { selectedCollaboration } = state;
    setState(prevState => ({
      ...prevState,
      selectedCollaboration: {
        ...state.selectedCollaboration,
        selectedCurrency: option,
        HourlyRateType: option.value
      }
    }));
    updateHourlyRate(
      selectedCollaboration.CollaborationId,
      selectedCollaboration.HourlyRate,
      option.value
    );
  };

  const handleBlur = () => {
    const { selectedCollaboration } = state;
    updateHourlyRate(
      selectedCollaboration.CollaborationId,
      selectedCollaboration.HourlyRate,
      selectedCollaboration.HourlyRateType
    );
  };

  const updateHourlyRate = (colaborationId, HourlyRate, currencyName) => {
    setState(prevState => ({ ...prevState, isLoading: true }));
    UpdateCollaborationHourlyRate(
      colaborationId,
      HourlyRate,
      currencyName
    ).then(res => {
      if (res.success) {
        GetAllColaborations(state.selectedOption.value);
        const info = {
          status: "success",
          message: "Collaboration updated Successfully"
        };
        setState(prevState => ({ ...prevState, isLoading: false }));
        notificationAction(info);
      } else {
        const info = {
          status: "error",
          message: "some error occured while updating collaboration"
        };
        setState(prevState => ({ ...prevState, isLoading: false }));
        notificationAction(info);
        return;
      }
    });
  };

  const handleDurationOptionChange = option => {
    setState(prevState => ({
      ...prevState,
      selectedDuration: option
    }));
    const { value } = option;
    let StartDate, EndDate;
    if (value == 3) {
      StartDate = moment().startOf("year");
      EndDate = moment();
    } else if (value == 2) {
      StartDate = moment()
        .subtract(1, "month")
        .startOf("month");
      EndDate = moment()
        .subtract(1, "month")
        .endOf("month");
    } else {
      StartDate = moment().startOf("month");
      EndDate = moment();
    }
    setState(prevState => ({
      ...prevState,
      StartDate: StartDate,
      EndDate: EndDate
    }));
    if (state.selectedCollaboration.CollaborationId)
      GetCollaborationTimeAndExpenseDetails(
        state.selectedCollaboration,
        StartDate,
        EndDate
      );
  };

  const GetCollaborationTimeDetail = (StartDate, EndDate, Collaboration) => {
    GetCollaborationTimeDetailApi(
      Collaboration.CollaborationId,
      StartDate,
      EndDate,
      token
    )
      .then(res => {
        setState(prevState => ({
          ...prevState,
          TimeDetail: res.items
        }));
      })
      .catch(err => console.log("Err ", err));
  };

  const GetCollaborationExpenseDetail = (StartDate, EndDate, Collaboration) => {
    GetCollaborationExpenseDetailApi(
      Collaboration.CollaborationId,
      StartDate,
      EndDate,
      token
    )
      .then(res => {
        setState(prevState => ({
          ...prevState,
          ExpenseDetail: res.items
        }));
      })
      .catch(err => console.log("Err ", err));
  };

  const handleStartDateChange = d => {
    if (moment(d) >= moment(state.EndDate)) {
      const info = {
        status: "error",
        message:
          labels.Collaboration_Time_And_Expense_Start_Date_Validation_Message
      };
      notificationAction(info);
      return;
    }
    setState(prevState => ({
      ...prevState,
      StartDate: d
    }));
    GetCollaborationTimeDetail(
      moment(d).format("DD/MM/YYYY"),
      moment(state.EndDate).format("DD/MM/YYYY"),
      state.selectedCollaboration
    );
    GetCollaborationExpenseDetail(
      moment(d).format("DD/MM/YYYY"),
      moment(state.EndDate).format("DD/MM/YYYY"),
      state.selectedCollaboration
    );
  };

  const handleEndDateChange = d => {
    if (moment(d) <= moment(state.StartDate)) {
      const info = {
        status: "error",
        message: labels.Collaboration_Time_And_Expense_Date_Validation_Message
      };
      notificationAction(info);
      return;
    }
    setState(prevState => ({
      ...prevState,
      EndDate: d
    }));
    GetCollaborationTimeDetail(
      moment(state.StartDate).format("DD/MM/YYYY"),
      moment(d).format("DD/MM/YYYY"),
      state.selectedCollaboration
    );
    GetCollaborationExpenseDetail(
      moment(state.StartDate).format("DD/MM/YYYY"),
      moment(d).format("DD/MM/YYYY"),
      state.selectedCollaboration
    );
  };

  const handleCollaborationClick = (item, collaborations = null) => {
    if (!item) {
      setState(prevState => ({
        ...prevState,
        selectedCollaboration: {}
      }));
      return;
    }
    let Currency = Currencies.find(a => a.CurrencyId == item.HourlyRateType);
    if (!item.IsShared) {
      GetSharedCollaborationInfo(item.CollaborationId, token)
        .then(res => {
          item = {
            ...item,
            SharedInfo: res.items.map(a => ({
              ...a,
              CollaborationId: item.CollaborationId
            }))
          };
          setState(prevState => {
            const updatedCollaborations =
              collaborations != null
                ? collaborations
                : prevState.allCollaborations;
            const allCollaborations = updatedCollaborations.map(a => ({
              ...a,
              isSelected: a.CollaborationId == item.CollaborationId,
              isActive: a.CollaborationId == item.CollaborationId,
              IsShared: a.UserId != UserId
            }));

            return {
              ...prevState,
              selectedCollaboration: {
                ...item,
                newEmail: "",
                PreviousEndDate: item.EndDate,
                selectedCurrency: Currency
              },
              allCollaborations: allCollaborations,
              isCollapsed2: false,
              isCollapsed3: false
            };
          });
        })
        .catch(err => console.log("Err ", err));
    } else {
      setState(prevState => {
        const updatedCollaborations =
          collaborations != null ? collaborations : prevState.allCollaborations;
        const allCollaborations = updatedCollaborations.map(a =>
          a.CollaborationId == item.CollaborationId
            ? { ...a, isSelected: true, isActive: true }
            : { ...a, isSelected: false, isActive: false }
        );

        return {
          ...prevState,
          selectedCollaboration: {
            ...item,
            newEmail: "",
            PreviousEndDate: item.EndDate,
            selectedCurrency: Currency
          },
          allCollaborations: allCollaborations,
          isCollapsed2: false
        };
      });
    }
    GetUserDetailByResumeAndRequestId(item.ResumeId, item.RequestId).then(
      res => {
        if (res.success) {
          setState(prevState => ({
            ...prevState,
            selectedUser: res.items
          }));
        }
      }
    );
    GetResumesByResumeId(item.ResumeId)
      .then(res => {
        if (res.success) {
          setState(prevState => ({
            ...prevState,
            selectedResume: res.items
          }));
        }
      })
      .catch(response => {});
  };

  const handleShareCollaborationDelete = item => {
    DeleteShareCollaborationApi(item.CollaborationId, item.UserId, token).then(
      res => {
        if (res.success) {
          setState(prevState => ({
            ...prevState,
            selectedCollaboration: {
              ...state.selectedCollaboration,
              SharedInfo: state.selectedCollaboration.SharedInfo.filter(
                a => a.UserId != item.UserId
              )
            }
          }));
          const info = {
            status: "success",
            message: labels.SNAPSHOT_COLLABORATIONS_UN_SHARE_SUCCESS
          };
          notificationAction(info);
        }
      }
    );
  };

  const handleShareCollaborationInputChange = e => {
    const { name, value } = e.target;
    setState(prevState => ({
      ...prevState,
      selectedCollaboration: {
        ...state.selectedCollaboration,
        newEmail: value
      }
    }));
  };

  const handleShareCollaboration = () => {
    const {
      CollaborationId,
      newEmail,
      SharedInfo
    } = state.selectedCollaboration;
    if (!isValidEmail(newEmail)) {
      const info = {
        status: "error",
        message: labels.VALID_EMAIL
      };
      notificationAction(info);
      return;
    }
    ShareCollaborationApi(CollaborationId, newEmail, token).then(res => {
      if (res.success) {
        const sharedInfo = SharedInfo.concat({
          CollaborationId: CollaborationId,
          UserEmail: newEmail,
          UserId: res.items.UserId
        });
        setState(prevState => ({
          ...prevState,
          selectedCollaboration: {
            ...state.selectedCollaboration,
            SharedInfo: sharedInfo,
            newEmail: "",
            PreviousEndDate: sharedInfo.EndDate
          }
        }));
        const info = {
          status: "success",
          message: labels.SNAPSHOT_COLLABORATIONS_SHARE_SUCCESS
        };
        notificationAction(info);
      } else {
        let info = {};
        if (res.message == "Collaboration already shared with that user.") {
          info = {
            status: "error",
            message: labels.USER_COLLABORATION_SHARE_MSG
          };
        } else if (res.message == "Email Address Not exist in PDCore") {
          info = {
            status: "error",
            message: labels.USER_EMAIL_DOESNOT_EXIST
          };
        } else {
          info = {
            status: "error",
            message: labels.COLLABORATION_SHARE_FAILED
          };
        }
        notificationAction(info);
      }
    });
  };

  const handleCollaborationEndDateChange = d => {
    setState(prevState => ({
      ...prevState,
      selectedCollaboration: {
        ...state.selectedCollaboration,
        EndDate: d
      }
    }));
    const EndDate = moment(d);
    const { CollaborationId } = state.selectedCollaboration;
    UpdateCollaborationEndDate(
      CollaborationId,
      moment(EndDate).format("DD/MM/YYYY")
    ).then(res => {
      if (res.success) {
        const info = {
          status: "success",
          message: labels.Collaboration_Extend_Success_Message
        };
        notificationAction(info);
        let allCollaborations = state.allCollaborations.map(a =>
          a.CollaborationId == CollaborationId
            ? { ...a, EndDate: EndDate, PreviousEndDate: EndDate }
            : a
        );
        if (state.selectedOption.value == "2") {
          allCollaborations = allCollaborations.filter(
            a => a.CollaborationId != CollaborationId
          );
        }
        setState(prevState => ({
          ...prevState,
          allCollaborations,
          selectedCollaboration: {
            ...state.selectedCollaboration,
            PreviousEndDate: EndDate
          },
          isEndDateCalendarOpen: false
        }));
        handleCollaborationClick(allCollaborations[0]);
        return;
      } else {
        const info = {
          status: "error",
          message: labels.Collaboration_Extend_Error_Message
        };
        notificationAction(info);
        return;
      }
    });
  };

  const handleCollaborationExtend = t => {
    if (t) {
      setState(prevState => ({
        ...prevState,
        collaborationDataChange1: true
      }));
      return false;
    }
    setState(prevState => ({
      ...prevState,
      isEndDateCalendarOpen: true
    }));
  };

  const handleDateOnBlur = () => {
    setState(prevState => ({
      ...prevState,
      isEndDateCalendarOpen: false
    }));
  };

  const {
    isCollapsed1,
    isCollapsed2,
    isCollapsed3,
    isCollapsed4,
    isCollapsed5,
    selectedOption,
    selectedCollaboration,
    selectedUser,
    selectedResume,
    isEndDateCalendarOpen,
    isLoading
  } = state;
  const {
    SNAPSHOT_COLLABORATIONS,
    SNAPSHOT_COLLABORATION_DETAIL,
    SNAPSHOT_COLLABORATION_TIME_AND_EXPENSES,
    SNAPSHOT_COLLABORATION_PRESENT,
    SNAPSHOT_COLLABORATION_RESUME
  } = labels;

  return (
    <div className="collaborations-view">
      {isLoading && (
        <div id="loader-wrapper">
          <div className="loader-container">
            <div className="loader">
              <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
                <defs>
                  <filter id="goo">
                    <feGaussianBlur
                      in="SourceGraphic"
                      stdDeviation="6"
                      result="blur"
                    />
                    <feColorMatrix
                      in="blur"
                      mode="matrix"
                      values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                      result="goo"
                    />
                    <feBlend in="SourceGraphic" in2="goo" />
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </div>
      )}
      <Column collapse={isCollapsed1} className="col-1">
        <Column.Collapsed
          text={SNAPSHOT_COLLABORATIONS}
          onClick={() =>
            setState(prevState => ({ ...prevState, isCollapsed1: false }))
          }
          isHelpActive={isHelpActive}
          tooltipButton={labels.ToolTipSnapshotExpand}
          tooltipHelp={labels.hlptxtSnapshotExpand}
          tooltipPlace="left"
          testId="expandbtn1"
        />
        <Column.Head>
          <div className="heading">{SNAPSHOT_COLLABORATIONS}</div>
          <Button
            className="collapseBtn"
            testId="Collapasebtn1"
            onClick={() =>
              setState(prevState => ({ ...prevState, isCollapsed1: true }))
            }
            tooltipButton={labels.ToolTipSnapshotCollapse}
            tooltipHelp={labels.hlptxtSnapshotCollapse}
            tooltipPlace="left"
            isHelpActive={isHelpActive}
          />
        </Column.Head>
        {!isCollapsed1 && (
          <Select
            readonly={true}
            searchable={false}
            testId={"collaborationDropdown"}
            name="AllPresentation"
            value={selectedOption}
            className={`select-input ${renderOptionClass(selectedOption)}`}
            placeholder={labels.presentPrimaryRolePlaceholder}
            onChange={selectedOption => handleOptionChange(selectedOption)}
            options={state.options}
            clearable={false}
          />
        )}
        <Column.Body>
          <div className="tictell-list">
            {state.allCollaborations &&
              state.allCollaborations.map(item => (
                <div
                  key={item.CollaborationId}
                  data-testid={`collaborations-list-item${
                    item.isActive ? "activeItem" : ""
                  }`}
                  className={`tictell-list-item-container  ${
                    item.isSelected ? "selected-item" : ""
                  } ${item.isActive ? "activeItem" : ""}`}
                  onClick={() => handleCollaborationClick(item)}
                >
                  <div
                    className={`dashItemImg ${
                      item.PresenatationLogo ? "" : "no-dp"
                    }`}
                  >
                    <img
                      className="roundImg"
                      src={
                        item.PresenatationLogo ? item.PresenatationLogo : ImgSrc
                      }
                      alt={"UserName"}
                    />
                  </div>
                  <div className="name-container">
                    <label data-testid={`collabs-list-owner-username`}>
                      {item.UserName}
                    </label>
                    <label data-testid={`collabs-list-owner-title`}>
                      {item.Title}
                    </label>
                  </div>
                  <span className={`${item.IsShared ? "shared-icon" : ""}`} />
                </div>
              ))}
          </div>
        </Column.Body>
      </Column>
      {state.allCollaborations &&
        state.allCollaborations.length > 0 &&
        selectedCollaboration && (
          <Column collapse={isCollapsed2} className="col-2">
            <Column.Collapsed
              text={SNAPSHOT_COLLABORATION_DETAIL}
              onClick={() =>
                setState(prevState => ({ ...prevState, isCollapsed2: false }))
              }
              isHelpActive={isHelpActive}
              tooltipButton={labels.ToolTipFeedBackExpandCreate}
              tooltipHelp={labels.HlpTooltipFeedbackExpandCreateButton}
              tooltipPlace="left"
              testId="expandbtn2"
            />
            <Column.Head>
              <div className="heading">{SNAPSHOT_COLLABORATION_DETAIL}</div>
              <Button
                className="collapseBtn"
                onClick={() =>
                  setState(prevState => ({
                    ...prevState,
                    isCollapsed2: true
                  }))
                }
                tooltipButton={labels.ToolTipFeedBackCollaspeCreate}
                tooltipHelp={labels.HlpTooltipFeedbackCollapseCreateButton}
                tooltipPlace="left"
                isHelpActive={isHelpActive}
                testId="collapsebtn2"
              />
            </Column.Head>
            {!isCollapsed2 && (
              <div
                className={`tictell-list-item-container activeItem collaboration-heading`}
              >
                <div
                  className={`dashItemImg ${
                    selectedCollaboration.PresenatationLogo ? "" : "no-dp"
                  }`}
                >
                  <img
                    className="roundImg"
                    src={
                      selectedCollaboration.PresenatationLogo
                        ? selectedCollaboration.PresenatationLogo
                        : ImgSrc
                    }
                    alt={"UserName"}
                  />
                </div>
                <div className="name-container">
                  <label data-testid={`collabs-details-owner-username`}>
                    {selectedCollaboration.UserName}
                  </label>
                  <label data-testid={`collabs-details-owner-title`}>
                    {selectedCollaboration.Title}
                  </label>
                </div>
                <span
                  className={`${
                    selectedCollaboration.IsShared ? "shared-icon" : ""
                  }`}
                />
              </div>
            )}
            <Column.Body>
              <CollaborationDetail
                labels={labels}
                handleBlur={handleBlur}
                selectedCollaboration={selectedCollaboration}
                handleShareCollaborationDelete={handleShareCollaborationDelete}
                handleShareCollaborationInputChange={
                  handleShareCollaborationInputChange
                }
                handleCollaborationShare={handleShareCollaboration}
                handleCollaborationEndDateChange={
                  handleCollaborationEndDateChange
                }
                handleCollaborationExtend={handleCollaborationExtend}
                isEndDateCalendarOpen={isEndDateCalendarOpen}
                collaborationDataChange1={state.collaborationDataChange1}
                allCurrencies={Currencies}
                handleDateOnBlur={handleDateOnBlur}
                handleCollabCurrencyChange={handleCollabCurrencyChange}
                handleCollabHourlyFeeChange={handleCollabHourlyFeeChange}
              ></CollaborationDetail>
            </Column.Body>
          </Column>
        )}
      {
        <Column collapse={isCollapsed3} className="col-3">
          <Column.Collapsed
            text={SNAPSHOT_COLLABORATION_TIME_AND_EXPENSES}
            onClick={() =>
              setState(prevState => ({ ...prevState, isCollapsed3: false }))
            }
            isHelpActive={isHelpActive}
            tooltipButton={labels.ToolTipFeedBackExpandCreate}
            tooltipHelp={labels.HlpTooltipFeedbackExpandCreateButton}
            tooltipPlace="left"
            testId="expandbtn3"
          />
          <Column.Head>
            <div className="heading">
              {SNAPSHOT_COLLABORATION_TIME_AND_EXPENSES}
            </div>
            <Button
              className="collapseBtn"
              onClick={() =>
                setState(prevState => ({ ...prevState, isCollapsed3: true }))
              }
              tooltipButton={labels.ToolTipFeedBackCollaspeCreate}
              tooltipHelp={labels.HlpTooltipFeedbackCollapseCreateButton}
              tooltipPlace="left"
              isHelpActive={isHelpActive}
              testId="collapsebtn3"
            />
          </Column.Head>
          <Column.Body>
            <TimeAndExpenses
              CollaborationId={selectedCollaboration.CollaborationId}
              labels={labels}
              token={token}
              options={state.durationOptions}
              selectedOption={state.selectedDuration}
              StartDate={state.StartDate}
              EndDate={state.EndDate}
              TimeDetail={state.TimeDetail}
              ExpenseDetail={state.ExpenseDetail}
              handleOptionChange={handleDurationOptionChange}
              handleStartDateChange={handleStartDateChange}
              handleEndDateChange={handleEndDateChange}
            />
          </Column.Body>
        </Column>
      }
      {
        <Column collapse={isCollapsed4} className="col-4">
          <Column.Collapsed
            text={SNAPSHOT_COLLABORATION_PRESENT}
            onClick={() =>
              setState(prevState => ({ ...prevState, isCollapsed4: false }))
            }
            isHelpActive={isHelpActive}
            tooltipButton={labels.ToolTipFeedBackExpandCreate}
            tooltipHelp={labels.HlpTooltipFeedbackExpandCreateButton}
            tooltipPlace="left"
            testId="expandbtn4"
          />
          <Column.Head>
            <div className="heading">{SNAPSHOT_COLLABORATION_PRESENT}</div>
            <Button
              className="collapseBtn"
              onClick={() =>
                setState(prevState => ({ ...prevState, isCollapsed4: true }))
              }
              tooltipButton={labels.ToolTipFeedBackCollaspeCreate}
              tooltipHelp={labels.HlpTooltipFeedbackCollapseCreateButton}
              tooltipPlace="left"
              isHelpActive={isHelpActive}
              testId="collapsebtn4"
            />
          </Column.Head>
          <Column.Body>
            <Presentations selectedUser={selectedUser} labels={labels} />
          </Column.Body>
        </Column>
      }
      {
        <Column collapse={isCollapsed5} className="col-5">
          <Column.Collapsed
            text={SNAPSHOT_COLLABORATION_RESUME}
            onClick={() =>
              setState(prevState => ({ ...prevState, isCollapsed5: false }))
            }
            isHelpActive={isHelpActive}
            tooltipButton={labels.ToolTipFeedBackExpandCreate}
            tooltipHelp={labels.HlpTooltipFeedbackExpandCreateButton}
            tooltipPlace="left"
            testId="expandbtn5"
          />
          <Column.Head>
            <div className="heading">{SNAPSHOT_COLLABORATION_RESUME}</div>
            <Button
              className="collapseBtn"
              onClick={() =>
                setState(prevState => ({ ...prevState, isCollapsed5: true }))
              }
              tooltipButton={labels.ToolTipFeedBackCollaspeCreate}
              tooltipHelp={labels.HlpTooltipFeedbackCollapseCreateButton}
              tooltipPlace="left"
              isHelpActive={isHelpActive}
              testId="collapsebtn5"
            />
          </Column.Head>
          <Column.Body className="ios-height-fix">
            <ResumeDetail resume={selectedResume} />
          </Column.Body>
        </Column>
      }
    </div>
  );
};

const mapStateToProps = ({ systemLabel, navigation, userInfo }) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  const User = user ? user : StorageService.getUser();
  const { isHelpActive } = navigation;
  return { labels, isHelpActive, User };
};
export default connect(mapStateToProps, { notificationAction })(Collaborations);
