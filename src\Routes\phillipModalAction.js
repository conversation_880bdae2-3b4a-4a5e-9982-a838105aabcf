import { ActionTypes } from "../actions/ActionsTypes";

export const onStateChangeActions = ({ show, isLoading }) => dispatch => {
  if (show !== undefined)
    dispatch({
      type: ActionTypes.phillipSearcherModal.SHOW_MODAL,
      payload: show
    });
  else if (isLoading !== undefined) {
    dispatch({
      type: ActionTypes.CREATE_NEW_OPPORTUNITY_SEARCHER.IS_LOADING,
      payload: isLoading
    });
  }
};
