import "./capsule-list.scss";

const CapsuleList = ({ children, className, testId }) => (
  <ul className={`round-circle-list ${className}`} data-testid={testId}>
    {children}
  </ul>
);

const Capsule = ({ children, testId }) => (
  <li data-testid={testId} className="round-circle-item">
    <div className="item-inner-wrapper">{children}</div>
  </li>
);

const Label = ({ children, testId }) => (
  <label className="form-label" data-testid={testId}>
    {children}
  </label>
);

const Button = ({ testId, onClick, onFocus }) => (
  <button
    onClick={onClick}
    className="closeBtn"
    data-testid={testId}
    onFocus={onFocus}
  />
);

CapsuleList.propType = {
  className: ""
};

CapsuleList.Capsule = Capsule;
CapsuleList.Label = Label;
CapsuleList.Button = Button;

export default CapsuleList;
