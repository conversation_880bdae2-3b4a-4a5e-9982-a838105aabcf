import moment from "moment";

export const getYear = date => {
  return date ? moment.utc(`${date}`).format("YYYY") : "";
};

export const getFullDate = date => {
  return date ? moment.utc(`${date}`).format() : "";
};

export const getFileData = (file, callback) => {
  const reader = new FileReader();
  reader.onload = function(e) {
    const arrayBuffer = e.target.result;
    const bytes = new Uint8Array(arrayBuffer);
    let binary = "";
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    const base64String = btoa(binary);
    callback(base64String);
  };
  reader.readAsArrayBuffer(file);
};
