import { useCallback, useEffect } from "react";
import { notificationAction } from "../../actions-alpha/notification";
import { htmlParser } from "../../utilities/helpers";
import "./notification.scss";
import { twMerge } from "tailwind-merge";
import { useDispatch, useSelector } from "react-redux";
import CheckIcon from "../../assets-alpha/images/svg/success-check.svg?react";

const Notification = ({ className }) => {
  const dispatch = useDispatch();
  const notification = useSelector(state => state.notification);

  const hideNotification = useCallback(() => {
    dispatch(
      notificationAction({
        message: "",
        status: "success"
      })
    );
  }, [dispatch]);

  useEffect(() => {
    const timer = setTimeout(() => {
      hideNotification();
    }, 6000);
    return () => clearTimeout(timer);
  }, [notification.message, hideNotification]);
  const status = notification.status;
  const message = notification.message;

  return (
    message && (
      <div
        className={twMerge(`
        fixed top-0 left-0 right-0 mx-auto w-[90%] text-center px-[10px] py-[8px] 
        text-[13px] z-[99999] rounded-b-[5px] border-b-2 text-white shadow-md cursor-pointer 
        bg-[url('/assets/images/dialog-bg.png')] bg-repeat-x bg-top
        flex items-center justify-center gap-2
        ${status === "success" ? "bg-green-500 border-b-green-600" : ""}
        ${status === "error" ? "bg-red-500 border-b-red-700" : ""}
        ${status === "info" ? "bg-blue-500 border-b-blue-700" : ""}
        ${message ? "animate-fadeInDown" : "animate-fadeOutUp"}
        ${className}
      `)}
        onClick={hideNotification}
      >
        {status === "success" && <CheckIcon />}
        <div>{htmlParser(message)}</div>
      </div>
    )
  );
};

export default Notification;
