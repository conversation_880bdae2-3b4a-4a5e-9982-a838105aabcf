import { useSelector } from "react-redux";
import PageWrapper from "../../components-alpha/PageWrapper/PageWrapper";
import DashboardIpro from "./DashboardIpro";
import DashboardRecruiter from "./DashboardRecruiter";

const Dashboard = () => {
  const { userInfo } = useSelector(state => state);
  const labels = useSelector(state => state.systemLabel.labels?.dashboard);

  return (
    <PageWrapper title={labels?.title}>
      {userInfo?.user?.IsFreelancer ? (
        <DashboardIpro />
      ) : (
        <DashboardRecruiter />
      )}
    </PageWrapper>
  );
};

export default Dashboard;
