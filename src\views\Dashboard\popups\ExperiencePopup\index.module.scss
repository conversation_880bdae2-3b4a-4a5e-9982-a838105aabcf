@use "../../../../assets/sass/variables" as *;
@use "../../../../assets/sass/mixins" as *;

.socialLinks {
  display: flex;
  gap: 15px;
  align-items: center;
  .socialLink {
    cursor: pointer;
    font-size: 25px;
    &.activeSocialLink,
    &:hover {
      color: $lightPurple;
    }
  }
}

.editProfileButton {
  @include breakpoint("screen991") {
    position: absolute !important;
    right: 10px;
    top: 10px;
  }
}
.experience_debounce_select {
  display: "inline-block" !important;
  width: 100% !important;
}
.experienceDescriptionInput {
  height: 130px;
  margin-bottom: 55px;
}

.RichTextEditor {
  border: solid;
}
.date_input {
  display: inline-block;
  width: 100%;
}
.checkbox_style {
  display: inline-block;
  width: 100%;
}
.popUpForm {
  h5 {
    font-size: 18px;
    font-weight: 400;
  }
  .customSelect {
    span,
    input {
      font-size: 12px;
    }
  }
}
@include breakpoint("screen560") {
  .checkbox_style {
    display: block;
    margin-top: 0px;
    width: 100%;
  }
  .date_input {
    width: "100%";
  }
}
