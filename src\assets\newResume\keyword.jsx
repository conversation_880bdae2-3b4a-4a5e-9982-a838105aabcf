
const keyword = props => {
  return (
    <svg
      width="35"
      height="35"
      viewBox="0 0 35 35"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M29.035 7.373c-1.989-1.992-4.738-3.224-7.774-3.224-3.017 0-5.75 1.216-7.736 3.185l-0.046 0.042c-1.992 1.99-3.224 4.741-3.224 7.779 0 1.409 0.265 2.756 0.748 3.994l-4.429 4.336c-0.144 0.141-0.233 0.337-0.233 0.554 0 0.002 0 0.005 0 0.007l0.020 1.342-1.357-0.020c-0.43 0.007-0.777 0.358-0.777 0.789 0 0.001 0 0.003 0 0.004l0.020 1.337-1.353-0.020c-0.431 0.006-0.778 0.356-0.778 0.788 0 0.001 0 0.002 0 0.004l0.017 1.342-1.369-0.020c-0.424 0.015-0.762 0.362-0.762 0.789 0 0.007 0 0.014 0 0.021l0.174 5.158c0.016 0.414 0.347 0.746 0.76 0.762l5.16 0.174c0.006 0 0.014 0 0.021 0 0.218 0 0.415-0.088 0.558-0.23l10.787-10.79c1.128 0.428 2.432 0.676 3.794 0.676 3.020 0 5.756-1.219 7.742-3.192l0.039-0.036c1.992-1.989 3.224-4.739 3.224-7.776s-1.232-5.787-3.224-7.776zM5.8 34.912l-4.079-0.138-0.123-3.597 1.322 0.020c0 0 0 0 0 0 0.437 0 0.792-0.354 0.792-0.792 0-0.001 0-0.001 0-0.002l-0.020-1.342 1.342 0.020c0 0 0 0 0.001 0 0.438 0 0.792-0.354 0.793-0.792l-0.020-1.337 1.336 0.015c0 0 0 0 0.001 0 0.438 0 0.793-0.355 0.793-0.793 0-0 0-0 0-0.001l-0.024-1.811 3.775-3.775c0.473 0.822 1.008 1.533 1.619 2.17q0.082 0.086 0.169 0.173c0.053 0.053 0.106 0.104 0.16 0.156l-4.902 4.902c-0.143 0.142-0.231 0.34-0.231 0.557 0 0.435 0.352 0.787 0.787 0.787 0.217 0 0.414-0.088 0.556-0.23l5.005-5.005c0.306 0.223 0.659 0.449 1.025 0.655zM27.925 21.818l-0.032 0.032c-1.705 1.684-4.049 2.725-6.637 2.725-2.268 0-4.35-0.8-5.978-2.132l-0.008-0.007q-0.163-0.134-0.321-0.276l-0.012-0.011c-0.039-0.035-0.077-0.071-0.116-0.107l-0.064-0.061q-0.085-0.081-0.168-0.163t-0.159-0.163l-0.043-0.045c-0.32-0.34-0.619-0.709-0.89-1.099l-0.029-0.045c-0.035-0.051-0.070-0.103-0.104-0.154-0.959-1.451-1.53-3.232-1.53-5.146 0-5.203 4.218-9.42 9.42-9.42s9.42 4.218 9.42 9.42c0 2.597-1.051 4.949-2.751 6.653zM22.878 11.188c-1.285 0.006-2.323 1.049-2.323 2.334 0 0.616 0.238 1.176 0.628 1.593l0.050 0.046c0.423 0.419 1.006 0.678 1.649 0.678 0.628 0 1.198-0.247 1.618-0.648l0.031-0.030c0.417-0.423 0.675-1.004 0.675-1.645 0-0.643-0.259-1.226-0.679-1.649-0.42-0.419-0.999-0.678-1.639-0.678-0.004 0-0.007 0-0.011 0z"
        stroke={props.color || "#8F82F5"}
        strokeWidth="1.5"
      />
    </svg>
  );
};

export default keyword;
