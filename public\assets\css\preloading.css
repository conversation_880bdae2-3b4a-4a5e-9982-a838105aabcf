body {
  background: #3e335e;
  margin: 0;
  height: 100vh;
}

.preLoader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  justify-content: center;
  align-items: center;
  text-align: center;
}

.preLoader div:first-child {
  margin-bottom: 0px;
}

.loaderImage {
  display: inline-block;
  max-width: 200px;
}

.loader-container {
  position: relative;
  height: 100px;
  width: 60px;
}

.loader {
  position: relative;
  width: 30px;
  aspect-ratio: 1/2;
  filter: url(#goo);
  margin: 0 auto;
  rotate: 90deg;
}

#goo {
  display: none;
}

.loader:before,
.loader:after {
  content: "";
  position: absolute;
  inset: 0;
  margin: auto;
  width: 100%;
  aspect-ratio: 1/1;
  animation: 1.5s ease-out infinite loader;
  border-radius: 50%;
}

.loader:before {
  background: rgb(2, 202, 168);
}

.loader:after {
  animation-delay: 0.75s;
  background: rgb(142, 129, 245);
}

@keyframes loader {
  0%,
  50%,
  100% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(70%);
  }
  75% {
    transform: translateY(-70%);
  }
}

@media (max-width: 600px) {
  .loaderImage {
    width: 170px;
  }
}
