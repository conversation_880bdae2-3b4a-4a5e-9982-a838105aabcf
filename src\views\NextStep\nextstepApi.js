import axios from "axios";
import { ApiUrl } from "../../api/apiUrls";

export const UploadAndGetRolesAndSkillsFromResumeApi = (fileData) => {
  return axios
    .post("Resumes/UploadAndGetRolesAndSkillsFromResume", {
      fileData,
    })
    .then(({ data }) => data);
};

export const UpdateResumeApi = (resume) => {
  return axios
    .put(ApiUrl.NextStep.UpdateResume, resume)
    .then(({ data }) => data)
    .catch((response) => response);
};

export const GetSuggestedProfilesForResumeApi = () => {
  return axios
    .get(`${ApiUrl.NextStep.GetSuggestedProfilesforResume}?isFreelancer=true`)
    .then(({ data }) => data)
    .catch((response) => response);
};

export const GetSuggestedSkillsForResumeApi = () => {
  return axios
    .get(ApiUrl.NextStep.GetSuggestedSkillsforResume)
    .then(({ data }) => data)
    .catch((response) => response);
};

export const GetMarketPopularityIndexApi = () => {
  return axios
    .get(ApiUrl.NextStep.GetMarketPopularityIndex)
    .then(({ data }) => data)
    .catch((response) => response);
};

export const getCurrenciesApi = () => {
  return axios.get(ApiUrl.Currency.AllCurrenciesLookup).then(({ data }) => {
    return data;
  });
};

export const GetUserExpectedSalaryApi = (countryId, currencyId, durationType) => {
  return axios
    .get(ApiUrl.NextStep.GetUserExpectedSalary(countryId, currencyId, durationType))
    .then(({ data }) => data)
    .catch((response) => response);
};

export const addResumeProfileApi = (profileId) => {
  return axios
    .get(ApiUrl.NextStep.AddResumeProfile(profileId ))
    .then(({ data }) => data)
    .catch((response) => response);
};

export const addResumeSkillApi = (skillId) => {
  return axios
    .get(ApiUrl.NextStep.AddResumeSkill(skillId ))
    .then(({ data }) => data)
    .catch((response) => response);
};
