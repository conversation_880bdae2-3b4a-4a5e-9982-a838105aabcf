import { Button } from "antd";
import { useSelector } from "react-redux";

const PrivacyPolicy = ({
  signUpData,
  setPrivacyPolicyDrawerOpen = { setPrivacyPolicyDrawerOpen }
}) => {
  const labels = useSelector(state => state.systemLabel.labels);
  const data = signUpData?.filter(item => item?.ConfigurationId === 4);
  if (!data?.length) return null;
  return (
    <div className="flex w-full justify-center overflow-y-scroll">
      <div
        className="w-full m-4 md:w-[71%] md:mt-11 mb-[108px] overflow-y-scroll
           [&_h2]:mt-[0px] [&_h2]:mb-[20px] [&_h2]:text-[#343333] [&_h2]:md:text-[24px]  [&_h2]:text-[18px]  [&_h2]:font-medium
           [&_h3]:mt-[20px] [&_h3]:mb-[20px] [&_h3]:text-[#343333] [&_h3]:md:text-[24px] [&_h3]:text-[18px] [&_h3]:font-medium
           [&_p]:md:text-[16px] [&_p]:text-[13px] [&_p]:text-[#878787] [&_p]:font-normal [&_p]:leading-6
           [&_ul]:!list-disc [&_ul]:md:!pl-[52px] [&_ul]:!pl-[32px] [&_ul]:!text-[#878787] 
           [&_li]:md:text-[16px] [&_li]:text-[13px] [&_li]:text-[#878787] [&_li]:font-normal [&_li]:leading-6"
        dangerouslySetInnerHTML={{ __html: data[0].ConfigValue }}
      />

      <div className="fixed bottom-0 bg-white px-4 md:px-[64px] flex justify-between items-center w-full h-[80px] border-t border-[#EAE5FC]">
        <div className="hidden md:flex items-center gap-3 max-w-[140px] ">
          <div className="text-sm text-[#343333] !font-poppins">
            © {new Date().getFullYear()} {labels?.Prodoo_Inc_Label}
          </div>
        </div>
        <div className="w-full md:w-auto">
          <Button
            className="w-full md:w-[76px]"
            type="primary"
            onClick={() => {
              setPrivacyPolicyDrawerOpen(false);
            }}
          >
            {labels?.CLOSE_LABEL}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicy;
