import { useState } from "react";
import { connect } from "react-redux";
import "./view-wrapper.scss";
import Navigation from "../Navigation/Navigation";
import Header from "../../components-alpha/Header/Header";

const ViewWrapper = props => {
  const [isNavShrink, setIsNavShrink] = useState(false);

  const handleNavigationToggle = () => {
    setIsNavShrink(!isNavShrink);
  };

  const handleSubMenuOpen = () => {
    if (isNavShrink && window.innerWidth > 1240) {
      setIsNavShrink(false);
    }
  };

  const removeNavShrink = () => {
    if (isNavShrink && window.innerWidth <= 1240) {
      setIsNavShrink(false);
    }
  };

  const handleMobileNavShrink = () => {
    if (!isNavShrink && window.innerWidth <= 1240) {
      setIsNavShrink(true);
    }
  };

  const { children } = props;
  return (
    <div className="app-container">
      <Navigation
        isNavShrink={isNavShrink}
        onSubMenuOpen={handleSubMenuOpen}
        onMobileNavShrink={handleMobileNavShrink}
        // location={location}
        onNavShrink={removeNavShrink}
      />
      <div className="app-view-container" onClick={removeNavShrink}>
        <Header onNavigationToggle={handleNavigationToggle} />
        {children}
      </div>
    </div>
  );
};

ViewWrapper.defaultProps = {
  className: ""
};

const mapStateToProps = ({ notification }) => {
  return { notification };
};

export default connect(mapStateToProps)(ViewWrapper);
