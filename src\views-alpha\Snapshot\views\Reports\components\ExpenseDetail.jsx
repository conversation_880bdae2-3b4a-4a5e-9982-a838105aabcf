import moment from "moment";
import CommonDrawer from "../../../../../common-alpha/Drawer/Drawer";
import ArrowDownIcon from "../../../../../assets-alpha/images/svg/arrow-down-icon.svg?react";
import { Grid } from "antd";
import { InfoItemCard } from "../../../../Resume/Component/ExperienceSection";
import { compact } from "lodash";

const ExpenseDetail = ({
  snapshotExpenseDate,
  Dateformatelabel,
  CategoryLabel,
  CurrencyLabel,
  CommentLabel,
  AmountLabel,
  AttachmentsLabel,
  currentExpense = {},
  DetailsLabel,
  currenciesList = [],
  expenseCategoriesList = [],
  ReviewedBylabel,
  ReviewedDatelabel,
  onClose,
  Status
}) => {
  const { md } = Grid.useBreakpoint();

  const {
    ExpenseId,
    Comment = "",
    SpentOn,
    Amount = "",
    CurrencyId,
    ReceiptPath,
    CollaborationId,
    UserId,
    ExpenseCategoryId,
    ExpenseFiles,
    ReviewedBy,
    ReviewedDate
  } = currentExpense || {};

  const files =
    ExpenseFiles?.length > 0
      ? ExpenseFiles?.map(attach => {
          return {
            label: "Attachment",
            value: (
              <a
                target="_blank"
                rel="noreferrer"
                href={`${"https://tictell.prodoo.com/TictellFileContainer/"}${
                  attach.Name
                }`}
                key={attach.Name}
                download
              >
                {attach.Name}
              </a>
            )
          };
        })
      : [];
  const data = compact([
    {
      label: snapshotExpenseDate,
      value: moment(currentExpense?.SpentOn).format("ddd MMM DD YYYY")
    },
    {
      label: CategoryLabel,
      value: currentExpense?.CategoryName?.Title
    },

    {
      label: AmountLabel,
      value: `${currentExpense?.Amount} ${currentExpense?.CollaborationCurrency}`
    },
    {
      label: "Description",
      value: currentExpense?.Comment || "None"
    },
    ...files,
    (Status === 2 || Status === 3) && {
      label: ReviewedBylabel,
      value: ReviewedBy
    },
    ReviewedDate &&
      (Status === 2 || Status === 3) && {
        label: ReviewedDatelabel,
        value: moment(ReviewedDate).format("DD-MM-YYYY")
      }
  ]);

  return (
    <CommonDrawer
      drawerKey="suggestions"
      zIndex={2000}
      title={"Expense Detail"}
      mask={md ? false : true}
      onClose={onClose}
      open={true}
      closeIcon={
        <ArrowDownIcon className="rotate-270 max-md:rotate-0 !text-[#343333] w-[28px] h-[28px]" />
      }
      width={400}
      rootClassName="
              [&_.ant-drawer-content-wrapper]:!inset-y-[90px] 
              [&_.ant-drawer-content-wrapper]:!right-[450px]
              [&_.ant-drawer-content-wrapper]:max-md:!inset-y-[8px]
              "
      className="[&_.ant-drawer-header]:md:!border-b-0 [&_.ant-drawer-header]:md:!pb-4.5
              [&_.ant-drawer-body]:!pt-0
              "
    >
      <div className="expense-detail">
        <InfoItemCard fields={data} labelClassName="min-w-[100px]" />
      </div>
    </CommonDrawer>
  );
};

export default ExpenseDetail;
