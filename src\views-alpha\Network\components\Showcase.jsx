import { Button } from "antd";
import CloseModal from "../../../assets-alpha/images/svg/close-modal.svg";
import AvatarIcon from "../../../assets-alpha/images/svg/avatar-workplace.svg";
import LocationIcon from "../../../assets-alpha/images/svg/location.svg?react";
import CallIcon from "../../../assets-alpha/images/svg/call.svg?react";
import { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { privateRoutes } from "../../../Routes/routing";
const Showcase = ({
  setShowAddNetworkDrawer,
  selectedUser,
  UsersList,
  state,
  handleAcceptNetwork,
  handleDeleteNetwork,
  handleRemoveConnectedNetwork,
  setShowAddNetworkModal,
  loading
}) => {
  const loggedUser = JSON.parse(localStorage.getItem("User"));
  const navigate = useNavigate();
  const location = useLocation();
  const [status, setStatus] = useState(
    location.pathname === "/invited-networks"
      ? "invited"
      : location.pathname === "/connected-networks"
      ? "connected"
      : location.pathname === "/invitations-networks"
      ? "invitations"
      : ""
  );
  const data = UsersList?.find(item => {
    return item?.UserId === selectedUser?.UserId;
  });
  return (
    <div className="flex flex-col">
      <div className="relative w-[100%] md:w-[371px] h-[64px] ">
        <div className="fixed md:w-[371px] w-[100%] h-[64px] border-b border-[#EAE5FC] z-[999] bg-white rounded-tl-2xl rounded-tr-2xl">
          <div className="flex w-full h-[64px] justify-between items-center p-6">
            <div className="flex justify-between md:gap-5 w-full md:h-10 items-center h-[64px] border-b border-[#EAE5FC] md:border-b-0 md:pl-0 md:pr-0">
              <img
                src={CloseModal}
                alt=""
                className="pointer md:order-1 order-2"
                onClick={() => {
                  setShowAddNetworkDrawer(false);
                }}
              />
              <h1 className="!m-0 flex-1 order-1 md:order-2 !text-[18px] !font-semibold md:!text-2xl md:font-medium">
                User detail
              </h1>
            </div>
          </div>
        </div>
      </div>
      <div className="p-6">
        <div className="flex flex-col w-full items-center bg-[#F4F2FE] rouded-[12px] border border-[#F4F2FE] p-4 rounded-[12px]">
          <img
            src={data?.ProfilePicture ? data?.ProfilePicture : AvatarIcon}
            alt="profile-image"
            className="w-[100px] h-[100px] rounded-2xl flex justify-center items-center"
          />
          <h1 className="!m-0 !text-2xl !font-medium text-[#343333] !mt-[14px]">
            {data?.UserFirstname} {data?.UserLastname}
          </h1>
          <p className="!mt-1 !text-sm font-normal text-[#878787]">
            {data?.UserEmail}
          </p>
          <div className="flex w-full justify-between mt-7 !px-2">
            <span className="flex gap-2 text-[13px] text-[#343333] font-normal">
              <LocationIcon className="text-[#878787]" />
              {state?.companyList?.CountryName || "N/A"}
            </span>
            <span className="h-[22.5px] border border-[#EAE5FC]"></span>
            <span className="flex gap-2 text-[13px] text-[#343333] font-normal">
              <CallIcon className="text-[#878787]" />
              {state?.companyList?.UserPhonenumberValue || "N/A"}
            </span>
          </div>
          <div className="w-full mt-[27px]">
            {status === "connected" && !loggedUser.IsFreelancer ? (
              <div className="flex w-full gap-3">
                <Button
                  type="primary"
                  className="w-full h-9"
                  onClick={() => {
                    navigate(
                      privateRoutes.searcherAcceptedCollaborations.path,
                      {
                        state: location.pathname
                      }
                    );
                  }}
                >
                  Create contract
                </Button>
                <Button
                  type="primary"
                  loading={loading}
                  className="!bg-[#FB4B41] w-full  h-9"
                  onClick={() => {
                    handleRemoveConnectedNetwork(selectedUser);
                  }}
                >
                  Disconnect
                </Button>
              </div>
            ) : status === "invited" ? (
              <Button
                type="primary"
                loading={loading}
                className="!bg-[#FB4B41] w-full  h-9"
                onClick={() => {
                  handleDeleteNetwork(selectedUser);
                }}
              >
                Cancel
              </Button>
            ) : status === "invitations" ? (
              <div className="flex w-full gap-2 ">
                <Button
                  type="primary"
                  className="!bg-[#FB4B41] w-full  h-9"
                  loading={loading}
                  onClick={() => {
                    handleDeleteNetwork(selectedUser);
                  }}
                >
                  Decline
                </Button>
                <Button
                  type="primary"
                  loading={loading}
                  className="!bg-[#30CB57] w-full  h-9"
                  onClick={() => {
                    handleAcceptNetwork(selectedUser);
                  }}
                >
                  Accept
                </Button>
              </div>
            ) : (
              ""
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Showcase;
