import { But<PERSON> } from "antd";
import clsx from "clsx";
import { useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";
import ArrowDown from "../../../../assets-alpha/images/svg/arrow-down.svg?react";
import LeftIcon from "../../../../assets-alpha/images/svg/arrow-left-1.svg?react";
import RightIcon from "../../../../assets-alpha/images/svg/arrow-right-1.svg?react";
import ContractReportsIcon from "../../../../assets-alpha/images/svg/contract-reports.svg?react";
import ExpenseIcon from "../../../../assets-alpha/images/svg/expense.svg?react";
import PoolReportIcon from "../../../../assets-alpha/images/svg/pool-reports.svg?react";
import TimesheetIcon from "../../../../assets-alpha/images/svg/timesheet.svg?react";
import {
  DownloadColllabTimeLogReport,
  DownloadPoolTimeLogReport,
  GetAllTimeSheetCount,
  GetPoolsApi,
  GetSearcherAllExpenseSheetsCount,
  getAllCollaborationsApi
} from "../../snapshotApi";
const Dashboard = ({ token, UserFeatures }) => {
  // Widget config array
  const widgetConfigs = [
    {
      name: "Snapshot-Home-Widget-Timesheets",
      render: () => <TimesheetsComponent token={token} />
    },
    {
      name: "Snapshot-Home-Widget-Expenses",
      render: () => <ExpensesComponent token={token} />
    }
  ];

  const reportConfigs = [
    {
      name: "Snapshot-Home-Widget-CollaborationReporting",
      render: () => <ContractReportsCard token={token} />
    },
    {
      name: "Snapshot-Home-Widget-PoolReporting",
      render: () => <PoolReportsCard token={token} />
    }
  ];

  // Get active feature names
  const activeFeatureNames = new Set(
    (UserFeatures || []).filter(f => f.IsActive).map(f => f.Name)
  );
  return (
    <div className="">
      <SectionTitle title="Work items" />
      <GapContainer>
        {widgetConfigs
          .filter(cfg => activeFeatureNames.has(cfg.name))
          .map(cfg => cfg.render())}
      </GapContainer>
      <div className="mt-10 max-md:mt-3">
        <SectionTitle title="Work items" />
        <GapContainer>
          {reportConfigs
            .filter(cfg => activeFeatureNames.has(cfg.name))
            .map(cfg => cfg.render())}
        </GapContainer>
      </div>
    </div>
  );
};
export default Dashboard;

const StatLabel = ({ label, value, className, labelClassName }) => {
  return (
    <div className={`flex flex-col items-center gap-2 ${className}`}>
      <label
        className={twMerge(
          "border-1 border-[#E6FAF6] rounded-full py-2 px-4.5 text-xs text-[#02A286]",
          labelClassName
        )}
      >
        {label}
      </label>
      <label className="text-xl">{value}</label>
    </div>
  );
};

const TimesheetCard = ({ title, stats, icon }) => {
  return (
    <CardContainer>
      <IconTitle icon={icon} title={title} />
      <div className="flex items-center text-[var(--dark)] gap-5 font-medium">
        {stats.map((stat, index) => (
          <StatLabel
            key={index}
            labelClassName={stat.labelClassName}
            label={stat.label}
            value={stat.value}
          />
        ))}
      </div>
    </CardContainer>
  );
};

const IconTitle = ({ icon, title }) => {
  return (
    <div className="flex flex-1 items-center gap-3">
      {icon}
      {title}
    </div>
  );
};

const SectionTitle = ({ title }) => {
  return (
    <h4 className="font-medium text-base text-[var(--gray-3)] mb-2">{title}</h4>
  );
};

const CardContainer = ({ children, className }) => {
  return (
    <div
      className={twMerge(
        "border-1 flex-1 border-[var(--light-gray)] rounded-2xl py-6 px-5 flex items-center",
        className
      )}
    >
      {children}
    </div>
  );
};

const HeaderWithPeriod = ({ title, icon, period }) => {
  return (
    <div className="flex w-full items-center justify-between">
      <IconTitle icon={icon} title={title} />
      <div className="flex items-center gap-1.5 text-xs">
        <LeftIcon className="w-4 cursor-pointer" />
        <span>{period}</span>
        <RightIcon className="w-4 cursor-pointer" />
      </div>
    </div>
  );
};

const CollaborationList = ({ items, emptyMessage, selectedItem, onSelect }) => {
  return (
    <div className="text-[var(--dark)] gap-1 flex flex-col w-full">
      {items.map(item => (
        <div
          className={twMerge(
            clsx(
              "border-1 border-[var(--light-gray)] w-full rounded-full px-6 py-4.5 text-[13px] cursor-pointer",
              {
                "bg-[var(--light-gray)]": selectedItem?.value === item.value
              }
            )
          )}
          key={item.value}
          onClick={() => onSelect(item)}
        >
          {item.label}
        </div>
      ))}

      {!items?.length && (
        <div className="w-full py-4.5 text-[13px] text-center">
          {emptyMessage}
        </div>
      )}
    </div>
  );
};

const GenerateReportButton = ({ onClick, className, disabled }) => {
  return (
    <div className="justify-end flex-1 flex w-full">
      <Button
        className={twMerge(
          clsx(
            "!border-1 !border-[#F0774C] !bg-[#FAD5C8] !text-[#F0774C] !rounded-full [&_.ant-btn-icon]:text-[0px]",
            { "opacity-50": disabled },
            className
          )
        )}
        onClick={onClick}
        iconPosition="end"
        disabled={disabled}
        icon={<ArrowDown className="w-4 h-4" />}
      >
        Generate report
      </Button>
    </div>
  );
};

const GapContainer = ({ children }) => {
  return <div className="flex gap-3 flex-wrap max-xl:flex-col">{children}</div>;
};

const ContractReportsCard = ({ token }) => {
  const [allActiveCollaborations, setAllActiveCollaborations] = useState([]);
  const [selectedPool, setSelectedPool] = useState({ label: "", value: "" });
  useEffect(() => {
    getAllCollaborationsApi(1, token).then(data => {
      if (data.success === true) {
        let arr = [];
        data.items.map((item, index) => {
          let obj = { label: item.Title, value: item.CollaborationId };
          arr.push(obj);
        });
        setAllActiveCollaborations(arr);
      } else {
        setAllActiveCollaborations([]);
      }
    });
  }, [token]);

  const handleDownloadPoolReport = () => {
    DownloadPoolTimeLogReport(parseInt(selectedPool.value), token)
      .then(data => {
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "Pool TimeLog report.pdf");
        document.body.appendChild(link);
        link.click();
      })
      .catch(err => {
        console.log("Error", err);
      });
  };

  return (
    <CardContainer className="p-5 flex-col items-start gap-7">
      <HeaderWithPeriod
        title="Contract reports"
        icon={<ContractReportsIcon />}
        period="This month"
      />
      <CollaborationList
        emptyMessage="No Contracts"
        items={allActiveCollaborations}
        selectedItem={selectedPool}
        onSelect={setSelectedPool}
      />
      <GenerateReportButton
        onClick={handleDownloadPoolReport}
        disabled={!selectedPool.value}
      />
    </CardContainer>
  );
};

const PoolReportsCard = ({ token }) => {
  const [selectedCollaboration, setSelectedCollaboration] = useState("");
  const [allPools, setAllPools] = useState([]);

  useEffect(() => {
    GetPoolsApi(token).then(data => {
      if (data.success === true) {
        const allPoolsArr = data.items.map(item => {
          return { label: item.Title, value: item.PoolId };
        });
        setAllPools(allPoolsArr);
      } else {
        setAllPools([]);
      }
    });
  }, [token]);

  const handleDownloadCollabReport = () => {
    DownloadColllabTimeLogReport(parseInt(selectedCollaboration.value), token)
      .then(data => {
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "Collaboration TimeLog report.pdf");
        document.body.appendChild(link);
        link.click();
      })
      .catch(err => {
        console.error(err);
      });
  };

  return (
    <CardContainer className="p-5 flex-col items-start gap-7">
      <HeaderWithPeriod
        title="Pool reports"
        icon={<PoolReportIcon />}
        period="This month"
      />
      <CollaborationList
        onSelect={setSelectedCollaboration}
        selectedItem={selectedCollaboration}
        emptyMessage="No Pools"
        items={allPools}
      />
      <GenerateReportButton
        onClick={handleDownloadCollabReport}
        disabled={!selectedCollaboration.value}
        className={"!border-[#3AA2F5] !text-[#3AA2F5] !bg-[#C2E2FC]"}
      />
    </CardContainer>
  );
};

const TimesheetsComponent = ({ token }) => {
  const [Pendingtimesheet, setPendingtimesheet] = useState(0);
  const [ApprovedTimesheet, setApprovedTimesheet] = useState(0);
  const [Rejectedtimesheet, setRejectedtimesheet] = useState(0);

  useEffect(() => {
    GetAllTimeSheetCount(token).then(res => {
      if (res.success) {
        setPendingtimesheet(res.items.Pending);
        setRejectedtimesheet(res.items.Rejected);
        setApprovedTimesheet(res.items.Approved);
      }
    });
  }, [token]);

  return (
    <TimesheetCard
      title="Timesheet"
      icon={<TimesheetIcon />}
      stats={[
        {
          label: "Accepted",
          value: ApprovedTimesheet || 0,
          labelClassName: "border-[#E6FAF6] text-[#02A286]"
        },
        {
          label: "Pending",
          value: Pendingtimesheet || 0,
          labelClassName: "border-[#F8F4FB] text-[#9773AA]"
        },
        {
          label: "Declined",
          value: Rejectedtimesheet || 0,
          labelClassName: "border-[#FEF1ED] text-[#C05F3D]"
        }
      ]}
    />
  );
};

const ExpensesComponent = ({ token }) => {
  const [Pendingexpense, setPendingexpense] = useState(0);
  const [ApprovedExpense, setApprovedExpense] = useState(0);
  const [Rejectedexpense, setRejectedexpense] = useState(0);

  useEffect(() => {
    GetSearcherAllExpenseSheetsCount(token).then(res => {
      if (res.success) {
        setPendingexpense(res.items.Pending);
        setRejectedexpense(res.items.Rejected);
        setApprovedExpense(res.items.Approved);
      }
    });
  }, [token]);

  return (
    <TimesheetCard
      title="Expenses"
      icon={<ExpenseIcon />}
      stats={[
        {
          label: "Accepted",
          value: ApprovedExpense || 0,
          labelClassName: "border-[#E6FAF6] text-[#02A286]"
        },
        {
          label: "Pending",
          value: Pendingexpense || 0,
          labelClassName: "border-[#F8F4FB] text-[#9773AA]"
        },
        {
          label: "Declined",
          value: Rejectedexpense || 0,
          labelClassName: "border-[#FEF1ED] text-[#C05F3D]"
        }
      ]}
    />
  );
};
