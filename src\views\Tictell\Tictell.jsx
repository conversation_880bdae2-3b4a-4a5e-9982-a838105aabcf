import { useEffect, useState, useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate, useLocation } from "react-router-dom";
import ConfirmDialog from "../../common/ConfirmDialog/ConfirmDialog";
import moment from "moment";
import {
  getAcceptedcollaborationapi,
  tictellLogin,
  tictellAppLoginApi,
  getCurrenciesApi,
  getExpensesCategoriesApi,
} from "./tictellApi";
import PageWrapper from "../../components/PageWrapper/PageWrapper";
import "./tictell.scss";
import { StorageService } from "../../api/storage";
import { privateRoutes } from "../../Routes/routing";
import TictellNavigation from "./components/Navigation/Navigation";
import TimeLogView from "./views/Timelog/TimeLogView";
import ExpenseLogView from "./views/Expense/ExpenseLogView";
import Dashboard from "./views/Dashboard/Dashboard";
import { logoutApi } from "../../components/Navigation/navigationApi";
import { getChildFeatureByName } from "../Snapshot/snapshotApi";

const Tictell = (props) => {
  const dispatch = useDispatch();
  const history = useNavigate();
  const location = useLocation();
  const labels = useSelector((state) => state.systemLabel.labels);
  const isHelpActive = useSelector((state) => state.navigation.isHelpActive);
  const user =
    useSelector((state) => state.userInfo.user) || StorageService.getUser();

  const [UserFeatures, setUserFeatures] = useState([]);
  const [selectedCollaboration, setSelectedCollaboration] = useState({});
  const [startDate, setStartDate] = useState(moment().startOf("isoWeek"));
  const [endDate, setEndDate] = useState(
    moment()
      .startOf("isoWeek")
      .add(6, "day")
  );
  const [Currencies, setCurrencies] = useState([]);
  const [ExpenseCategories, setExpenseCategories] = useState([]);
  const [token, setToken] = useState("");
  const [dialogMessage, setDialogMessage] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isEmpty, setIsEmpty] = useState(false);
  const [CollaborationList, setCollaborationList] = useState([]);
  const [currentView, setCurrentView] = useState();

  // Timer props from parent
  const { hours, minutes, seconds, stopTimer, pauseTimer, startTimer } = props;
  const handleSelectedDateChange = (type) => {
    let _startDate = startDate;
    let _endDate = endDate;
    if (type === 1) {
      if (
        moment(_startDate)
          .subtract(1, "days")
          .month() !== moment(_startDate).month()
      ) {
        setStartDate(
          moment(_startDate)
            .subtract(1, "days")
            .startOf("isoWeek")
        );
        setEndDate(
          moment(_startDate)
            .subtract(1, "days")
            .endOf("month")
        );
      } else {
        _startDate = moment(_startDate)
          .subtract(7, "days")
          .startOf("isoWeek");
        _endDate = moment(_startDate).endOf("isoWeek");
        const isMonthsSame = _startDate.month() === _endDate.month();
        setStartDate(
          isMonthsSame
            ? _startDate
            : moment(_startDate)
                .add(1, "month")
                .startOf("month")
        );
        setEndDate(
          isMonthsSame
            ? _endDate
            : moment(_startDate)
                .add(1, "w")
                .subtract(1, "days")
        );
      }
    } else if (type === 2) {
      if (
        moment(_endDate)
          .add(1, "days")
          .month() !== moment(_endDate).month()
      ) {
        setStartDate(
          moment(_endDate)
            .add(1, "days")
            .startOf("month")
        );
        setEndDate(
          moment(_endDate)
            .add(1, "days")
            .endOf("isoWeek")
        );
      } else {
        _startDate = moment(_startDate)
          .add(7, "days")
          .startOf("isoWeek");
        _endDate = moment(_startDate).endOf("isoWeek");
        const isMonthsSame = _startDate.month() === _endDate.month();
        setStartDate(
          isMonthsSame
            ? _startDate
            : moment(_startDate)
                .endOf("month")
                .startOf("isoWeek")
        );
        setEndDate(isMonthsSame ? _endDate : moment(_startDate).endOf("month"));
      }
    } else if (type === 3) {
      _startDate = moment().startOf("isoWeek");
      _endDate = moment().endOf("isoWeek");
      const isMonthsSame = _startDate.month() === _endDate.month();
      if (
        moment()
          .startOf("isoWeek")
          .isSame(
            moment()
              .endOf("month")
              .startOf("isoWeek")
          )
      ) {
        setStartDate(
          isMonthsSame
            ? moment().startOf("isoWeek")
            : moment(_startDate)
                .endOf("month")
                .startOf("isoWeek")
        );
        setEndDate(
          isMonthsSame
            ? moment().endOf("month")
            : moment(_startDate).endOf("month")
        );
      } else {
        setStartDate(
          isMonthsSame
            ? moment().startOf("isoWeek")
            : moment(_startDate)
                .add(1, "month")
                .startOf("month")
        );
        setEndDate(
          isMonthsSame
            ? moment()
                .startOf("isoWeek")
                .add(6, "day")
                .add(23, "hours")
                .add(59, "minutes")
            : moment(_startDate)
                .add(1, "w")
                .subtract(1, "days")
                .add(23, "hours")
                .add(59, "minutes")
        );
      }
    }
  };

  useEffect(() => {
    handleSelectedDateChange(3);
  }, []);

  useEffect(() => {
    getChildFeatureByName({ featureName: "tictell" }).then((res) => {
      setUserFeatures(res.items);
      const { items } = res;
      const firstUrl = items.find((a) => a.Url);
      let queryString = window.location.href;
      queryString = queryString.toLowerCase().split("tictell")[1];
      if (queryString === "") {
        if (firstUrl) {
          const isApp = window.location.hash.toLowerCase().indexOf("apps") > -1;
          history((isApp ? "/apps" : "") + "/tictell/" + firstUrl.Url);
        }
      }
    });
  }, [history]);

  const getAcceptedcollaborations = useCallback(() => {
    if (!Currencies?.length) return;
    getAcceptedcollaborationapi().then((data) => {
      if (data.success && data.items.length > 0) {
        const newList = data.items.map((item) => {
          const newItem = {
            ...item,
            value: item.CollaborationId,
            label: item.Title,
            HourlyRateType: !isNaN(item.HourlyRateType)
              ? Currencies.find((a) => a.CurrencyId == item.HourlyRateType)
                  ?.Name
              : item.HourlyRateType,
          };
          return newItem;
        });
        setCollaborationList(newList);
        setSelectedCollaboration(newList[0]);
        setIsLoading(false);
        setIsEmpty(false);
      } else {
        setDialogMessage(labels.Collaboration_Dialog_Message);
        setIsLoading(false);
        setIsEmpty(true);
      }
    });
  }, [Currencies, labels.Collaboration_Dialog_Message]);

  useEffect(() => {
    getAcceptedcollaborations();
  }, [getAcceptedcollaborations]);

  const getExpenseCategories = useCallback((access_token) => {
    getExpensesCategoriesApi(access_token)
      .then((res) => {
        if (res.success) {
          setExpenseCategories(
            res.items.map((item) => ({
              ...item,
              value: item.ExpenseCategoryId,
              label: item.Title,
            }))
          );
        }
      })
      .catch((err) => console.log("Err ", err));
  }, []);

  const getCurrencies = useCallback(() => {
    getCurrenciesApi()
      .then((res) => {
        if (res.success) {
          setCurrencies(
            res.items.map((item) => ({
              ...item,
              value: item.CurrencyId,
              label: item.Name,
            }))
          );
        }
      })
      .catch((err) => console.log("Err ", err));
  }, []);

  useEffect(() => {
    let Email = user?.UserEmail || user?.userEmail || user?.Email;
    if (!Email) {
      const User = StorageService.getUser();
      if (User) {
        Email = User.Email || User.UserEmail || User.userEmail;
      }
    }
    
    console.log("Tictell useEffect - Email:", Email, "user:", user);
    
    if (Email && Email !== 'undefined') {
      tictellAppLoginApi(Email)
      .then((res) => {
        if (res.success) {
          tictellLogin(Email)
            .then((res) => {
              if (res.success) {
                setToken(res.items.access_token);
                getCurrencies(res.items.access_token);
                getExpenseCategories(res.items.access_token);
              }
            })
            .catch((err) => console.log("Err ", err));
        }
      })
      .catch((err) => console.log("Err ", err));
    } else {
      console.warn("No valid email found for Tictell login");
    }
  }, [getCurrencies, getExpenseCategories, user?.UserEmail, user?.userEmail, user?.Email]);

  useEffect(() => {
    // onRouteChanged
    // Only run when location changes
    const firstUrl = UserFeatures.find((a) => a.Url);
    let queryString = window.location.href;
    queryString = queryString.toLowerCase().split("tictell")[1];
    if (queryString === "") {
      if (firstUrl) {
        const isApp = window.location.hash.toLowerCase().indexOf("apps") > -1;
        history((isApp ? "/apps" : "") + "/tictell/" + firstUrl.Url);
      }
    }
  }, [UserFeatures, history]);

  const handleCollabSelectChange = (selectedOption) => {
    setSelectedCollaboration(selectedOption);
  };

  const handleOkClick = () => {
    const currentPath = window.location.hash.toLowerCase();
    const isIndividualComponent = currentPath.includes("/apps/tictell/home");
    if (isIndividualComponent) {
      logoutApi()
        .then((response) => {
          if (response.success) {
            StorageService.clearAll();
            history("/app-login");
          } else {
            console.error("Logout failed:", response.message);
          }
        })
        .catch((error) => {
          console.error("An error occurred during logout:", error);
        });
    } else {
      history(privateRoutes.dashboard.path);
    }
  };

  const handleMenuClick = (currentView) => {
    if (currentView === 1 || currentView === 2) return;
    setCurrentView(currentView);
  };

  const currentViewHash =
    window.location.hash.toLowerCase().split("/")[1] === "apps"
      ? window.location.hash.toLocaleLowerCase().split("/")[3]
      : window.location.hash.toLocaleLowerCase().split("/")[2];

  return (
    <PageWrapper className={`tictell-page`}>
      {isLoading && (
        <div id="loader-wrapper">
          <div className="loader-container">
            <div className="loader">
              <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
                <defs>
                  <filter id="goo">
                    <feGaussianBlur
                      in="SourceGraphic"
                      stdDeviation="6"
                      result="blur"
                    />
                    <feColorMatrix
                      in="blur"
                      mode="matrix"
                      values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                      result="goo"
                    />
                    <feBlend in="SourceGraphic" in2="goo" />
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </div>
      )}
      {dialogMessage && (
        <ConfirmDialog testId="confirm-diloag">
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              onClick={handleOkClick}
            >
              {"Ok"}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      <TictellNavigation
        labels={labels}
        disabled={`${isEmpty ? "disabled" : ""}`}
        handleMenuClick={handleMenuClick}
        isHelpActive={isHelpActive}
        UserFeatures={UserFeatures}
      />
      {!isLoading && (
        <div
          style={isEmpty ? { pointerEvents: "none", flex: 1 } : { flex: 1 }}
          className={isEmpty ? "disabled " : ""}
        >
          {currentViewHash === "home" &&
            UserFeatures.findIndex(
              (a) =>
                a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
            ) > -1 && (
              <Dashboard
                token={token}
                collaborationsList={CollaborationList}
                UserFeatures={UserFeatures}
              />
            )}

          {currentViewHash === "timesheets" &&
            UserFeatures.findIndex(
              (a) =>
                a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
            ) > -1 && (
              <TimeLogView
                token={token}
                startTimer={startTimer}
                pauseTimer={pauseTimer}
                stopTimer={stopTimer}
                seconds={seconds}
                minutes={minutes}
                hours={hours}
                collaborationsList={CollaborationList}
                selectedCollaboration={selectedCollaboration}
                handleCollabSelectChange={handleCollabSelectChange}
                startDate={startDate}
                handleSelectedDateChange={handleSelectedDateChange}
                labels={labels}
                endDate={endDate}
                Currencies={Currencies}
              />
            )}
          {UserFeatures.findIndex(
            (a) => a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
          ) > -1 &&
            currentViewHash === "expensesheets" && (
              <ExpenseLogView
                token={token}
                collaborationsList={CollaborationList}
                selectedCollaboration={selectedCollaboration}
                handleCollabSelectChange={handleCollabSelectChange}
                startDate={startDate}
                handleSelectedDateChange={handleSelectedDateChange}
                Currencies={Currencies}
                ExpenseCategories={ExpenseCategories}
                labels={labels}
                endDate={endDate}
              />
            )}
        </div>
      )}
    </PageWrapper>
  );
};

export default Tictell;
