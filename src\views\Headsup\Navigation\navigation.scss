@use "../../../assets/sass/importFiles" as *;
.product-menus {
  margin-top: 10px;
  > div {
    align-self: center;
  }
  .help-icon {
    bottom: 10px;
  }

  .leftNavIcon {
    border-radius: 50%;
    min-width: 50px;
    text-align: center;
    height: 50px;
    display: flex;
    align-items: center;
    margin-right: 10px;
    background-color: transparent;
    border: 1px solid $purple4;
    &:before {
      /* use !important to prevent issues with browser extensions that change fonts */
      font-family: "icomoon" !important;
      speak: none;
      font-style: normal;
      font-weight: normal;
      font-variant: normal;
      text-transform: none;
      line-height: 1;

      /* Better Font Rendering =========== */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      content: "";
      display: inline-block;
      margin: 0 auto;
      // @extend %fi;
      font-size: 30px;
      color: $purple4; // content: $fi-profile;
    }
    &:hover,
    &.activeBtn {
      background-color: $purple4;
    }
    @media (max-width: 330px) {
      min-width: 35px;
      height: 35px;
    }
  }

  .headsup-home-icon {
    @extend .leftNavIcon;
    &:before {
      content: $fi-Assort;
    }
  }

  .headsup-savedsearch-icon {
    @extend .leftNavIcon;
    &:before {
      content: $fi-Saved-search;
    }
  }

  .headsup-shortlist-icon {
    @extend .leftNavIcon;
    &:before {
      content: $fi-Shortlist;
    }
  }

  .headsup-opportunity-icon {
    @extend .leftNavIcon;
    &:before {
      content: $fi-Opportunity;
    }
  }
}
