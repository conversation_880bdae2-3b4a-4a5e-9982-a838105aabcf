import { Typography } from "antd";
import { twMerge } from "tailwind-merge";
const { Title } = Typography;

const PageWrapper = ({
  children,
  className,
  title,
  contentClassName,
  titleClassName
}) => {
  return (
    <div
      className={twMerge(
        "flex flex-1 flex-col overflow-auto h-[calc(100vh_-_var(--header-height))] bg-white p-[24px_24px] max-lg:p-[var(--page-spacing-lg)]",
        className
      )}
    >
      {title && (
        <Title
          level={2}
          className={twMerge(
            "!text-[var(--dark)] !font-[600] max-md:!text-[18px]",
            titleClassName
          )}
        >
          {title}
        </Title>
      )}
      <div className={twMerge("w-full flex flex-1 flex-col", contentClassName)}>
        {children}
      </div>
    </div>
  );
};

export default PageWrapper;
