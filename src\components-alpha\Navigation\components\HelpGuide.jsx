import Icon from "../../../common-alpha/Icon/Icon";
import { HelpTooltip } from "../../../common-alpha/Tooltip/Tooltip";
import InformationIcon from "../../../assets-alpha/images/svg/information.svg?react";

const HelpGuideIcon = ({ testId, tooltipHelp }) => {
  const onClick = e => {
    e.preventDefault();
    e.stopPropagation();
  };
  return (
    <span onClick={onClick}>
      <HelpTooltip
        testId={testId}
        content={tooltipHelp}
        placement={"right"}
        trigger="click"
      >
        <Icon
          className="text-[var(--green)] w-[20px] h-[20px]"
          src={InformationIcon}
          alt={"guide"}
          renderSvg
        />
      </HelpTooltip>
    </span>
  );
};

export default HelpGuideIcon;
