import { useClientMutation, useClientQuery } from "../../api-alpha/api-service";
import { ApiUrl } from "../../api-alpha/apiUrls";

export const useCreateRequestApi = () => {
  return useClientMutation({ url: ApiUrl.Opportunity.SendOpportunity });
};

export const useGetWorkplaceApi = () => {
  return useClientQuery({ url: ApiUrl.UserCompany.Get });
};

export const useGetCurrencyApi = () => {
  return useClientQuery({ url: ApiUrl.Currency.AllCurrenciesLookup });
};

export const useCreateWorkplaceApi = () => {
  return useClientMutation({ url: ApiUrl.UserCompany.Add });
};

export const useGetRecruiterContractsApi = () => {
  return useClientQuery({
    url: ApiUrl.Dashboard.GetAllCollaborationByUserId
  });
};
export const useGetIproContractsApi = () => {
  return useClientQuery({
    url: ApiUrl.Dashboard.GetAllCollaboration
  });
};
