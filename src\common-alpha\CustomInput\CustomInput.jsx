import { Form, Input, Typography } from "antd";
import { useState } from "react";
import { twMerge } from "tailwind-merge";

import EyeUnlockIcon from "../../assets-alpha/images/svg/eye-slash.svg?react";
import EyeIcon from "../../assets-alpha/images/svg/eye.svg?react";
const { Text } = Typography;

const CustomInput = ({
  name,
  label,
  suffixText,
  rules,
  onChange,
  listField = {},
  suffixInput,
  type,
  isShowPassword,
  disabled,
  PrefixIcon,
  prefixIconClass,
  placeholder,
  className,
  inputClassName,
  onBlur,
  onFocus,
  onKeyDown,
  value,
  allowClear,
  layout,
  onClear,
  rows,
  addonAfter,
  autoSize,
  readOnly
}) => {
  const prefixIconDefaultClass = twMerge(
    "text-[var(--gray-3)] h-full !flex items-center w-[20px] justify-center",
    prefixIconClass
  );
  const [showPassword, setShowPassword] = useState(false);
  return (
    <Form.Item
      className={twMerge(
        "!mb-0 [&_.ant-input-prefix]:border-r-1 [&_.ant-input-prefix]:pr-[12px] [&_.ant-input-prefix]:!mr-[12px] [&_.ant-input-prefix]:border-r-[var(--gray-2)] [&_.ant-form-item-label]:!pb-[6px]",
        className
      )}
      label={label}
      name={name}
      rules={rules}
      extra={
        suffixText && (
          <Text className="!text-xs !mt-2 flex !text-[var(--gray-3)]">
            {suffixText}
          </Text>
        )
      }
      layout={layout}
      {...listField}
    >
      {type === "textarea" ? (
        <Input.TextArea
          value={value}
          onChange={onChange}
          onKeyDown={onKeyDown}
          disabled={disabled}
          prefix={
            PrefixIcon && <PrefixIcon className={prefixIconDefaultClass} />
          }
          className={twMerge("!rounded-xl", inputClassName)}
          suffix={suffixInput}
          allowClear={allowClear}
          onClear={onClear}
          onFocus={onFocus}
          onBlur={onBlur}
          placeholder={placeholder}
          rows={rows}
          autoSize={autoSize}
          readOnly={readOnly}
        />
      ) : (
        <Input
          value={value}
          onChange={onChange}
          onKeyDown={onKeyDown}
          disabled={disabled}
          prefix={
            PrefixIcon && <PrefixIcon className={prefixIconDefaultClass} />
          }
          addonAfter={addonAfter}
          className={twMerge("max-md:!h-[40px] ", inputClassName)}
          suffix={
            isShowPassword ? (
              <>
                {showPassword ? (
                  <EyeUnlockIcon onClick={() => setShowPassword(false)} />
                ) : (
                  <EyeIcon onClick={() => setShowPassword(true)} />
                )}
              </>
            ) : (
              suffixInput
            )
          }
          allowClear={allowClear}
          onClear={onClear}
          onFocus={onFocus}
          onBlur={onBlur}
          placeholder={placeholder}
          readOnly={readOnly}
          type={showPassword ? "text" : type}
        />
      )}
    </Form.Item>
  );
};

export default CustomInput;
