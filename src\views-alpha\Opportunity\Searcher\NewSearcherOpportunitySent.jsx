import { Drawer } from "antd";
import { filter, find, head, includes, isEmpty, map, toLower } from "lodash";
import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { connect, useDispatch, useSelector } from "react-redux";
import { notificationAction } from "../../../actions/notification";
import ConfirmDialog from "../../../common/ConfirmDialog/ConfirmDialog";
import LoadingMask from "../../../common/LoadingMask/LoadingMask";
import { formatDate, formatTime } from "../../../utilities/helpers";
import {
  getCurrenciesApi,
  getOpportunityIprosResumeApi
} from "../../Collaboration/common/collaborationApi";
import {
  getShortlistResumeApi
} from "../../../views/Shortlist/shortlistApi";
import {
  deleteSendOpportunityApi,
  getOpportunityResumesApi,
  getSentOpportunites<PERSON><PERSON>,
  getDraftOpportunites<PERSON>pi,
  getSentOpportunityDetail<PERSON>pi,
  getShortlistResumesApi
} from "../opportunityApi";
import ShortListResume from "../Searcher/NewCreateOpportunity/component/resumeResumeList";
import NewOpportunityDetail from "./common/NewOpportunityDetail";
import { onStateChangeAction } from "./searcherOpportunitySentAction";
import DeleteModal from "../../../common-alpha/DeleteModal/DeleteModal";
const SearcherOpportunitySent = props => {
  const {
    setSentLength,
    setDraftLength,
    filteredJobList,
    setCreateJobInvitation,
    refresh,
    setRefresh,
    draftActive,
    resetFormState
  } = props;
  const [overviewActive, setOverviewActive] = useState(true);
  const [state, setState] = useState({
    declinedRequests: "",
    acceptedRequests: "",
    pendingRequests: "",
    isSearchFocus: false,
    searchListHover: false,
    showModal: { show: false },
    status: null,
    isResumeDetail: false,
    active: "",
    windowWidth: window?.innerWidth
  });
  const [detailDraweropen, setDetailDrawerOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const {
    labels,
    opportunities,
    filterOpportunires,
    selectedOpportunity,
    selectedShortlists,
    shortlistResumes,
    currentOpportunityId,
    selectedAction,
    iprosList,
    allCurrenciesList,
    dialogMessage,
    isLoading
  } = useSelector(state => ({
    labels: state.systemLabel.labels,
    isHelpActive: state.navigation.isHelpActive,
    ...state.searcherOpportunitySent
  }));
  useEffect(() => {
    dispatch(
      onStateChangeAction({
        opportunities: [],
        filterOpportunires: [],
        selectedOpportunity: {},
        shortlistResumes: []
      })
    );
    getOpportunities();
    // getCurrencies();
    setState(prevState => ({
      ...prevState,
      active: window.location.hash?.slice(1)
    }));
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [refresh]);

  const handleResize = () => {
    setState(prevState => ({ ...prevState, windowWidth: window.innerWidth }));
  };

  const getOpportunities = () => {
    const { isFreelancer } = props;
    dispatch(onStateChangeAction({ isFetching: true, isLoading: true }));
    getSentOpportunitesApi({ isFreelancer })
      .then(response => {
        const {
          success,
          items: { Sent }
        } = response;
        if (success) {
          setSentLength(response?.items?.Sent?.length);
          const filterData = response?.items?.Sent?.map(single => ({
            ...single,
            label: single?.RequestName,
            value: single?.RequestId
          }));
          dispatch(
            onStateChangeAction({
              opportunities: filterData,
              filterOpportunires: filterData,
              isFetching: false,
              isLoading: false
            })
          );
          const spliturl = location.pathname.match(/\d+/g);
          if (Sent.length > 0) {
            if (spliturl != null) {
              let filteredOppertunity = Sent.filter(
                res => res.RequestId == spliturl[0]
              );
            }
          }
        }
      })
      .catch(() => {
        dispatch(onStateChangeAction({ isLoading: false }));
      });
    getDraftOpportunitesApi({ isFreelancer })
      .then(response => {
        const { success, items } = response;
        if (success) {
          setDraftLength(items?.Draft?.length);
        }
      })
      .catch(() => {
        setState(prevState => ({ ...prevState, isLoading: false }));
      });
  };

  const getCurrencies = () => {
    getCurrenciesApi()
      .then(res => {
        if (res.success) {
          const allCurrenciesList = res.items.map(item => ({
            ...item,
            value: item.CurrencyId,
            label: item.Name
          }));
          dispatch(onStateChangeAction({ allCurrenciesList }));
        }
      })
      .catch(err => console.log("Err ", err));
  };

  const getOpportunityIProsResume = requestId => {
    getOpportunityIprosResumeApi(requestId).then(data => {
      if (data.success) {
        const iprosList = map(data.items.user, item => ({
          ...item,
          value: item.ResumeId,
          label: item.Title
        }));
        dispatch(onStateChangeAction({ iprosList }));
      }
    });
  };

  const handleOpportunityClick = ({ selectedOpportunity }) => {
    const { RequestId } = selectedOpportunity;
    getOpportunityIProsResume(RequestId);

    dispatch(
      onStateChangeAction({
        currentOpportunityId: RequestId,
        isFetchingShortlist: true,
        isFetchingShortlistResume: true,
        selectedShortlists: [],
        selectedOpportunity,
        shortlistResumes: [],
        selectedResume: {},
        selectedIpro: {},
        hideShortList: false
      })
    );
    getSentOpportunityDetailApi({ RequestId })
      .then(response => {
        if (response.success) {
          const { items } = response;

          const filterShortList = items?.ShortLists?.map(single => ({
            ...single,
            label: single?.ShortlistName,
            value: single?.ShortlistId
          }));

          const selectedOpportunity = {
            ...items,
            FeeCurrencyType:
              allCurrenciesList.find(x => x.CurrencyId == items.FeeCurrencyType)
                ?.Name || items.FeeCurrencyType
          };
          dispatch(
            onStateChangeAction({
              selectedOpportunity
            })
          );
          setState(prevState => ({
            ...prevState,
            declinedRequests: items.Declined || 0,
            acceptedRequests: items.Accepted || 0,
            pendingRequests:
              items.Total - (items.Accepted || 0) - (items.Declined || 0),
            showDetail: true
          }));

          if (isEmpty(filterShortList)) {
            dispatch(
              onStateChangeAction({
                selectedShortlists: [],
                selectedShortlist: null,
                shortlistResumes: [],
                selectedResume: {},
                selectedIpro: {},
                isFetchingShortlist: false
              })
            );
            return;
          }

          const selectedShortlist = head(filterShortList);
          dispatch(
            onStateChangeAction({
              selectedShortlists: filterShortList,
              selectedShortlist,
              isFetchingShortlist: false
            })
          );

          getShortlistResumesApi({ ShortlistId: selectedShortlist.ShortlistId })
            .then(response => {
              if (response.success) {
                const { items } = response;
                dispatch(
                  onStateChangeAction({
                    shortlistResumes: items,
                    selectedResume: {},
                    selectedIpro: {},
                    isFetchingShortlistResume: false
                  })
                );
              }
              dispatch(onStateChangeAction({ isLoading: false }));
            })
            .catch(() => {
              dispatch(onStateChangeAction({ isLoading: false }));
            });
        }
      })
      .catch(() => {
        dispatch(
          onStateChangeAction({
            isFetchingShortlist: false,
            isFetchingShortlistResume: false,
            isLoading: false
          })
        );
      });
  };

  const handleSearchChange = e => {
    const { value } = e.target;
    const filterOpportunires = filter(opportunities, opportunity =>
      includes(toLower(opportunity.RequestName), toLower(value))
    );
    dispatch(onStateChangeAction({ filterOpportunires, searchkey: value }));
  };

  const handleOpportunityDelete = ({ selectedOpportunity, e }) => {
    e.stopPropagation();
    setDeleteModalOpen(true);
    dispatch(
      onStateChangeAction({
        selectedOpportunity
      })
    );
  };

  const handleYesClick = () => {
    deleteOpportunity({ RequestId: currentOpportunityId });
  };

  const deleteOpportunity = ({ RequestId }) => {
    dispatch(onStateChangeAction({ isLoading: true }));
    deleteSendOpportunityApi({ RequestId })
      .then(response => {
        if (response.success) {
          setDeleteModalOpen(false);
          setDetailDrawerOpen(false);
          const info = {
            message: labels.oppDeletedSuccessfully,
            status: "success"
          };
          dispatch(notificationAction(info));
          getOpportunities();
          dispatch(
            onStateChangeAction({
              selectedOpportunity: null,
              opportunity: null,
              isLoading: false
            })
          );
        }
        dispatch(
          onStateChangeAction({
            selectedOpportunity: null,
            opportunity: null,
            isLoading: false
          })
        );
      })
      .catch(() => {
        dispatch(
          onStateChangeAction({
            isLoading: false,
            selectedOpportunity: null,
            opportunity: null
          })
        );
        setDeleteModalOpen(false);
        setDetailDrawerOpen(false);
      });
  };

  const handleNoClick = () => {
    if (
      dialogMessage ===
      labels.InfoSearcherOpportunityDraftOpportunityDeleteAssociationsConformationMsg
    ) {
      dispatch(onStateChangeAction({ isLoading: true }));
      deleteOpportunity({ RequestId: currentOpportunityId });
    }
    dispatch(onStateChangeAction({ dialogMessage: "", yesClickCount: 0 }));
  };

  const handleSearchBlur = () => {
    if (state.searchListHover) return;
    setState(prevState => ({ ...prevState, isSearchFocus: false }));
  };

  const handleShortListSelect = ({ selectedShortlist }) => {
    const alreadySelected = filter(
      selectedShortlists,
      shortlist => shortlist.ShortlistId === selectedShortlist.ShortlistId
    );
    if (isEmpty(alreadySelected)) {
      dispatch(
        onStateChangeAction({
          selectedShortlists: [...selectedShortlists, selectedShortlist]
        })
      );
      setState(prevState => ({ ...prevState, isSearchFocus: false }));
      return;
    }
    setState(prevState => ({ ...prevState, isSearchFocus: false }));
  };

  const handleSelectShortlist = item => {
    dispatch(
      onStateChangeAction({
        selectedShortlist: item,
        shortlistResumes: [],
        selectedResume: {}
      })
    );
    getShortlistResumeApi(item.ShortlistId)
      .then(data => {
        if (data.success) {
          const selectedResume = head(data.items);
          const selectedIpro =
            iprosList.find(ipro => ipro.ResumeId === selectedResume.ResumeId) ||
            {};
          dispatch(
            onStateChangeAction({
              shortlistResumes: data.items,
              fetchingResumes: false,
              isFetchingShortlistResume: false,
              selectedResume,
              selectedIpro
            })
          );
        }
      })
      .catch(() => {
        dispatch(onStateChangeAction({ fetchingResumes: false }));
      });
  };

  const handleSelectedShortlistClick = ({ selectedShortlist }) => {
    getShortlistResumesApi({ ShortlistId: selectedShortlist.ShortlistId })
      .then(response => {
        if (response.success) {
          const { items } = response;
          const selectedResume = head(items);
          const selectedIpro =
            iprosList.find(ipro => ipro.ResumeId == selectedResume.ResumeId) ||
            {};
          if (isEmpty(items)) {
            const info = {
              message: labels.InfoSearcherOpportunityDraftEmptyShortlist,
              status: "error"
            };
            dispatch(notificationAction(info));
          }
          dispatch(
            onStateChangeAction({
              selectedShortlist,
              shortlistResumes: items,
              selectedResume,
              selectedIpro,
              fetchingResumes: false,
              isFetchingShortlistResume: false
            })
          );
        }
      })
      .catch(() => {});
  };

  const setFieldValue = ({ name, value }) => {
    dispatch(
      onStateChangeAction({
        selectedOpportunity: {
          ...selectedOpportunity,
          [name]: value
        }
      })
    );
  };

  const handleResumeSelect = selectedResume => {
    selectedResume = find(shortlistResumes, {
      ResumeId: selectedResume.ResumeId
    });
    const selectedIpro =
      iprosList.find(ipro => ipro.ResumeId == selectedResume.ResumeId) || {};
    dispatch(onStateChangeAction({ selectedResume, selectedIpro }));
  };
  const handleOkClick = () => {
    // handleOpportunityUpdate({ IsSent: true });
    dispatch(onStateChangeAction({ dialogMessage: "" }));
  };

  const getOpportunityResumes = searchkey => {
    dispatch(onStateChangeAction({ isLoading: true }));
    getOpportunityResumesApi({ requestId: currentOpportunityId })
      .then(response => {
        if (response.success) {
          const { items } = response;
          dispatch(
            onStateChangeAction({
              shortlistResumes: items,
              isFetchingShortlistResume: false
            })
          );
          const selectedResume = head(filteredList) || {};
          const selectedIpro =
            iprosList.find(ipro => ipro.ResumeId == selectedResume.ResumeId) ||
            {};
          dispatch(
            onStateChangeAction({
              hideShortList: true,
              isLoading: false,
              showResumeList: true,
              shortlistResumes: filteredList,
              isFetchingShortlistResume: false,
              selectedResume,
              selectedIpro
            })
          );
        }
      })
      .catch(() => {
        dispatch(onStateChangeAction({ isLoading: false }));
      });
  };

  const handleAcceptedResumes = () => {
    getOpportunityResumes(1);
  };

  const handleDeclinedResumes = () => {
    getOpportunityResumes(2);
  };

  const handleNoActionResumes = () => {
    getOpportunityResumes(3);
  };

  const handleCloseModal = () => {
    setState(prevState => ({ ...prevState, showModal: false }));
  };

  const handleStatus = e => {
    setState(prevState => ({ ...prevState, status: e }));
  };

  const handleModalClick = data => {
    setState(prevState => ({
      ...prevState,
      showModal: { show: true, data: data }
    }));
  };

  const handleMobileModalClose = () => {
    setState(prevState => ({
      ...prevState,
      isResumeDetail: false
    }));
  };

  const handleListOpenMobile = () => {
    setState(prevState => ({ ...prevState, isResumeDetail: true }));
  };

  const {
    declinedRequests,
    acceptedRequests,
    pendingRequests,
    isResumeDetail,
    showDetail
  } = state;
  return (
    <>
      <div className="bg-white flex w-full">
        {isLoading && <LoadingMask />}
        {dialogMessage && (
          <ConfirmDialog testId="confirm-diloag">
            <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
            <ConfirmDialog.ActionButtons>
              {dialogMessage ===
              labels.InfoSearcherOpportunityDraftSendConformationMsg ? (
                <ConfirmDialog.Button
                  className="dialog-btn"
                  testId="opportunity-sent-ok"
                  onClick={handleOkClick}
                >
                  Ok
                </ConfirmDialog.Button>
              ) : (
                <ConfirmDialog.Button
                  className="dialog-btn"
                  testId="opportunity-sent-yes"
                  onClick={handleYesClick}
                >
                  Yes
                </ConfirmDialog.Button>
              )}
              <ConfirmDialog.Button
                className="dialog-btn"
                onClick={handleNoClick}
              >
                No
              </ConfirmDialog.Button>
            </ConfirmDialog.ActionButtons>
          </ConfirmDialog>
        )}
        <DeleteModal
          open={deleteModalOpen}
          onCancel={() => {
            setDeleteModalOpen(false);
          }}
          onConfirm={handleYesClick}
          isLoading={isLoading}
          title={labels?.Cancel_Invitation_Title}
          description={labels?.Cancel_Invitation_Description}
          cancelText={labels?.companyDeleteCurtainNOBtnText}
          confirmText={labels?.Confirm_Cancelinvitation_Text}
        />
        <div className="flex flex-col w-full mb-[0px]">
          <div className="h-full w-full flex flex-col">
            <>
              <div>
                {filterOpportunires?.length > 0 ? (
                  <div className="w-full flex flex-col gap-1 mb-4">
                    {filterOpportunires?.map((item, id) => (
                      <div
                        key={id}
                        onClick={() => {
                          handleOpportunityClick({
                            selectedOpportunity: item
                          });
                          setDetailDrawerOpen(true);
                        }}
                        className="flex justify-between w-full rounded-2xl border-[0.5px] min-h-[60px] border-[#EAE5FC] px-4 py-4 items-center pointer hover:bg-gray-50"
                      >
                        <div className="flex gap-2">
                          <div className="flex flex-col gap-2">
                            <label className="text-[#343333] text-sm font-medium leading-[20px]">
                              {item.label}
                            </label>
                            <p className="flex gap-3 text-[#878787] text-xs font-normal leading-[100%]">
                              Date: {formatDate(item?.UpdatedOn)}
                              <span> Time: {formatTime(item?.UpdatedOn)}</span>
                            </p>
                          </div>
                        </div>
                        <div></div>
                      </div>
                    ))}
                  </div>
                ) : (
                  ""
                )}
              </div>
            </>
          </div>
        </div>
      </div>
      <div className="[&_.ant-drawer-body]:[-ms-overflow-style:none] [&_.ant-drawer-body]:[scrollbar-width:none] [&_.ant-drawer-body::-webkit-scrollbar]:[display:none] [&_.ant-drawer-body::-webkit-scrollbar]:[width:0] [&_.ant-drawer-body::-webkit-scrollbar]:[height:0]">
        <Drawer
          placement="right"
          closable={true}
          open={detailDraweropen}
          key="placement"
          headerStyle={{ display: "none" }}
          onClose={() => {
            setDetailDrawerOpen(false);
            setOverviewActive(true);
          }}
          rootClassName=" 
                  sm:[&_.ant-drawer-content-wrapper]:!m-[8px]
                  [&_.ant-drawer-content-wrapper]:!mt-[8px] 
                  md:[&_.ant-drawer-content]:!rounded-[16px]
                  [&_.ant-drawer-content]:!rounded-tl-[16px]
                  [&_.ant-drawer-content]:!rounded-tr-[16px]
                  sm:[&_.ant-drawer-content-wrapper]:!w-[497px]
                  [&_.ant-drawer-content-wrapper]:!w-[100%]
                  [&_.ant-drawer-content-wrapper]:!rounded-[16px]
                  [&_.ant-drawer-body]:!p-[0px]"
        >
          {showDetail ? (
            <>
              <div className="flex items-center justify-between w-full gap-2 "></div>
              {isResumeDetail ? (
                <div className="h-full">
                  <ShortListResume
                    resumeList={shortlistResumes}
                    score={0}
                    handleModalClick={handleModalClick}
                    name="opportunity"
                  />
                </div>
              ) : (
                <>
                  {selectedOpportunity?.RequestName ? (
                    <NewOpportunityDetail
                      declinedRequests={declinedRequests}
                      acceptedRequests={acceptedRequests}
                      pendingRequests={pendingRequests}
                      selectedOpportunity={selectedOpportunity}
                      labels={labels}
                      onAcceptedResumes={handleAcceptedResumes}
                      onDeclinedResumes={handleDeclinedResumes}
                      onNoActionResumes={handleNoActionResumes}
                      handleListOpenMobile={handleListOpenMobile}
                      selectedShortlists={selectedShortlists}
                      selectedAction={selectedAction}
                      status={state?.status}
                      handleStatusChange={handleStatus}
                      setDetailDrawerOpen={setDetailDrawerOpen}
                      resumeList={shortlistResumes}
                      score={0}
                      handleModalClick={handleModalClick}
                      handleOpportunityDelete={handleOpportunityDelete}
                      name="opportunity"
                      setCreateJobInvitation={setCreateJobInvitation}
                      overviewActive={overviewActive}
                      setOverviewActive={setOverviewActive}
                      resetFormState={resetFormState}
                      onSelectedActionChange={({ selectedAction }) =>
                        dispatch(
                          onStateChangeAction({
                            selectedAction
                          })
                        )
                      }
                    />
                  ) : (
                    ""
                  )}
                </>
              )}
            </>
          ) : (
            ""
          )}
        </Drawer>
      </div>
    </>
  );
};

const mapStateToProps = ({
  systemLabel,
  userInfo,
  navigation,
  searcherOpportunitySent
}) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  const { isHelpActive } = navigation;
  let { IsFreelancer } = user;
  IsFreelancer =
    IsFreelancer !== undefined
      ? IsFreelancer
      : JSON.parse(localStorage.getItem("IsFreelancer"));
  return {
    labels,
    isFreelancer: IsFreelancer,
    isHelpActive,
    ...searcherOpportunitySent
  };
};

export default connect(mapStateToProps, {
  notificationAction,
  onStateChangeAction
})(SearcherOpportunitySent);
