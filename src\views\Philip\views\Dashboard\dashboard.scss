@use "../../../../assets/sass/importFiles" as *;
@use "../../variables.scss" as *;
.statistic- {
  .page-column {
    width: 100%;
    flex: none;
  }
}
.dashboard-view {
  min-height: 89%;
  display: flex;
  flex-flow: row;
  .page-column {
    max-width: 350px;
  }
  .column-1 {
    max-width: 330px;
  }
  @include breakpoint(screen767) {
    flex-flow: column;
    .page-column {
      max-width: 100%;
    }
    .column-2 {
      margin-top: 20px;
    }
  }
  .snapshot_widget {
    margin-bottom: 20px;
    background-color: $white;
    background: rgb(222, 213, 247);
    position: relative;
    border-radius: 5px;
    box-shadow: 1px 0px 1px 0px #ccc;
    .help-tooltip {
      position: absolute;
      top: 0;
      right: 0px;
    }
    hr {
      height: 1px;
      width: 90%;
      background-color: #e7e2fb;
      border: none;
      margin: 0px 10px;
    }
    .header {
      display: flex;
      background: #9783cf;
      color: #fff;
      font-size: 15px;
      min-width: 200px;
      min-height: 40px;
      border-radius: 5px;
      margin-left: 5px;
      top: 4px;
      margin-right: 5px;
      position: relative;
      label {
        align-self: center;
        margin-left: 24px;
      }
    }
    .item {
      @extend %clearfix;
      padding: 10px;
      cursor: pointer;
    }
    .item-simple-cursor {
      cursor: unset;
    }

    .common-background-img {
      background-size: contain !important;
      content: "";
      min-height: 100%;
      width: 20px;
      height: 20px;
      margin-left: 15px;
    }
    .common-nav-icon {
      text-align: center;
      margin: 5px 5px 5px -10px;
      display: flex;
      align-items: center;
      margin-right: 0px;
      background-color: transparent;
    }
    .collab-icon {
      @extend .common-nav-icon;
      &:before {
        background: url("../../../../assets/images/apps-icons/collaboration-light.png")
          no-repeat 98% center;
        @extend .common-background-img;
      }
    }
    .leftNavIcon {
      min-width: 50px;
      text-align: center;
      display: flex;
      align-items: center;
      margin-right: 0px;
      background-color: transparent;
      &:before {
        content: "";
        display: block;
        margin: 0 auto;
        @extend %fi;
        font-size: 20px;
        padding-top: 3px;
        color: #9783cf; // content: $fi-profile;
      }
    }
    .request-icon {
      @extend .leftNavIcon;
      &:before {
        @extend %fi;
        content: $fi-request;
        color: $white;
        font-size: 20px;
        background: none;
      }
    }
    .expensesheet-icon {
      @extend .common-nav-icon;
      &:before {
        background: url("../../../../assets/images/apps-icons/expenses-light.png")
          no-repeat 98% center;
        @extend .common-background-img;
        width: 25px;
      }
    }

    .widget-content {
      display: flex;
      min-height: 120px;
      color: $font-color-light_grey_purple;
      > div {
        display: flex;
        flex-flow: column;
        min-width: 69px;
        justify-content: start;
        flex: 1;
        align-self: center;
        label {
          text-align: center;
        }
        label:first-child {
          @extend .x-lg-heading;
        }
        label:last-child {
          @extend .lg-heading;
        }
      }
    }
    .collab-widget-content {
      min-height: 120px;
      color: $font-color-light_grey_purple;
      padding-top: 10px;
      padding-bottom: 10px;
      > div {
        display: flex;
        justify-content: space-around;
        label:first-child {
          @extend .lg-heading;
          max-width: 30px;
          min-width: 30px;
          margin: 0px 10px 0px 5px;
          text-align: center;
          font-size: 25px;
        }
        label:last-child {
          @extend .lg-heading;
          flex: 1;
          margin-left: 10px;
          align-self: center;
        }
      }
    }
  }
  .column-2 {
    .snapshot_widget {
      margin-left: 20px;
      @include breakpoint(screen767) {
        margin-left: 0px;
        margin-top: 20px;
      }
    }
  }
}
