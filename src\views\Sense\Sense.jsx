import { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { StorageService } from "../../api/storage";
import ConfirmDialog from "../../common/ConfirmDialog/ConfirmDialog";
import PageWrapper from "../../components/PageWrapper/PageWrapper";
import { privateRoutes } from "../../Routes/routing";
import { getChildFeatureByName } from "../Snapshot/snapshotApi";
import {
  getCurrenciesApi,
  getExpensesCategoriesApi,
  tictellAppLoginApi,
  tictellLogin
} from "../Tictell/tictellApi";
import SnapshotNavigation from "./Navigation/Navigation";
import "./sense.scss";
import { GetAcceptedCollaborationCount } from "./senseApi";
import Collaborations from "./views/Collaborations/Collaborations";

const Sense = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  // State management
  const [userFeatures, setUserFeatures] = useState([]);
  const [token, setToken] = useState("");
  const [expenseCategories, setExpenseCategories] = useState([]);
  const [currencies, setCurrencies] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [userId, setUserId] = useState(-1);
  const [dialogMessage, setDialogMessage] = useState("");

  // Redux state
  const { labels, isHelpActive, User } = useSelector(state => ({
    labels: state.systemLabel?.labels,
    isHelpActive: state.navigation?.isHelpActive,
    User: state.userInfo?.user || StorageService.getUser()
  }));

  // Route handling
  const onRouteChanged = useCallback(() => {
    const firstUrl = userFeatures[0];
    const queryString = window.location.href.toLowerCase().split("sense")[1];

    if (!queryString && firstUrl) {
      const isApp = window.location.hash.toLowerCase().includes("apps");

      navigate(`${isApp ? "/apps" : ""}/sense/${firstUrl.Url}`);
    }
  }, [userFeatures, navigate]);

  // Initial data fetch
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const res = await getChildFeatureByName({ featureName: "sense" });
        setUserFeatures(res.items);

        const firstUrl = res.items[0];
        const queryString = window.location.href
          .toLowerCase()
          .split("sense")[1];

        if (!queryString && firstUrl) {
          const isApp = window.location.hash.toLowerCase().includes("apps");

          navigate(`${isApp ? "/apps" : ""}/sense/${firstUrl.Url}`);
        }
      } catch (err) {
        console.error("Error fetching features:", err);
      }

      try {
        const storedUser = StorageService.getUser();
        const email = User?.UserEmail || User?.userEmail || User?.Email || 
                     storedUser?.Email || storedUser?.UserEmail || storedUser?.userEmail;
        setUserId(User?.UserId || -1);

        if (email && email !== 'undefined') {
          const appLoginRes = await tictellAppLoginApi(email);
          if (appLoginRes.success) {
            const loginRes = await tictellLogin(email);
          if (loginRes.success) {
            const { access_token } = loginRes.items;
            setToken(access_token);
            getCurrencies(access_token);
            getAllActiveCollaborations(access_token);
          }
        }
        } else {
          console.warn("No valid email found for Sense Tictell login");
        }
      } catch (err) {
        console.error("Login error:", err);
      }
    };

    fetchInitialData();
  }, [User, navigate]);

  // Data fetching functions
  const getAllActiveCollaborations = useCallback(
    async accessToken => {
      try {
        const res = await GetAcceptedCollaborationCount(accessToken);
        if (res.success) {
          if (res.items.length === 0) {
            setDialogMessage(labels?.Snapshot_Collaboration_Dialog_Msg);
            setIsLoading(true);
          } else {
            setIsLoading(false);
          }
        }
      } catch (err) {
        console.error("Collaboration error:", err);
      }
    },
    [labels]
  );

  const getCurrencies = useCallback(async accessToken => {
    try {
      const res = await getCurrenciesApi();
      if (res.success) {
        setCurrencies(
          res.items.map(item => ({
            ...item,
            value: item.CurrencyId,
            label: item.Name
          }))
        );
        getExpenseCategories(accessToken);
      }
    } catch (err) {
      console.error("Currency error:", err);
    }
  }, []);

  const getExpenseCategories = useCallback(async accessToken => {
    try {
      const res = await getExpensesCategoriesApi(accessToken);
      if (res.success) {
        setExpenseCategories(
          res.items.map(item => ({
            ...item,
            value: item.ExpenseCategoryId,
            label: item.Title
          }))
        );
      }
    } catch (err) {
      console.error("Expense category error:", err);
    }
  }, []);

  // Handlers
  const handleOkClick = useCallback(() => {
    navigate(privateRoutes.dashboard.path);
  }, [navigate]);

  // Route change detection
  useEffect(() => {
    onRouteChanged();
  }, [location, onRouteChanged]);

  // Current view detection
  const currentViewHash = window.location.hash.toLowerCase().includes("apps")
    ? window.location.hash.toLowerCase().split("/")[3]
    : window.location.hash.toLowerCase().split("/")[2];

  return (
    <PageWrapper className="sense-page">
      {isLoading && (
        <div id="loader-wrapper">
          <div data-testid="loader" id="loader" />
        </div>
      )}

      <SnapshotNavigation
        labels={labels}
        disabled={isLoading ? "disabled" : ""}
        isHelpActive={isHelpActive}
        UserFeatures={userFeatures}
      />

      {dialogMessage && (
        <ConfirmDialog testId="confirm-dialog">
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              onClick={handleOkClick}
            >
              {"Ok"}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}

      {!isLoading && currentViewHash === "home" && (
        <Collaborations
          token={token}
          Currencies={currencies}
          ExpenseCategories={expenseCategories}
          UserId={userId}
          locationProp={location}
        />
      )}
    </PageWrapper>
  );
};

export default Sense;
