import { Accordion, AccordionItem } from "@szhsin/react-accordion";

const SuggestedLookupsAccordian = ({
  suggestObject,
  componentName,
  localSuggestedSkills,
  onAddSuggestions,
  propId,
  classes,
  propValue
}) => {
  return (
    <Accordion>
      <AccordionItem
        expanded={true}
        header={`Suggested ${componentName} for ${suggestObject &&
          suggestObject.Value}`}
      >
        {/* <AccordionItemTitle>
          <div className="accordion__arrow" />
        </AccordionItemTitle> */}
        <div>
          {localSuggestedSkills && localSuggestedSkills.length > 0 ? (
            <div className={classes.suggestedSkills}>
              <div className={classes.suggestions} id="suggested-skill">
                {localSuggestedSkills.map(item => (
                  <div key={item[propId]} className={classes.suggestedKeyword}>
                    <p>{item[propValue]}</p>
                    <button
                      className={classes.addSkillButton}
                      onClick={() =>
                        onAddSuggestions(item[propId], item, componentName)
                      }
                    >
                      +
                    </button>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <p className={classes.noSuggestionFoundMessage}>No Suggestions</p>
          )}
        </div>
      </AccordionItem>
    </Accordion>
  );
};

export default SuggestedLookupsAccordian;
