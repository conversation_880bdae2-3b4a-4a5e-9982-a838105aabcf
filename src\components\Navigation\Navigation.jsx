import { filter, map, replace, sortBy } from "lodash";
import {
  Fragment,
  useCallback,
  useEffect,
  useRef,
  useState
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { privateRoutes, publicRoutes } from "../../Routes/routing";
import { StorageService } from "../../api/storage";
import { loginAcionUpdate } from "../../store/initialConfig";
import { htmlParser } from "../../utilities/helpers";
import NavButton from "./components/NavButton";
import NavLinkButton from "./components/NavLinkButton";
import NavigationSkeleton from "./components/NavigationSkeleton";
import {
  changeProfileApi,
  getCollaborationCountApi,
  getIProCollaborationCountApi,
  getMessageCountApi,
  getNavigationMenusApi,
  getNavigationMenusForVisitorApi,
  getOpportunityCount<PERSON><PERSON>,
  getUser<PERSON>pi
} from "./navigationApi";

import {
  unreadCollaborationsAction,
  unreadMessagesAction,
  unreadOpportunitiesAction
} from "../../actions/navigationActions";
import { resumeChangeAction } from "../../actions/resumeActions";
import NotificationCircle from "../../common/NotificationCircle/NotificationCircle";
import Switch from "../../common/Switch/Switch";
import { HelpTooltip, Tooltip } from "../../common/Tooltip/Tooltip";
import "./navigation.scss";

const HelpGuideIcon = props => {
  const { testId, disabled, tooltipHelp, isHelpActive } = props;
  return (
    <span className={`nav-help-icon-container`}>
      <HelpTooltip testId={testId} content={tooltipHelp} />
    </span>
  );
};

const NavigationToolTip = ({ labels, children, Label, isHelpActive }) => (
  <>
    {isHelpActive || window.innerWidth <= 1240 ? (
      <>{children}</>
    ) : (
      <Tooltip
        followCursor={true}
        content={labels[replace(Label, "_LABEL", "_TOOLTIP")]}
      >
        <div>{children}</div>
      </Tooltip>
    )}
  </>
);

const SubNav = ({ children }) => (
  <div className="sub-nav-wrapper">{children}</div>
);

const Navigation = ({
  isNavShrink,
  onSubMenuOpen,
  onMobileNavShrink,
  onNavShrink
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const labels = useSelector(state => state.systemLabel.labels);
  const user = useSelector(state => state.userInfo.user);
  const isHelpActive = useSelector(state => state.navigation.isHelpActive);
  const unreadMessages = useSelector(state => state.navigation.unreadMessages);
  const unreadOpportunities = useSelector(
    state => state.navigation.unreadOpportunities
  );
  const unreadCollaborations = useSelector(
    state => state.navigation.unreadCollaborations
  );
  const acceptedCollaboration = useSelector(
    state => state.navigation.acceptedCollaboration
  );
  const declinedCollaboration = useSelector(
    state => state.navigation.declinedCollaboration
  );
  const unreadproInactiveCollaborations = useSelector(
    state => state.navigation.unreadproInactiveCollaborations
  );
  const searcherInActiveCollaboration = useSelector(
    state => state.navigation.searcherInActiveCollaboration
  );
  const isResumeChange = useSelector(state => state.resume.isResumeChange);
  const isRoleSwitchToggle = useSelector(
    state => state.resume.isRoleSwitchToggle
  );

  const [navigationMenu, setNavigationMenu] = useState([]);
  const [isNavigationFetching, setIsNavigationFetching] = useState(false);
  // const [selectedSubMenuUrl, setSelectedSubMenuUrl] = useState("");
  // const [selectedMenuId, setSelectedMenuId] = useState(null);

  const prevPropsRef = useRef({
    unreadMessages,
    unreadCollaborations,
    unreadOpportunities,
    acceptedCollaboration,
    declinedCollaboration,
    searcherInActiveCollaboration,
    unreadproInactiveCollaborations
  });
  const prevIsRoleSwitchToggle = useRef(isRoleSwitchToggle);

  const getNavigationMenus = useCallback(
    updatedUser => {
      const newUser = updatedUser || user;
      const IsFreelancer = newUser.IsFreelancer;
      const isVisitor = IsFreelancer === null;
      setIsNavigationFetching(true);

      if (isVisitor) {
        getNavigationMenusForVisitorApi({
          featureTypeId: 1,
          appTypeId: 1,
          isVisitor
        }).then(response => {
          if (response.success) {
            const updatedMenu = map(response.items, item => {
              if (item.Name === "NavHeader") {
                item.toggleDisabled = true;
                item.Url = publicRoutes.visitorsearch.path;
              }
              return item;
            });
            setNavigationMenu(updatedMenu);
            setIsNavigationFetching(false);
          }
        });
        return;
      }

      const featureTypeId = IsFreelancer ? 2 : 1;
      getNavigationMenusApi({ featureTypeId, appTypeId: 1 }).then(response => {
        if (response.success) {
          const filteredNavigationMenu = filter(response.items, item => {
            if (
              item.FeatureType === 1 ||
              item.FeatureType === 2 ||
              item.FeatureType === 4 ||
              item.FeatureType === 5
            )
              return sortBy(item.SubNavigation, subNavItem => subNavItem.Order);
          });

          const navigationMenu = map(filteredNavigationMenu, (item, index) => {
            item.HelpIcon = false;
            item.testId = `navigation-item-${index + 1}`;
            item.SubNavigation = map(item.SubNavigation, (subNav, index) => {
              subNav.testId = `${item.testId}-sub-nav-item-${index + 1}`;
              return subNav;
            });
            return item;
          });

          setNavigationMenu(navigationMenu);
          setIsNavigationFetching(false);
          const updatedUser = { ...newUser, userFeatures: navigationMenu };
          const UserSaved = StorageService.getUser();
          if (!UserSaved.isProfileChanged) {
            dispatch(loginAcionUpdate({ user: updatedUser }));
          }

          getMessageCount();
          getOpportunityCount();
          getCollaborationCount();
        }
      });
    },
    [user, dispatch]
  );

  const getCurrentUser = useCallback(() => {
    setIsNavigationFetching(true);
    if (user.IsFreelancer) {
      getUserApi().then(response => {
        if (response.success) {
          getNavigationMenus();
        }
      });
    } else {
      getNavigationMenus();
    }
  }, [user.IsFreelancer, getNavigationMenus]);

  const changeProfile = useCallback(() => {
    const { IsFreelancer } = user;
    changeProfileApi({ IsFreelancer }).then(response => {
      if (response.success) {
        const resultUpdate = {
          ...response.items,
          isProfileChanged: true
        };
        dispatch(loginAcionUpdate({ user: resultUpdate }));
        getNavigationMenus(resultUpdate);
        navigate(privateRoutes.dashboard.path);
      }
    });
  }, [user, dispatch, navigate, getNavigationMenus]);

  const getMessageCount = useCallback(() => {
    getMessageCountApi()
      .then(response => {
        if (response.success) {
          dispatch(unreadMessagesAction({ unreadMessages: response.total }));
          setNavigationMenu(prevMenu =>
            map(prevMenu, item => {
              if (item.Name === "Message") {
                return {
                  ...item,
                  NotificationCount: response.total,
                  SubNavigation: map(item.SubNavigation, subNav =>
                    subNav.Name === "MessageInbox"
                      ? { ...subNav, NotificationCount: response.total }
                      : subNav
                  )
                };
              }
              return item;
            })
          );
        }
      })
      .catch(console.error);
  }, [dispatch]);

  const getOpportunityCount = useCallback(() => {
    const { IsFreelancer } = user;
    if (!IsFreelancer) return;

    getOpportunityCountApi({ IsFreelancer })
      .then(response => {
        if (response.success) {
          dispatch(
            unreadOpportunitiesAction({
              unreadOpportunities: {
                newOpportunities: response.items.NewRequests
              }
            })
          );
          setNavigationMenu(prevMenu =>
            map(prevMenu, item => {
              if (item.Name === "Opportunity") {
                return {
                  ...item,
                  NotificationCount: response.items.NewRequests,
                  SubNavigation: map(item.SubNavigation, subNav =>
                    subNav.Name === "OpportunityCreate"
                      ? {
                          ...subNav,
                          NotificationCount: response.items.NewRequests
                        }
                      : subNav
                  )
                };
              }
              return item;
            })
          );
        }
      })
      .catch(console.error);
  }, [user.IsFreelancer, dispatch]);

  const getCollaborationCount = useCallback(() => {
    const { IsFreelancer } = user;
    if (IsFreelancer) {
      getIProCollaborationCountApi()
        .then(response => {
          if (response.success) {
            dispatch(
              unreadCollaborationsAction({
                unreadCollaborations: response.items.newCollaborations,
                unreadproInactiveCollaborations: response.items.inActiveColl
              })
            );
            setNavigationMenu(prevMenu =>
              map(prevMenu, item => {
                if (item.Name === "Collaboration") {
                  const total =
                    response.items.newCollaborations +
                    response.items.inActiveColl;
                  return {
                    ...item,
                    NotificationCount: total,
                    SubNavigation: map(item.SubNavigation, subNav => {
                      if (subNav.Name === "CollaborationNew") {
                        return {
                          ...subNav,
                          NotificationCount: response.items.newCollaborations
                        };
                      }
                      if (subNav.Name === "CollaborationInActive") {
                        return {
                          ...subNav,
                          NotificationCount: response.items.inActiveColl
                        };
                      }
                      return subNav;
                    })
                  };
                }
                return item;
              })
            );
          }
        })
        .catch(console.error);
    } else {
      getCollaborationCountApi()
        .then(response => {
          if (response.success) {
            dispatch(
              unreadCollaborationsAction({
                acceptedCollaboration: response.items.acceptedColl,
                declinedCollaboration: response.items.declinedColl,
                searcherInActiveCollaboration: response.items.inActiveColl
              })
            );
            setNavigationMenu(prevMenu =>
              map(prevMenu, item => {
                if (item.Name === "CollaborationSearcher") {
                  const total =
                    response.items.acceptedColl +
                    response.items.declinedColl +
                    response.items.inActiveColl;
                  return {
                    ...item,
                    NotificationCount: total,
                    SubNavigation: map(item.SubNavigation, subNav => {
                      if (subNav.Name === "CollaborationSearcherAccepted") {
                        return {
                          ...subNav,
                          NotificationCount: response.items.acceptedColl
                        };
                      }
                      if (subNav.Name === "CollaborationSearcherDeclined") {
                        return {
                          ...subNav,
                          NotificationCount: response.items.declinedColl
                        };
                      }
                      if (subNav.Name === "CollaborationSearcherInActive") {
                        return {
                          ...subNav,
                          NotificationCount: response.items.inActiveColl
                        };
                      }
                      return subNav;
                    })
                  };
                }
                return item;
              })
            );
          }
        })
        .catch(console.error);
    }
  }, [user.IsFreelancer, dispatch]);

  useEffect(() => {
    getCurrentUser();
  }, []);

  useEffect(() => {
    const prevProps = prevPropsRef.current;
    if (
      prevProps.unreadMessages !== unreadMessages ||
      prevProps.unreadCollaborations !== unreadCollaborations ||
      prevProps.unreadOpportunities !== unreadOpportunities ||
      prevProps.acceptedCollaboration !== acceptedCollaboration ||
      prevProps.declinedCollaboration !== declinedCollaboration ||
      prevProps.searcherInActiveCollaboration !==
        searcherInActiveCollaboration ||
      prevProps.unreadproInactiveCollaborations !==
        unreadproInactiveCollaborations
    ) {
      setNavigationMenu(prevMenu =>
        map(prevMenu, item => {
          if (item.Name === "Message") {
            return {
              ...item,
              NotificationCount: unreadMessages,
              SubNavigation: map(item.SubNavigation, subNav =>
                subNav.Name === "MessageInbox"
                  ? { ...subNav, NotificationCount: unreadMessages }
                  : subNav
              )
            };
          }
          if (item.Name === "Opportunity") {
            const newOpportunities = unreadOpportunities.newOpportunities;
            return {
              ...item,
              NotificationCount: newOpportunities,
              SubNavigation: map(item.SubNavigation, subNav =>
                subNav.Name === "OpportunityCreate"
                  ? { ...subNav, NotificationCount: newOpportunities }
                  : subNav
              )
            };
          }
          if (item.Name === "Collaboration") {
            const total =
              unreadCollaborations + unreadproInactiveCollaborations;
            return {
              ...item,
              NotificationCount: total,
              SubNavigation: map(item.SubNavigation, subNav => {
                if (subNav.Name === "CollaborationNew") {
                  return {
                    ...subNav,
                    NotificationCount: unreadCollaborations
                  };
                }
                if (subNav.Name === "CollaborationInActive") {
                  return {
                    ...subNav,
                    NotificationCount: unreadproInactiveCollaborations
                  };
                }
                return subNav;
              })
            };
          }
          if (item.Name === "CollaborationSearcher") {
            const total =
              acceptedCollaboration +
              declinedCollaboration +
              searcherInActiveCollaboration;
            return {
              ...item,
              NotificationCount: total,
              SubNavigation: map(item.SubNavigation, subNav => {
                if (subNav.Name === "CollaborationSearcherAccepted") {
                  return {
                    ...subNav,
                    NotificationCount: acceptedCollaboration
                  };
                }
                if (subNav.Name === "CollaborationSearcherDeclined") {
                  return {
                    ...subNav,
                    NotificationCount: declinedCollaboration
                  };
                }
                if (subNav.Name === "CollaborationSearcherInActive") {
                  return {
                    ...subNav,
                    NotificationCount: searcherInActiveCollaboration
                  };
                }
                return subNav;
              })
            };
          }
          return item;
        })
      );
    }
    prevPropsRef.current = {
      unreadMessages,
      unreadCollaborations,
      unreadOpportunities,
      acceptedCollaboration,
      declinedCollaboration,
      searcherInActiveCollaboration,
      unreadproInactiveCollaborations
    };
  }, [
    unreadMessages,
    unreadCollaborations,
    unreadOpportunities,
    acceptedCollaboration,
    declinedCollaboration,
    searcherInActiveCollaboration,
    unreadproInactiveCollaborations
  ]);

  useEffect(() => {
    if (window.innerWidth <= 1240 && isNavShrink) return;
    if (isNavShrink || (window.innerWidth <= 1240 && !isNavShrink)) {
      setNavigationMenu(prevMenu =>
        map(prevMenu, item => ({ ...item, SubNavActive: false }))
      );
    }
  }, [isNavShrink]);

  useEffect(() => {
    setNavigationMenu(prevMenu =>
      map(prevMenu, menu => {
        if (menu.SubNavigation?.length > 0) {
          const currentSubMenu = filter(
            menu.SubNavigation,
            subNav => subNav.Url === location.pathname
          );

          if (
            currentSubMenu.length > 0 &&
            currentSubMenu[0].ParentFeatureId === menu.Id
          ) {
            const isMobile = window.innerWidth <= 1240;
            return {
              ...menu,
              SubNavActive:
                isMobile && isNavShrink ? true : !isMobile && !isNavShrink,
              isSubNavActive: true
            };
          } else {
            return { ...menu, isSubNavActive: false };
          }
        }
        return menu;
      })
    );
  }, [location.pathname, isNavShrink]);
  useEffect(() => {
    if (
      isRoleSwitchToggle &&
      isRoleSwitchToggle !== prevIsRoleSwitchToggle.current
    ) {
      dispatch(resumeChangeAction({ isRoleSwitchToggle: false }));
      changeProfile();
    }
    prevIsRoleSwitchToggle.current = isRoleSwitchToggle;
  }, [isRoleSwitchToggle, dispatch, changeProfile]);

  const handleForSubNavClick = useCallback(
    selectedItem => {
      setNavigationMenu(prevMenu =>
        map(prevMenu, item =>
          item.Name === selectedItem.Name
            ? { ...item, SubNavActive: !item.SubNavActive }
            : item
        )
      );
      onSubMenuOpen();
      onMobileNavShrink();
    },
    [onSubMenuOpen, onMobileNavShrink]
  );

  const handleSwitchChange = useCallback(
    e => {
      e.preventDefault();
      e.stopPropagation();
      if (isResumeChange) {
        dispatch(
          resumeChangeAction({
            isResumeChange,
            isRoleSwitchTrigger: true,
            message: labels.RESUME_EDIT_UNSAVED_CHANGES_MESSAGE
          })
        );
        return;
      }
      changeProfile();
    },
    [isResumeChange, labels, dispatch, changeProfile]
  );

  const handleSubMenuClick = useCallback(
    ({ selectedMenuId, selectedSubMenuUrl }) => {
      // setSelectedSubMenuUrl(selectedSubMenuUrl);
      // setSelectedMenuId(selectedMenuId);
      setNavigationMenu(prevMenu =>
        map(prevMenu, item =>
          item.Id === selectedMenuId ? { ...item, isSubNavActive: true } : item
        )
      );
      onNavShrink();
    },
    [onNavShrink]
  );

  const handleMenuClick = useCallback(() => {
    setNavigationMenu(prevMenu =>
      map(prevMenu, item => ({ ...item, isSubNavActive: false }))
    );
    onNavShrink();
  }, [onNavShrink]);

  return (
    <div
      className={`navigation ${isNavShrink && "nav-shrink-container"}`}
      data-testid={`${
        user.IsFreelancer ? "ipro-navigation-list" : "searcher-navigation-list"
      }`}
    >
      {isNavigationFetching ? (
        <NavigationSkeleton />
      ) : (
        <div>
          {map(navigationMenu, menu => {
            const {
              Id,
              Label,
              Url,
              CssClassIcon,
              CssClass,
              Name,
              IsActive,
              NotificationBadge,
              NotificationCount,
              SubNavActive,
              testId,
              SubNavigation,
              isSubNavActive,
              Disabled,
              toggleDisabled
            } = menu;
            if (Name === "NavHeader") {
              return (
                IsActive && (
                  <Fragment key={Id}>
                    <NavLinkButton
                      className={CssClass}
                      classIcon={CssClassIcon}
                      to={Url}
                      testId={testId}
                      onClick={handleMenuClick}
                    >
                      {htmlParser(labels[Label])}
                      {isHelpActive && (
                        <HelpGuideIcon
                          testId={`${testId}-help`}
                          isNavShrink={isNavShrink}
                          tooltipHelp={
                            labels[replace(Label, "_LABEL", "_HELP_TOOLTIP")]
                          }
                        />
                      )}
                      {!toggleDisabled && (
                        <Switch
                          IsFreelancer={user.IsFreelancer}
                          onChange={handleSwitchChange}
                        />
                      )}
                    </NavLinkButton>
                  </Fragment>
                )
              );
            }
            if (SubNavigation && !SubNavigation.length) {
              return (
                IsActive && (
                  <Fragment key={Id}>
                    <NavigationToolTip
                      labels={labels}
                      Label={Label}
                      isHelpActive={isHelpActive}
                    >
                      <NavLinkButton
                        disabled={Disabled}
                        className={CssClass}
                        classIcon={CssClassIcon}
                        to={Url}
                        testId={testId}
                        onClick={handleMenuClick}
                      >
                        {htmlParser(labels[Label])}
                        {isHelpActive && (
                          <HelpGuideIcon
                            testId={`${testId}-help`}
                            isNavShrink={isNavShrink}
                            tooltipHelp={
                              labels[replace(Label, "_LABEL", "_HELP_TOOLTIP")]
                            }
                          />
                        )}
                        {NotificationBadge && NotificationCount > 0 && (
                          <NotificationCircle testId={`${testId}-notification`}>
                            {NotificationCount > 9 ? "9+" : NotificationCount}
                          </NotificationCircle>
                        )}
                      </NavLinkButton>
                    </NavigationToolTip>
                  </Fragment>
                )
              );
            }

            if (SubNavigation && SubNavigation.length > 0) {
              return (
                IsActive && (
                  <Fragment key={Id}>
                    <NavigationToolTip
                      labels={labels}
                      Label={Label}
                      isHelpActive={isHelpActive}
                    >
                      <NavButton
                        disabled={Disabled}
                        onClick={() => handleForSubNavClick(menu)}
                        className={`arrowCls ${CssClass} ${
                          isSubNavActive ? "activeBtn" : ""
                        }`}
                        classIcon={CssClassIcon}
                        name={Label}
                        testId={testId}
                      >
                        {htmlParser(labels[Label])}
                        {isHelpActive && (
                          <HelpGuideIcon
                            testId={`${testId}-help`}
                            isNavShrink={isNavShrink}
                            tooltipHelp={
                              labels[replace(Label, "_LABEL", "_HELP_TOOLTIP")]
                            }
                          />
                        )}
                        {NotificationBadge && NotificationCount > 0 && (
                          <NotificationCircle testId={`${testId}-notification`}>
                            {NotificationCount > 9 ? "9+" : NotificationCount}
                          </NotificationCircle>
                        )}
                      </NavButton>
                    </NavigationToolTip>
                    {SubNavActive && (
                      <SubNav>
                        {map(
                          SubNavigation,
                          subNav =>
                            subNav.IsActive && (
                              <NavigationToolTip
                                key={subNav.Id}
                                labels={labels}
                                Label={subNav.Label}
                                isHelpActive={isHelpActive}
                              >
                                <NavLinkButton
                                  className={subNav.CssClass}
                                  classIcon={subNav.CssClassIcon}
                                  to={subNav.Url}
                                  testId={subNav.testId}
                                  onClick={() =>
                                    handleSubMenuClick({
                                      selectedMenuId: menu.Id,
                                      selectedSubMenuUrl: subNav.Url
                                    })
                                  }
                                >
                                  {htmlParser(labels[subNav.Label])}
                                  {isHelpActive && (
                                    <HelpGuideIcon
                                      testId={`${subNav.testId}-help`}
                                      isNavShrink={isNavShrink}
                                      tooltipHelp={
                                        labels[
                                          replace(
                                            Label,
                                            "_LABEL",
                                            "_HELP_TOOLTIP"
                                          )
                                        ]
                                      }
                                    />
                                  )}
                                  {subNav.NotificationBadge &&
                                    subNav.NotificationCount > 0 && (
                                      <NotificationCircle
                                        testId={`${subNav.testId}-notification`}
                                      >
                                        {subNav.NotificationCount > 9
                                          ? "9+"
                                          : subNav.NotificationCount}
                                      </NotificationCircle>
                                    )}
                                </NavLinkButton>
                              </NavigationToolTip>
                            )
                        )}
                      </SubNav>
                    )}
                  </Fragment>
                )
              );
            }
          })}
        </div>
      )}
    </div>
  );
};

export default Navigation;
