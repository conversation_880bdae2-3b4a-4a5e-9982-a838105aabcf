import { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { notificationAction } from "../../actions/notification";
import { StorageService } from "../../api/storage";
import { logoutApi } from "../../components/Navigation/navigationApi";
import PageWrapper from "../../components/PageWrapper/PageWrapper";
import { privateRoutes, publicRoutes } from "../../Routes/routing";
import { logoutAction } from "../../store/initialConfig";
import { RESET_LANDINGPAGE_URL } from "../../utilities/enviroments";
import { loginAuth, redirectLoginAuth } from "./components/Auth/loginAction";
import MobileLogin from "./components/Auth/MobileLogin";
import "./lead-page.scss";
import {
  emailTokenConfirmationApi,
  getDashboardMenusApi,
  NetworkInvitation<PERSON>ction<PERSON><PERSON>,
  UserNotificationsUpdate
} from "./leadPageApi";

const LeadPage = props => {
  const [state, setState] = useState({
    email: "",
    password: "",
    isFacebookConnected: false,
    IgeDomains: [],
    dialogMessage: "",
    code: "",
    errorMessage: "",
    showLogin: false,
    showSignup: false,
    isLoading: true,
    showForgotPassword: false,
    showTokenResend: false,
    systemLabelData: {},
    signInConfigs: [],
    footerMenu: null,
    resumeAnalyzerMenu: null,
    mobileAppsMenu: null,
    benefitsMenu: null,
    articlesMenu: null,
    prodooVideoMenu: null,
    searcherPhillipMenu: null,
    contactUsMenu: null,
    howItWorksMenu: null,
    activePage: {
      slider: true
    },
    isMobileApp: false,
    aboutUsMenu: {
      IsActive: true
    },
    showGoogleLogin: false
  });

  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  useEffect(() => {
    const isMobileApp =
      location.state &&
      location.state.from &&
      location.state.from.pathname.toLowerCase().indexOf("/apps/") > -1;
    const isCommon = window.location.href.toLowerCase().indexOf("/ipro") < 0;
    setState(st => ({
      ...st,
      isMobileApp,
      isCommon
    }));

    getDashboardMenusApi().then(res => {
      const items = res.items;
      const footer = items && items.find(x => x.Name == "FooterMenus");
      const aboutUs = items && items.find(x => x.Name == "AboutUs");
      const howItWorks = items && items.find(x => x.Name == "HowItWorksMenu");
      const resumeAnalyser =
        items && items.find(x => x.Name == "ResumeAnalyser");
      const mobileApps = items && items.find(x => x.Name == "MobileAppsMenu");
      const benefits = items && items.find(x => x.Name == "BenefitsMenu");
      const articles = items && items.find(x => x.Name == "ArticlesMenu");
      const prodooVideo = items && items.find(x => x.Name == "ProdooVideoMenu");
      const searcherPhillip =
        items && items.find(x => x.Name == "SearcherPhillipMenu");
      const contactUs = items && items.find(x => x.Name == "ContactUsMenu");
      setState(st => ({
        ...st,
        footerMenu: footer,
        aboutUsMenu: aboutUs,
        howItWorksMenu: howItWorks,
        resumeAnalyzerMenu: resumeAnalyser,
        mobileAppsMenu: mobileApps,
        benefitsMenu: benefits,
        articlesMenu: articles,
        prodooVideoMenu: prodooVideo,
        searcherPhillipMenu: searcherPhillip,
        contactUsMenu: contactUs
      }));
    });

    var loggedUser = props.user;
    const query = new URLSearchParams(location.search);
    const password = query.get("password");
    const email = query.get("email");
    const uId = query.get("uId");
    const niid = query.get("networkInvitationId");
    const status1 = query.get("status");
    const unsubNotificationToken = query.get("unsubNotificationToken");
    const showSignup = query.get("showRegistrationForm");
    if (showSignup) {
      setState(st => ({
        ...st,
        showSignup: true
      }));
    }
    const loginModel = {
      email,
      password
    };

    if (niid != null && status1 != null) {
      if (loggedUser.UserId && loggedUser.UserId == uId) {
        NetworkInvitationActionApi(niid, status1)
          .then(response => {
            if (response.success) {
              const info = {
                message: response.message,
                status: "success"
              };
              props.notificationAction(info);
            } else {
              const info = {
                message: response.message,
                status: "error"
              };
              props.notificationAction(info);
            }
          })
          .catch(err => {
            const info = {
              message: "Error occured while accepting invitation",
              status: "error"
            };
            props.notificationAction(info);
          });
      } else {
        const info = {
          message:
            "Please login to the Account which have recieved the invitation",
          status: "error"
        };
        props.notificationAction(info);
      }
    }
    if (email != null && password != null) {
      props.loginAuth(loginModel).then(response => {
        navigate(privateRoutes.settings.changePasswordPath);
      });
    }
    if (((location.state || {}).from || {}).pathname === "/settings/e") {
      if (!props.isAuthenticated) {
        const info = {
          message: "Please login first to edit settings",
          status: "error"
        };
        props.notificationAction(info);
      }
    }
    if (unsubNotificationToken != null) {
      unsubNotifications(unsubNotificationToken);
    }

    if (location.pathname.indexOf(publicRoutes.emailverification.path) >= 0) {
      const { token } = params;
      const user = JSON.parse(localStorage.getItem("User"));
      user
        ? logoutApi().then(response => {
            if (response.success) {
              props.logoutAction();
              StorageService.clearAll();

              window.location.href = RESET_LANDINGPAGE_URL;
              emailConfirmation(token); //not sure why its here? it will never call
            }
          })
        : emailConfirmation(token);
    }

    if (location.pathname.indexOf(publicRoutes.redirectedfrom.path) >= 0) {
      const redirectedFrom = location.search.split("=")[1];
      const loginModel = {
        email: redirectedFrom + "dsa5d54sa6d21xc",
        password: "21s242sdassd54" + redirectedFrom + "a34s6dasa22sd",
        token: redirectedFrom
      };
      if (redirectedFrom) {
        props.loginAuth(loginModel).then(response => {
          navigate(privateRoutes.dashboard.path);
        });
      } else {
        const info = {
          message:
            "There was not an error while redirecting Please try to login manually",
          status: "error"
        };
        props.notificationAction(info);
      }
    }
  }, []);

  const unsubNotifications = unsubNotificationToken => {
    UserNotificationsUpdate(unsubNotificationToken).then(response => {
      if (response.success) {
        navigate(privateRoutes.settings.emailnotification);
      }
    });
  };

  const emailConfirmation = token => {
    const { labels } = props;
    setTimeout(() => {
      emailTokenConfirmationApi(token)
        .then(response => {
          if (response.success) {
            const info = {
              message:
                labels.EMAIL_VERIFICATION_ACCOUNT_SUCCESS ||
                "Your account has been verified, Thank you.",
              status: "success"
            };
            props.notificationAction(info);
          } else {
            const info = {
              message:
                response.message || labels.EMAIL_VERIFICATION_ACCONT_FAIL,
              status: "error"
            };
            props.notificationAction(info);
          }

          navigate(publicRoutes.login.path);
        })
        .catch(err => {
          const info = {
            message: labels.EMAIL_VERIFICATION_ACCONT_FAIL,
            status: "error"
          };
          props.notificationAction(info);
        });
    }, 1500);
  };

  useEffect(() => {
    if (location.pathname.indexOf(publicRoutes.redirectedLogin.path) >= 0) {
      redirectLoginAuthCHeck();
    } else {
      checkUser();
    }
  }, []);

  const redirectLoginAuthCHeck = () => {
    const query = new URLSearchParams(location.search);
    const token = query.get("token");
    props.redirectLoginAuth(token).then(response => {
      navigate(privateRoutes.dashboard.path);
    });
  };

  const checkUser = () => {
    const user = StorageService.getUser();
    if (user) {
      navigate(privateRoutes.dashboard.path);
    } else {
      if (location.state?.from?.pathname?.toLowerCase()?.includes("/apps/")) {
        setState(st => ({ ...st, isLoading: false }));
        const statePathName = location.state.from.pathname.toLowerCase();
        let appType = "";
        switch (true) {
          case statePathName.includes("snapshot"):
            appType = "snapshot";
            break;
          case statePathName.includes("tictell"):
            appType = "tictell";
            break;
          case statePathName.includes("searcher-philip"):
            appType = "searcherPhilip";
            break;
          case statePathName.includes("ipro-philip"):
            appType = "iproPhilip";
            break;
          case statePathName.includes("sense"):
            appType = "sense";
            break;
          case statePathName.includes("headsup"):
            appType = "headsup";
            break;
          case statePathName.includes("embark"):
            appType = "embark";
            break;
          case statePathName.includes("assort"):
            appType = "assort";
            break;
          case statePathName.includes("next-step"):
            appType = "nextStep";
            break;
          default:
          // handle default case
        }

        StorageService.saveAppType(appType);

        navigate("/app-login");
      } else {
        StorageService.clearAll();

        window.location.href = RESET_LANDINGPAGE_URL;
      }
    }
  };
  const { isLoading } = state;
  return (
    <PageWrapper className="lead-page">
      {isLoading ? (
        <div id="root">
          <div id="preLoader" className="preLoader">
            <div>
              <img
                className="loaderImage"
                src="../../assets/images/logo.png"
                alt="logo"
              />
            </div>
            <div>
              <div id="loader-wrapper">
                <div id="loader" />
              </div>
            </div>
          </div>
        </div>
      ) : (
        <MobileLogin />
      )}
    </PageWrapper>
  );
};

const mapStateToProps = ({
  systemLabel: { labels },
  userInfo: { user, isAuthenticated },
  landing: { searchCriteria }
}) => {
  return {
    labels,
    user,
    isAuthenticated,
    searchCriteria
  };
};

export default connect(mapStateToProps, {
  notificationAction,
  logoutAction,
  loginAuth,
  redirectLoginAuth
})(LeadPage);
