import { useState, useEffect } from "react";
import "./shortlistResumes.scss";
import join from "lodash/join";
import { notificationAction } from "./../../../../../actions/notification";
import { deleteResumeApi } from "../../../../Shortlist/shortlistApi";
import { connect } from "react-redux";
import {
  AvailablityGreenCriteria,
  AvailablityYellowCriteria
} from "../../../../../utilities/constants";
import ClampLines from "../../../../../common/ClampLines/ClampLines";
import { useNavigate } from "react-router-dom";
import ConfirmDialog from "../../../../../common/ConfirmDialog/ConfirmDialog";
import List from "../../../../../common/List/List";

const ShortlistResumes = ({
  selectedResume,
  onResumeSelect,
  onShortlistActive,
  fetchingResumes,
  shortlistResumes,
  labels,
  showCorrelation,
  selectedShortlist,
  handleSelectShortlist,
  dispatch
}) => {
  const [dialogMessage, setDialogMessage] = useState("");
  const [deletedItem, setDeletedItem] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    if (selectedResume) {
      // Force update logic if needed
    }
  }, [selectedResume]);

  const renderProfiles = resume => {
    const profileValues = resume.Profiles.map(item => item.ProfileValue);
    return join(profileValues, ", ");
  };

  const renderSkills = resume => {
    const skillsValues = resume.Skills.map(item => item.SkillValue);
    return join(skillsValues, ", ");
  };

  const handleDeleteResume = item => {
    setDeletedItem(item);
    setDialogMessage(labels.oppRemoveResumeShortlistMessage);
  };

  const handleYesClick = () => {
    setDialogMessage("");
    deleteResumeApi(deletedItem.ResumeId, selectedShortlist.ShortlistId)
      .then(data => {
        if (data) {
          const info = {
            message: data.message,
            status: "success"
          };
          dispatch(notificationAction(info));
          setDeletedItem(null);
        }
        handleSelectShortlist(selectedShortlist);
      })
      .catch(() => {
        // Handle error if needed
      });
  };

  const handleNoClick = () => {
    setDialogMessage("");
    setDeletedItem(null);
  };

  const getAvailablity = availableDate => {
    const { searchedDate } = this.props;
    const ResumeBusyUntilDate = new Date(availableDate);
    const SearchAvailDate = new Date(searchedDate.date);

    const green = addDays(SearchAvailDate, AvailablityGreenCriteria);
    const yellow = addDays(SearchAvailDate, AvailablityYellowCriteria);

    if (ResumeBusyUntilDate <= green) {
      return "markedGreen";
    } else if (ResumeBusyUntilDate > green && ResumeBusyUntilDate < yellow) {
      return "markedYellow";
    } else if (ResumeBusyUntilDate >= yellow) {
      return "markedRed";
    }
  };

  const addDays = (date, days) => {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  };

  return (
    <div className="headsup-shortlist-resume-component">
      {dialogMessage && (
        <ConfirmDialog>
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <button className="dialog-btn" onClick={handleYesClick}>
              {labels.ShortlistResumeListDeletionCurtainYESBtnText}
            </button>
            <button className="dialog-btn" onClick={handleNoClick}>
              {labels.ShortlistResumeListDeletionCurtainNOBtnText}
            </button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      <List isFetching={fetchingResumes}>
        {shortlistResumes.map((item, index) => (
          <List.ListItem
            key={index}
            onClick={() => onResumeSelect(item)}
            className={`${
              selectedResume.ResumeId === item.ResumeId
                ? "selected-item"
                : "listItems"
            }
                ${showCorrelation ? "correlation-item" : ""}`}
          >
            {showCorrelation && (
              <div
                className={`SkillNum ${getAvailablity(item.AvailabilityDate)}`}
              >
                {item.CorrelationScore}
              </div>
            )}
            <div className="profile">
              <ClampLines
                text={renderProfiles(item)}
                lines={1}
                buttons={false}
              />
            </div>
            <div className="location">
              <ClampLines text={item.Region} lines={1} buttons={false} />
            </div>
            <div className="skills">
              {item.Skills && item.Skills.length && (
                <ClampLines
                  text={renderSkills(item)}
                  lines={1}
                  buttons={false}
                  basedOn="letters"
                />
              )}
            </div>
            {onShortlistActive && (
              <div
                className={`${
                  item.active ? "short-list-active" : ""
                } short-list`}
                onClick={e => {
                  e.stopPropagation();
                  onShortlistActive(item);
                }}
              />
            )}
          </List.ListItem>
        ))}
      </List>
    </div>
  );
};

const mapStateToProps = ({ systemLabel }) => {
  const { labels } = systemLabel;
  return { labels };
};

export default connect(mapStateToProps, { notificationAction })(
  ShortlistResumes
);
