@use "../../assets/sass/importFiles" as *;
.disabled {
  opacity: 0.4;
}
.page-wrapper {
  display: flex;
  flex: 1;
  flex-flow: row;
  background: $ViewsCnt;
  // height: 100vh;
  overflow: hidden;
  .page-content {
    width: 100%;
    display: flex;
    flex-flow: column;
    flex: 1;
  }
  .views-cnt {
    flex: 1;
    display: flex;
    align-content: flex-start;
    flex-flow: row wrap;
    overflow-x: auto;
    padding: 0px 10px;
    background: #fff;
  }

  #loader-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    z-index: 1000;
    background: none !important;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .loader-container {
    text-align: center;
  }

  .loader {
    position: relative;
    width: 30px;
    margin: 0 auto;
    filter: url(#goo);
    rotate: 90deg;
  }

  #goo {
    display: none;
  }

  .loader:before,
  .loader:after {
    content: "";
    position: absolute;
    inset: 0;
    margin: auto;
    width: 100%;
    height: 30px;
    animation: 1.5s ease-out infinite loader;
    border-radius: 50%;
  }

  .loader:before {
    background: rgb(2, 202, 168);
  }

  .loader:after {
    animation-delay: 0.75s;
    background: rgb(142, 129, 245);
  }

  .loader-text {
    color: white;
    margin-top: 20px;
    font-size: 16px;
  }

  @keyframes loader {
    0%,
    50%,
    100% {
      transform: translateY(0);
    }
    25% {
      transform: translateY(40px);
    }
    75% {
      transform: translateY(-40px);
    }
  }
}
.page-wrapper.lead-page {
  display: block !important;
  overflow: unset !important;
  .views-cnt {
    display: block !important;
    padding-right: 0 !important;
    padding-bottom: 0 !important;
    overflow: unset !important;
  }
}
