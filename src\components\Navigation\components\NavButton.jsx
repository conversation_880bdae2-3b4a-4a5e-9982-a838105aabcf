import "./navbutton.scss";

const NavButton = ({
  children,
  classIcon,
  className,
  onClick,
  name,
  testId,
  disabled
}) => {
  return (
    <button
      onClick={onClick}
      className={`nav-button ${className}`}
      name={name}
      data-testid={testId}
      disabled={disabled}
    >
      <i className={classIcon} />
      <span>{children}</span>
    </button>
  );
};

NavButton.defaultProps = {
  className: ""
};

export default NavButton;
