import { useEffect, useState } from "react";
import PresentationDetailsNew from "./presentationView";
import CompanyDetailNew from "../../../../../common/companyDetail/companyDetail";
import CloseModal from "../../../../../assets-alpha/images/svg/close-modal.svg";
import ArrowDown from "../../../../../assets-alpha/images/svg/arrow-down-1.svg?react";
import InformationIcon from "../../../../../assets-alpha/images/svg/information.svg?react";
import { Button, Select } from "antd";
import { HelpTooltip } from "../../../../../common-alpha/Tooltip/Tooltip";
import Icon from "../../../../../common-alpha/Icon/Icon";
import { getCurrenciesApi } from "../opportunityApi";
const JobDetailIpro = ({
  selectedOpportunityNeww,
  showCompanyDetails,
  showPresentationDetail,
  allpresentation,
  selectedPresentation,
  setSelectedPresentation,
  loading,
  labels,
  socialMedia,
  getAllOpportunity,
  allJobsList,
  requestId,
  selectedCompanyy,
  handleOpportunityDetailDeclinedClick,
  handleSubmitPresentDetail,
  setIsDrawerOpen,
  isHelpActive
}) => {
  const jobDetail = selectedOpportunityNeww.filter(item => {
    return requestId === item.RequestId;
  });
  const selectedOpportunityNew = jobDetail[0];
  const [currencies, setCurrencies] = useState();
  const [overviewActive, setOverviewActive] = useState(true);
  useEffect(() => {
    getCurrenciesApi()
      .then(res => {
        if (res.success) {
          setCurrencies(res?.items);
        }
      })
      .catch(err => console.log("Err ", err));
  }, []);
  const currType = currencies?.find(item => {
    return item?.CurrencyId === Number(selectedOpportunityNew?.FeeCurrencyType);
  });
  useEffect(() => {
    if (allpresentation?.length) {
      setSelectedPresentation(undefined);
    }
  }, [allpresentation, setSelectedPresentation]);
  const handleChange = value => {
    setSelectedPresentation(value);
  };
  return (
    <div className="w-full sm:w-[497px] h-[64px]">
      <div className="relative w-full sm:w-[497px] h-[64px]">
        <div className="fixed sm:w-[497px] w-full h-[64px] border-b border-[#EAE5FC] z-[999] bg-white rounded-tl-2xl rounded-tr-2xl">
          <div className="flex w-full h-[64px] justify-between items-center p-6">
            <div className="flex justify-between sm:gap-5 w-full sm:h-10 items-center h-[64px] border-b border-[#EAE5FC] sm:border-b-0 sm:pl-0 sm:pr-0">
              <img
                src={CloseModal}
                alt=""
                className="pointer sm:order-1 order-2"
                onClick={() => {
                  setIsDrawerOpen(false);
                }}
              />
              <h1 className="!m-0 flex-1 order-1 sm:order-2 !text-[18px] !font-semibold sm:!text-2xl sm:font-medium">
                {labels?.iPro_Invitation_Details_Label}
              </h1>
            </div>
            {selectedOpportunityNew?.Status === "new" && (
              <div className="hidden sm:block">
                <div className="flex gap-3">
                  <Button
                    type="filled"
                    className="!m-0 !bg-[#FEEDED] !w-[68px] !h-9 !text-[#FF3B30] pointer"
                    onClick={() => {
                      handleOpportunityDetailDeclinedClick();
                    }}
                  >
                    {labels?.iProOpportunityNewDetailBtnDecline}
                  </Button>
                  <Button
                    type="primary"
                    className="!m-0 !text-[white] !w-[68px] !h-9 pointer"
                    disabled={selectedPresentation === undefined}
                    onClick={() => {
                      handleSubmitPresentDetail();
                    }}
                  >
                    {labels?.iProOpportunityNewDetailBtnAccept}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="flex w-[336px] bg-[#F3F1FD] border border-[#EAE5FC] rounded-[99px] gap-2 sm:ml-6 ml-4 mt-[22px]">
        <Button
          className={`w-[162px] !h-10 !rounded-[99px] !text-sm !font-medium ${
            overviewActive
              ? "!bg-white !text-[#343333] "
              : "!bg-transparent !text-[#878787]"
          }`}
          onClick={() => {
            setOverviewActive(true);
          }}
        >
          {labels?.Job_Overview_Label}
        </Button>
        <Button
          className={`w-[162px] !h-10 !rounded-[99px] !text-sm !font-medium ${
            !overviewActive
              ? "!bg-white !text-[#343333] "
              : "!bg-transparent !text-[#878787]"
          }`}
          onClick={() => {
            setOverviewActive(false);
          }}
        >
          {labels?.SearcherSentCollaborationCompanyLabel}
        </Button>
      </div>
      {selectedOpportunityNew?.Status === "new" && (
        <div className="sm:hidden flex w-full h-[64px] bg-white fixed bottom-0 justify-center items-center  border-t-[0.5px] border-[#EAE5FC] gap-[9px] pl-4 pr-4">
          <div className=" w-full">
            <div className="flex w-full gap-3">
              <Button
                type="filled"
                className="!m-0 !bg-[#FEEDED] !w-full !h-9 !text-[#FF3B30] pointer"
                onClick={() => {
                  handleOpportunityDetailDeclinedClick();
                }}
              >
                {labels?.iProOpportunityNewDetailBtnDecline}
              </Button>
              <Button
                type="primary"
                disabled={selectedPresentation === undefined}
                className="!m-0 !text-[white] !w-full !h-9  pointer"
                onClick={() => {
                  handleSubmitPresentDetail();
                }}
              >
                {labels?.iProOpportunityNewDetailBtnAccept}
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col gap-8">
        {overviewActive ? (
          <PresentationDetailsNew
            selectedOpportunityNew={selectedOpportunityNew}
            showCompanyDetails={showCompanyDetails}
            showPresentationDetail={showPresentationDetail}
            currType={currType}
            loading={loading}
            labels={labels}
            socialMedia={socialMedia}
            getAllOpportunity={getAllOpportunity}
            allJobsList={allJobsList}
            handleOk={() => {}}
          />
        ) : (
          <CompanyDetailNew
            selectedCompanyy={selectedCompanyy}
            allJobsList={allJobsList}
          />
        )}
        <div
          className={`flex flex-col gap-3 md:!ml-6 w-[90%] md:!mr-6 mx-4 ${
            selectedOpportunityNew?.Status === "new" ? "block" : "hidden"
          }`}
        >
          <p className="flex gap-2 font-medium font-inter !text-sm text-[#343333]">
            {labels?.Select_Portfolio_Label}
            {isHelpActive && (
              <span>
                <HelpTooltip content={labels?.Portfolio_Select_Tooltip}>
                  <Icon
                    className="!z-10 text-green-500"
                    src={InformationIcon}
                    renderSvg
                  />
                </HelpTooltip>
              </span>
            )}
          </p>
          <Select
            showSearch
            placeholder={labels?.Select_Portfolio_Lbl}
            value={selectedPresentation}
            optionFilterProp="children"
            filterSort={(optionA, optionB) =>
              (optionA?.children ?? "")
                .toLowerCase()
                .localeCompare((optionB?.children ?? "").toLowerCase())
            }
            onChange={handleChange}
            suffixIcon={<ArrowDown />}
            className="pl-4 md:!h-[52px] !rounded-2xl truncate"
            rootClassName="[&_.ant-select-selector]:!rounded-[18px]"
          >
            {allpresentation?.map(item => (
              <Option key={item.UserDetailId} value={item.UserDetailId}>
                {item.Title}
              </Option>
            ))}
          </Select>
        </div>
      </div>
    </div>
  );
};

export default JobDetailIpro;
