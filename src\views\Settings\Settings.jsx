import { filter, find, map } from "lodash";
import { useEffect, useState } from "react";
import { connect, useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import PageWrapper from "../../components/PageWrapper/PageWrapper";
import "./components/usersettings.scss";

import { Accordion, AccordionItem } from "@szhsin/react-accordion";
import { v1 as uuidv1 } from "uuid";
import { StorageService } from "../../../src/api/storage";
import {
  logoutApi
} from "../../../src/components/Navigation/navigationApi";
import {
  loginAcionUpdate,
  logoutAction
} from "../../../src/store/initialConfig";
import { helpActiveAction } from "../../actions/navigationActions";
import { notificationAction } from "../../actions/notification";
import Button from "../../common/Button/Button";
import Column from "../../common/Column/Column";
import ConfirmDialog from "../../common/ConfirmDialog/ConfirmDialog";
import List from "../../common/List/List";
import LoadingMask from "../../common/LoadingMask/LoadingMask";
import { RESET_LANDINGPAGE_URL } from "../../utilities/enviroments";
import { loadImageOrientation } from "../../utilities/helpers";
import {
  deleteUserEmailApi,
  deleteUserPhoneApi,
  getCountriesApi,
  getSocialMediaApi,
  getUserEmailAddressApi,
  getUserPhoneNumberApi,
  getUserProfileApi,
  getUserSocialMediaLinks,
  SaveSocialMediaLink,
  updateEmailSettingApi,
  updatePhoneNumberSettingApi,
  updateUserProfileApi
} from "../Settings/settingsApi";
import ChangePassword from "./components/ChangePassword";
import Profile from "./components/Profile";
import SocialMediaLinks from "./components/SocialMediaLinks";
import UserEmails from "./components/UserEmails";
import UserNotifications from "./components/UserNotifications";
import UserPhoneNumbers from "./components/UserPhoneNumbers";
import { onStateChangeAction } from "./settingAction";
import "./settings.scss";

const Settings = props => {
  const navigate = useNavigate();
  const location = useLocation();
  const [state, setState] = useState({
    userPhoneList: [],
    userEmailList: [],
    selectedProfile: null,
    countries: null,
    dialogMessage: "",
    dialogMessagePass: "",
    isInvalidUserFirstname: false,
    isInvalidUserLastname: false,
    isInvalidPhone: false,
    isInvalidEmail: false,
    isUserNameSelected: true,
    isPhoneNumberSelected: false,
    addItemMessage: "",
    isEmailSelected: false,
    isDeleteEmail: false,
    isDeletePhoneNumber: false,
    emailIdToDelete: 0,
    phoneIdToDelete: 0,
    isChangePassSelected: false,
    isEmailNotificationSelected: false,
    rightcoloumHeading: "",
    isLoading: false,
    isFetching: false,
    passwordEmailsList: [],
    settingsList: [],
    socialLinks: [],
    selectedItem: {
      Id: 1
    }
  });

  useEffect(() => {
    props.onStateChangeAction({
      passwordEmailsList: [
        {
          Id: 4,
          Name: props.labels.USER_SETTING_LIST_NOTIFICATION_PASSWORD_HEADING
        },
        {
          Id: 5,
          Name: props.labels.USER_SETTING_LIST_NOTIFICATION_NOTIFICATION_HEADING
        }
      ],
      settingsList: [
        {
          Id: 1,
          Name: props.labels.USER_INFO_LABEL
        },
        {
          Id: 2,
          Name: props.labels.USER_SETTING_LIST_PROFILE_PHONE_HEADING
        },
        {
          Id: 3,
          Name: props.labels.USER_SETTING_LIST_PROFILE_EMAIL_HEADING
        },
        {
          Id: 7,
          Name: props.labels.USER_SOCIAL_LINKS_LBL || "User Social Links"
        }
      ]
    });

    if (props.isFetching) {
      getUserEmailAddress();
      getUserPhoneNumber();
    }
    getUserProfile();
    getSocialMedia();
    getCountries();

    if (location.pathname.includes("unsubscribe")) {
      openEmailNotificationAccordionOpen();
    }

    const query = new URLSearchParams(location.search);
    const isPasswordChange = query.get("isPasswordChange");
    if (isPasswordChange != null) {
      props.onStateChangeAction({
        isChangePassSelected: true
      });
    }
  }, []);

  const openEmailNotificationAccordionOpen = () => {
    const { passwordEmailsList } = props;
    let selectedItem = find(passwordEmailsList, { Id: 4 });
    selectedItem = {
      Id: 5,
      Name: '"Email Notifications"'
    };
    props.onStateChangeAction({
      isEmailNotificationSelected: true,
      isChangePassSelected: false,
      isEmailSelected: true,
      isPhoneNumberSelected: false,
      isUserNameSelected: false,
      selectedItem,
      expanded: {
        notification: true
      }
    });
  };

  const handleHelpClick = () => {
    const { helpActiveAction, isHelpActive } = props;
    helpActiveAction({ isHelpActive: !isHelpActive });
  };

  const getUserEmailAddress = () => {
    getUserEmailAddressApi().then(response => {
      if (response.success) {
        const userEmailList = response.items.EmailList.map(item => ({
          ...item,
          uniqueId: uuidv1(),
          isInvalid: false
        }));

        props.onStateChangeAction({
          userEmailList: userEmailList
        });
      }
    });
  };

  const getUserPhoneNumber = () => {
    getUserPhoneNumberApi().then(response => {
      if (response.success) {
        const userPhoneList = response.items.PhoneList.map(item => ({
          ...item,
          uniqueId: uuidv1(),
          isInvalid: false
        }));
        props.onStateChangeAction({
          userPhoneList: userPhoneList
        });
      }
    });
  };

  const getUserProfile = () => {
    props.onStateChangeAction({ isFetching: true });
    getUserProfileApi().then(response => {
      if (response.success) {
        props.onStateChangeAction({
          selectedProfile: response.items,
          isFetching: false
        });
      }
    });
  };

  const getUserSocialMediaLinksFn = () => {
    getUserSocialMediaLinks().then(data => {
      if (data.success) {
        props.onStateChangeAction({
          UserSocialMediaLinks: data.items
        });
        mergeSocialMediaList(data.items);
      }
    });
  };

  const getSocialMedia = () => {
    getSocialMediaApi().then(data => {
      if (data.success) {
        getUserSocialMediaLinksFn();
        const socialMedia = data.items.map(item => ({
          ...item,
          URL: "",
          SocialMediaId: item.Id
        }));
        props.onStateChangeAction({
          socialMedia
        });
      }
    });
  };

  const mergeSocialMediaList = UserSocialMediaLinks => {
    const { socialMedia } = props;
    var socialLinks = map(socialMedia, function(item) {
      return {
        ...item,
        ...find(UserSocialMediaLinks, ["SocialMediaId", item.Id])
      };
    });
    handleSocialMediaActive(socialLinks[0]);
    setState(prevState => ({
      ...prevState,
      socialLinks
    }));
  };

  const handleSocialMediaActive = activeItem => {
    const { socialLinks } = state;
    const SocialLinks = socialLinks.map(item => ({
      ...item,
      isActive: item.SocialMediaId === activeItem.SocialMediaId
    }));
    setState(prevState => ({
      ...prevState,
      socialLinks: SocialLinks
    }));
  };

  const handleSocialMediaSave = () => {
    const { socialLinks } = state;
    const SocialLinks = socialLinks.find(item => item.isActive);

    SaveSocialMediaLink(SocialLinks).then(res => {
      if (res.success) {
        const info = {
          message: "Social Link saved successfully",
          status: "success"
        };
        props.notificationAction(info);
      }
    });
  };

  const handleSocialMediaChange = (selectedItem, value) => {
    const { socialLinks } = state;
    const SocialLinks = socialLinks.map(item => ({
      ...item,
      URL: selectedItem.Id === item.Id ? value : item.URL
    }));
    setState(prevState => ({
      ...prevState,
      socialLinks: SocialLinks
    }));
  };

  const getCountries = () => {
    getCountriesApi().then(data => {
      const filterCountries = data.items.filter(item => item.CountryName);

      const countries = filterCountries.map(item => ({
        ...item,
        value: item.CountryId,
        label: item.CountryName
      }));
      props.onStateChangeAction({ countries });
    });
  };

  const handleSettingsList = id => {
    if (id == 1 || id == 2 || id == 3 || id == 7) {
      const { settingsList } = props;
      let selectedItem = find(settingsList, { Id: id });
      selectedItem = {
        ...selectedItem,
        Id: selectedItem.Id
      };
      props.onStateChangeAction({ selectedItem });
    }
    if (id == 4 || id == 5) {
      const { passwordEmailsList } = props;
      let selectedItem = find(passwordEmailsList, { Id: id });
      selectedItem = {
        ...selectedItem,
        Id: selectedItem.Id
      };
      props.onStateChangeAction({ selectedItem });
    }

    switch (id) {
      case 1:
        props.onStateChangeAction({
          isUserNameSelected: true,
          isPhoneNumberSelected: false,
          isSocialMediaSelected: false,
          isEmailSelected: false,
          isChangePassSelected: false,
          isEmailNotificationSelected: false,
          rightcoloumHeading:
            props.labels.USER_SETTING_LIST_PROFILE_PROFILE_HEADING
        });
        break;
      case 2:
        props.onStateChangeAction({
          isUserNameSelected: false,
          isPhoneNumberSelected: true,
          isEmailSelected: false,
          isChangePassSelected: false,
          isSocialMediaSelected: false,
          isEmailNotificationSelected: false,
          rightcoloumHeading:
            props.labels.USER_SETTING_LIST_PROFILE_PHONE_HEADING,
          addItemMessage: props.labels.USER_SETTING_DETAILS_ADD_PHONE_NUMBER
        });
        break;
      case 3:
        props.onStateChangeAction({
          isUserNameSelected: false,
          isPhoneNumberSelected: false,
          isEmailSelected: true,
          isChangePassSelected: false,
          isSocialMediaSelected: false,
          isEmailNotificationSelected: false,
          rightcoloumHeading:
            props.labels.USER_SETTING_LIST_PROFILE_EMAIL_HEADING,
          addItemMessage: props.labels.USER_SETTING_DETAILS_ADD_EMAIL
        });
        break;
      case 4:
        props.onStateChangeAction({
          isUserNameSelected: false,
          isPhoneNumberSelected: false,
          isEmailSelected: false,
          isChangePassSelected: true,
          isEmailNotificationSelected: false,
          isSocialMediaSelected: false,
          rightcoloumHeading:
            props.labels.USER_SETTING_LIST_NOTIFICATION_PASSWORD_HEADING
        });
        break;
      case 5:
        props.onStateChangeAction({
          isUserNameSelected: false,
          isPhoneNumberSelected: false,
          isEmailSelected: false,
          isChangePassSelected: false,
          isEmailNotificationSelected: true,
          isSocialMediaSelected: false,
          rightcoloumHeading:
            props.labels.USER_SETTING_LIST_NOTIFICATION_NOTIFICATION_HEADING
        });
        break;
      case 7:
        props.onStateChangeAction({
          isUserNameSelected: false,
          isPhoneNumberSelected: false,
          isEmailSelected: false,
          isChangePassSelected: false,
          isEmailNotificationSelected: false,
          isSocialMediaSelected: true,
          rightcoloumHeading:
            props.labels.USER_SETTING_LIST_NOTIFICATION_NOTIFICATION_HEADING
        });
        break;
    }
  };

  const handleItemAdd = () => {
    const { isPhoneNumberSelected, isEmailSelected, userEmailList } = props;

    if (isPhoneNumberSelected) {
      handlePhoneNumberAdd();
    }

    if (isEmailSelected) {
      handleEmailAdd(userEmailList);
    }
  };

  const handlePhoneNumberAdd = () => {
    const { userPhoneList } = props;
    const invalidPhone = filter(userPhoneList, item => item.isInvalidPhone);
    if (invalidPhone.length > 0) {
      const info = {
        message: props.labels.USER_SETTING_PHONE_VALIDATION_MESSAGE,
        status: "error"
      };
      props.notificationAction(info);
      return;
    }

    const phoneItem = {
      UserPhonenumberId: 0,
      UserId: 0,
      uniqueId: uuidv1()
    };
    props.onStateChangeAction({
      userPhoneList: [phoneItem, ...userPhoneList]
    });
  };

  const handleEmailAdd = emailList => {
    const invalidEmails = filter(emailList, item => item.isInvalidEmail);
    if (invalidEmails.length > 0) {
      const info = {
        message: props.labels.USER_SETTING_EMAIL_VALIDATION_MESSAGE,
        status: "error"
      };
      props.notificationAction(info);
      return;
    }

    const { userEmailList } = props;
    const emailItem = {
      UserEmailId: 0,
      UserId: 0,
      uniqueId: uuidv1()
    };
    props.onStateChangeAction({
      userEmailList: [emailItem, ...userEmailList]
    });
  };

  const handlePhoneNumberDelete = deletedItem => {
    if (deletedItem.UserPhonenumberId == 0) {
      const phoneList = props.userPhoneList.filter(
        item => deletedItem.uniqueId !== item.uniqueId
      );
      props.onStateChangeAction({ userPhoneList: phoneList });
    } else {
      props.onStateChangeAction({
        dialogMessage: "Do you want to delete phone number?",
        isDeletePhoneNumber: true,
        phoneIdToDelete: deletedItem.UserPhonenumberId
      });
    }
  };

  const handleEmailDelete = deletedItem => {
    if (deletedItem.UserEmailId == 0) {
      const emailList = props.userEmailList.filter(
        item => deletedItem.uniqueId !== item.uniqueId
      );
      props.onStateChangeAction({ userEmailList: emailList });
    } else {
      props.onStateChangeAction({
        dialogMessage: "Do you want to delete email address?",
        isDeleteEmail: true,
        emailIdToDelete: deletedItem.UserEmailId
      });
    }
  };

  const handlePhoneNumberChange = (uniqueId, e) => {
    const { value } = e.target;
    const userPhoneList = props.userPhoneList.map(item => ({
      ...item,
      UserPhonenumberValue:
        item.uniqueId === uniqueId ? value : item.UserPhonenumberValue,
      isInvalidPhone: item.uniqueId === uniqueId ? validatePhone(value) : false
    }));
    props.onStateChangeAction({
      userPhoneList
    });
  };

  const handleEmailChange = (uniqueId, e) => {
    const { value } = e.target;
    const userEmailList = props.userEmailList.map(item => ({
      ...item,
      UserEmailValue: item.uniqueId === uniqueId ? value : item.UserEmailValue,
      isInvalidEmail: item.uniqueId === uniqueId ? validatEmail(value) : false
    }));
    props.onStateChangeAction({
      userEmailList
    });
  };

  const validatEmail = email => {
    var re = /^([a-zA-Z0-9_\.\-\+])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;

    if (
      email.trim() === null ||
      email === "" ||
      !re.test(email) ||
      emailAvailability(email)
    ) {
      return true;
    }
  };

  const emailAvailability = email => {
    const filteremail = filter(props.userEmailList, function(o) {
      return o.UserEmailId != 0;
    });
    const found = find(filteremail, function(f) {
      return f.UserEmailValue === email;
    });
    if (found) {
      return true;
    }
  };

  const phoneAvailability = phone => {
    const filterphone = filter(props.userPhoneList, function(o) {
      return o.UserPhonenumberId != 0;
    });
    const found = find(filterphone, function(f) {
      return f.UserPhonenumberValue === phone;
    });
    if (found) {
      return true;
    }
  };

  const validatePhone = phone => {
    var re = /^[\d\s\+?\(?\)?\-?\.?]+$/;
    if (phone.length > 20) {
      return true;
    }
    if (
      phone.trim() === null ||
      phone === "" ||
      !re.test(phone) ||
      phoneAvailability(phone)
    ) {
      return true;
    }
  };

  const isValidName = value => {
    return value.length < 50 ? true : false;
  };

  const handleFormFieldChange = e => {
    const { name, value } = e.target;
    const { selectedProfile } = props;
    props.onStateChangeAction({
      selectedProfile: {
        ...selectedProfile,
        [name]: value
      },
      [`isInvalid${name}`]: !(value && isValidName(value))
    });
  };

  const handleSaveUserSetting = () => {
    const {
      isInvalidUserFirstname,
      isInvalidUserLastname,
      isUserNameSelected,
      isPhoneNumberSelected,
      isEmailSelected,
      userEmailList,
      userPhoneList,
      isSocialMediaSelected
    } = props;

    if (isEmailSelected) {
      const invalidEmails = filter(userEmailList, item => item.isInvalidEmail);
      if (invalidEmails.length > 0) {
        const info = {
          message: props.labels.USER_SETTING_EMAIL_VALIDATION_MESSAGE,
          status: "error"
        };
        props.notificationAction(info);
        return;
      }
    }

    if (isSocialMediaSelected) {
      const { socialLinks } = state;
      const selectedLink = socialLinks.filter(item => item.isActive);
      if (selectedLink.URL) {
        const info = {
          message: "Please enter social media link to save",
          status: "error"
        };
        props.notificationAction(info);
        return;
      }
    }

    if (isPhoneNumberSelected) {
      const invalidPhone = filter(userPhoneList, item => item.isInvalidPhone);
      if (invalidPhone.length > 0) {
        const info = {
          message: props.labels.USER_SETTING_PHONE_VALIDATION_MESSAGE,
          status: "error"
        };
        props.notificationAction(info);
        return;
      }
    }

    if (isInvalidUserFirstname || isInvalidUserLastname) {
      const info = {
        message: props.labels.USER_SETTING_PROFILE_NAME_VALIDATION_MESSAGE,
        status: "info"
      };
      props.notificationAction(info);
      return;
    }

    props.onStateChangeAction({
      dialogMessage: props.labels.USER_SETTING_SAVE_CONFIRMATION_MESSAGE
    });
  };

  const SaveUserProfile = () => {
    const { selectedProfile } = props;
    const dispatch = useDispatch();
    const profileObj = {
      UserFirstname: selectedProfile.UserFirstname,
      UserLastname: selectedProfile.UserLastname,
      ProfilePicture: selectedProfile.ProfilePicture,
      CountryId: selectedProfile.CountryId
    };

    updateUserProfileApi(profileObj).then(response => {
      if (response.success) {
        const user = { ...props.user, FirstName: response.items };

        dispatch(loginAcionUpdate({ user }));
        const info = {
          message: props.labels.USER_SETTING_SAVE_PROFILE_SUCCESS_MESSAGE,
          status: "success"
        };
        props.notificationAction(info);
        getUserProfile();
        getCountries();
      }
    });
  };

  const SaveUserPhones = () => {
    const { userPhoneList } = props;
    var settingObj = {
      PhoneNumberList: userPhoneList
    };

    updatePhoneNumberSettingApi(settingObj).then(() => {
      const info = {
        message: props.labels.USER_SETTING_SAVE_PHONE_SUCCESS_MESSAGE,
        status: "success"
      };
      props.notificationAction(info);
      getUserPhoneNumber();
    });
  };

  const SaveUserEmail = () => {
    const { userEmailList } = props;
    var settingObj = {
      EmailList: userEmailList
    };

    updateEmailSettingApi(settingObj).then(() => {
      const info = {
        message: props.labels.USER_SETTING_SAVE_EMAIL_SUCCESS_MESSAGE,
        status: "success"
      };
      props.notificationAction(info);
      getUserEmailAddress();
    });
  };

  const DeleteUserEmail = emailId => {
    deleteUserEmailApi({ emailId }).then(response => {
      if (response.success) {
        const info = {
          message: "User email is successfully deleted.",
          status: "success"
        };
        props.notificationAction(info);
      } else {
        const info = {
          message: "User email is associated with user details.",
          status: "info"
        };
        props.notificationAction(info);
      }
      getUserEmailAddress();
      props.onStateChangeAction({ isDeleteEmail: false });
    });
  };

  const DeleteUserPhoneNumber = phoneId => {
    deleteUserPhoneApi({ phoneId }).then(response => {
      if (response.success) {
        const info = {
          message: "Phone number is successfully deleted.",
          status: "success"
        };
        props.notificationAction(info);
      } else {
        const info = {
          message: "Phone number is associated with company or presentaion.",
          status: "info"
        };
        props.notificationAction(info);
      }
      getUserPhoneNumber();
      props.onStateChangeAction({ isDeletePhoneNumber: false });
    });
  };

  const handleYesClick = () => {
    props.onStateChangeAction({ dialogMessage: "" });
    const {
      isUserNameSelected,
      isPhoneNumberSelected,
      isEmailSelected,
      isDeleteEmail,
      isDeletePhoneNumber,
      emailIdToDelete,
      phoneIdToDelete,
      isSocialMediaSelected
    } = props;
    if (isUserNameSelected) {
      SaveUserProfile();
      return;
    }
    if (isPhoneNumberSelected && !isDeletePhoneNumber) {
      SaveUserPhones();
      return;
    }
    if (isDeletePhoneNumber && phoneIdToDelete > 0) {
      DeleteUserPhoneNumber(phoneIdToDelete);
      return;
    }

    if (isEmailSelected && !isDeleteEmail) {
      SaveUserEmail();
    }
    if (isDeleteEmail && emailIdToDelete > 0) {
      DeleteUserEmail(emailIdToDelete);
      return;
    }
    if (isSocialMediaSelected) {
      handleSocialMediaSave();
      return;
    }
  };

  const handleNoClick = () => {
    props.onStateChangeAction({ dialogMessage: "" });
  };

  const handleLogoutClick = () => {
    logoutApi().then(response => {
      if (response.success) {
        props.logoutAction();
        StorageService.clearAll();

        window.location.href = RESET_LANDINGPAGE_URL;
      }
    });
  };

  const handleFormSelectChange = event => {
    if (!event) return;
    const { selectedProfile } = props;
    props.onStateChangeAction({
      selectedProfile: {
        ...selectedProfile,
        CountryId: event.CountryId
      }
    });
  };
  const updateAvatar = image => {
    const { selectedProfile } = props;

    props.onStateChangeAction({
      selectedProfile: {
        ...selectedProfile,
        ProfilePicture: image
      }
    });
  };
  const handleAvatarChange = e => {
    if (e.target.files && e.target.files[0]) {
      loadImageOrientation(e.target.files[0], updateAvatar);
      e.target.value = "";
    }
  };

  const OldPassCallback = value => {
    props.onStateChangeAction({
      invalidOldPassword: value
    });
  };

  const dialogMessagePassCallback = message => {
    props.onStateChangeAction({
      dialogMessagePass: message
    });
  };

  // const getCurrentUser = () => {
  //   getUserApi().then(response => {
  //     if (response.success) {
  //       const { user } = props;
  //       user.UserFirstname = response.items.UserFirstname;
  //       props.loginAcionUpdate({ user });
  //     }
  //   });
  // };

  const handleGetImgSrc = () => {
    const { selectedProfile } = props;
    props.onStateChangeAction({
      selectedProfile: {
        ...selectedProfile,
        ProfilePicture: null
      }
    });
  };
  const handleExpanded = name => {
    const { expanded } = props;
    props.onStateChangeAction({
      expanded: {
        // ...expanded,
        [name]: !expanded[name]
      }
    });
  };
  const { labels, isHelpActive } = props;
  const {
    selectedItem,
    settingsList,
    passwordEmailsList,
    selectedProfile,
    listCollapsed,
    formCollapsed,
    userPhoneList,
    userEmailList,
    dialogMessage,
    dialogMessagePass,
    isInvalidUserFirstname,
    isInvalidUserLastname,
    isUserNameSelected,
    isPhoneNumberSelected,
    isEmailSelected,
    isChangePassSelected,
    isEmailNotificationSelected,
    rightcoloumHeading,
    isLoading,
    addItemMessage,
    countries,
    isFetching,
    invalidOldPassword,
    expanded,
    isSocialMediaSelected,
    SocialLinks
  } = props;
  return (
    <PageWrapper className="settings-page">
      {isLoading && <LoadingMask />}
      {dialogMessage && (
        <ConfirmDialog testId="passwrod-confirm-diloag">
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              testId="user-setting-save-yes"
              onClick={handleYesClick}
            >
              {"Yes"}
            </ConfirmDialog.Button>

            <ConfirmDialog.Button
              className="dialog-btn"
              testId="user-setting-save-no"
              onClick={handleNoClick}
            >
              {"No"}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}

      {dialogMessagePass && (
        <ConfirmDialog testId="setting-password-confirm-diloag">
          <ConfirmDialog.Message>{dialogMessagePass}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              testId="setting-password-delete-yes"
              onClick={handleLogoutClick}
            >
              {"Logout"}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      <Column collapse={listCollapsed} className="col-1">
        <Column.Collapsed
          text={labels.USER_SETTING_LIST_TITLE}
          onClick={() => props.onStateChangeAction({ listCollapsed: false })}
          tooltipButton={labels.USER_SETTING_LIST_SECTION_EXPAND_BUTTON_TOOLTIP}
          isHelpActive={isHelpActive}
          tooltipHelp={labels.USER_SETTING_LIST_SECTION_EXPAND_BUTTON_HELP}
          tooltipPlace="left"
        />
        <Column.Head className>
          <div className="heading">{labels.USER_SETTING_LIST_HEADING}</div>
          <Button
            className="collapseBtn"
            tooltipButton={
              labels.USER_SETTING_LIST_SECTION_COLLAPSE_BUTTON_TOOLTIP
            }
            tooltipHelp={labels.USER_SETTING_LIST_SECTION_COLLAPSE_BUTTON_HELP}
            tooltipPlace="left"
            isHelpActive={isHelpActive}
            onClick={() => props.onStateChangeAction({ listCollapsed: true })}
          />
        </Column.Head>
        <Column.Body>
          <Accordion>
            <AccordionItem
              initialEntered
              buttonProps={{
                className: "accordion__title accordion__arrow"
              }}
              expanded={expanded.basic}
              header={labels.USER_SETTING_LIST_PROFILE_HEADING}
              data-testid="accordionBasicInfo"
            >
              <div>
                <List>
                  {settingsList.map(item => (
                    <List.ListItem
                      onClick={() => handleSettingsList(item.Id)}
                      isSelected={item.Id === selectedItem.Id}
                      key={item.Id}
                    >
                      {item.Name}
                    </List.ListItem>
                  ))}
                </List>
              </div>
            </AccordionItem>

            <AccordionItem
              expanded={expanded.notification}
              data-testid="accordionEmailSetting"
              header={labels.USER_SETTING_LIST_NOTIFICATION_HEADING}
            >
              <div>
                <List>
                  {passwordEmailsList.map(item => (
                    <List.ListItem
                      onClick={() => handleSettingsList(item.Id)}
                      isSelected={item.Id === selectedItem.Id}
                      key={item.Id}
                      testId={`password-emails-list-${item.Id}`}
                    >
                      {item.Name}
                    </List.ListItem>
                  ))}
                </List>
              </div>
            </AccordionItem>
          </Accordion>
        </Column.Body>
      </Column>
      <Column collapse={formCollapsed} className="col-2">
        <Column.Collapsed
          onClick={() => props.onStateChangeAction({ formCollapsed: false })}
          text={labels.USER_SETTING_DETAILS_SECTION_TITLE}
          tooltipButton={
            labels.USER_SETTING_DETAILS_SECTION_EXPAND_BUTTON_TOOLTIP
          }
          isHelpActive={isHelpActive}
          tooltipHelp={labels.USER_SETTING_DETAILS_SECTION_EXPAND_BUTTON_HELP}
          tooltipPlace="left"
        />
        <Column.Head>
          <div className="heading">{rightcoloumHeading}</div>
          {(isPhoneNumberSelected || isEmailSelected) && (
            <Button
              className="AddBtn"
              tooltipButton={addItemMessage}
              tooltipHelp={addItemMessage}
              isHelpActive={isHelpActive}
              tooltipPlace="left"
              testId="user-setting-update-btn"
              testIdHelp="user-setting-update-help-btn"
              onClick={() => handleItemAdd()}
            />
          )}

          {(isPhoneNumberSelected || isEmailSelected || isUserNameSelected) && (
            <Button
              className="SaveCloudBtn"
              tooltipButton={
                labels.USER_SETTING_DETAILS_SECTION_SAVE_BUTTON_TOOLTIP
              }
              tooltipHelp={
                labels.USER_SETTING_DETAILS_SECTION_SAVE_BUTTON_TOOLTIP
              }
              isHelpActive={isHelpActive}
              tooltipPlace="left"
              testId="user-setting-update-btn"
              testIdHelp="user-setting-update-help-btn"
              onClick={() => handleSaveUserSetting()}
            />
          )}

          <Button
            className="collapseBtn"
            tooltipButton={
              labels.USER_SETTING_DETAILS_SECTION_COLLAPSE_BUTTON_TOOLTIP
            }
            tooltipHelp={
              labels.USER_SETTING_DETAILS_SECTION_COLLAPSE_BUTTON_HELP
            }
            tooltipPlace="left"
            isHelpActive={isHelpActive}
            onClick={() => props.onStateChangeAction({ formCollapsed: true })}
          />
        </Column.Head>
        <Column.Body>
          {isUserNameSelected && (
            <Profile
              selectedProfile={selectedProfile}
              onFormFieldChange={handleFormFieldChange}
              isInvalidUserFirstname={isInvalidUserFirstname}
              isInvalidUserLastname={isInvalidUserLastname}
              countries={countries}
              onFormSelectChange={handleFormSelectChange}
              onAvatarChange={handleAvatarChange}
              onSaveUserSetting={handleSaveUserSetting}
              isFetching={isFetching}
              getImgSrc={handleGetImgSrc}
            />
          )}
          {isPhoneNumberSelected && (
            <UserPhoneNumbers
              phoneNumbers={userPhoneList}
              onPhoneNumberDelete={handlePhoneNumberDelete}
              onSaveUserSetting={handleSaveUserSetting}
              onPhoneNumberChange={handlePhoneNumberChange}
            />
          )}
          {isEmailSelected && (
            <UserEmails
              emails={userEmailList}
              onEmailDelete={handleEmailDelete}
              onSaveUserSetting={handleSaveUserSetting}
              onEmailChange={handleEmailChange}
            />
          )}
          {isSocialMediaSelected && (
            <SocialMediaLinks
              emails={userEmailList}
              onEmailDelete={handleEmailDelete}
              onSaveUserSetting={handleSaveUserSetting}
              onEmailChange={handleEmailChange}
              SocialLinks={state.socialLinks}
              onSocialMediaActive={handleSocialMediaActive}
              onSocialMediaChange={handleSocialMediaChange}
            />
          )}

          {isChangePassSelected && (
            <ChangePassword
              dialogMessagePass={dialogMessagePassCallback}
              OldPassCallback={OldPassCallback}
              invalidOldPassword={invalidOldPassword}
            />
          )}

          {isEmailNotificationSelected && <UserNotifications />}
        </Column.Body>
      </Column>
    </PageWrapper>
  );
};

const mapStateToProps = ({ systemLabel, userInfo, navigation, setting }) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  const { isHelpActive } = navigation;
  return {
    labels,
    user,
    isHelpActive,
    ...setting
  };
};

const actions = {
  loginAcionUpdate,
  logoutAction,
  helpActiveAction,
  notificationAction,
  onStateChangeAction
};
export default connect(mapStateToProps, actions)(Settings);
