import "./button.scss";
import { HelpTooltip, Tooltip } from "../Tooltip/Tooltip";
import clsx from "clsx";

const Button = ({
  className,
  children,
  text,
  onClick,
  disabled,
  testId,
  isHelpActive,
  followCursor,
  tooltipButton,
  tooltipHelp,
  testIdHelp,
  mainClassName
}) => {
  const renderButton = () => {
    return (
      <button
        onClick={onClick}
        className={`button-primary ${className}`}
        disabled={disabled}
        data-testid={testId}
        type="button"
      >
        {text}
        {children}
      </button>
    );
  };

  return (
    <div className={clsx("button-wrapper", mainClassName)}>
      {isHelpActive ? (
        <>
          {renderButton()}
          <HelpTooltip content={tooltipHelp} testId={testIdHelp} />
        </>
      ) : tooltipButton ? (
        <Tooltip
          content={tooltipButton}
          // trigger={!isMobile ? "mouseenter" : "focus"}
          followCursor={followCursor}
        >
          {renderButton()}
        </Tooltip>
      ) : (
        renderButton()
      )}
    </div>
  );
};

Button.defaultProps = {
  className: "",
  mainClassName: "",
  followCursor: true
};

export default Button;
