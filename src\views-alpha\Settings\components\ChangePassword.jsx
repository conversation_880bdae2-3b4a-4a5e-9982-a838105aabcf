import LockIcon from "../../../assets-alpha/images/svg/lock.svg?react";
import CustomInput from "../../../common-alpha/CustomInput/CustomInput";
import { useSelector } from "react-redux";

const ChangePassword = () => {
  const labels = useSelector(state => state.systemLabel.labels?.setting);

  return (
    <div className="gap-4 flex flex-col mt-5">
      <div>
        <CustomInput
          label={labels?.oldPassword}
          type="password"
          name={"OldPassword"}
          rules={[
            {
              required: true,
              message: labels?.oldPasswordRequired
            }
          ]}
          PrefixIcon={LockIcon}
          isShowPassword
        />
      </div>
      <div>
        <CustomInput
          label={labels?.newPassword}
          name={"NewPassword"}
          type="password"
          rules={[
            {
              required: true,
              message: labels?.newPasswordRequired
            },
            { min: 6, message: labels?.passwordMinLength }
          ]}
          PrefixIcon={LockIcon}
          isShowPassword
        />
      </div>

      <div>
        <CustomInput
          label={labels?.confirmPassword}
          name={"RepeatPassword"}
          type="password"
          dependencies={["NewPassword"]}
          rules={[
            {
              required: true,
              message: labels?.confirmPasswordRequired
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue("NewPassword") === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error(labels?.passwordMismatch));
              }
            })
          ]}
          PrefixIcon={LockIcon}
          isShowPassword
        />
      </div>
    </div>
  );
};

export default ChangePassword;
