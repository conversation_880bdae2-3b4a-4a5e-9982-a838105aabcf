import { useState } from "react";
import Icon from "../../../common-alpha/Icon/Icon";
import clsx from "clsx";
import HelpGuideIcon from "./HelpGuide";
import { replace } from "lodash";
import { CollapseableIcon } from "../Navigation";
import { twMerge } from "tailwind-merge";

const NavButton = ({
  children,
  className,
  onClick,
  name,
  testId,
  disabled,
  navIcon,
  childClassName,
  iconClassName,
  isActive,
  isHelpActive,
  isNavShrink,
  labels,
  Label,
  isSubNavActive,
  hasSubMenus,
  isSubNav
}) => {
  const [isHover, setIsHover] = useState(false);

  return (
    <button
      onClick={onClick}
      className={clsx(
        `w-full p-[8px_8px] max-lg:p-[10px_8px] text-[14px] border-none text-left items-center flex flex-row relative no-underline whitespace-nowrap rounded-[10px] gap-2`,
        {
          "!bg-[var(--light-purple)]": isHover && !isSubNav,
          "!text-[var(--purple)]": isHover

          // "bg-[var(--light-purple)]": isHover || isActive,
          // "!text-[var(--purple)]": isHover || isActive
        },
        className
      )}
      name={name}
      data-testid={testId}
      disabled={disabled}
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
    >
      {isHelpActive && hasSubMenus ? (
        <CollapseableIcon
          isSubNavActive={isSubNavActive}
          isHelpActive={isHelpActive}
        />
      ) : (
        navIcon && (
          <Icon
            src={isHover || isActive ? navIcon.active : navIcon.icon}
            className={twMerge(clsx("min-w-[24px] h-[24px]", iconClassName))}
            renderSvg
          />
        )
      )}

      <span
        className={clsx(
          "child-text flex-[1] flex justify-between gap-[10px]",
          childClassName
        )}
      >
        {children}

        {isHelpActive && (
          <HelpGuideIcon
            testId={`${testId}-help`}
            isNavShrink={isNavShrink}
            tooltipHelp={labels[replace(Label, "_LABEL", "_HELP_TOOLTIP")]}
          />
        )}
      </span>
    </button>
  );
};

export default NavButton;
