import AvatarIcon from "../../../../../assets-alpha/images/svg/avatar.svg";
import ProfileIcon from "../../../../../assets-alpha/images/svg/profile.svg?react";
import LocationIcon from "../../../../../assets-alpha/images/svg/location.svg?react";
import RankingIcon from "../../../../../assets-alpha/images/svg/ranking.svg?react";
import { formatDate } from "../../../../../utilities/helpers";
import { RolesLevels } from "../../../../../utilities-alpha/constant";
import moment from "moment";
import { round } from "lodash";
import { Avatar, Tag } from "antd";
import { UserOutlined } from "@ant-design/icons";
const SingleResume = ({
  labels,
  filteredResume,
  setPreviewResumeDetail,
  isEditable
}) => {
  const selectedResume = filteredResume[0];
  return (
    <div
      className={`flex flex-col bg-white overflow-y-auto ${
        isEditable ? "h-full mb-[70px] md:mb-0 md:h-[653px]" : "h-full mb-6"
      } [scrollbar-width:'none'] [-ms-overflow-style:'none'] [&::-webkit-scrollbar]:hidden mt-3`}
    >
      <div className="flex items-center gap-2 mb-6 mt-3">
        <Avatar size={80} icon={<UserOutlined />} src={AvatarIcon} />
        <div className="flex flex-col gap-1">
          <p className="font-medium text-[14px] text-lg flex items-center text-[#878787] gap-[3px]">
            <span className="text-[#878787] flex items-center gap-[4px]">
              <ProfileIcon />
              {selectedResume?.UserFirstName} {selectedResume?.UserLastName}
            </span>
          </p>
          <p className="text-sm text-[14px] !mt-0.5 text-[#7C7C7C]">
            <span className="text-[var(--purple)] flex items-center gap-[4px]">
              <LocationIcon />
              {selectedResume?.Region}
            </span>
          </p>
          <p className="text-sm text-[14px] text-[var(--green-2)] !mt-1 flex gap-[4px]">
            <RankingIcon className="!w-5 !h-5" />
            {labels?.Match_Score_Label} (
            {round(
              (selectedResume?.CorrelationScore /
                (selectedResume?.TotalScore || 1)) *
                100,
              2
            )}
            %)
          </p>
        </div>
      </div>
      <p className="text-[#878787] text-sm font-normal !mb-2">
        {labels?.More_Info_Label}
      </p>

      {/* More Info Section */}
      <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mb-[6px] max-md:!mb-[6px]">
        <p className="text-gray-500 text-sm !mb-2">
          {labels?.Availability_Label}
        </p>
        <p className="font-medium text-sm mt-1">
          <span className="text-gray-500 text-sm !mb-2">
            {labels?.IProSentMessageDetailDateLabel}:
          </span>
          {formatDate(selectedResume?.AvailabilityDate)}
        </p>
      </div>

      {/* Roles Section */}
      {selectedResume?.Profiles?.length > 0 && (
        <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mb-2 max-md:!mb-[6px]">
          <p className="text-gray-500 text-sm !mb-2">
            {labels?.ADMIN_ROLES_LABEL}
          </p>
          {selectedResume?.Profiles.map(role => (
            <div
              key={role.ProfileId}
              className="flex justify-between items-center text-sm mb-2"
            >
              <span>
                {renderScore(role.Score)}
                {role.ProfileValue}
              </span>
              {renderTags(RolesLevels[role.ExperienceLevel])}
            </div>
          ))}
        </div>
      )}

      {/* Skills Section */}
      {selectedResume?.Skills?.length > 0 && (
        <div className="w-full bg-[#F6F3FC] rounded-xl p-4">
          <p className="text-gray-500 text-sm !mb-2">{labels?.Skills_Label}</p>
          {selectedResume?.Skills.map(skill => (
            <div
              key={skill.SkillId}
              className="flex justify-between items-center text-sm mb-2"
            >
              <span>
                {renderScore(skill.Score)}
                {skill.SkillValue}
              </span>
              {renderTags(RolesLevels[skill.ExperienceLevel])}
            </div>
          ))}
        </div>
      )}
      {/* Certification Section */}
      {selectedResume?.Certifications?.length > 0 && (
        <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mt-2">
          <p className="text-gray-500 text-sm !mb-2">
            {labels?.ResumeHeadingCertification}
          </p>
          {selectedResume?.Certifications.map(item => (
            <div
              key={item.CertificationId}
              className="flex justify-between items-center text-sm mb-2"
            >
              <span>
                {renderScore(item.Score)}
                {item.CertificationValue}
              </span>
              {renderTags(moment(item.CertificationDate).format("YYYY"))}
            </div>
          ))}
        </div>
      )}

      {/* Education Section */}
      {selectedResume?.Educations?.length > 0 && (
        <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mt-2">
          <p className="text-gray-500 text-sm !mb-2">
            {labels?.ResumeHeadingEducation}
          </p>
          {selectedResume?.Educations.map(item => (
            <div
              key={item.EducationId}
              className="flex justify-between items-center text-sm mb-2"
            >
              <span>
                {renderScore(item.Score)}
                {item.EducationValue}
              </span>
              {renderTags(item.EducationYear)}
            </div>
          ))}
        </div>
      )}

      {/* Industry Section */}
      {selectedResume?.Industries?.length > 0 && (
        <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mt-2">
          <p className="text-gray-500 text-sm !mb-2">
            {labels?.ResumeHeadingIndustry}
          </p>
          {selectedResume?.Industries.map(item => (
            <div
              key={item.IndustryId}
              className="flex justify-between text-sm mb-2"
            >
              <span>
                {renderScore(item.Score)}
                {item.IndustryValue}
              </span>
              {renderTags(RolesLevels[item.ExperienceLevel])}
            </div>
          ))}
        </div>
      )}

      {/* Language Section */}
      {selectedResume?.Languages?.length > 0 && (
        <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mt-2">
          <p className="text-gray-500 text-sm !mb-2">
            {labels?.ADMIN_LANGUAGES}
          </p>
          {selectedResume?.Languages.map(lang => (
            <div
              key={lang.LanguageId}
              className="flex justify-between text-sm mb-2"
            >
              <span>{lang.LanguageValue}</span>
              {renderTags(RolesLevels[lang.ExperienceLevel])}
            </div>
          ))}
        </div>
      )}

      {/* Keyword Section */}
      {selectedResume?.Keywords?.length > 0 && (
        <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mt-2">
          <p className="text-gray-500 text-sm !mb-2">
            {labels?.oppResumeDetailKeywords}
          </p>
          <div className="flex flex-wrap gap-y-2">
            {selectedResume?.Keywords.map(lang => (
              <span
                key={lang.keywordId}
                // className="flex justify-between text-sm mb-2"
              >
                {renderTags(lang.KeywordValue)}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Professional Experience Section */}
      {selectedResume?.ResumeExperience?.length > 0 && (
        <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mt-2">
          <p className="text-gray-500 text-sm !mb-2">
            {labels?.oppResumeDetailExperience}
          </p>
          {selectedResume?.ResumeExperience.map(item => (
            <div
              key={item.ResumeExperienceId}
              className="flex justify-between text-sm mb-2"
            >
              <span>
                {/* {renderScore(item.)} */}
                {item.CompanyWorked}
              </span>
              {renderTags(item.ProfileValue)}
            </div>
          ))}
        </div>
      )}

      {/* Achivenments Section */}
      {selectedResume?.ResumeOtherAchivenments?.length > 0 && (
        <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mt-2">
          <p className="text-gray-500 text-sm !mb-2">
            {labels?.RESUME_EDIT_ACHIEVEMENTS_SECTION_TITLE}
          </p>
          {selectedResume?.ResumeOtherAchivenments.map(item => (
            <div
              key={item.AchivenmentId}
              className="flex justify-between text-sm mb-2"
            >
              {/* {renderScore(item.Title)} */}
              <span>{item.AchivenmentName}</span>
              {renderTags(item.Title)}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
const renderScore = score => {
  return <span className="text-green-500 font-medium mr-2">{score}</span>;
};
const renderTags = name => {
  return (
    name && (
      <Tag className="!bg-white rounded-lg border-none text-xs !px-3 !py-0.5 !text-[#8E81F5]">
        {name}
      </Tag>
    )
  );
};
export default SingleResume;
