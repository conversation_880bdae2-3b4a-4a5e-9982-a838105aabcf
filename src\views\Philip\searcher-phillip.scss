@use "../../assets/sass/importFiles" as *;
@use "./variables.scss" as *;
@use 'sass:math';

.searcher-phillip {
  width: 100%;
  // display: flex;
  // justify-content: center;
  font-family: "Inter" !important;
  background: #fff !important;
  .select-input .Select-value-label,
  .input-text {
    color: #8d82ab !important;
    font-weight: 600;
    font-size: 14px;
  }
  @include breakpoint(screen767) {
    // margin-top:50px;
  }
  li {
    display: block;
    max-width: 100%;
    border-radius: 5px;
    filter: none;
    background: #f0edfe;
    border: solid 5px transparent;
    transition: 0.15s;
    min-width: 200px;
    height: 40px;
    max-height: 40px;
    margin: 10px;
    &:hover {
      filter: grayscale(0);
    }
  }
  .timesheet-icon {
    display: inline-block;
    border: none;
    min-width: 20px;
    min-height: 20px;
    font-size: 0;
    align-self: center;
    margin-top: 10px;
    &:after {
      @extend %fi;
      font-size: 20px;
      content: $fi-Timesheet;
      color: #fff;
      margin: auto;
      width: 20px;
      height: 20px;
      background-size: 20px;
    }
  }
  .input-text {
    background: #f0edfe;
  }
  .select-input .Select-control .Select-value {
    background: #f0edfe;
  }
  .opportunity-rate {
    margin-right: 10px;
  }
}
.page-wrapper.philip-page {
  color: $white;
  // font-family: "Source Sans Pro" !important;
  background: $back-color-pale_purple_light;
  .page-1 {
    align-self: center;
  }
  .marketing-page {
    // display: flex;
    // flex-direction: column;
    color: #8d82ab;
    font-weight: 600;
    height: 100%;
    justify-content: center;
    h1 {
      margin-left: 5%;
    }
    .steps {
      // display: flex;
      margin: 2% 5%;
      // flex: 1;
      .action-btn {
        color: white;
        background: #05caa8;
        border: transparent;
        border-radius: 5px;
        align-self: center;
        margin-bottom: 5px;
        padding: 5px 15px;
      }
      .step {
        // display: flex;
        // align-items: center;
        // flex-flow: column;
        // flex: 1;
        &.disabled {
          pointer-events: none;
          opacity: 0.5;
          .body {
            flex: 1;
          }
        }
        .header {
          display: flex;
        }
        .badge {
          height: 100px;
          width: 100px;
          max-width: 100px;
          max-height: 100px;
          background: #f0edfe;
          border-radius: 50%;
          align-self: center;
          /* flex: 1; */
          text-align: center;
          line-height: 100px;
          font-size: 50px;
          span {
            color: white;
          }
        }
        h3 {
          flex: 2 1;
          margin-left: -10px;
          color: #707070;
          z-index: 99999999;
          opacity: 0.5;
          font-size: 20px;
        }
        .js_textarea_wrap,
        body {
          display: flex;
          width: 90%;
          color: #8d82ab !important;
          flex-flow: column;
          flex: 1;
          .text-area-wrapper {
            background: #f0edfe;
            display: flex;
            flex-flow: column;
            flex: 1 1;
            border-radius: 10px;
          }
          .js_textarea {
            width: 100%;
            height: 95%;
            outline: none;
            background: #f0edfe;
            border-radius: 5px;
            padding: 10px;
            border: none;
            color: 8d82ab;
          }
          label {
            color: #8d82ab;
            margin-bottom: 10px;
            font-weight: 800;
          }
        }
        .marketing-page {
          color: #8d82ab;
          font-weight: 800;
        }
        .body {
          flex: 1;
          .settings-container {
            label {
              &:first-of-type {
                display: block;
                margin: 5px 5px;
                color: #8d82ab;
                font-weight: 800;
              }
            }
          }
          .input-row {
            display: flex;
            &:first-child {
              margin-right: 10px;
            }
          }
          .draggable {
            color: #8d82ab !important;
            font-weight: 800;
            background: #fff !important;
            &:before {
              transform: rotate(45deg) !important;
              color: #8d82ab;
              right: 8px;
            }
          }
          .custom-input-text {
            border: transparent;
            border-radius: 5px;
            background-color: #eae6fc;
            outline: none;
            color: #8d82ab;
            padding: 5px 10px;
            display: block;
            width: 92%;
          }
        }
      }
      @include breakpoint(screen767) {
        flex-flow: column;
        .step {
          min-height: 400px;
          padding: 20px 5px;
          h3 {
            align-self: center;
          }
        }
      }
    }
    .footer {
      display: flex;
      left: 0;
      bottom: 0;
      position: absolute;
      width: 100%;
      justify-content: space-between;
      overflow: hidden;
      .side-img {
        margin-left: -75px;
        margin-top: 30px;
        margin-bottom: -30px;
        opacity: 0.6;
      }
      .form {
        label {
          color: azure;
        }
        input,
        select {
          background: #c0c0c0;
          color: #3e335e;
        }
        margin-bottom: 50px;
        margin-top: 50px;
        margin-left: 50px;
        flex: 2;
        border: 1px solid $lightPurple;
        display: flex;
        color: black;
        border-radius: 20px;
        .fields {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding: 20px;
          select,
          input {
            padding: 5px;
            border-radius: 5px;
            margin-bottom: 10px;
          }
        }
        .checkboxes {
          flex: 1;
          justify-content: center;
          display: flex;
          flex-direction: column;
          .check-row {
            display: flex;
            margin: 10px;
            label {
              flex: 1;
            }
            input {
              width: 20px;
              height: 20px;
            }
          }
        }
        .check-btn {
          flex: 1;
          margin-top: 50px;
          align-self: center;
          margin-right: 20px;
          button {
            margin-left: 40px;
          }
        }
      }
      .side-buttons {
        flex: 1;
        .item {
          cursor: pointer;
          max-height: 100px;
          background: #8d77cc;
          border-top-left-radius: 25px;
          border-bottom-left-radius: 25px;
          max-height: 100px;
          margin-bottom: 20px;
          margin-left: 50%;
          align-items: center;
          display: flex;
          img {
            cursor: pointer;
            max-height: 60px;
            margin-left: -5px;
          }
          label {
            cursor: pointer;
          }
        }
      }
    }
  }
  /// Mixin to place items on a circle
  /// <AUTHOR> Giraudel
  /// <AUTHOR> Tudor
  /// @param {Integer} $item-count - Number of items on the circle
  /// @param {Length} $circle-size - Large circle size
  /// @param {Length} $item-size - Single item size
  @mixin on-circle($item-count, $circle-size, $item-size) {
    position: relative;
    width: $circle-size;
    height: $circle-size;
    border-radius: 50%;
    padding: 0;
    list-style: none;

    > li {
      display: block;
      position: absolute;
      top: 50%;
      left: 50%;
      margin: -(math.div($item-size, 2));
      width: $item-size;
      height: $item-size;

      $angle: math.div(360, $item-count);
      $rot: 0;

      @for $i from 1 through $item-count {
        &:nth-of-type(#{$i}) {
          transform: rotate($rot * 1deg)
            translate(math.div($circle-size, 2))
            rotate($rot * -1deg);
        }

        $rot: $rot + $angle;
      }
    }
  }

  .circle-container {
    @include on-circle($item-count: 8, $circle-size: 20em, $item-size: 6em);
    border: solid 5px transparent;

    li {
      display: flex;
      max-width: 100%;
      border-radius: 5px;
      filter: none;
      background: #f0edfe;
      border: solid 5px transparent;
      transition: 0.15s;
      min-width: 200px;
      &:hover {
        filter: grayscale(0);
      }
      .timesheet-icon {
        align-self: center;
        margin-top: 0px;
        :after {
          margin-bottom: 3px;
        }
      }
      label {
        align-self: center;
        margin-left: 10px;
      }
    }
  }

  .product-menus {
    flex: 1 1;
    max-width: 80px;
    min-width: 80px;
    margin: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-top: 0px;
    @include breakpoint(screen767) {
      flex: 1 1;
      max-width: 100%;
      margin: 10px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      // margin-top: 45px;
      justify-content: center;
    }
  }
  .loading-list-mask:empty {
    background-color: $back-color-pale_purple_dark;
    /* change height to see repeat-y behavior */
    background-image: linear-gradient(
        100deg,
        rgba(255, 255, 255, 0),
        rgba(255, 255, 255, 0.5) 50%,
        rgba(255, 255, 255, 0) 80%
      ),
      linear-gradient(#dfd9f7 20px, rgba(0, 0, 0, 0) 0%),
      linear-gradient(#dfd9f7 20px, rgba(0, 0, 0, 0) 0%),
      linear-gradient(#dfd9f7 20px, rgba(0, 0, 0, 0) 0%),
      linear-gradient(#dfd9f7 20px, rgba(0, 0, 0, 0) 0%);
  }
  .page-column {
    position: relative;
  }
  .datepicker-input,
  .input-text {
    // font-family: "Source Sans Pro" !important;
  }
  .views-cnt {
    overflow: auto;
    flex-flow: row;
    .dashboard-view,
    .collaborations-view,
    .reports-view,
    .pools-view {
      height: 100%;
      flex: 1;
    }
    @include breakpoint(screen767) {
      flex-flow: column;
      .dashboard-view,
      .collaborations-view,
      .reports-view,
      .pools-view {
        width: 100%;
        flex: 1;
        // margin-top: 5px;
        overflow: auto;
      }
    }
  }
  .heading {
    @extend .lg-heading;
    background: #8d82ab;
    color: $white;
    text-align: center;
    height: 45px;
  }
  .expand-heading {
    cursor: pointer;
    background: #8d82ab;
    color: #f0edfe;
  }
  .collapseBtn {
    height: 45px;
    width: 45px;
    background: #8d82ab;
    color: $white;
    &:before {
      color: #f0edfe;
    }
  }
  .emptySkeletonResult {
    margin-top: 50px;
  }
  .column-body {
    background: #dfd9f7;
    padding: 0px 0px;
    // overflow: auto;
    .start-end {
      display: flex;
      justify-content: space-between;
      > div {
        flex: 1;
        &:first-of-type {
          margin-right: 5px;
        }
      }
      > label {
        color: $font-color-light_grey_purple;
        margin-right: 5px;
        align-self: center;
        margin-bottom: 20px;
      }
    }
  }
  .tictell-list {
    padding-bottom: 50px;
  }
  .input-field textarea {
    height: 50px;
  }
  .action-button-wrapper {
    position: absolute;
    bottom: 0px;
    z-index: 999;
    margin-left: 10px;
    &:last-of-type {
      margin-right: 10px;
      right: 0;
    }
    .help-icon {
      top: 0px;
      right: 0px;
    }
    @include breakpoint(screen767) {
      height: 55px;
      min-height: 55px;
      .button-wrapper:first-of-type {
        margin: 0px 0px 2px 2px;
      }
      .button-wrapper:last-of-type {
        margin: 0px 2px 2px 0px;
      }
    }
    .add-btn {
      width: 55px;
      border-radius: 50%;
      background: $green;
      outline: none;
      padding: 0;
      height: 55px;
      margin-bottom: 10px;
      border: none;
      align-self: center;
      margin-top: 5px;
      @include breakpoint(screen767) {
        height: 45px;
        width: 45px;
      }
      &.disabled-class {
        pointer-events: none;
        cursor: default;
        opacity: 0.5;
        border: 1px solid $back-color-light_purple;
        background: $back-color-pale_purple_dark;
        &:before {
          color: $back-color-light_purple;
        }
      }
      &:before {
        @extend %fi;
        font-size: 30px;
        content: $fi-close;
        color: $white;
        vertical-align: middle;
        transform: rotate(45deg);
        margin: -2px -4px 0 1px;
        box-sizing: border-box;
      }
    }
    .approve-btn {
      width: 55px;
      border-radius: 50%;
      background: $green;
      outline: none;
      padding: 0;
      height: 55px;
      margin-bottom: 10px;
      border: none;
      align-self: center;
      margin-top: 5px;
      @include breakpoint(screen767) {
        height: 40px;
        width: 40px;
      }
      &.disabled-class {
        pointer-events: none;
        cursor: default;
        opacity: 0.5;
        border: 1px solid $back-color-light_purple;
        background: $back-color-pale_purple_dark;
        &:before {
          color: $back-color-light_purple;
        }
      }
      &:before {
        @extend %fi;
        font-size: 30px;
        content: $fi-Approved;
        color: $white;
        vertical-align: middle;
        margin: -1px 0px 0 1px;
        box-sizing: border-box;
      }
    }
    .reject-btn {
      width: 55px;
      border-radius: 50%;
      background: $red;
      outline: none;
      padding: 0;
      height: 55px;
      margin-bottom: 10px;
      align-self: center;
      margin-top: 5px;
      border: none;
      @include breakpoint(screen767) {
        height: 40px;
        width: 40px;
      }
      &.disabled-class {
        pointer-events: none;
        cursor: default;
        opacity: 0.5;
        border: 1px solid $back-color-light_purple;
        background: $back-color-pale_purple_dark;
        &:before {
          color: $back-color-light_purple;
        }
      }
      &:before {
        @extend %fi;
        font-size: 30px;
        content: $fi-ApprovalRejected;
        color: $white;
        vertical-align: middle;
        margin: -1px 0px 0 1px;
        box-sizing: border-box;
      }
      &.status-1 {
        @extend .disabled-class;
        &:before {
          @extend %fi;
          content: $fi-WaitingForApproval;
        }
      }

      &.status-2 {
        @extend .disabled-class;
        &:before {
          @extend %fi;
          content: $fi-Approved;
        }
      }

      &.status-3 {
        background: $purple4;
        &:before {
          @extend %fi;
          content: $fi-SendForApproval;
        }
      }

      &.status-4 {
        background: $purple4;
        &.disabled-class {
          background: $back-color-pale_purple_dark;
        }
        &:before {
          @extend %fi;
          content: $fi-SendForApproval;
        }
      }
    }
  }
  .textarea-field {
    margin-bottom: 0px;
  }
  a {
    text-decoration: none;
    color: #8d82ab;
  }
  .coll-time,
  .coll-expenses {
    display: flex;
    flex-flow: column;
    color: #8d82ab;
    margin-bottom: 5px;
    > div {
      display: flex;
      justify-content: space-between;
      margin: 5px;
      label {
        flex: 1;
        &:first-child {
          flex: 2;
        }
        &:not(:first-child) {
          text-align: end;
        }
      }
    }
  }
}
