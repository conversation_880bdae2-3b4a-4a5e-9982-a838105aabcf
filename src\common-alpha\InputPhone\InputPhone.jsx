import { Form, Typography } from "antd";
import PhoneInput from "antd-phone-input";
import { twMerge } from "tailwind-merge";

const { Text } = Typography;

const InputPhone = ({
  name,
  label,
  suffixText,
  rules,
  onChange,
  listField = {},
  suffixInput,
  PrefixIcon,
  prefixIconClass
}) => {
  const prefixDefaultIconClass = twMerge(
    "text-gray-400 border-r-1 pr-1.5 border-r-[var(--gray-2)] h-full !flex items-center w-[24px] justify-center",
    prefixIconClass
  );

  return (
    <Form.Item
      className="!mb-0"
      label={label}
      name={name}
      rules={rules}
      extra={
        suffixText && (
          <Text className="!text-xs !mt-2 flex !text-[var(--gray-3)]">
            {suffixText}
          </Text>
        )
      }
      {...listField}
    >
      <PhoneInput
        onChange={onChange}
        prefix={PrefixIcon && <PrefixIcon className={prefixDefaultIconClass} />}
        suffix={suffixInput}
        enableSearch
      />
    </Form.Item>
  );
};

export default InputPhone;
