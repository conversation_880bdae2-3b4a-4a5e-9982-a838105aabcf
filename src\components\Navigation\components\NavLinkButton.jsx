import { NavLink } from "react-router-dom";
import "./navbutton.scss";

const NavLinkButton = ({
  children,
  classIcon,
  className,
  to,
  testId,
  onClick,
  disabled
}) => {
  return (
    <NavLink
      // activeClassName={`${!disabled && "activeBtn"}`}
      to={!disabled && to}
      onClick={onClick}
      // className={`nav-button ${className}`}
      className={({ isActive }) =>
        `nav-button ${className} ${isActive && !disabled ? "activeBtn" : ""}`
      }
      data-testid={testId}
      disabled={disabled}
    >
      <i className={classIcon} />
      <span>{children}</span>
    </NavLink>
  );
};

NavLinkButton.defaultProps = {
  className: "",
  to: "/dashboard"
};

export default NavLinkButton;
