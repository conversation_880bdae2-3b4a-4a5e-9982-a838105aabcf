import "./select.scss";
import SelectField from "react-select";

const customStyles = {
  control: (provided) => ({
    ...provided,
    backgroundColor: 'transparent',
    border: 'transparent',
    "&:focus": {
      border: 'none',
    }
  }),
  placeholder: (provided) => ({
    ...provided,
    color: '#1f2937',
  }),
  menu: (provided) => ({
    ...provided,
    backgroundColor: '#fff',
    borderRadius: '5px'
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected ? '#8f82f5' : '#fff',
    color: state.isSelected ? '#fff' : '#000',
    "&:hover": {
      backgroundColor: '#8f82f59c',
      color: '#fff'
    }
  })
};

const Select = ({
  name,
  className = "",
  value,
  testId,
  onChange,
  options,
  clearable = false,
  onBlur,
  disabled,
  placeholder,
  ...rest
}) => (
  <SelectField
    name={name}
    data-testid={testId}
    className={`select-input custom-container-style-select ${className}}`}
    value={value || ""}
    onChange={onChange}
    options={options}
    clearable={clearable}
    onBlur={onBlur}
    disabled={disabled}
    styles={customStyles}
    placeholder={placeholder}
    {...rest}
  />
);

const Creatable = ({
  name,
  className,
  value,
  placeholder,
  promptTextCreator,
  onChange,
  autoBlur = false,
  options,
  clearable = false,
  autoComplete = false,
  ...rest
}) => (
  <SelectField.Creatable
    name={name}
    className={`select-input ${className}`}
    value={value}
    placeholder={placeholder}
    promptTextCreator={promptTextCreator}
    onChange={onChange}
    autoBlur={autoBlur}
    options={options}
    clearable={clearable}
    autoComplete={autoComplete}
    {...rest}
  />
);

Select.Creatable = Creatable;

Select.Creatable.defaultProps = {
  className: ""
};

Select.defaultProps = {
  className: ""
};

export default Select;
