import Title from "antd/es/typography/Title";
import clsx from "clsx";
import "./empty-view.scss";
import { twMerge } from "tailwind-merge";

const EmptyView = ({ title, detail, icon, noBorder, className }) => {
  return (
    <div
      className={twMerge(
        "flex flex-col items-center justify-center text-center mt-25",
        className
      )}
    >
      <div
        className={clsx(
          "empty-view max-md:before:!content-none rounded-[25px] max-w-[90%]",
          {
            "before:!content-none": noBorder
          }
        )}
      >
        <div
          className={clsx(
            "max-md:p-0 bg-white rounded-[inherit] flex flex-col items-center justify-center",
            {
              "p-[112px_88px]": !noBorder
            }
          )}
        >
          <div className="empty-icon flex justify-center w-[150px] h-[135px] [&_svg]:max-w-full [&_svg]:max-h-full text-center">
            {icon}
          </div>

          <Title
            level={3}
            className="mt-4 !text-base max-md:!text-sm font-semibold"
          >
            {title}
          </Title>
          <p className="text-gray-500 text-sm max-md:text-[13px]">{detail}</p>
        </div>
      </div>
    </div>
  );
};

export default EmptyView;
