.screenshot-gallery {
    margin-top: 20px;
    padding: 15px;
    background-color: #dfd9f7;
    border-radius: 4px;
  }
  
  .screenshot-gallery h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #9783cf;
  }
  
  .screenshots-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .screenshot-item {
    width: 150px;
    cursor: pointer;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    transition: transform 0.2s;
  }
  
  .screenshot-item:hover {
    transform: scale(1.05);
  }
  
  .screenshot-item img.thumbnail {
    width: 100%;
    height: 100px;
    object-fit: cover;
  }
  
  .screenshot-item .timestamp {
    padding: 5px;
    text-align: center;
    background-color: #9783cf;
    font-size: 12px;
  }
  
  .screenshot-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  .modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 90%;
    max-height: 90%;
    position: relative;
  }
  
  .modal-content img {
    max-width: 100%;
    max-height: 70vh;
  }
  
  .close-button {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    cursor: pointer;
    color: #333;
  }
  
  .screenshot-details {
    margin-top: 15px;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
  }