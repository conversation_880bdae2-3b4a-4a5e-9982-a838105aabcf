import { useDispatch } from "react-redux";
import {
  useGetUserProfileApi,
  useUpdateUserProfileApi
} from "../Settings/settingsApi";
import { useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { notificationAction } from "../../actions-alpha/notification";
import { ApiUrl } from "../../api-alpha/apiUrls";
import { loadImageOrientation } from "../../utilities/helpers";

export const useProfileUpdate = () => {
  const {
    mutate: updateProfileApi,
    isPending: loadingProfile
  } = useUpdateUserProfileApi();
  const {
    data: userData,
    isFetching: loadingUserData
  } = useGetUserProfileApi();

  const [name, setName] = useState("");
  const [phone, setPhone] = useState("");

  useEffect(() => {
    if (!userData?.items) return;
    setName(
      `${userData?.items?.UserFirstname} ${userData?.items?.UserLastname}`
    );
    setPhone(userData?.items?.PhoneList?.[0]?.UserPhonenumberValue || "");
  }, [userData]);

  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const onUpload = file => {
    if (file) {
      const previewImage = URL.createObjectURL(file);
      loadImageOrientation(previewImage, image => {
        updateProfileApi(
          {
            ...userData?.items,
            ProfilePicture: image
          },
          {
            onSuccess: data => {
              const notify = {
                message: data?.message,
                status: data?.success ? "success" : "error"
              };
              dispatch(notificationAction(notify));
              if (data?.success) {
                queryClient.invalidateQueries({
                  queryKey: [ApiUrl.Settings.GetUserProfile]
                });
              }
            }
          }
        );
      });
    }
  };
  const onSave = ({ onSuccess }) => {
    const [UserFirstname, ...UserLastname] = name.split(" ");

    updateProfileApi(
      {
        ...userData?.items,
        UserFirstname,
        UserLastname: UserLastname.join(" "),
        PhoneNumberList: userData?.items?.PhoneList?.length
          ? userData?.items?.PhoneList?.map((phoneItem, index) => ({
              ...phoneItem,
              UserPhonenumberValue:
                index === 0 ? phone : phoneItem.UserPhonenumberValue
            }))
          : [
              {
                UserPhonenumberValue: phone
              }
            ],
        SocialMediaLinks: userData?.items?.SocialLinks || []
      },
      {
        onSuccess: data => {
          const notify = {
            message: data?.message,
            status: data?.success ? "success" : "error"
          };
          dispatch(notificationAction(notify));
          if (data?.success) {
            queryClient.invalidateQueries({
              queryKey: [ApiUrl.Settings.GetUserProfile]
            });
            onSuccess();
            // setIsEditInfoModalOpen(false);
          }
        }
      }
    );
  };

  return {
    onSave,
    loadingProfile: loadingProfile || loadingUserData,
    userData,
    phone,
    setPhone,
    name,
    setName,
    onUpload
  };
};
