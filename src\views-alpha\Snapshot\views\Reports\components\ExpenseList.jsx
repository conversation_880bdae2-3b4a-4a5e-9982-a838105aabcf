import moment from "moment";
import CommonDrawer from "../../../../../common-alpha/Drawer/Drawer";
import { KeyValueDisplay, SubHeading, Title } from "./TimelogList";
import { List, ListItem } from "../Reports";
const ExpenseList = ({
  expenseList,
  onItemClick,
  updateExpenseSheetStatus,
  ExpenseSheetStatusId,
  isHelpActive,
  labels,
  currentTimeReport,
  onClose,
  currentExpenselog
}) => {
  return (
    <>
      <CommonDrawer
        open={true}
        width={440}
        title={
          <Title
            title="Expense"
            TimeSheetStatusId={ExpenseSheetStatusId}
            updateTimeSheetStatus={updateExpenseSheetStatus}
          />
        }
        onClose={onClose}
      >
        <SubHeading>Overall</SubHeading>
        {currentTimeReport.ExpenseSheetId > 0 && (
          <div className="mb-10 bg-[var(--light-purple)] p-4 rounded-[12px] flex items-center justify-between">
            <div>
              <div className="font-medium text-base mb-1 text-[var(--dark)]">
                {currentTimeReport?.IProName}
              </div>
              <div className="text-[var(--gray-3)] mb-3 text-xs">
                {`${moment(currentTimeReport.StartDate).format(
                  "MMM D"
                )}-${moment(currentTimeReport?.EndDate).format("D")}, ${moment(
                  currentTimeReport?.StartDate
                ).format("YYYY")} • Week ${moment(
                  currentTimeReport?.StartDate
                ).format("W")}`}
              </div>
              <div className="text-[var(--gray-3)] text-xs">
                Title:
                <span className="text-[var(--dark)] ml-1">
                  {currentTimeReport.CollaborationTitle}
                </span>
              </div>
            </div>
            <div className="flex flex-col items-center">
              <span className="text-[var(--gray-3)] font-medium text-[10px]">
                Total amount
              </span>
              <span className="text-[var(--purple)] font-medium text-[18px]">
                {currentTimeReport.Amount}
              </span>
            </div>
          </div>
        )}
        <List className="timelog-list">
          {expenseList &&
            expenseList.map(item => (
              <ListItem
                onClick={() => onItemClick(item)}
                item={item}
                key={item.ExpenseId}
                isSelected={item.ExpenseId === currentExpenselog?.ExpenseId}
                text={
                  <KeyValueDisplay
                    keyLabel="Expense Date:"
                    value={
                      item.SpentOn &&
                      moment(item.SpentOn).format("ddd, MMM Do YYYY")
                    }
                  />
                }
                right={`${item.Amount} ${item.CurrencyName.Name} ${
                  item.CurrencyName.Name != item.CollaborationCurrency
                    ? `(${item.CollaborationCurrencyAmount} ${item.CollaborationCurrency})`
                    : ""
                }`}
                // {`${item?.Amount} ${item?.CurrencyName?.Name}`}
                subText={
                  <KeyValueDisplay
                    keyLabel="Category:"
                    value={item.CategoryName.Title}
                  />
                }
                detail={item?.Comment}
              ></ListItem>
            ))}
        </List>
        {/* {ExpenseSheetStatusId == 1 && (
          <Button
            testId={`reject-expense-btn`}
            mainClassName={"action-button-wrapper"}
            className={`reject-btn ${
              ExpenseSheetStatusId == 3 || ExpenseSheetStatusId == 2
                ? "!hidden"
                : ""
            }`}
            onClick={() => updateExpenseSheetStatus(3)}
            isHelpActive={isHelpActive}
            tooltipHelp={labels.snapshotRejectTimesheetBtn}
            testIdHelp={"reject-expense-btn-help"}
          />
        )}
        {ExpenseSheetStatusId == 1 && (
          <Button
            testId={`approve-expense-btn`}
            mainClassName={"action-button-wrapper"}
            className={`approve-btn ${
              ExpenseSheetStatusId == 3 || ExpenseSheetStatusId == 2
                ? "!hidden"
                : ""
            }`}
            onClick={() => updateExpenseSheetStatus(2)}
            isHelpActive={isHelpActive}
            tooltipHelp={labels.snapshotAcceptTimesheetBtn}
            testIdHelp={"approve-expense-btn-help"}
          />
        )} */}
      </CommonDrawer>
    </>
  );
};

export default ExpenseList;
