import { filter, isEqual, map } from "lodash";
import moment from "moment";
import { useEffect, useRef, useState } from "react";
import { connect } from "react-redux";
import { useNavigate } from "react-router-dom";
import { v1 as uuidv1 } from "uuid";
import { notificationAction } from "../../actions/notification";
import { resumeChangeAction } from "../../actions/resumeActions";
import Certifications from "../../assets/newResume/certifications";
import ExperienceSVG from "../../assets/newResume/experience";
import Industry from "../../assets/newResume/industry";
import KeywordSVG from "../../assets/newResume/keyword";
import Language from "../../assets/newResume/language";
import LocationIcon from "../../assets/newResume/location";
import Role from "../../assets/newResume/role";
import Settings from "../../assets/newResume/settings";
import Teacher from "../../assets/newResume/teacher";
import CustomCarousel from "../../common/Carousel/Carousel";
import { privateRoutes } from "../../Routes/routing";
import {
  getCertificationsApi,
  getCertificationSuggestionsApi,
  getEducationsApi,
  getIndustriesApi,
  getKeywordApi,
  getResumesApi,
  getRolesApi,
  getRoleSuggestionsApi,
  getSkillsApi,
  getSkillSuggestionsApi,
  saveUpdatedResumeApi,
  updateResumeAvailabilityApi
} from "../Resume/ResumeEdit/resumeEditApi";
import Experience from "./components/experience/experience";
import Location from "./components/location/location";
import Achievements from "./components/others/Achievements";
import Skill from "./components/skill/skill";
import { DatePicker, Select, Switch } from "antd";
import classes from "./newResume.module.scss";
import LoadingMask from "../../common/LoadingMask/LoadingMask";
import Notification from "../../common/Notification/Notification";
import { isNullOrUndefined } from "../../utilities/helpers";
import { onStateChangeAction } from "../Resume/ResumeEdit/resumeEditAction";
import Education from "./components/education/education";

const { Option } = Select;

const ResumeItem = ({ marginRight, Svg, text, onClick, focus, id, labels }) => (
  <div
    className={`${classes.carouselItem} ${marginRight &&
      classes.marginRight} ${focus === text && classes.focusedItem}`}
    onClick={() => onClick()}
  >
    <div
      className={`${classes.skillsInfo}  ${focus === text && classes.focused}`}
    >
      {<Svg color={focus === text ? "#fff" : "#8F82F5"} />}
    </div>
    <p className={classes.carouselItemText}>{text}</p>
  </div>
);

const initialCarouselItemData = data => [
  {
    text: data?.labels?.RESUME_EDIT_ROLE_SECTION_TITLE,
    svg: Role,
    marginRight: true,
    id: 4,
    skillOptions: [
      "Database Engineer",
      "ERP Specialist",
      "Business Analyst",
      "Graphics Designer"
    ],
    levelOptions: ["Beginner", "Proficient", "Expert"],
    generic: true
  },
  {
    text: data?.labels?.RESUME_EDIT_SKILL_SECTION_TITLE,
    svg: Settings,
    marginRight: true,
    id: 3,
    skillOptions: ["Automated Testing", "HTML", "MySQL", "Node.js"],
    levelOptions: ["Beginner", "Proficient", "Expert"],
    generic: true
  },
  {
    text: data?.labels?.RESUME_EDIT_LANGUAGE_SECTION_TITLE,
    svg: Language,
    marginRight: true,
    id: 2,
    levelOptions: [
      "Beginner",
      "Proficient",
      "Expert"
      // "Advanced",
      // "Fluent",
    ]
  },

  {
    text: data?.labels?.RESUME_EDIT_LOCATION_SECTION_TITLE,
    svg: LocationIcon,
    marginRight: true,
    id: 0
  },

  {
    text: data?.labels?.RESUME_EDIT_EDUCATION_SECTION_TITLE,
    svg: Teacher,
    marginRight: true,
    id: 1
  },

  {
    text: data?.labels?.RESUME_EDIT_CERTIFICATION_SECTION_TITLE,
    svg: Certifications,
    marginRight: true,
    id: 6,
    skillOptions: [
      "Summation Support Specialist",
      "Access Data Mobile Examiner (AME)",
      "Service Desk Manager"
    ],
    levelOptions: [],
    generic: true
  },
  {
    text: data?.labels?.RESUME_EDIT_INDUSTRY_SECTION_TITLE,
    svg: Industry,
    marginRight: false,
    id: 7,
    levelOptions: ["Beginner", "Proficient", "Expert"]
  },
  {
    text: data?.labels?.RESUME_EDIT_EXPERIENCE_SECTION_TITLE,
    svg: ExperienceSVG,
    marginRight: true,
    id: 8,
    levelOptions: ["Details", "Edit"]
  },
  {
    text: data?.labels?.RESUME_EDIT_KEYWORD_SECTION_TITLE,
    svg: KeywordSVG,
    marginRight: false,
    id: 9,
    levelOptions: ["Details", "Edit"]
  },
  {
    text: data?.labels?.RESUME_EDIT_ACHIEVEMENTS_SECTION_TITLE,
    svg: Teacher,
    marginRight: true,
    id: 10
  }
];

const ResumeEdit = props => {
  const isLeaving = useRef();
  const initialLocations = useRef();
  const intialRoles = useRef();
  const intialOtherAcheivemnts = useRef();
  const intialExperiences = useRef();
  const intialEducations = useRef();
  const intialCertifications = useRef();
  const intialSkills = useRef();
  const intialLanguages = useRef();
  const intialKeywords = useRef();
  const intialIndustries = useRef();
  const InitialAvailabilityDate = useRef();
  const InitialAvailabilityType = useRef();
  const isSaved = useRef();

  const [state, setState] = useState({
    busyFilter: false,
    isResumeAvailable: true,
    focused: initialCarouselItemData(props)[0],
    yearsList: [],
    carouselItemData: initialCarouselItemData(props),
    languageDefaultOptions: [],
    isMonthNotice: false
  });

  const navigate = useNavigate();

  const availabilityUpdate = (selectedAvailability, date) => {
    props.onStateChangeAction({ isLoading: true });
    let data = {
      IsAvailable: false,
      AvailabilityDate: moment(date).format("MM/DD/YYYY")
    };
    if (selectedAvailability === "notice") {
      data = {
        IsAvailable: false,
        AvailabilityDate: new moment().add(1, "M").format("MM/DD/YYYY"),
        AvailablityType: 2
      };
    } else if (selectedAvailability === "date") {
      data = {
        IsAvailable: false,
        AvailabilityDate: moment(date).format("MM/DD/YYYY"),
        AvailablityType: 3
      };
    } else if (selectedAvailability === "now") {
      data = {
        IsAvailable: true,
        AvailabilityDate: new moment().format("MM/DD/YYYY"),
        AvailablityType: 1
      };
    }
    updateResumeAvailabilityApi(data).then(({ data }) => {
      if (data.success) {
        if (selectedAvailability === "notice") {
          setState(st => ({
            ...st,
            isMonthNotice: !st.isMonthNotice,
            isResumeAvailable: false
          }));
        }
        if (selectedAvailability === "date") {
          setState(st => ({
            ...st,
            isMonthNotice: false,
            isResumeAvailable: false
          }));
        }
      }
      props.onStateChangeAction({ isLoading: false });
    });
  };

  const availabilityCHange = () => {
    const { AvailabilityDate } = props;
    setState(st => ({
      ...st,
      isResumeAvailable: !st.isResumeAvailable,
      isMonthNotice: false
    }));
    if (state.isResumeAvailable) {
      availabilityUpdate("date", AvailabilityDate);
    }
    if (!state.isResumeAvailable) {
      availabilityUpdate("now", AvailabilityDate);
    }
  };

  const monthlyNoticeAvailabilityCHange = () => {
    const { AvailabilityDate } = props;
    if (state.isMonthNotice) {
      availabilityUpdate("date", AvailabilityDate);
    }
    if (!state.isMonthNotice) {
      availabilityUpdate("notice", AvailabilityDate);
    }
  };

  const handleYearsLoad = () => {
    var currentYear = new Date().getFullYear();
    var startYear = 1970;
    var yearList = [];
    var newCarouselData = [];
    while (currentYear >= startYear) {
      yearList.push(currentYear);
      currentYear--;
    }
    state.carouselItemData.map(item => {
      if (item.text === "Certification") {
        newCarouselData.push({ ...item, levelOptions: yearList });
        console.log("yearList", yearList);
      } else {
        newCarouselData.push(item);
      }
    });

    setState(st => ({
      ...st,
      carouselItemData: newCarouselData
    }));
  };

  const handleChange = value => {
    if (value === "Now") {
      availabilityCHange();
    }
    if (value === "1 Month Notice") {
      monthlyNoticeAvailabilityCHange();
    }
    if (value === "Busy Until") {
      setState(st => ({ ...st, busyFilter: true }));
    } else {
      setState(st => ({ ...st, busyFilter: false }));
    }
  };

  const handleYesClick = () => {
    const { isLogoutTrigger, isRoleSwitchTrigger } = props;
    if (isLogoutTrigger || isRoleSwitchTrigger) {
      handleResumeSave({ isLogoutTrigger, isRoleSwitchTrigger });
    }
    if (isLeaving.current) {
      props.onStateChangeAction({
        dialogMessage: ""
      });
      handleResumeSave();
      return;
    }
    const { Type, UniqueId, ItemValue } = props.currentCandidate;
    if (Type === "Language") {
      const languageList = props.languageList.map(item => ({
        ...item,
        LanguageId: item.uniqueId === UniqueId ? 0 : item.LanguageId,
        LanguageValue:
          item.uniqueId === UniqueId ? ItemValue : item.LanguageValue,
        isInvalid: item.uniqueId === UniqueId ? false : item.isInvalid,
        isCandidate: item.uniqueId === UniqueId ? true : item.isCandidate
      }));
      props.onStateChangeAction({
        languageList
      });
    } else if (Type === "Role") {
      const rolesList = props.rolesList.map(item => ({
        ...item,
        ProfileId: item.uniqueId === UniqueId ? 0 : item.ProfileId,
        ProfileValue:
          item.uniqueId === UniqueId ? ItemValue : item.ProfileValue,
        isInvalid: item.uniqueId === UniqueId ? false : item.isInvalid,
        isCandidate: item.uniqueId === UniqueId ? true : item.isCandidate
      }));
      props.onStateChangeAction({
        rolesList
      });
    } else if (Type === "Skill") {
      const skillsList = props.skillsList.map(item => ({
        ...item,
        SkillId: item.uniqueId === UniqueId ? 0 : item.SkillId,
        SkillValue: item.uniqueId === UniqueId ? ItemValue : item.SkillValue,
        isInvalid: item.uniqueId === UniqueId ? false : item.isInvalid,
        isCandidate: item.uniqueId === UniqueId ? true : item.isCandidate
      }));
      props.onStateChangeAction({
        skillsList
      });
    } else if (Type === "Certification") {
      const certificationsList = props.certificationsList.map(item => ({
        ...item,
        CertificationId: item.uniqueId === UniqueId ? 0 : item.CertificationId,
        CertificationValue:
          item.uniqueId === UniqueId ? ItemValue : item.CertificationValue,
        isInvalid: item.uniqueId === UniqueId ? false : item.isInvalid,
        isCandidate: item.uniqueId === UniqueId ? true : item.isCandidate
      }));
      props.onStateChangeAction({
        certificationsList
      });
    } else if (Type === "Education") {
      const educationsList = props.educationsList.map(item => ({
        ...item,
        EducationId: item.uniqueId === UniqueId ? 0 : item.EducationId,
        EducationValue:
          item.uniqueId === UniqueId ? ItemValue : item.EducationValue,
        isInvalid: item.uniqueId === UniqueId ? false : item.isInvalid,
        isCandidate: item.uniqueId === UniqueId ? true : item.isCandidate
      }));
      props.onStateChangeAction({
        educationsList
      });
    } else if (Type === "Keyword") {
      const keywordsList = props.keywordsList.map(item => ({
        ...item,
        KeywordId: item.uniqueId === UniqueId ? 0 : item.KeywordId,
        KeywordValue:
          item.uniqueId === UniqueId ? ItemValue : item.KeywordValue,
        isInvalid: item.uniqueId === UniqueId ? false : item.isInvalid,
        isCandidate: item.uniqueId === UniqueId ? true : item.isCandidate
      }));
      props.onStateChangeAction({
        keywordsList
      });
    } else if (Type === "Industry") {
      const industriesList = props.industriesList.map(item => ({
        ...item,
        IndustryId: item.uniqueId === UniqueId ? 0 : item.IndustryId,
        IndustryValue:
          item.uniqueId === UniqueId ? ItemValue : item.IndustryValue,
        isInvalid: item.uniqueId === UniqueId ? false : item.isInvalid,
        isCandidate: item.uniqueId === UniqueId ? true : item.isCandidate
      }));
      props.onStateChangeAction({
        industriesList
      });
    }
    props.onStateChangeAction({
      dialogMessage: ""
    });
  };

  const handleResumeChange = () => {
    const { isLogoutTrigger, isRoleSwitchTrigger, resumeChangeAction } = props;
    if (isLogoutTrigger || isRoleSwitchTrigger) {
      resumeChangeAction({
        isResumeChange: false,
        message: "",
        isLogout: isLogoutTrigger,
        isRoleSwitchToggle: isRoleSwitchTrigger
      });
    }
    return;
  };

  const handleNoClick = () => {
    handleResumeChange();
    if (isLeaving.current) {
      navigate(location.pathname);
      return;
    }
    const { Type, UniqueId } = props.currentCandidate;
    if (Type === "Language") {
      const languageList = props.languageList.map(item => ({
        ...item,
        isInvalid:
          item.uniqueId === UniqueId && item.LanguageValue === ""
            ? true
            : item.isInvalid
      }));
      props.onStateChangeAction({
        languageList
      });
    } else if (Type === "Role") {
      const rolesList = props.rolesList.map(item => ({
        ...item,
        isInvalid:
          item.uniqueId === UniqueId && item.ProfileValue === ""
            ? true
            : item.isInvalid
      }));
      props.onStateChangeAction({
        rolesList
      });
    } else if (Type === "Skill") {
      const skillsList = props.skillsList.map(item => ({
        ...item,
        isInvalid:
          item.uniqueId === UniqueId && item.SkillValue === ""
            ? true
            : item.isInvalid
      }));
      props.onStateChangeAction({
        skillsList
      });
    } else if (Type === "Certification") {
      const certificationsList = props.certificationsList.map(item => ({
        ...item,
        isInvalid:
          item.uniqueId === UniqueId && item.CertificationValue === ""
            ? true
            : item.isInvalid
      }));
      props.onStateChangeAction({
        certificationsList
      });
    } else if (Type === "Education") {
      const educationsList = props.educationsList.map(item => ({
        ...item,
        isInvalid:
          item.uniqueId === UniqueId && item.EducationValue === ""
            ? true
            : item.isInvalid
      }));
      props.onStateChangeAction({
        educationsList
      });
    } else if (Type === "Keyword") {
      const keywordsList = props.keywordsList.map(item => ({
        ...item,
        isInvalid:
          item.uniqueId === UniqueId && item.KeywordValue === ""
            ? true
            : item.isInvalid
      }));
      props.onStateChangeAction({
        keywordsList
      });
    } else if (Type === "Industry") {
      const industriesList = props.industriesList.map(item => ({
        ...item,
        isInvalid:
          item.uniqueId === UniqueId && item.IndustryValue === ""
            ? true
            : item.isInvalid
      }));
      props.onStateChangeAction({
        industriesList
      });
    }
    props.onStateChangeAction({
      currentCandidate: {}
    });
    props.onStateChangeAction({ dialogMessage: "" });
  };

  useEffect(() => {
    props.onStateChangeAction({
      dialogMessage: ""
    });
    handleRoleDefaultOptionsLoad();
    handleSkillDefaultOptionsLoad();
    handleCertificationDefaultOptionsLoad();
    handleEducationDefaultOptionsLoad();
    handleKeywordDefaultOptionsLoad();
    handleIndustryDefaultOptionsLoad();
    getResume();
    handleYearsLoad();
  }, []);

  const isThereIsAnyUnSavedChange = () => {
    let { AvailablityType, AvailabilityDate } = props.resumes;
    const {
      busyUntil,
      locationList,
      languageList,
      rolesList,
      skillsList,
      certificationsList,
      educationsList,
      industriesList,
      keywordsList,
      experiencesList,
      otherAchivenmentsList
    } = props;
    if (
      AvailablityType !== props.AvailablityType ||
      !moment(AvailabilityDate).isSame(moment(busyUntil)) ||
      !(
        initialLocations.current.length === locationList.length &&
        initialLocations.current.every(
          (v, i) => v.CountryName === locationList[i].CountryName
        )
      ) ||
      !isEqual(intialLanguages.current, languageList) ||
      !(
        intialRoles.current.length === rolesList.length &&
        intialRoles.current.every(
          (v, i) =>
            v.ProfileValue === rolesList[i].ProfileValue &&
            v.ExperienceLevel === rolesList[i].ExperienceLevel
        )
      ) ||
      !(
        intialSkills.current.length === skillsList.length &&
        intialSkills.current.every(
          (v, i) =>
            v.SkillValue === skillsList[i].SkillValue &&
            v.ExperienceLevel === skillsList[i].ExperienceLevel
        )
      ) ||
      !(
        intialCertifications.current.length === certificationsList.length &&
        intialCertifications.current.every(
          (v, i) =>
            v.CertificationValue === certificationsList[i].CertificationValue &&
            v.CertificationDate === certificationsList[i].CertificationDate
        )
      ) ||
      !isEqual(intialEducations.current, educationsList) ||
      !isEqual(intialIndustries.current, industriesList) ||
      !isEqual(intialExperiences.current, experiencesList) ||
      !isEqual(intialOtherAcheivemnts.current, otherAchivenmentsList) ||
      !isEqual(intialKeywords.current, keywordsList)
    ) {
      return true;
    } else {
      return false;
    }
  };

  // const blockRoute = history.block(targetLocation => {
  //   if (isLeaving.current || isSaved.current) {
  //     return true;
  //   }
  //   if (isThereIsAnyUnSavedChange()) {
  //     props.onStateChangeAction({
  //       dialogMessage: props.labels.RESUME_EDIT_UNSAVED_CHANGES_MESSAGE
  //     });
  //     isLeaving.current = true;
  //     pathname = targetLocation.pathname;
  //     return false;
  //   }
  //   return true;
  // });

  const handleRoleDefaultOptionsLoad = () => {
    getRolesApi({ searchKey: "a" }).then(response => {
      if (response.success) {
        const roleDefaultOptions = map(response.items, profile => {
          return {
            ...profile,
            label: profile.ProfileValue,
            value: profile.ProfileId
          };
        });
        props.onStateChangeAction({
          roleDefaultOptions
        });
      }
    });
  };
  const handleSkillDefaultOptionsLoad = () => {
    getSkillsApi({ searchKey: "a" }).then(response => {
      if (response.success) {
        const skillDefaultOptions = map(response.items, skill => {
          return {
            ...skill,
            label: skill.SkillValue,
            value: skill.SkillId
          };
        });
        props.onStateChangeAction({
          skillDefaultOptions
        });
      }
    });
  };
  const handleCertificationDefaultOptionsLoad = () => {
    getCertificationsApi({ searchKey: "a" }).then(response => {
      if (response.success) {
        const certificationDefaultOptions = map(
          response.items,
          certification => {
            return {
              ...certification,
              label: certification.CertificationValue,
              value: certification.CertificationId
            };
          }
        );
        props.onStateChangeAction({
          certificationDefaultOptions
        });
      }
    });
  };
  const handleEducationDefaultOptionsLoad = () => {
    getEducationsApi({ searchKey: "a" }).then(response => {
      if (response.success) {
        const educationDefaultOptions = map(response.items, education => {
          return {
            ...education,
            label: education.EducationValue,
            value: education.EducationId
          };
        });
        props.onStateChangeAction({
          educationDefaultOptions
        });
      }
    });
  };
  const handleKeywordDefaultOptionsLoad = () => {
    getKeywordApi({ searchKey: "a" }).then(response => {
      if (response.success) {
        const keywordDefaultOptions = map(response.items, Keyword => {
          return {
            ...Keyword,
            label: Keyword.KeywordValue,
            value: Keyword.KeywordId
          };
        });
        props.onStateChangeAction({
          keywordDefaultOptions
        });
      }
    });
  };
  const handleIndustryDefaultOptionsLoad = () => {
    getIndustriesApi({ searchKey: "a" }).then(response => {
      if (response.success) {
        const IndustryDefaultOptions = map(response.items, Industry => {
          return {
            ...Industry,
            label: Industry.IndustryValue,
            value: Industry.IndustryId
          };
        });
        props.onStateChangeAction({
          IndustryDefaultOptions
        });
      }
    });
  };

  const getResume = () => {
    props.onStateChangeAction({ isLoading: true });
    getResumesApi()
      .then(data => {
        if (data.success) {
          props.onStateChangeAction({
            resumes: data.items
          });
          InitialAvailabilityDate.current = isNullOrUndefined(
            data.items.AvailabilityDate
          )
            ? null
            : moment(data.items.AvailabilityDate);
          InitialAvailabilityType.current =
            data.items.AvailablityType && data.items.AvailablityType;
          const locationList = data.items.Regions.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isInvalid: false
          }));
          const languageList = data.items.Languages.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isInvalid: false,
            isCandidate: false
          }));
          const candidatelanguageList = data.items.ResumeCandidateLanguage.map(
            item => ({
              ExperienceLevel: item.ExperienceLevel,
              LanguageId: item.ResumeCandidateLanguageId,
              LanguageValue: item.ResumeCandidateLanguageValue,
              uniqueId: uuidv1(),
              isInvalid: false,
              isCandidate: true
            })
          );
          languageList.push(...candidatelanguageList);
          const rolesList = data.items.Profiles.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isInvalid: false,
            isCandidate: false,
            isActive: false
          }));
          const candidateRolesList = data.items.ResumeCandidateProfiles.map(
            item => ({
              ExperienceLevel: item.ExperienceLevel,
              ProfileId: item.ResumeCandidateProfileId,
              ProfileValue: item.ResumeCandidateProfileName,
              uniqueId: uuidv1(),
              isInvalid: false,
              isCandidate: true,
              isActive: false
            })
          );

          rolesList.push(...candidateRolesList);
          props.onStateChangeAction({
            rolesList
          });
          const firstApprovedRole = rolesList.find(x => !x.isCandidate);
          {
            firstApprovedRole &&
              handleRoleSuggestions(
                firstApprovedRole.ProfileId,
                "Profile",
                firstApprovedRole.ProfileValue,
                firstApprovedRole.uniqueId
              );
          }
          const skillsList = data.items.Skills.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isInvalid: false,
            isCandidate: false,
            isActive: false
          }));
          const candidateSkillsList = data.items.ResumeCandidateSkills.map(
            item => ({
              ExperienceLevel: item.ExperienceLevel,
              SkillId: item.ResumeCandidateSkillId,
              SkillValue: item.ResumeCandidateSkillName,
              uniqueId: uuidv1(),
              isInvalid: false,
              isCandidate: true,
              isActive: false
            })
          );
          skillsList.push(...candidateSkillsList);

          const certificationsList = data.items.Certifications.map(item => ({
            ...item,
            CertificationDate: moment(
              item.CertificationDate,
              "YYYY-MM-DDTHH:mm:ss"
            ).format("MM/DD/YYYY"),
            uniqueId: uuidv1(),
            isCandidate: false,
            isInvalid: false,
            isActive: false
          }));
          const candidateCertificationList = data.items.ResumeCandidateCertifications.map(
            item => ({
              CertificationDate: moment(
                item.CertificationDate,
                "YYYY-MM-DDTHH:mm:ss"
              ).format("MM/DD/YYYY"),
              CertificationId: item.ResumeCandidateCertificationId,
              CertificationValue: item.ResumeCandidateCertificationValue,
              uniqueId: uuidv1(),
              isInvalid: false,
              isCandidate: true,
              isActive: false
            })
          );
          certificationsList.push(...candidateCertificationList);

          const educationsList = data.items.Educations.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isInvalid: false
          }));
          const candidateEducationList = data.items.ResumeCandidateEducations.map(
            item => ({
              EducationYear: item.EducationYear,
              EducationId: item.ResumeCandidateEducationId,
              EducationValue: item.ResumeCandidateEducationValue,
              EducationTypeId: item.EducationTypeId,
              uniqueId: uuidv1(),
              isInvalid: false,
              isCandidate: true
            })
          );
          educationsList.push(...candidateEducationList);

          const keywordsList = data.items.Keywords.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isCandidate: false,
            isInvalid: false
          }));
          const candidateKeywordList = data.items.ResumeCandidateKeywords.map(
            item => ({
              KeywordId: item.ResumeCandidateKeywordId,
              KeywordValue: item.ResumeCandidateKeywordValue,
              uniqueId: uuidv1(),
              isInvalid: false,
              isCandidate: true
            })
          );
          keywordsList.push(...candidateKeywordList);

          const industriesList = data.items.Industries.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isInvalid: false,
            isCandidate: false
          }));
          const candidateIndustriesList = data.items.ResumeCandidateIndustries.map(
            item => ({
              ExperienceLevel: item.ExperienceLevel,
              IndustryId: item.ResumeCandidateIndustryId,
              IndustryValue: item.ResumeCandidateIndustryValue,
              uniqueId: uuidv1(),
              isInvalid: false,
              isCandidate: true
            })
          );
          industriesList.push(...candidateIndustriesList);
          const otherAchivenmentsList = data.items.ResumeOtherAchivenments.map(
            item => ({
              ...item,
              uniqueId: uuidv1(),
              isInvalid: false
            })
          );
          const experiencesList = data.items.ResumeExperience.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isWorking: isNullOrUndefined(item.EndDate) ? true : false,
            StartDate: moment(item.StartDate, "YYYY-MM-DDTHH:mm:ssZ").format(
              "MM/DD/YYYY"
            ),
            EndDate: !isNullOrUndefined(item.EndDate)
              ? moment(item.EndDate, "YYYY-MM-DDTHH:mm:ssZ").format(
                  "MM/DD/YYYY"
                )
              : moment(new Date(), "MM/DD/YYYY"),
            isInvalid: false,
            isRoleInvalid: false,
            isIndustryInvalid: false
          }));

          setState(st => ({
            ...st,
            isResumeAvailable:
              data.items.AvailablityType == 1 || data.items.AvailablityType == 2
          }));
          setState(st => ({
            ...st,
            isMonthNotice: data.items.AvailablityType == 2
          }));
          props.onStateChangeAction({
            IsAvailable: data.items.AvailablityType == 1,
            AvailablityType: data.items.AvailablityType,
            busyUntil: moment(data.items.AvailabilityDate),
            locationList,
            languageList,
            keywordsList,
            otherAchivenmentsList,
            skillsList,
            certificationsList,
            educationsList,
            industriesList,
            experiencesList
          });
          initialLocations.current = locationList;
          intialRoles.current = rolesList;
          intialOtherAcheivemnts.current = otherAchivenmentsList;
          intialExperiences.current = experiencesList;
          intialEducations.current = educationsList;
          intialCertifications.current = certificationsList;
          intialSkills.current = skillsList;
          intialLanguages.current = languageList;
          intialKeywords.current = keywordsList;
          intialIndustries.current = industriesList;
        }
        props.onStateChangeAction({ isLoading: false });
      })
      .catch(() => {});
  };

  const handleLocationChange = (option, uniqueId) => {
    if (
      !isNullOrUndefined(option) &&
      props.locationList.findIndex(x => x.CountryName === option.label) > -1
    ) {
      let duplicateMessage =
        props.labels.RESUME_EDIT_DUPLICATE_VALIDATION_MESSAGE;
      duplicateMessage = duplicateMessage.replace("@item@", option.label);
      const info = {
        message: duplicateMessage,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }
    const locationList = props.locationList.map(item => ({
      ...item,
      CountryId:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.CountryId,
      isInvalid:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isInvalid
          : item.uniqueId === uniqueId
          ? false
          : item.isInvalid,
      CountryName:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? ""
          : item.uniqueId === uniqueId
          ? option.label
          : item.CountryName
    }));
    props.onStateChangeAction({
      locationList
    });
  };
  const handleLocationDelete = deletedItem => {
    const locationList = props.locationList.filter(
      item => deletedItem.uniqueId !== item.uniqueId
    );
    props.onStateChangeAction({ locationList }, resumeChange);
  };
  const handleLocationAdd = () => {
    const { locationList } = props;
    const LocationNewItem = {
      CountryId: 0,
      uniqueId: uuidv1(),
      CountryName: ""
    };
    props.onStateChangeAction(
      {
        locationList: [LocationNewItem, ...locationList]
      },
      resumeChange
    );
  };
  const handleLocationBlur = (value, id) => {
    if (value === 0) {
      const locationList = props.locationList.map(item => ({
        ...item,
        isInvalid: item.uniqueId === id ? true : item.isInvalid
      }));
      props.onStateChangeAction({
        locationList
      });
    }
  };

  const handleLanguageNameChange = (option, uniqueId) => {
    if (
      !isNullOrUndefined(option) &&
      props.languageList.findIndex(x => x.LanguageValue === option.label) > -1
    ) {
      let duplicateMessage =
        props.labels.RESUME_EDIT_DUPLICATE_VALIDATION_MESSAGE;
      duplicateMessage = duplicateMessage.replace("@item@", option.label);
      const info = {
        message: duplicateMessage,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }
    const languageList = props.languageList.map(item => ({
      ...item,
      LanguageId:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.LanguageId,
      LanguageValue:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? ""
          : item.uniqueId === uniqueId
          ? option.label
          : item.LanguageValue,
      isInvalid:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isInvalid
          : item.uniqueId === uniqueId
          ? false
          : item.isInvalid,
      isCandidate:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isCandidate
          : item.uniqueId === uniqueId
          ? false
          : item.isCandidate
    }));
    props.onStateChangeAction({
      languageList
    });
  };
  const handleLanguageLevelChange = (option, uniqueId) => {
    const languageList = props.languageList.map(item => ({
      ...item,
      ExperienceLevel:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.ExperienceLevel
    }));
    props.onStateChangeAction({
      languageList
    });
  };
  const handleLanguageDelete = deletedItem => {
    const languageList = props.languageList.filter(
      item => deletedItem.uniqueId !== item.uniqueId
    );
    props.onStateChangeAction({ languageList }, resumeChange);
  };
  const handleLanguageAdd = () => {
    const { languageList } = props;
    const languageItem = {
      LanguageValue: "",
      ExperienceLevel: 3,
      LanguageId: 0,
      ResumeLanguageId: 0,
      uniqueId: uuidv1(),
      isInvalid: false,
      isCandidate: false
    };
    props.onStateChangeAction(
      {
        languageList: [languageItem, ...languageList]
      },
      resumeChange
    );
  };
  const handleLanguageBlur = (itemId, uniqueId, option) => {
    const { value } = option.target;
    if (
      option.target.value !== "" &&
      props.languageList.findIndex(x => x.LanguageValue === value) > -1
    ) {
      let duplicateMessage =
        props.labels.RESUME_EDIT_DUPLICATE_VALIDATION_MESSAGE;
      duplicateMessage = duplicateMessage.replace("@item@", option.label);
      const info = {
        message: duplicateMessage,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }
    if (
      (value !== "" && itemId === 0) ||
      (value !== "" &&
        props.languageList.find(
          x =>
            x.uniqueId === uniqueId &&
            x.LanguageId === itemId &&
            x.LanguageValue !== value
        ))
    ) {
      props.onStateChangeAction({
        currentCandidate: {
          Type: "Language",
          ItemId: itemId,
          UniqueId: uniqueId,
          ItemValue: value
        }
      });
      const { RESUME_EDIT_CANDIDATE_PROMPT_MESSAGE } = props.labels;
      var content_holder = stripHtml(RESUME_EDIT_CANDIDATE_PROMPT_MESSAGE);
      props.onStateChangeAction({
        dialogMessage: isNullOrUndefined(content_holder)
          ? "Want to add new role"
          : content_holder
      });
    } else if (value === "" && itemId === 0) {
      const languageList = props.languageList.map(item => ({
        ...item,
        isInvalid:
          item.uniqueId === uniqueId && isNullOrUndefined(option)
            ? 0
            : item.uniqueId === uniqueId
            ? true
            : item.isInvalid
      }));
      props.onStateChangeAction({
        languageList
      });
    }
  };

  const handleRoleNameChange = (option, uniqueId) => {
    if (
      !isNullOrUndefined(option) &&
      props.rolesList.findIndex(x => x.ProfileValue === option.label) > -1
    ) {
      let duplicateMessage =
        props.labels.RESUME_EDIT_DUPLICATE_VALIDATION_MESSAGE;
      duplicateMessage = duplicateMessage.replace("@item@", option.label);
      const info = {
        message: duplicateMessage,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }
    const rolesList = props.rolesList.map(item => ({
      ...item,
      ProfileId:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.ProfileId,
      ProfileValue:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? ""
          : item.uniqueId === uniqueId
          ? option.label
          : item.ProfileValue,
      isInvalid:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isInvalid
          : item.uniqueId === uniqueId
          ? false
          : item.isInvalid,
      isCandidate:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isCandidate
          : item.uniqueId === uniqueId
          ? false
          : item.isCandidate
    }));
    props.onStateChangeAction({
      rolesList
    });
  };
  const handleRoleLevelChange = (option, uniqueId) => {
    const rolesList = props.rolesList.map(item => ({
      ...item,
      ExperienceLevel:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.ExperienceLevel
    }));
    props.onStateChangeAction({
      rolesList
    });
  };
  const handleRoleDelete = deletedItem => {
    const rolesList = props.rolesList.filter(
      item => deletedItem.uniqueId !== item.uniqueId
    );
    props.onStateChangeAction({ rolesList }, resumeChange);
  };
  const handleRoleAdd = () => {
    const { rolesList } = props;
    const roleItem = {
      ProfileValue: "",
      ExperienceLevel: 3,
      ProfileId: 0,
      ResumeProfileId: 0,
      uniqueId: uuidv1(),
      isInvalid: false,
      isCandidate: false
    };
    props.onStateChangeAction(
      {
        rolesList: [roleItem, ...rolesList]
      },
      resumeChange
    );
  };
  const stripHtml = html => {
    var temporalDivElement = document.createElement("div");
    temporalDivElement.innerHTML = html;
    return temporalDivElement.textContent || temporalDivElement.innerText || "";
  };

  const handleRoleBlur = (itemId, uniqueId, option) => {
    const { value } = option.target;
    if (
      option.target.value !== "" &&
      props.rolesList.findIndex(x => x.ProfileValue === value) > -1
    ) {
      let duplicateMessage =
        props.labels.RESUME_EDIT_DUPLICATE_VALIDATION_MESSAGE;
      duplicateMessage = duplicateMessage.replace("@item@", option.label);
      const info = {
        message: duplicateMessage,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }
    if (
      (value !== "" && itemId === 0) ||
      (value !== "" &&
        props.rolesList.find(
          x =>
            x.uniqueId === uniqueId &&
            x.ProfileId === itemId &&
            x.ProfileValue !== value
        ))
    ) {
      props.onStateChangeAction({
        currentCandidate: {
          Type: "Role",
          ItemId: itemId,
          UniqueId: uniqueId,
          ItemValue: value
        }
      });
      const { RESUME_EDIT_CANDIDATE_PROMPT_MESSAGE } = props.labels;
      var content_holder = stripHtml(RESUME_EDIT_CANDIDATE_PROMPT_MESSAGE);
      props.onStateChangeAction({
        dialogMessage: isNullOrUndefined(content_holder)
          ? "Want to add new role"
          : content_holder
      });
    } else if (value === "" && itemId === 0) {
      const rolesList = props.rolesList.map(item => ({
        ...item,
        isInvalid:
          item.uniqueId === uniqueId && isNullOrUndefined(option)
            ? 0
            : item.uniqueId === uniqueId
            ? true
            : item.isInvalid
      }));
      props.onStateChangeAction({
        rolesList
      });
    }
  };
  const handleRoleSuggestions = (id, type, value, uniqueId) => {
    if (id === props.suggestObject.Id) {
      const rolesList = props.rolesList.map(item => ({
        ...item,
        isActive: item.uniqueId === uniqueId ? false : false
      }));
      props.onStateChangeAction({
        rolesList
      });
      return;
    }
    const suggestObject = {
      Id: id,
      Value: value,
      Type: type
    };
    props.onStateChangeAction({
      suggestObject
    });
    const rolesList = props.rolesList.map(item => ({
      ...item,
      isActive: item.uniqueId === uniqueId ? true : false
    }));
    props.onStateChangeAction({
      rolesList
    });
    const skillsList = props.skillsList.map(item => ({
      ...item,
      isActive: item.isActive ? false : item.isActive
    }));
    props.onStateChangeAction({
      skillsList
    });
    const certificationsList = props.certificationsList.map(item => ({
      ...item,
      isActive: item.isActive ? false : item.isActive
    }));
    props.onStateChangeAction({
      certificationsList
    });
    getRoleSuggestionsApi({ Id: id, type: type }).then(data => {
      if (data.success) {
        var suggestedRoles = data.items.map(item => ({
          ProfileId: item.CorrelatedProfiles.ProfileId,
          ProfileValue: item.CorrelatedProfiles.ProfileValue
        }));
        let uersObj = props.rolesList.reduce(
          (a, c) => Object.assign(a, { [c.ProfileValue]: c.ProfileValue }),
          {}
        );
        suggestedRoles = suggestedRoles.filter(
          v =>
            !uersObj[v.ProfileValue] ||
            uersObj[v.ProfileValue] !== v.ProfileValue
        );
        props.onStateChangeAction({
          suggestedRoles
        });
      }
    });
    getSkillSuggestionsApi({ Id: id, type: type }).then(data => {
      if (data.success) {
        var suggestedSkills = data.items.map(item => ({
          SkillId: item.Skills.SkillId,
          SkillValue: item.Skills.SkillValue
        }));
        let uersObj = props.skillsList.reduce(
          (a, c) => Object.assign(a, { [c.SkillValue]: c.SkillValue }),
          {}
        );
        suggestedSkills = suggestedSkills.filter(
          v => !uersObj[v.SkillValue] || uersObj[v.SkillValue] !== v.SkillValue
        );
        props.onStateChangeAction({
          suggestedSkills
        });
      }
    });
    getCertificationSuggestionsApi({ Id: id, type: type }).then(data => {
      if (data.success) {
        var suggestedCertifications = data.items.map(item => ({
          CertificationId: item.Certifications.CertificationId,
          CertificationValue: item.Certifications.CertificationValue
        }));
        let uersObj = props.certificationsList.reduce(
          (a, c) =>
            Object.assign(a, { [c.CertificationValue]: c.CertificationValue }),
          {}
        );
        suggestedCertifications = suggestedCertifications.filter(
          v =>
            !uersObj[v.CertificationValue] ||
            uersObj[v.CertificationValue] !== v.CertificationValue
        );
        props.onStateChangeAction({
          suggestedCertifications
        });
      }
    });
  };
  const handleSkillSuggestions = (id, type, value, uniqueId) => {
    if (id === props.suggestObject.Id) {
      const skillsList = props.skillsList.map(item => ({
        ...item,
        isActive: item.uniqueId === uniqueId
      }));
      props.onStateChangeAction({
        skillsList
      });
      return;
    }
    const suggestObject = {
      Id: id,
      Value: value,
      Type: type
    };
    setState(st => ({
      ...st,
      suggestedSkillObject: suggestObject
    }));
    props.onStateChangeAction({
      suggestObject
    });
    const skillsList = props.skillsList.map(item => ({
      ...item,
      isActive: item.uniqueId === uniqueId ? true : false
    }));
    props.onStateChangeAction({
      skillsList
    });
    const rolesList = props.rolesList.map(item => ({
      ...item,
      isActive: item.isActive ? false : item.isActive
    }));
    props.onStateChangeAction({
      rolesList
    });
    const certificationsList = props.certificationsList.map(item => ({
      ...item,
      isActive: item.isActive ? false : item.isActive
    }));
    props.onStateChangeAction({
      certificationsList
    });
    getRoleSuggestionsApi({ Id: id, type: type }).then(data => {
      if (data.success) {
        var suggestedRoles = data.items.map(item => ({
          ProfileId: item.Profiles.ProfileId,
          ProfileValue: item.Profiles.ProfileValue
        }));
        let uersObj = props.rolesList.reduce(
          (a, c) => Object.assign(a, { [c.ProfileValue]: c.ProfileValue }),
          {}
        );
        suggestedRoles = suggestedRoles.filter(
          v =>
            !uersObj[v.ProfileValue] ||
            uersObj[v.ProfileValue] !== v.ProfileValue
        );
        props.onStateChangeAction({
          suggestedRoles
        });
      }
    });
    getSkillSuggestionsApi({ Id: id, type: type }).then(data => {
      if (data.success) {
        var suggestedSkills = data.items.map(item => ({
          SkillId: item.CorrelatedSkills.SkillId,
          SkillValue: item.CorrelatedSkills.SkillValue
        }));
        let uersObj = props.skillsList.reduce(
          (a, c) => Object.assign(a, { [c.SkillValue]: c.SkillValue }),
          {}
        );
        suggestedSkills = suggestedSkills.filter(
          v => !uersObj[v.SkillValue] || uersObj[v.SkillValue] !== v.SkillValue
        );
        props.onStateChangeAction({
          suggestedSkills
        });
      }
    });
    props.onStateChangeAction({
      suggestedCertifications: []
    });
  };
  const handleCertificationSuggestions = (id, type, value, uniqueId) => {
    if (id === props.suggestObject.Id) {
      const certificationsList = props.certificationsList.map(item => ({
        ...item,
        isActive: item.uniqueId === uniqueId ? false : false
      }));
      props.onStateChangeAction({
        certificationsList
      });
      return;
    }
    const suggestObject = {
      Id: id,
      Value: value,
      Type: type
    };
    props.onStateChangeAction({
      suggestObject
    });
    const certificationsList = props.certificationsList.map(item => ({
      ...item,
      isActive: item.uniqueId === uniqueId ? true : false
    }));
    props.onStateChangeAction({
      certificationsList
    });
    const rolesList = props.rolesList.map(item => ({
      ...item,
      isActive: item.isActive ? false : item.isActive
    }));
    props.onStateChangeAction({
      rolesList
    });
    const skillsList = props.skillsList.map(item => ({
      ...item,
      isActive: item.isActive ? false : item.isActive
    }));
    props.onStateChangeAction({
      skillsList
    });
    getRoleSuggestionsApi({ Id: id, type: type }).then(data => {
      if (data.success) {
        var suggestedRoles = data.items.map(item => ({
          ProfileId: item.CorrelatedProfiles.ProfileId,
          ProfileValue: item.CorrelatedProfiles.ProfileValue
        }));
        let uersObj = props.rolesList.reduce(
          (a, c) => Object.assign(a, { [c.ProfileValue]: c.ProfileValue }),
          {}
        );
        suggestedRoles = suggestedRoles.filter(
          v =>
            !uersObj[v.ProfileValue] ||
            uersObj[v.ProfileValue] !== v.ProfileValue
        );
        props.onStateChangeAction({
          suggestedRoles
        });
      }
    });
    props.onStateChangeAction({
      suggestedSkills: []
    });
    getCertificationSuggestionsApi({ Id: id, type: type }).then(data => {
      if (data.success) {
        var suggestedCertifications = data.items.map(item => ({
          CertificationId: item.CorrelatedCertifications.CertificationId,
          CertificationValue: item.CorrelatedCertifications.CertificationValue
        }));
        let uersObj = props.certificationsList.reduce(
          (a, c) =>
            Object.assign(a, { [c.CertificationValue]: c.CertificationValue }),
          {}
        );
        suggestedCertifications = suggestedCertifications.filter(
          v =>
            !uersObj[v.CertificationValue] ||
            uersObj[v.CertificationValue] !== v.CertificationValue
        );
        props.onStateChangeAction({
          suggestedCertifications
        });
      }
    });
  };
  const handleSuggestedRoleAdd = (Id, Value) => {
    const { rolesList } = props;
    const roleItem = {
      ProfileValue: Value,
      ExperienceLevel: 3,
      ProfileId: Id,
      uniqueId: uuidv1(),
      isInvalid: false
    };
    props.onStateChangeAction({
      rolesList: [roleItem, ...rolesList]
    });
    const suggestedRoles = props.suggestedRoles.filter(
      item => Id !== item.ProfileId
    );
    props.onStateChangeAction({ suggestedRoles });
  };

  const handleSkillNameChange = (option, uniqueId) => {
    if (
      !isNullOrUndefined(option) &&
      props.skillsList.findIndex(x => x.SkillValue === option.label) > -1
    ) {
      let duplicateMessage =
        props.labels.RESUME_EDIT_DUPLICATE_VALIDATION_MESSAGE;
      duplicateMessage = duplicateMessage.replace("@item@", option.label);
      const info = {
        message: duplicateMessage,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }
    const skillsList = props.skillsList.map(item => ({
      ...item,
      SkillId:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.SkillId,
      SkillValue:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? ""
          : item.uniqueId === uniqueId
          ? option.label
          : item.SkillValue,
      isInvalid:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isInvalid
          : item.uniqueId === uniqueId
          ? false
          : item.isInvalid,
      isCandidate:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? false
          : item.isCandidate
    }));
    props.onStateChangeAction({
      skillsList
    });
  };
  const handleSkillLevelChange = (option, uniqueId) => {
    const skillsList = props.skillsList.map(item => ({
      ...item,
      ExperienceLevel:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.ExperienceLevel
    }));
    props.onStateChangeAction({
      skillsList
    });
  };
  const handleSkillDelete = deletedItem => {
    const skillsList = props.skillsList.filter(
      item => deletedItem.uniqueId !== item.uniqueId
    );
    props.onStateChangeAction({ skillsList }, resumeChange);
  };
  const handleSkillAdd = () => {
    const { skillsList } = props;
    const skillItem = {
      SkillValue: "",
      ExperienceLevel: 3,
      SkillId: 0,
      uniqueId: uuidv1(),
      isInvalid: false,
      isCandidate: false
    };
    props.onStateChangeAction(
      {
        skillsList: [skillItem, ...skillsList]
      },
      resumeChange
    );
  };
  const handleSkillBlur = (itemId, uniqueId, option) => {
    const { value } = option.target;
    if (
      option.target.value !== "" &&
      props.skillsList.findIndex(x => x.SkillValue === value) > -1
    ) {
      let duplicateMessage =
        props.labels.RESUME_EDIT_DUPLICATE_VALIDATION_MESSAGE;
      duplicateMessage = duplicateMessage.replace("@item@", option.label);
      const info = {
        message: duplicateMessage,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }
    if (
      (value !== "" && itemId === 0) ||
      (value !== "" &&
        props.skillsList.find(
          x =>
            x.uniqueId === uniqueId &&
            x.SkillId === itemId &&
            x.SkillValue !== value
        ))
    ) {
      props.onStateChangeAction({
        currentCandidate: {
          Type: "Skill",
          ItemId: itemId,
          UniqueId: uniqueId,
          ItemValue: value
        }
      });
      const { RESUME_EDIT_CANDIDATE_PROMPT_MESSAGE } = props.labels;
      var content_holder = stripHtml(RESUME_EDIT_CANDIDATE_PROMPT_MESSAGE);
      props.onStateChangeAction({
        dialogMessage: isNullOrUndefined(content_holder)
          ? "Want to add new skill"
          : content_holder
      });
    } else if (value === "" && itemId === 0) {
      const skillsList = props.skillsList.map(item => ({
        ...item,
        isInvalid:
          item.uniqueId === uniqueId && isNullOrUndefined(option)
            ? 0
            : item.uniqueId === uniqueId
            ? true
            : item.isInvalid
      }));
      props.onStateChangeAction({
        skillsList
      });
    }
  };
  const handleSuggestedSkillAdd = (Id, Value) => {
    const { skillsList } = props;
    const skillItem = {
      SkillValue: Value,
      ExperienceLevel: 3,
      SkillId: Id,
      uniqueId: uuidv1(),
      isInvalid: false
    };
    props.onStateChangeAction({
      skillsList: [skillItem, ...skillsList]
    });
    const suggestedSkills = props.suggestedSkills.filter(
      item => Id !== item.SkillId
    );
    props.onStateChangeAction({ suggestedSkills });
  };

  const handleKeywordNameChange = (option, uniqueId) => {
    if (
      !isNullOrUndefined(option) &&
      props.keywordsList.findIndex(x => x.KeywordValue === option.label) > -1
    ) {
      let duplicateMessage =
        props.labels.RESUME_EDIT_DUPLICATE_VALIDATION_MESSAGE;
      duplicateMessage = duplicateMessage.replace("@item@", option.label);
      const info = {
        message: duplicateMessage,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }
    const keywordsList = props.keywordsList.map(item => ({
      ...item,
      KeywordId:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.KeywordId,
      KeywordValue:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? ""
          : item.uniqueId === uniqueId
          ? option.label
          : item.KeywordValue,
      isInvalid:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isInvalid
          : item.uniqueId === uniqueId
          ? false
          : item.isInvalid,
      isCandidate:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isCandidate
          : item.uniqueId === uniqueId
          ? false
          : item.isCandidate
    }));
    props.onStateChangeAction({
      keywordsList
    });
  };
  const handleKeywordDelete = deletedItem => {
    const keywordsList = props.keywordsList.filter(
      item => deletedItem.uniqueId !== item.uniqueId
    );
    props.onStateChangeAction({ keywordsList }, resumeChange);
  };
  const handleKeywordAdd = () => {
    const { keywordsList } = props;
    const keyworditem = {
      KeywordId: 0,
      KeywordValue: "",
      uniqueId: uuidv1(),
      isCandidate: false
    };
    props.onStateChangeAction(
      {
        keywordsList: [keyworditem, ...keywordsList]
      },
      resumeChange
    );
  };
  const handleKeywordBlur = (itemId, uniqueId, option) => {
    const { value } = option.target;
    if (
      option.target.value !== "" &&
      props.keywordsList.findIndex(x => x.KeywordValue === value) > -1
    ) {
      let duplicateMessage =
        props.labels.RESUME_EDIT_DUPLICATE_VALIDATION_MESSAGE;
      duplicateMessage = duplicateMessage.replace("@item@", option.label);
      const info = {
        message: duplicateMessage,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }
    if (
      (value !== "" && itemId === 0) ||
      (value !== "" &&
        props.keywordsList.find(
          x =>
            x.uniqueId === uniqueId &&
            x.KeywordId === itemId &&
            x.KeywordValue !== value
        ))
    ) {
      props.onStateChangeAction({
        currentCandidate: {
          Type: "Keyword",
          ItemId: itemId,
          UniqueId: uniqueId,
          ItemValue: value
        }
      });
      const { RESUME_EDIT_CANDIDATE_PROMPT_MESSAGE } = props.labels;
      var content_holder = stripHtml(RESUME_EDIT_CANDIDATE_PROMPT_MESSAGE);
      props.onStateChangeAction({
        dialogMessage: isNullOrUndefined(content_holder)
          ? "Want to add new role"
          : content_holder
      });
    } else if (value === "" && itemId === 0) {
      const keywordsList = props.keywordsList.map(item => ({
        ...item,
        isInvalid:
          item.uniqueId === uniqueId && isNullOrUndefined(option)
            ? 0
            : item.uniqueId === uniqueId
            ? true
            : item.isInvalid
      }));
      props.onStateChangeAction({
        keywordsList
      });
    }
  };

  const handleOtherAcheivementNameChange = (uniqueId, e) => {
    const { value } = e.target;
    const otherAchivenmentsList = props.otherAchivenmentsList.map(item => ({
      ...item,
      Title: item.uniqueId === uniqueId ? value : item.Title,
      isInvalid: item.uniqueId === uniqueId ? !value : item.isInvalid
    }));
    props.onStateChangeAction({
      otherAchivenmentsList
    });
  };
  const handleOtherAcheivementYearChange = (option, uniqueId) => {
    const otherAchivenmentsList = props.otherAchivenmentsList.map(item => ({
      ...item,
      Year:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.Year
    }));
    props.onStateChangeAction({
      otherAchivenmentsList
    });
  };
  const handleOtherAcheivementTypeChange = (option, uniqueId) => {
    const otherAchivenmentsList = props.otherAchivenmentsList.map(item => ({
      ...item,
      AchivenmentId:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.AchivenmentId
    }));
    props.onStateChangeAction({
      otherAchivenmentsList
    });
  };
  const handleOtherAcheivementDescriptionChange = (uniqueId, content) => {
    const otherAchivenmentsList = props.otherAchivenmentsList.map(item => ({
      ...item,
      Description: item.uniqueId === uniqueId ? content : item.Description
    }));
    props.onStateChangeAction({
      otherAchivenmentsList
    });
  };
  const handleOtherAcheivementDelete = deletedItem => {
    const otherAchivenmentsList = props.otherAchivenmentsList.filter(
      item => deletedItem.uniqueId !== item.uniqueId
    );
    props.onStateChangeAction({ otherAchivenmentsList }, resumeChange);
  };

  const handleCertificationNameChange = (option, uniqueId) => {
    if (
      !isNullOrUndefined(option) &&
      props.certificationsList.findIndex(
        x => x.CertificationValue === option.label
      ) > -1
    ) {
      let duplicateMessage =
        props.labels.RESUME_EDIT_DUPLICATE_VALIDATION_MESSAGE;
      duplicateMessage = duplicateMessage.replace("@item@", option.label);
      const info = {
        message: duplicateMessage,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }
    const certificationsList = props.certificationsList.map(item => ({
      ...item,
      CertificationId:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.CertificationId,
      CertificationValue:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? ""
          : item.uniqueId === uniqueId
          ? option.label
          : item.CertificationValue,
      isInvalid:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isInvalid
          : item.uniqueId === uniqueId
          ? false
          : item.isInvalid,
      isCandidate:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isCandidate
          : item.uniqueId === uniqueId
          ? false
          : item.isCandidate
    }));
    props.onStateChangeAction({
      certificationsList
    });
  };
  const handleCertificationLevelChange = (option, uniqueId) => {
    const certificationsList = props.certificationsList.map(item => ({
      ...item,
      CertificationDate:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? moment(item.CertificationDate, "MM/DD/YYYY").year(option.value)
          : item.CertificationDate
    }));
    props.onStateChangeAction({
      certificationsList
    });
  };
  const handleCertificationAdd = () => {
    const { certificationsList } = props;
    const CertificationItem = {
      CertificationValue: "",
      CertificationDate: new Date(),
      CertificationId: 0,
      uniqueId: uuidv1(),
      isCandidate: false,
      isInvalid: false
    };
    props.onStateChangeAction(
      {
        certificationsList: [CertificationItem, ...certificationsList]
      },
      resumeChange
    );
  };
  const handleCertificationDelete = deletedItem => {
    const certificationsList = props.certificationsList.filter(
      item => deletedItem.uniqueId !== item.uniqueId
    );
    props.onStateChangeAction({ certificationsList }, resumeChange);
  };
  const handleCertificationBlur = (itemId, uniqueId, option) => {
    const { value } = option.target;
    if (
      option.target.value !== "" &&
      props.certificationsList.findIndex(x => x.CertificationValue === value) >
        -1
    ) {
      let duplicateMessage =
        props.labels.RESUME_EDIT_DUPLICATE_VALIDATION_MESSAGE;
      duplicateMessage = duplicateMessage.replace("@item@", option.label);
      const info = {
        message: duplicateMessage,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }
    if (
      (value !== "" && itemId === 0) ||
      (value !== "" &&
        props.certificationsList.find(
          x =>
            x.uniqueId === uniqueId &&
            x.CertificationId === itemId &&
            x.CertificationValue !== value
        ))
    ) {
      props.onStateChangeAction({
        currentCandidate: {
          Type: "Certification",
          ItemId: itemId,
          UniqueId: uniqueId,
          ItemValue: value
        }
      });
      const { RESUME_EDIT_CANDIDATE_PROMPT_MESSAGE } = props.labels;
      var content_holder = stripHtml(RESUME_EDIT_CANDIDATE_PROMPT_MESSAGE);
      props.onStateChangeAction({
        dialogMessage: isNullOrUndefined(content_holder)
          ? "Want to add new role"
          : content_holder
      });
    } else if (value === "" && itemId === 0) {
      const certificationsList = props.certificationsList.map(item => ({
        ...item,
        isInvalid:
          item.uniqueId === uniqueId && isNullOrUndefined(option)
            ? 0
            : item.uniqueId === uniqueId
            ? true
            : item.isInvalid
      }));
      props.onStateChangeAction({
        certificationsList
      });
    }
  };
  const handleSuggestedCertificationAdd = (Id, Value) => {
    const { certificationsList } = props;
    const certificationItem = {
      CertificationValue: Value,
      CertificationDate: new Date(),
      CertificationId: Id,
      uniqueId: uuidv1(),
      isInvalid: false
    };
    props.onStateChangeAction({
      certificationsList: [certificationItem, ...certificationsList]
    });
    const suggestedCertifications = props.suggestedCertifications.filter(
      item => Id !== item.CertificationId
    );
    props.onStateChangeAction({ suggestedCertifications });
  };

  const handleEducationNameChange = (option, uniqueId) => {
    if (
      !isNullOrUndefined(option) &&
      props.educationsList.findIndex(x => x.EducationValue === option.label) >
        -1
    ) {
      let duplicateMessage =
        props.labels.RESUME_EDIT_DUPLICATE_VALIDATION_MESSAGE;
      duplicateMessage = duplicateMessage.replace("@item@", option.label);
      const info = {
        message: duplicateMessage,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }
    const educationsList = props.educationsList.map(item => ({
      ...item,
      EducationId:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.EducationId,
      EducationValue:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? ""
          : item.uniqueId === uniqueId
          ? option.label
          : item.EducationValue,
      isInvalid:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isInvalid
          : item.uniqueId === uniqueId
          ? false
          : item.isInvalid,
      isCandidate:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isCandidate
          : item.uniqueId === uniqueId
          ? false
          : item.isCandidate
    }));
    props.onStateChangeAction({
      educationsList
    });
  };
  const handleEducationLevelChange = (option, uniqueId) => {
    const educationsList = props.educationsList.map(item => ({
      ...item,
      EducationTypeId:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.EducationTypeId
    }));
    props.onStateChangeAction({
      educationsList
    });
  };
  const handleEducationYearChange = (option, uniqueId) => {
    const educationsList = props.educationsList.map(item => ({
      ...item,
      EducationYear:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.EducationYear
    }));
    props.onStateChangeAction({
      educationsList
    });
  };
  const handleEducationDelete = deletedItem => {
    const educationsList = props.educationsList.filter(
      item => deletedItem.uniqueId !== item.uniqueId
    );
    props.onStateChangeAction({ educationsList }, resumeChange);
  };
  const handleEducationAdd = () => {
    const { educationsList } = props;
    const EducationItem = {
      EducationValue: "",
      EducationId: 0,
      EducationTypeId: 3,
      uniqueId: uuidv1(),
      isInvalid: false,
      isCandidate: false,
      EducationYear: 2018
    };
    props.onStateChangeAction(
      {
        educationsList: [EducationItem, ...educationsList]
      },
      resumeChange
    );
  };
  const handleEducationBlur = (itemId, uniqueId, option) => {
    const { value } = option.target;
    if (
      option.target.value !== "" &&
      props.educationsList.findIndex(x => x.EducationValue === value) > -1
    ) {
      let duplicateMessage =
        props.labels.RESUME_EDIT_DUPLICATE_VALIDATION_MESSAGE;
      duplicateMessage = duplicateMessage.replace("@item@", option.label);
      const info = {
        message: duplicateMessage,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }
    if (
      (value !== "" && itemId === 0) ||
      (value !== "" &&
        props.educationsList.find(
          x =>
            x.uniqueId === uniqueId &&
            x.EducationId === itemId &&
            x.EducationValue !== value
        ))
    ) {
      props.onStateChangeAction({
        currentCandidate: {
          Type: "Education",
          ItemId: itemId,
          UniqueId: uniqueId,
          ItemValue: value
        }
      });
      const { RESUME_EDIT_CANDIDATE_PROMPT_MESSAGE } = props.labels;
      var content_holder = stripHtml(RESUME_EDIT_CANDIDATE_PROMPT_MESSAGE);
      props.onStateChangeAction({
        dialogMessage: isNullOrUndefined(content_holder)
          ? "Want to add new role"
          : content_holder
      });
    } else if (value === "" && itemId === 0) {
      const educationsList = props.educationsList.map(item => ({
        ...item,
        isInvalid:
          item.uniqueId === uniqueId && isNullOrUndefined(option)
            ? 0
            : item.uniqueId === uniqueId
            ? true
            : item.isInvalid
      }));
      props.onStateChangeAction({
        educationsList
      });
    }
  };

  const handleIndustryNameChange = (option, uniqueId) => {
    if (
      !isNullOrUndefined(option) &&
      props.industriesList.findIndex(x => x.IndustryValue === option.label) > -1
    ) {
      let duplicateMessage =
        props.labels.RESUME_EDIT_DUPLICATE_VALIDATION_MESSAGE;
      duplicateMessage = duplicateMessage.replace("@item@", option.label);
      const info = {
        message: duplicateMessage,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }
    const industriesList = props.industriesList.map(item => ({
      ...item,
      IndustryId:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.IndustryId,
      IndustryValue:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? ""
          : item.uniqueId === uniqueId
          ? option.label
          : item.IndustryValue,
      isInvalid:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isInvalid
          : item.uniqueId === uniqueId
          ? false
          : item.isInvalid,
      isCandidate:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isCandidate
          : item.uniqueId === uniqueId
          ? false
          : item.isCandidate
    }));
    props.onStateChangeAction({
      industriesList
    });
  };
  const handleIndustryLevelChange = (option, uniqueId) => {
    const industriesList = props.industriesList.map(item => ({
      ...item,
      ExperienceLevel:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.ExperienceLevel
    }));
    props.onStateChangeAction({
      industriesList
    });
  };
  const handleIndustryDelete = deletedItem => {
    const industriesList = props.industriesList.filter(
      item => deletedItem.uniqueId !== item.uniqueId
    );
    props.onStateChangeAction({ industriesList }, resumeChange);
  };
  const handleIndustryAdd = () => {
    const { industriesList } = props;
    const IndustryItem = {
      IndustryValue: "",
      ExperienceLevel: 3,
      IndustryId: 0,
      uniqueId: uuidv1(),
      isCandidate: false
    };
    props.onStateChangeAction(
      {
        industriesList: [IndustryItem, ...industriesList]
      },
      resumeChange
    );
  };
  const handleIndustryBlur = (itemId, uniqueId, option) => {
    const { value } = option.target;
    if (
      option.target.value !== "" &&
      props.industriesList.findIndex(x => x.IndustryValue === value) > -1
    ) {
      let duplicateMessage =
        props.labels.RESUME_EDIT_DUPLICATE_VALIDATION_MESSAGE;
      duplicateMessage = duplicateMessage.replace("@item@", option.label);
      const info = {
        message: duplicateMessage,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }
    if (
      (value !== "" && itemId === 0) ||
      (value !== "" &&
        props.industriesList.find(
          x =>
            x.uniqueId === uniqueId &&
            x.IndustryId === itemId &&
            x.IndustryValue !== value
        ))
    ) {
      props.onStateChangeAction({
        currentCandidate: {
          Type: "Industry",
          ItemId: itemId,
          UniqueId: uniqueId,
          ItemValue: value
        }
      });
      const { RESUME_EDIT_CANDIDATE_PROMPT_MESSAGE } = props.labels;
      var content_holder = stripHtml(RESUME_EDIT_CANDIDATE_PROMPT_MESSAGE);
      props.onStateChangeAction({
        dialogMessage: isNullOrUndefined(content_holder)
          ? "Want to add new role"
          : content_holder
      });
    } else if (value === "" && itemId === 0) {
      const industriesList = props.industriesList.map(item => ({
        ...item,
        isInvalid:
          item.uniqueId === uniqueId && isNullOrUndefined(option)
            ? 0
            : item.uniqueId === uniqueId
            ? true
            : item.isInvalid
      }));
      props.onStateChangeAction({
        industriesList
      });
    }
  };

  const handleExperienceCompanyNameChange = (uniqueId, e) => {
    const { value } = e.target;
    const experiencesList = props.experiencesList.map(item => {
      return {
        ...item,
        CompanyWorked: value,
        isInvalid: item.uniqueId === uniqueId ? !value : item.isInvalid
      };
    });
    props.onStateChangeAction({
      experiencesList
    });
  };
  const handleExperienceIndustryNameChange = (option, uniqueId) => {
    const experiencesList = props.experiencesList.map(item => ({
      ...item,
      IndustryId:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.IndustryId,
      IndustryValue:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? ""
          : item.uniqueId === uniqueId
          ? option.label
          : item.IndustryValue,
      isIndustryInvalid:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isIndustryInvalid
          : item.uniqueId === uniqueId
          ? false
          : item.isIndustryInvalid
    }));
    props.onStateChangeAction({
      experiencesList
    });
  };
  const handleExperienceRoleNameChange = (option, uniqueId) => {
    const experiencesList = props.experiencesList.map(item => ({
      ...item,
      ProfileId:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? option.value
          : item.ProfileId,
      ProfileValue:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? ""
          : item.uniqueId === uniqueId
          ? option.label
          : item.ProfileValue,
      isRoleInvalid:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? item.isRoleInvalid
          : item.uniqueId === uniqueId
          ? false
          : item.isRoleInvalid
    }));

    props.onStateChangeAction({
      experiencesList
    });
  };
  const handleExperienceFromYearChange = (option, uniqueId) => {
    if (isNullOrUndefined(option)) {
      return;
    }
    const experiencesList = props.experiencesList.map(item => ({
      ...item,
      StartDate:
        item.uniqueId === uniqueId
          ? moment(item.StartDate, "MM/DD/YYYY").year(option.value)
          : item.StartDate
    }));
    props.onStateChangeAction({
      experiencesList
    });
  };
  const handleExperienceFromMonthChange = (option, uniqueId) => {
    const experiencesList = props.experiencesList.map(item => ({
      ...item,
      StartDate:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? moment(item.StartDate, "MM/DD/YYYY").month(option.value)
          : item.StartDate
    }));
    props.onStateChangeAction({
      experiencesList
    });
  };
  const handleExperienceToYearChange = (option, uniqueId) => {
    if (isNullOrUndefined(option)) {
      return;
    }
    const experiencesList = props.experiencesList.map(item => ({
      ...item,
      EndDate:
        item.uniqueId === uniqueId
          ? moment(item.EndDate, "MM/DD/YYYY").year(option.value)
          : item.EndDate
    }));
    props.onStateChangeAction({
      experiencesList
    });
  };
  const handleExperienceToMonthChange = (option, uniqueId) => {
    const experiencesList = props.experiencesList.map(item => ({
      ...item,
      EndDate:
        item.uniqueId === uniqueId && isNullOrUndefined(option)
          ? 0
          : item.uniqueId === uniqueId
          ? moment(item.EndDate, "MM/DD/YYYY").month(option.value)
          : item.EndDate
    }));
    props.onStateChangeAction({
      experiencesList
    });
  };
  const handleExperienceAdd = experience => {
    const { experiencesList } = props;
    const ExperienceItem = {
      CompanyWorked: "",
      uniqueId: uuidv1(),
      Description: "",
      isInvalid: false,
      isRoleInvalid: false,
      isIndustryInvalid: false,
      ...experience
    };
    props.onStateChangeAction(
      {
        experiencesList: [ExperienceItem, ...experiencesList]
      },
      resumeChange
    );
  };
  const handleExpierenceDelete = deletedItem => {
    const experiencesList = props.experiencesList.filter(
      item => deletedItem.uniqueId !== item.uniqueId
    );
    props.onStateChangeAction({ experiencesList }, resumeChange);
  };
  const handleExperienceDescriptionChange = (uniqueId, content) => {
    const experiencesList = props.experiencesList.map(item => ({
      ...item,
      Description: item.uniqueId === uniqueId ? content : item.Description
    }));
    props.onStateChangeAction({
      experiencesList
    });
  };
  const handleExperienceIndustryBlur = (value, id) => {
    if (value === 0) {
      const experiencesList = props.experiencesList.map(item => ({
        ...item,
        isIndustryInvalid: item.uniqueId === id ? true : item.isInvalid
      }));
      props.onStateChangeAction({
        experiencesList
      });
    }
  };
  const handleExperienceRoleBlur = (value, id) => {
    if (value === 0) {
      const experiencesList = props.experiencesList.map(item => ({
        ...item,
        isRoleInvalid: item.uniqueId === id ? true : item.isInvalid
      }));
      props.onStateChangeAction({
        experiencesList
      });
    }
  };
  const handleIsWorkingInputChange = uniqueId => {
    const experiencesList = props.experiencesList.map(item => ({
      ...item,
      isWorking: item.uniqueId === uniqueId ? !item.isWorking : item.isWorking
    }));
    props.onStateChangeAction({
      experiencesList
    });
  };

  const handleOtherAcheivementAdd = () => {
    const { otherAchivenmentsList } = props;
    const languageItem = {
      AchivenmentId: 2,
      ResumeOtherAchivenmentId: 0,
      uniqueId: uuidv1(),
      Description: "",
      Title: "",
      Year: 2018,
      isInvalid: false
    };
    props.onStateChangeAction(
      {
        otherAchivenmentsList: [languageItem, ...otherAchivenmentsList]
      },
      resumeChange
    );
  };

  const handleResumeSave = () => {
    const {
      IsAvailable,
      AvailablityType,
      busyUntil,
      locationList,
      languageList,
      rolesList,
      keywordsList,
      otherAchivenmentsList,
      skillsList,
      certificationsList,
      educationsList,
      industriesList,
      experiencesList,
      resumes
    } = props;
    let resume = {
      AvailablityType: AvailablityType,
      IsAvailable: IsAvailable,
      Regions: [],
      ResumeCandidateCertifications: [],
      ResumeCandidateEducations: [],
      ResumeCandidateIndustries: [],
      ResumeCandidateKeywords: [],
      ResumeCandidateLanguages: [],
      ResumeCandidateProfiles: [],
      ResumeCandidateSkills: [],
      ResumeCertifications: [],
      ResumeEducations: [],
      ResumeExperiences: [],
      ResumeId: resumes.ResumeId,
      ResumeIndustries: [],
      ResumeKeywords: [],
      ResumeLanguages: [{}],
      ResumeOtherAchivenment: [],
      ResumeProfiles: [],
      ResumeSkills: []
    };
    props.onStateChangeAction({
      isLoading: true
    });
    if (props.AvailablityType === 3) {
      resume = {
        ...resume,
        AvailabilityDate: moment(busyUntil)
      };
    }

    const locationListNew = props.locationList.map(item => ({
      ...item,
      isInvalid: item.CountryId === 0 ? true : item.isInvalid
    }));
    props.onStateChangeAction({
      locationList: locationListNew
    });

    const languageListNew = props.languageList.map(item => ({
      ...item,
      isInvalid:
        item.LanguageId === 0 && !item.isCandidate ? true : item.isInvalid
    }));
    props.onStateChangeAction({
      languageList: languageListNew
    });
    const roleListNew = props.rolesList.map(item => ({
      ...item,
      isInvalid:
        item.ProfileId === 0 && !item.isCandidate ? true : item.isInvalid
    }));
    props.onStateChangeAction({
      rolesList: roleListNew
    });

    const skillListNew = props.skillsList.map(item => ({
      ...item,
      isInvalid: item.SkillId === 0 && !item.isCandidate ? true : item.isInvalid
    }));
    props.onStateChangeAction({
      skillsList: skillListNew
    });

    const certificationListNew = props.certificationsList.map(item => ({
      ...item,
      isInvalid:
        item.CertificationId === 0 && !item.isCandidate ? true : item.isInvalid
    }));
    props.onStateChangeAction({
      certificationsList: certificationListNew
    });
    const educationListNew = props.educationsList.map(item => ({
      ...item,
      isInvalid:
        item.EducationId === 0 && !item.isCandidate ? true : item.isInvalid
    }));
    props.onStateChangeAction({
      educationsList: educationListNew
    });
    const keywordListNew = props.keywordsList.map(item => ({
      ...item,
      isInvalid:
        item.KeywordId === 0 && !item.isCandidate ? true : item.isInvalid
    }));
    props.onStateChangeAction({
      keywordsList: keywordListNew
    });
    const industryListNew = props.industriesList.map(item => ({
      ...item,
      isInvalid:
        item.IndustryId === 0 && !item.isCandidate ? true : item.isInvalid
    }));
    props.onStateChangeAction({
      industriesList: industryListNew
    });
    const experienceListNew = props.experiencesList.map(item => ({
      ...item,
      isInvalid: item.CompanyWorked === "" ? true : item.isInvalid,
      isIndustryInvalid:
        item.IndustryId === 0 && !item.isCandidate
          ? true
          : item.isIndustryInvalid,
      isRoleInvalid:
        item.ProfileId === 0 && !item.isCandidate ? true : item.isRoleInvalid
    }));
    props.onStateChangeAction({
      experiencesList: experienceListNew
    });
    const otherAcheivementListNew = props.otherAchivenmentsList.map(item => ({
      ...item,
      isInvalid: item.Title === "" ? true : item.isInvalid
    }));
    props.onStateChangeAction({
      otherAchivenmentsList: otherAcheivementListNew
    });

    if (
      locationListNew.findIndex(x => x.isInvalid) > -1 ||
      languageListNew.findIndex(x => x.isInvalid) > -1 ||
      roleListNew.findIndex(x => x.isInvalid) > -1 ||
      skillListNew.findIndex(x => x.isInvalid) > -1 ||
      certificationListNew.findIndex(x => x.isInvalid) > -1 ||
      educationListNew.findIndex(x => x.isInvalid) > -1 ||
      industryListNew.findIndex(x => x.isInvalid) > -1 ||
      keywordListNew.findIndex(x => x.isInvalid) > -1 ||
      experienceListNew.findIndex(
        x => x.isRoleInvalid || x.isIndustryInvalid || x.isInvalid
      ) > -1 ||
      otherAcheivementListNew.findIndex(x => x.isInvalid) > -1
    ) {
      const info = {
        message: props.labels.RESUME_EDIT_FIX_VALIDATION_MESSAGE,
        status: "error"
      };
      props.notificationAction(info);
      props.resumeChangeAction({
        isResumeChange: true,
        isLogoutTrigger: false
      });
      props.onStateChangeAction({
        isLoading: false
      });
      return;
    }

    resume.Regions = map(locationList, item => ({
      CountryId: item.CountryId
    }));

    let filteredLanguages = filter(languageList, item => {
      if (!item.isCandidate && item.LanguageId !== 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          LanguageId: item.LanguageId
        };
        return newItem;
      }
    });
    resume.ResumeLanguages = map(filteredLanguages, item => ({
      ExperienceLevel: item.ExperienceLevel,
      LanguageId: item.LanguageId
    }));
    filteredLanguages = filter(languageList, item => {
      if (item.isCandidate || item.LanguageId === 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          LanguageId: item.LanguageId,
          LanguageValue: item.LanguageValue
        };
        return newItem;
      }
    });
    {
      filteredLanguages &&
        (resume.ResumeCandidateLanguages = map(filteredLanguages, item => {
          const newItem = {
            ExperienceLevel: item.ExperienceLevel,
            ResumeCandidateLanguageId: "0",
            ResumeCandidateLanguageValue: item.LanguageValue
          };
          return newItem;
        }));
    }

    let filteredProfiles = filter(rolesList, item => {
      if (!item.isCandidate && item.ProfileId !== 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          ProfileId: item.ProfileId
        };
        return newItem;
      }
    });
    resume.ResumeProfiles = map(filteredProfiles, item => ({
      ExperienceLevel: item.ExperienceLevel,
      ProfileId: item.ProfileId
    }));
    filteredProfiles = filter(rolesList, item => {
      if (item.isCandidate || item.ProfileId === 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          ProfileId: item.ProfileId,
          ProfileValue: item.ProfileValue
        };
        return newItem;
      }
    });
    {
      filteredProfiles &&
        (resume.ResumeCandidateProfiles = map(filteredProfiles, item => {
          const newItem = {
            ExperienceLevel: item.ExperienceLevel,
            ResumeCandidateProfileId: "0",
            ResumeCandidateProfileName: item.ProfileValue
          };
          return newItem;
        }));
    }

    let filteredSkills = filter(skillsList, item => {
      if (!item.isCandidate && item.SkillId !== 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          SkillId: item.SkillId
        };
        return newItem;
      }
    });
    resume.ResumeSkills = map(filteredSkills, item => ({
      ExperienceLevel: item.ExperienceLevel,
      SkillId: item.SkillId
    }));
    filteredSkills = filter(skillsList, item => {
      if (item.isCandidate || item.SkillId === 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          SkillId: item.SkillId,
          SkillValue: item.SkillValue
        };
        return newItem;
      }
    });
    {
      filteredSkills &&
        (resume.ResumeCandidateSkills = map(filteredSkills, item => {
          const newItem = {
            ExperienceLevel: item.ExperienceLevel,
            ResumeCandidateSkillId: "0",
            ResumeCandidateSkillName: item.SkillValue
          };
          return newItem;
        }));
    }

    let filteredIndustries = filter(industriesList, item => {
      if (!item.isCandidate && item.IndustryId !== 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          IndustryId: item.IndustryId
        };
        return newItem;
      }
    });
    resume.ResumeIndustries = map(filteredIndustries, item => ({
      ExperienceLevel: item.ExperienceLevel,
      IndustryId: item.IndustryId
    }));
    filteredIndustries = filter(industriesList, item => {
      if (item.isCandidate || item.IndustryId === 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          IndustryId: item.IndustryId,
          IndustryValue: item.IndustryValue
        };
        return newItem;
      }
    });
    {
      filteredIndustries &&
        (resume.ResumeCandidateIndustries = map(filteredIndustries, item => {
          const newItem = {
            ExperienceLevel: item.ExperienceLevel,
            ResumeCandidateIndustryId: "0",
            ResumeCandidateIndustryValue: item.IndustryValue
          };
          return newItem;
        }));
    }

    let filteredCertifications = filter(certificationsList, item => {
      if (!item.isCandidate && item.IndustryId !== 0) {
        const newItem = {
          CertificationDate: moment(
            item.CertificationDate,
            "MM/DD/YYYY"
          ).format("YYYY-MM-DDTHH:mm:ss"),
          CertificationId: item.CertificationId
        };
        return newItem;
      }
    });
    resume.ResumeCertifications = map(filteredCertifications, item => ({
      CertificationDate: moment(item.CertificationDate, "MM/DD/YYYY").format(
        "YYYY-MM-DDTHH:mm:ss"
      ),
      CertificationId: item.CertificationId
    }));
    filteredCertifications = filter(certificationsList, item => {
      if (item.isCandidate || item.IndustryId === 0) {
        const newItem = {
          CertificationDate: item.CertificationDate,
          CertificationId: item.CertificationId,
          CertificationValue: item.CertificationValue
        };
        return newItem;
      }
    });
    {
      filteredCertifications &&
        (resume.ResumeCandidateCertifications = map(
          filteredCertifications,
          item => {
            const newItem = {
              CertificationDate: item.CertificationDate,
              ResumeCertificationId: "0",
              ResumeCandidateCertificationValue: item.CertificationValue
            };
            return newItem;
          }
        ));
    }

    let filteredEducations = filter(educationsList, item => {
      if (!item.isCandidate && item.EducationId !== 0) {
        const newItem = {
          EducationId: item.EducationId,
          EducationTypeId: item.EducationTypeId,
          EducationYear: Number(item.EducationYear)
        };
        return newItem;
      }
    });
    resume.ResumeEducations = map(filteredEducations, item => ({
      EducationTypeId: item.EducationTypeId,
      EducationYear: Number(item.EducationYear),
      EducationId: item.EducationId
    }));
    filteredEducations = filter(educationsList, item => {
      if (item.isCandidate || item.EducationId === 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          EducationId: item.EducationId,
          EducationValue: item.EducationValue
        };
        return newItem;
      }
    });
    {
      filteredEducations &&
        (resume.ResumeCandidateEducations = map(filteredEducations, item => {
          const newItem = {
            ExperienceLevel: item.ExperienceLevel,
            ResumeCandidateEducationId: "0",
            ResumeCandidateEducationValue: item.EducationValue,
            EducationTypeId: item.EducationTypeId,
            EducationYear: Number(item.EducationYear)
          };
          return newItem;
        }));
    }

    let filteredKeywords = filter(keywordsList, item => {
      if (!item.isCandidate && item.KeywordId !== 0) {
        const newItem = {
          KeywordId: item.KeywordId
        };
        return newItem;
      }
    });
    resume.ResumeKeywords = map(filteredKeywords, item => ({
      KeywordId: item.KeywordId
    }));
    filteredKeywords = filter(keywordsList, item => {
      if (item.isCandidate || item.KeywordId === 0) {
        const newItem = {
          KeywordId: item.KeywordId,
          KeywordValue: item.KeywordValue
        };
        return newItem;
      }
    });
    {
      filteredKeywords &&
        (resume.ResumeCandidateKeywords = map(filteredKeywords, item => {
          const newItem = {
            ResumeCandidateKeywordId: "0",
            ResumeCandidateKeywordValue: item.KeywordValue
          };
          return newItem;
        }));
    }

    resume.ResumeExperiences = map(experiencesList, item => ({
      CompanyWorked: item.CompanyWorked,
      Description: item.Description,
      EndDate: item.isWorking ? null : item.EndDate,
      IndustryId: item.IndustryId,
      ProfileId: item.ProfileId,
      ResumeExperienceId: item.ResumeExperienceId || "0",
      ResumeId: resumes.ResumeId,
      StartDate: item.StartDate
    }));
    resume.ResumeOtherAchivenment = map(otherAchivenmentsList, item => ({
      AchivenmentId: item.AchivenmentId,
      Description: item.Description,
      ResumeId: resume.ResumeId,
      ResumeOtherAchivenmentId: "" + item.ResumeOtherAchivenmentId || "0",
      Title: item.Title,
      Year: Number(item.Year)
    }));
    saveUpdatedResumeApi({ resume }).then(response => {
      if (response.success) {
        if (props.isLogoutTrigger || props.isRoleSwitchTrigger) {
          props.resumeChangeAction({
            isResumeChange: false,
            message: "",
            isLogout: props.isLogoutTrigger,
            isRoleSwitchToggle: props.isRoleSwitchTrigger
          });
          return;
        }
        props.resumeChangeAction({ isResumeChange: false });
        props.onStateChangeAction({
          isLoading: false
        });
        const info = {
          message: props.labels[response.message],
          status: "success",
          className: `${
            response.message == "resumeFirstEditSuccessMessage"
              ? "first-resume-save"
              : "resume-save"
          }`
        };
        props.notificationAction(info);

        if (isLeaving.current) {
          navigate(location.pathname);
          return;
        }
        isSaved.current = true;

        navigate(privateRoutes.resume.path);
        return;
      }
      props.onStateChangeAction({
        isLoading: false
      });
    });
  };

  const handleDateChange = d => {
    const dateLimit = moment(d, "YYYY-MM-DD");
    const now = moment();
    if (!now.isSameOrBefore(dateLimit)) {
      return;
    }

    availabilityUpdate("date", d);
    props.onStateChangeAction({
      busyUntil: d,
      IsAvailable: false,
      AvailablityType: 3
    });
  };

  const resumeChange = () => {
    if (isThereIsAnyUnSavedChange()) {
      props.resumeChangeAction({ isResumeChange: true });
      return;
    }
    props.resumeChangeAction({ isResumeChange: false });
  };

  const { labels, isHelpActive } = props;
  const { isResumeAvailable, isMonthNotice, suggestedSkillObject } = state;
  const {
    IsAvailable,
    dialogMessage,
    isLoading,
    AvailablityType,
    busyUntil,
    languageList,
    rolesList,
    roleDefaultOptions,
    keywordsList,
    otherAchivenmentsList,
    skillsList,
    certificationsList,
    educationsList,
    industriesList,
    experiencesList,
    suggestedRoles,
    suggestObject,
    suggestedCertifications,
    suggestedSkills,
    skillDefaultOptions,
    certificationDefaultOptions,
    educationDefaultOptions,
    keywordDefaultOptions,
    IndustryDefaultOptions,
    locationList,
    resumes,
    notification
  } = props;
  console.log("props", { state });
  return (
    <div className={classes.newResume}>
      {isLoading && <LoadingMask />}
      {notification.message && (
        <Notification
          status={notification.status}
          className={`${notification.className}`}
        >
          {notification.message}
        </Notification>
      )}
      <div className={classes.resumeDetails}>
        <div className={classes.resumeHeader}>
          <h1>{labels?.ipro_resume_edit_title}</h1>
          <div className={classes.desktopFilters}>
            <div className={classes.availableFilter}>
              <p>{labels?.RESUME_EDIT_IS_AVAILABLE_BUTTON_LABEL}</p>
              <Switch
                onChange={() => availabilityCHange()}
                checkedChildren="On"
                unCheckedChildren="Off"
                checked={isResumeAvailable && !isMonthNotice}
              />
            </div>
            <div className={classes.noticeFilter}>
              <p>{labels?.RESUME_EDIT_ONE_MONTH_BUTTON_LABEL}</p>
              <Switch
                onChange={() => monthlyNoticeAvailabilityCHange()}
                checkedChildren="On"
                unCheckedChildren="Off"
                checked={isMonthNotice}
              />
            </div>
            <div className={classes.busyFilter}>
              <p>{labels?.RESUME_EDIT_BUSY_UNTIL_BUTTON_LABEL}</p>
              <DatePicker
                className={classes.inputDateResumeEditMobile}
                onChange={d => handleDateChange(d)}
                defaultValue={moment(busyUntil)}
              />
            </div>
          </div>
          <div className={classes.mobileFilters}>
            <p>{labels?.RESUME_EDIT_IS_AVAILABLE_BUTTON_LABEL}</p>
            <Select
              value={
                isResumeAvailable && !isMonthNotice
                  ? "Now"
                  : isMonthNotice
                  ? "1 Month Notice"
                  : "Busy Until"
              }
              className={classes.filterDrop}
              onChange={handleChange}
            >
              <Option value="Now">Now</Option>
              <Option value="Busy Until">Busy Until</Option>
              <Option value="1 Month Notice">1 Month Notice</Option>
            </Select>
            <div
              className={
                state.busyFilter || (!isResumeAvailable && !isMonthNotice)
                  ? `${classes.visible} mt-2 `
                  : classes.hidden
              }
            >
              <div className="flex justify-end">
                <DatePicker
                  className={`${classes.inputDateResumeEditMobile}`}
                  onChange={d => handleDateChange(d)}
                  defaultValue={moment(busyUntil)}
                />{" "}
              </div>
            </div>
          </div>
        </div>
        <div className={classes.carouselParent}>
          <div className={classes.carousel}>
            {state?.carouselItemData?.map(item => (
              <ResumeItem
                key={item.id}
                labels={labels}
                text={item.text}
                marginRight={item.marginRight}
                Svg={item.svg}
                id={item.id}
                onClick={() => {
                  setState(st => ({
                    ...st,
                    focused: item
                  }));
                }}
                focus={state.focused.text}
              />
            ))}
          </div>
          <div className={classes.mCarousel}>
            <div className="NewResumeMultiCarousel">
              <CustomCarousel>
                {state?.carouselItemData?.map(item => (
                  <ResumeItem
                    key={item.id}
                    text={item.text}
                    marginRight={item.marginRight}
                    Svg={item.svg}
                    id={item.id}
                    onClick={() => {
                      setState(st => ({
                        ...st,
                        focused: item
                      }));
                    }}
                    focus={state.focused.text}
                  />
                )) || []}
              </CustomCarousel>
            </div>
          </div>
          <div className={classes.carouselBorder} />
        </div>

        {state.focused.text === "Skill" && skillDefaultOptions?.length > 0 && (
          <Skill
            {...state.focused}
            isHelpActive={isHelpActive}
            labels={labels}
            skillsList={skillsList}
            notificationAction={props.notificationAction}
            onStateChangeAction={props.onStateChangeAction}
            onSkillNameChange={handleSkillNameChange}
            onSkillLevelChange={handleSkillLevelChange}
            onSkillDelete={handleSkillDelete}
            onSkillAdd={handleSkillAdd}
            onSkillBlur={handleSkillBlur}
            suggestedSkills={suggestedSkills}
            onSkillSuggestionButtonClick={handleSkillSuggestions}
            onSuggesstedSkillAdd={handleSuggestedSkillAdd}
            suggestObject={suggestObject}
            skillDefaultOptions={skillDefaultOptions}
            propValue={"SkillValue"}
            propId={"SkillId"}
            componentName={"Skill"}
            userDetails={resumes}
            getResume={getResume}
          />
        )}
        {state.focused.text === "Roles" && roleDefaultOptions?.length > 0 && (
          <Skill
            {...state.focused}
            isHelpActive={isHelpActive}
            notificationAction={props.notificationAction}
            labels={labels}
            skillsList={rolesList}
            onSkillNameChange={handleRoleNameChange}
            onStateChangeAction={props.onStateChangeAction}
            onSkillLevelChange={handleRoleLevelChange}
            onSkillDelete={handleRoleDelete}
            onSkillAdd={handleRoleAdd}
            onSkillBlur={handleRoleBlur}
            suggestedSkills={suggestedRoles}
            suggestedSkillsSKL={suggestedSkills}
            suggestedCertifications={suggestedCertifications}
            onSkillSuggestionButtonClick={handleRoleSuggestions}
            onSuggesstedSkillAdd={handleSuggestedRoleAdd}
            suggestObject={suggestObject}
            suggestedSkillObject={suggestedSkillObject}
            skillDefaultOptions={roleDefaultOptions}
            propValue={"ProfileValue"}
            propId={"ProfileId"}
            componentName={"Role"}
            userDetails={resumes}
            getResume={getResume}
          />
        )}

        {state.focused.text === "Certification" && (
          <Skill
            {...state.focused}
            isHelpActive={isHelpActive}
            labels={labels}
            skillsList={certificationsList}
            notificationAction={props.notificationAction}
            onSkillNameChange={handleCertificationNameChange}
            onSkillLevelChange={handleCertificationLevelChange}
            onSkillDelete={handleCertificationDelete}
            onSkillAdd={handleCertificationAdd}
            onStateChangeAction={props.onStateChangeAction}
            onSkillBlur={handleCertificationBlur}
            suggestedSkills={suggestedCertifications}
            onSkillSuggestionButtonClick={handleCertificationSuggestions}
            onSuggesstedSkillAdd={handleSuggestedCertificationAdd}
            suggestObject={suggestObject}
            skillDefaultOptions={certificationDefaultOptions}
            propValue={"CertificationValue"}
            propId={"CertificationId"}
            componentName={"Certification"}
            userDetails={resumes}
            getResume={getResume}
          />
        )}

        {state.focused.text === "Industry" && (
          <Skill
            {...state.focused}
            isHelpActive={isHelpActive}
            labels={labels}
            notificationAction={props.notificationAction}
            skillsList={industriesList}
            onSkillNameChange={handleIndustryNameChange}
            onSkillLevelChange={handleIndustryLevelChange}
            onSkillDelete={handleIndustryDelete}
            onSkillAdd={handleIndustryAdd}
            onStateChangeAction={props.onStateChangeAction}
            onSkillBlur={handleIndustryBlur}
            skillDefaultOptions={IndustryDefaultOptions}
            propValue={"IndustryValue"}
            propId={"IndustryId"}
            componentName={"Industry"}
            hideEye={true}
            userDetails={resumes}
            getResume={getResume}
          />
        )}

        {state.focused.text === "Language" && (
          <Skill
            {...state.focused}
            isHelpActive={isHelpActive}
            labels={labels}
            notificationAction={props.notificationAction}
            onSkillNameChange={handleLanguageNameChange}
            onSkillLevelChange={handleLanguageLevelChange}
            onSkillDelete={handleLanguageDelete}
            onSkillAdd={handleLanguageAdd}
            onSkillBlur={handleLanguageBlur}
            onStateChangeAction={props.onStateChangeAction}
            propValue={"LanguageValue"}
            propId={"LanguageId"}
            componentName={"Language"}
            hideEye={true}
            userDetails={resumes}
            getResume={getResume}
            skillsList={languageList}
          />
        )}
        {state.focused.text === "Keyword" && (
          <Skill
            {...state.focused}
            isHelpActive={isHelpActive}
            labels={labels}
            notificationAction={props.notificationAction}
            onStateChangeAction={props.onStateChangeAction}
            propValue={"KeywordValue"}
            propId={"KeywordId"}
            componentName={"Keyword"}
            hideEye={true}
            hideLevel={true}
            userDetails={resumes}
          />
        )}

        {state.focused.text === "Experience" && (
          <Experience
            {...state.focused}
            isHelpActive={isHelpActive}
            labels={labels}
            experiencesList={experiencesList}
            onExpierenceCompanyNameChange={handleExperienceCompanyNameChange}
            onStateChangeAction={props.onStateChangeAction}
            notificationAction={props.notificationAction}
            onExpierenceIndustryNameChange={handleExperienceIndustryNameChange}
            onExpierenceRoleNameChange={handleExperienceRoleNameChange}
            onExpierenceDescriptionChange={handleExperienceDescriptionChange}
            onExpierenceFromMonthChange={handleExperienceFromMonthChange}
            onExpierenceFromYearChange={handleExperienceFromYearChange}
            onExpierenceToMonthChange={handleExperienceToMonthChange}
            onExpierenceToYearChange={handleExperienceToYearChange}
            onExperienceDelete={handleExpierenceDelete}
            onExperienceAdd={handleExperienceAdd}
            onExpierenceIndustryBlur={handleExperienceIndustryBlur}
            onExperienceRoleBlur={handleExperienceRoleBlur}
            industryDefaultOptions={IndustryDefaultOptions}
            roleDefaultOptions={roleDefaultOptions}
            onIsWorkingInputChange={handleIsWorkingInputChange}
            userDetails={resumes}
            getResume={getResume}
          />
        )}

        {state.focused.text === "Location" && (
          <Location
            {...state.focused}
            locationList={locationList}
            onStateChangeAction={props.onStateChangeAction}
            userDetails={resumes}
            notificationAction={props.notificationAction}
            getResume={getResume}
          />
        )}

        {state.focused.text === "Education" && (
          <Education
            {...state.focused}
            educationsList={educationsList}
            educationDefaultOptions={educationDefaultOptions}
            onEducationLevelChange={handleEducationLevelChange}
            labels={labels}
            onStateChangeAction={props.onStateChangeAction}
            notificationAction={props.notificationAction}
            onEducationNameChange={handleEducationNameChange}
            onEducationBlur={handleEducationBlur}
            onEducationYearChange={handleEducationYearChange}
            userDetails={resumes}
            getResume={getResume}
          />
        )}
        {state.focused.text === "Achievements" && (
          <Achievements
            {...state.focused}
            isHelpActive={isHelpActive}
            labels={labels}
            experiencesList={experiencesList}
            onExpierenceCompanyNameChange={handleExperienceCompanyNameChange}
            onStateChangeAction={props.onStateChangeAction}
            notificationAction={props.notificationAction}
            onExpierenceIndustryNameChange={handleExperienceIndustryNameChange}
            onExpierenceRoleNameChange={handleExperienceRoleNameChange}
            onExpierenceDescriptionChange={handleExperienceDescriptionChange}
            onExpierenceFromMonthChange={handleExperienceFromMonthChange}
            onExpierenceFromYearChange={handleExperienceFromYearChange}
            onExpierenceToMonthChange={handleExperienceToMonthChange}
            onExpierenceToYearChange={handleExperienceToYearChange}
            onExperienceDelete={handleExpierenceDelete}
            onExperienceAdd={handleExperienceAdd}
            onExpierenceIndustryBlur={handleExperienceIndustryBlur}
            onExperienceRoleBlur={handleExperienceRoleBlur}
            industryDefaultOptions={IndustryDefaultOptions}
            roleDefaultOptions={roleDefaultOptions}
            onIsWorkingInputChange={handleIsWorkingInputChange}
            userDetails={resumes}
            getResume={getResume}
          />
        )}
      </div>
    </div>
  );
};

const mapStateToProps = ({
  systemLabel,
  navigation,
  resumeEdit,
  notification,
  resume: { message, isLogoutTrigger, isRoleSwitchTrigger }
}) => {
  const { labels } = systemLabel;
  const { isHelpActive } = navigation;
  return {
    labels,
    isHelpActive,
    message,
    isLogoutTrigger,
    isRoleSwitchTrigger,
    notification,
    ...resumeEdit
  };
};
export default connect(mapStateToProps, {
  notificationAction,
  resumeChangeAction,
  onStateChangeAction
})(ResumeEdit);
