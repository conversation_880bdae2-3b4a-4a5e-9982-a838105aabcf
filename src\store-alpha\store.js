import { configureStore } from "@reduxjs/toolkit";
import rootReducer from "../reducers";
const legacyReducerKey = [
  "systemLabel",
  "currency",
  "searcherModal",
  "userInfo",
  "notification",
  "navigation",
  "resume",
  "present",
  "company",
  "newSearch",
  "newSavedSearch",
  "newShortlist",
  "search",
  "savedSearchRed",
  "savedSearch",
  "shortlist",
  "messageNew",
  "messageInbox",
  "messageSent",
  "createNewOpportunity",
  "searcherOpportunitySent",
  "searcherOpportunityDraft",
  "resumeEdit",
  "landing",
  "iproOpportunityAccepted",
  "iproOpportunityDeclined",
  "iproOpportunityNew",
  "iproCollaborationNew",
  "iproCollaborationInActive",
  "iproCollaborationActive",
  "searcherCollaborationPending",
  "searcherCollaborationAccepted",
  "searcherCollaborationInActive",
  "searcherCollaborationDeclined",
  "createCollaboration",
  "feedback",
  "about",
  "setting"
];
export const store = configureStore({
  reducer: rootReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      immutableCheck: { ignoredPaths: legacyReducerKey },
      serializableCheck: { ignoredPaths: legacyReducerKey }
    })
  // .concat(logger)
  //   enhancers: () => [offline(offlineConfig)],
  // Thunk is included by default; devTools are enabled by default
});
