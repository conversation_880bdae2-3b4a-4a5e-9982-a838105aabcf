import { Component } from "react";
import { useSelector, useDispatch } from "react-redux";
import Network from "./Network";
import { notificationAction } from "../../../src/actions/notification";

const InvitationsNetwork = () => {
  const dispatch = useDispatch();
  const labels = useSelector(state => state.systemLabel.labels);

  const {
    iProOpportunityNewDetailBtnAccept,
    iProOpportunityNewDetailBtnDecline
  } = labels || {};

  return (
    <Network
      isInvitationsView={true}
      BtnAccept={iProOpportunityNewDetailBtnAccept}
      BtnDecline={iProOpportunityNewDetailBtnDecline}
    />
  );
};

export default InvitationsNetwork;
