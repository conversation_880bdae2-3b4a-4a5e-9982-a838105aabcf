import { useSelector } from "react-redux";
import { getYear } from "../../../utilities-alpha/helper";
import { Profiles } from "../constant";
import { Space } from "./EducationSection";
import { ShowMoreSection } from "./LanguagesSection";
// Certifications Section
const CertificationsSection = ({ userData, openDrawer }) => {
  const resume = useSelector(state => state.systemLabel.labels?.resume);
  return (
    <ShowMoreSection
      title={resume?.certification}
      items={userData?.items?.Certifications || []}
      openDrawer={openDrawer}
      addEditAction={Profiles.certification}
      itemKeyId="CertificationId"
      viewMore
      itemValueKey="CertificationValue"
      emptyMessage={resume?.certificationEmptyMessage}
      addButtonAction={Profiles.certification}
      renderItem={cert => (
        <div
          key={cert.CertificationId}
          className="flex-1 truncate min-w-full gap-2 justify-between flex border bg-[#F3F1FD] border-[#EAE5FC] rounded-lg px-4 py-2 text-[13px]"
        >
          <span className="truncate">
            <span className="text-[#343333]">
              {cert.CertificationValue || ""}
            </span>
          </span>
          <Space />
          <span className="">
            <span className="text-[#343333]">
              {getYear(cert.CertificationDate) || ""}
            </span>
          </span>
        </div>
      )}
    />
  );
};

export default CertificationsSection;
