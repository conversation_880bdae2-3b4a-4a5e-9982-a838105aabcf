import { Collapse } from "antd";
import clsx from "clsx";
import ArrowDown from "../../assets-alpha/images/svg/arrow-down-1.svg?react";
import { twMerge } from "tailwind-merge";

const CollapseUi = ({ onChange, items, defaultActiveKey, className }) => {
  return (
    <Collapse
      bordered={false}
      className={twMerge(
        `!bg-[transparent]
       [&_.ant-collapse-item]:bg-[var(--light-purple)] [&_.ant-collapse-item]:mb-[8px] [&_.ant-collapse-item]:!rounded-[16px]
       [&_.ant-collapse-content]:!text-[var(--gray-3)]
        [&_.ant-collapse-header,&_.ant-collapse-content-box]:!px-[20px]
         [&_.ant-collapse-item-active_.ant-collapse-header]:!font-[500] [&_.ant-collapse-item-active_.ant-collapse-header]:!text-[var(--dark)]
    `,
        className
      )}
      defaultActiveKey={defaultActiveKey}
      onChange={onChange}
      items={items}
      expandIconPosition="end"
      expandIcon={({ isActive }) => (
        <ArrowDown
          className={clsx("!transition-all", {
            "rotate-180": isActive
          })}
        />
      )}
    />
  );
};

export default CollapseUi;
