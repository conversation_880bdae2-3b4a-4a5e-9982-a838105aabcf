import { useSelector, useDispatch } from "react-redux";
import Network from "./Network";

const ConnectedNetwork = () => {
  const dispatch = useDispatch();
  const labels = useSelector(state => state.systemLabel.labels);

  return (
    <Network
      isConnectedView={true}
      BtnAddOpportunity={labels?.searcher_network_connected_btn1_text}
      BtnAddCollaboration={labels?.searcher_network_connected_btn2_text}
    />
  );
};

export default ConnectedNetwork;
