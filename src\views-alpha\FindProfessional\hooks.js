import { useEffect, useRef, useState } from "react";
import { useDeleteSavedSearch } from "./findProfessionalApi";
import { useQueryClient } from "@tanstack/react-query";
import { ApiUrl } from "../../api-alpha/apiUrls";
import { useDispatch } from "react-redux";
import { notificationAction } from "../../actions-alpha/notification";

export const useOutsideClick = callback => {
  const ref = useRef();
  const [hasFocusInput, setHasFocusInput] = useState(false);

  useEffect(() => {
    const handleClick = event => {
      if (ref.current && !ref.current.contains(event.target)) {
        callback();
      }
    };

    document.addEventListener("click", handleClick, true);

    return () => {
      document.removeEventListener("click", handleClick, true);
    };
  }, [callback]);

  return { ref, hasFocusInput, setHasFocusInput };
};

export const useDeleteSaveSearches = () => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const {
    mutate: deleteSearch,
    isPending: loadingDelete
  } = useDeleteSavedSearch();
  const onDeleteSearch = item => {
    deleteSearch(
      { suffixUrl: `?savedSearchId=${item.SavedSearchId}` },
      {
        onSuccess: data => {
          queryClient.invalidateQueries({
            queryKey: [ApiUrl.ResumeSearches.SavedSearchesBase]
          });
          const info = {
            message: data?.message,
            status: data?.success ? "success" : "info"
          };
          dispatch(notificationAction(info));
        }
      }
    );
  };
  return { onDeleteSearch, loadingDelete };
};
