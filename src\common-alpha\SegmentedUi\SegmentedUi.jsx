import { Segmented } from "antd";
import { twMerge } from "tailwind-merge";

const SegmentedUi = ({ options, onChange, className, value, disabled }) => {
  return (
    <Segmented
      className={twMerge(
        `!m-auto !bg-[var(--light-purple)] !rounded-[99px] 
        [&_.ant-segmented-item-label]:!cursor-pointer [&_.ant-segmented-item-label]:!p-[16px_44px] [&_.ant-segmented-item-label]:!min-h-auto [&_.ant-segmented-item-label]:!leading-[normal] [&_.ant-segmented-item-label]:!text-[var(--dark)] [&_.ant-segmented-item-label]:!font-[500]
        `,
        className
      )}
      disabled={disabled}
      value={value}
      options={options}
      onChange={onChange}
    />
  );
};

export default SegmentedUi;
