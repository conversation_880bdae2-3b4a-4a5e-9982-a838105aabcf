import { useState } from "react";
import CButton from "../../components/CButton/CButton";
import {
  Modal,
  Typography,
  Row,
  Col
} from "antd";
import classes from "./index.module.scss";
import closeIcon from "../../../../assets/images/Icons/close-new.png";

const { Title, Text } = Typography;

const style = {
  fontWeight: 500
};

const LabelValue = ({ title, value }) => {
  return (
    <>
      <Text className="fnt-s-12" style={{ color: "#00000066" }}>
        {title}
      </Text>
      <div>
        <Text className="fnt-s-16 " style={style}>
          {value}
        </Text>
      </div>
    </>
  );
};

function CompanyPopup({ userCompany, labels }) {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const handleCancel = () => {
    setIsModalVisible(false);
  };
  const onFinish = () => {
    setIsModalVisible(false);
  };

  return (
    <>
      <CButton
        onClick={() => setIsModalVisible(true)}
        className="mt-2 fnt-s-12"
        style={{ borderRadius: "25px" }}
        title={"View Workplace"}
      />

      <Modal
        open={isModalVisible}
        // onOk={handleOk}
        onCancel={handleCancel}
        bodyStyle={{ overflowY: "auto", maxHeight: "calc(100vh - 70px)" }}
        footer={null}
        centered
        closable={false}
        className={`new-design-modal ${classes.popUpForm} custom-modal`}
      >
        <div className="text-center header-modal">
          <h5 className={classes.heading}>
            {labels.SEARCHER_NAVIGATION_COMPANY_LABEL}
          </h5>
          <div onClick={() => handleCancel()} className="pointer">
            <img className="close-icon" src={closeIcon} alt="" />
          </div>
        </div>
        <Row gutter={[6, 12]} className="w-full" style={{ padding: "20px" }}>
          <Col span={12}>
            <LabelValue
              title={labels.NAME_LABEL}
              value={userCompany?.CompanyName || labels.NOT_AVAILABLE_LABEL}
            />
          </Col>
          <Col span={12}>
            <LabelValue
              title={labels.ADDRESS_LABEL}
              value={userCompany?.CompanyAddress || labels.NOT_AVAILABLE_LABEL}
            />
          </Col>
          <Col span={12}>
            <LabelValue
              title={labels.INDUSTRY_LABEL}
              value={
                userCompany?.Industries?.IndustryValue ||
                labels.NOT_AVAILABLE_LABEL
              }
            />
          </Col>
          <Col span={12}>
            <LabelValue
              title={labels?.COUNTRY_LABEL}
              value={
                userCompany?.Country?.CountryName || labels.NOT_AVAILABLE_LABEL
              }
            />
          </Col>
          <Col span={12}>
            <LabelValue
              title={labels.PHONE_LABEL}
              value={
                userCompany?.UserPhonenumberValue || labels.NOT_AVAILABLE_LABEL
              }
            />
          </Col>
          <Col span={12}>
            <LabelValue
              title={labels?.WEB_URL_LABEL}
              value={userCompany?.WebUrl || labels.NOT_AVAILABLE_LABEL}
            />
          </Col>
          <Col span={12}>
            <LabelValue
              title={labels?.VAT_LABEL}
              value={userCompany?.Vat || labels.NOT_AVAILABLE_LABEL}
            />
          </Col>
          <Col span={24}>
            <LabelValue title={"Description"} value={userCompany.Detail} />
          </Col>
        </Row>
      </Modal>
    </>
  );
}

export default CompanyPopup;
