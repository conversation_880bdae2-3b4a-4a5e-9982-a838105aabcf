import Dropzone from "react-dropzone";
import "./dragimage.scss";

const DragImage = props => {
  const handleDropAccepted = files => {
    const file = files[0];
    var reader = new FileReader();
    reader.onload = theFile => {
      return e => {
        props.getImgSrc(e.target.result);
      };
    };
    reader.readAsDataURL(file);
  };

  const handleCloseClick = () => {
    props.getImgSrc();
  };

  const { imgSrc, labels } = props;

  return (
    <div className="drag-image-component">
      <div className={`drag-img-box ${imgSrc ? "active" : ""}`}>
        {!imgSrc && <p>{labels.searcherFeedbackNoImage}</p>}
        {imgSrc && (
          <div>
            <button className="closeBtn" onClick={handleCloseClick} />
            <img className="droped-image" src={imgSrc} alt="dropped" />
          </div>
        )}
      </div>
      <Dropzone
        className="feedDrag demo-droppable"
        acceptClassName="drag-accepted"
        rejectClassName="drag-rejected"
        accept="image/*"
        multiple={false}
        onDropAccepted={handleDropAccepted}
      >
        <p>{labels.searcherFeedbackImageFormate}</p>
      </Dropzone>
    </div>
  );
};

export default DragImage;
