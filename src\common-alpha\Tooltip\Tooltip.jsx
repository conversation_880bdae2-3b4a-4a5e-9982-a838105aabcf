import Tippy from "@tippy.js/react";
import { htmlParser } from "../../utilities/helpers";
import { Tooltip as AntTooltip } from "antd";
import "./tooltip.scss";
export const Tooltip = ({ children, content, ...rest }) => {
  return (
    <Tippy
      delay={[500, 0]}
      allowHTML
      arrow
      theme="light"
      content={htmlParser(content)}
      {...rest}
    >
      {children}
    </Tippy>
  );
};

export const HelpTooltip = ({
  title,
  content,
  children,
  placement,
  trigger
}) => (
  <AntTooltip
    title={
      <>
        {title && (
          <div className="font-medium text-[16px] tex-[#343333] leading-[100%] mb-4">
            {title}
          </div>
        )}
        <div className="text-sm text-[#878787] font-normal leading-[21px]">
          {htmlParser(content)}
        </div>
      </>
    }
    showArrow
    placement={placement}
    trigger={trigger}
    rootClassName="tooltip-ant-theme-white"
    // overlayClassName="tooltip-ant-theme-white"
  >
    <span>{children}</span>
  </AntTooltip>
);

Tooltip.defaultProps = {
  arrow: true
};
