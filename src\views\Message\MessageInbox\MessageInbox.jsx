import { Col, Input, Row, Select, Space, Typography } from "antd";
import filter from "lodash/filter";
import first from "lodash/first";
import includes from "lodash/includes";
import isEmpty from "lodash/isEmpty";
import map from "lodash/map";
import toLower from "lodash/toLower";
import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { unreadMessagesAction } from "../../../actions/navigationActions";
import { notificationAction } from "../../../actions/notification";
import ArrowLeft from "../../../assets/images/Icons/arrow-left.png";
import DeleteIcon from "../../../assets/images/Icons/delete-modal.png";
import PageWrapper from "../../../components/PageWrapper/PageWrapper";
import { privateRoutes } from "../../../Routes/routing";
import {
  getCollabStatus,
  getInboxMessagesApi,
  getRequestStatus,
  sendMessage,
  updateMessageApi
} from "../messageApi";
import CreateMessageModal from "./components/createMessageModal";
import MessageDetail from "./components/MessageDetail";
import "./messageinbox.scss";
import { onStateChangeAction } from "./messageInboxAction";

import moment from "moment";
import SearchIcon from "../../../assets/images/secrch-icon.png";
import ConfirmDialog from "../../../common/ConfirmDialog/ConfirmDialog";
import EmptyInfo from "../../../common/EmptyInfo/EmptyInfo";
const { Title, Text } = Typography;

const MessageInbox = props => {
  const [state, setState] = useState({
    selectedmessage: [],
    CollabStatus: "",
    RequestStatus: "",
    open: false,
    windowWidth: window.innerWidth,
    isMobileDevice: false,
    active: ""
  });
  const location = useLocation();

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    labels,
    isHelpActive,
    IsFreelancer,
    messageForm,
    dialogMessage,
    selectedMessage,
    filterMessages,
    isReply,
    isFetching,
    searchKey
  } = useSelector(state => ({
    labels: state.systemLabel.labels,
    isHelpActive: state.navigation.isHelpActive,
    ...state.messageInbox
  }));

  useEffect(() => {
    getAllMessages();
    window.addEventListener("resize", handleResize);
    setState(prevState => ({
      ...prevState,
      active: window.location.hash?.slice(1)
    }));
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const handleResize = () => {
    setState(prevState => ({ ...prevState, windowWidth: window.innerWidth }));
  };

  const getAllMessages = () => {
    dispatch(onStateChangeAction({ isFetching: true }));

    getInboxMessagesApi().then(data => {
      if (data.success) {
        const messageList = map(data.items, item => ({
          ...item,
          userRequest: first(item.UserRequestMessage)
        }));

        dispatch(
          onStateChangeAction({
            messageList,
            filterMessages: messageList,
            isFetching: false
          })
        );
        const spliturl = location.pathname.match(/\d+/g);
        if (spliturl != null) {
          let filterrequestid = data.items.filter(
            a => a.MessageId == spliturl[0]
          );
          handleMessageSelect(filterrequestid[0]);
        }
      }
    });
  };

  const handleContentChange = e => {
    const { value, name } = e.target;
    const data = {
      ...messageForm,
      [name]: value
    };
    dispatch(onStateChangeAction({ messageForm: data }));
  };

  const handleReply = () => {
    dispatch(onStateChangeAction({ isReply: true }));
  };

  const handleVisitRequest = () => {
    const { selectedMessage } = props;
    var reqType = 1;
    var reqId = selectedMessage.RequestId;
    var IsCollaboration = selectedMessage.IsCollaboration;
    if (selectedMessage.IsCollaboration) {
      const {
        CollaborationIdm,
        IsAccepted,
        IsDeclined,
        IsIProClosed,
        IsIProDeleted,
        IsIProRead,
        IsSearcherClosed,
        IsSearcherDeleted,
        IsSearcherRead
      } = state.CollabStatus;

      if (!IsAccepted && !IsDeclined) {
        navigate({
          pathname: privateRoutes.iproNewCollaborations.path,
          search: "",
          hash: "",
          state: { CollaborationId: selectedMessage.CollaborationId }
        });
      } else if (
        IsAccepted &&
        !IsSearcherClosed &&
        !IsIProClosed &&
        !IsSearcherDeleted &&
        !IsIProDeleted
      ) {
        navigate({
          pathname: privateRoutes.iproActiveCollaborations.path,
          search: "",
          hash: "",
          state: { CollaborationId: selectedMessage.CollaborationId }
        });
      } else if (IsSearcherClosed || IsIProClosed) {
        navigate({
          pathname: privateRoutes.iproNewCollaborations.path,
          search: "",
          hash: "",
          state: { CollaborationId: selectedMessage.CollaborationId }
        });
      } else if (IsDeclined) {
        navigate({
          pathname: privateRoutes.IproCollaborationDeclined.path,
          search: "",
          hash: "",
          state: { CollaborationId: selectedMessage.CollaborationId }
        });
      }
    } else {
      const { IsConfirmed, IsDeclined, IsDeleted } = state.RequestStatus;
      if (!IsConfirmed && !IsDeclined && !IsDeleted) {
        navigate({
          pathname: privateRoutes.iproNewOpportunity.path,
          search: "",
          hash: "",
          state: { visitRequestId: reqId }
        });
      } else if (IsConfirmed) {
        navigate({
          pathname: privateRoutes.iproAccpetedOpportunity.path,
          search: "",
          hash: "",
          state: { visitRequestId: reqId }
        });
      } else if (IsDeclined) {
        navigate({
          pathname: privateRoutes.iproDeclinedOpportunity.path,
          search: "",
          hash: "",
          state: { visitRequestId: reqId }
        });
      }
    }
  };

  const handleSubmitMessage = () => {
    const { content, title } = messageForm;
    if (isEmpty(content)) {
      dispatch(
        notificationAction({
          status: "error",
          message: "Please add content to proceed"
        })
      );
      return;
    }

    const message = {
      IsRead: false,
      MessageBody: content,
      MessageId: 0,
      Subject: selectedMessage.Subject,
      UserIds: [selectedMessage.SenderId],
      UserRequestMessageId: 0
    };
    dispatch(onStateChangeAction({ isFetching: true }));
    sendMessage({ message })
      .then(response => {
        dispatch(onStateChangeAction({ messageForm: {} }));
        getAllMessages();
        setState(prevState => ({ ...prevState, open: false }));
        dispatch(
          notificationAction({
            status: "success",
            message: labels.InfoIProCreateMsgAddSuccessfully
          })
        );
      })
      .catch(error => {
        dispatch(onStateChangeAction({ isFetching: false }));
      });
  };

  const handleSearchChange = e => {
    const { value } = e.target;
    const filterMessages = filter(props.messageList, message =>
      includes(toLower(message.Subject), toLower(value))
    );
    dispatch(onStateChangeAction({ filterMessages, searchKey: value }));
  };

  const handleDeleteMessage = message => {
    dispatch(
      onStateChangeAction({
        deletedId: message.UserRequestMessageId
          ? message.UserRequestMessageId
          : null,
        dialogMessage: labels.AlrtDelMsg
      })
    );
  };

  const handleYesClick = () => {
    const { deletedId } = props;
    const message = {
      MessageId: deletedId,
      isDeleteInboxMessage: true
    };
    dispatch(onStateChangeAction({ dialogMessage: "" }));
    updateMessageApi({ message }).then(data => {
      if (data.success) {
        const info = {
          message: labels.InfoIProInboxMsgDeleteSuccessfully,
          status: "success"
        };
        dispatch(notificationAction(info));
        getAllMessages();
        dispatch(
          onStateChangeAction({
            selectedMessage: {},
            deletedId: null
          })
        );
      }
    });
  };

  const handleNoClick = () => {
    dispatch(onStateChangeAction({ dialogMessage: "", deletedId: null }));
  };

  const handleMessageSelect = async selectedMessage => {
    if (state.windowWidth < 767) {
      setState(prevState => ({ ...prevState, isMobileDevice: true }));
    }

    if (selectedMessage.IsCollaboration) {
      const response = await getCollabStatus({
        colId: selectedMessage.CollaborationId
      });

      if (response?.success) {
        setState(prevState => ({ ...prevState, CollabStatus: response.items }));
      }
    } else {
      if (selectedMessage.RequestId) {
        const response = await getRequestStatus({
          reqId: selectedMessage.RequestId
        });

        if (response?.success) {
          setState(prevState => ({
            ...prevState,
            RequestStatus: response.items
          }));
        }
      }
    }

    if (selectedMessage?.IsRead) {
      dispatch(onStateChangeAction({ selectedMessage, isReply: false }));
      return;
    }

    const message = {
      IsRead: true,
      MessageId: selectedMessage.UserRequestMessageId
    };

    const updateMessageResult = await updateMessageApi({ message });

    if (updateMessageResult?.success) {
      const unreadMessages = filter(
        props.messageList,
        message =>
          message.MessageId !== selectedMessage.MessageId && !message.IsRead
      );

      dispatch(unreadMessagesAction({ unreadMessages: unreadMessages.length }));

      const filterMessages = map(props.messageList, message => {
        if (message.MessageId === selectedMessage.MessageId) {
          message.IsRead = true;
        }
        return message;
      });

      dispatch(onStateChangeAction({ filterMessages }));
    }

    dispatch(onStateChangeAction({ selectedMessage, isReply: false }));
  };

  const handleClose = () => {
    setState(prevState => ({ ...prevState, mobileModal: false }));
  };

  const handleReplyModal = () => {
    setState(prevState => ({ ...prevState, open: true }));
  };

  const handleReplyModalClose = () => {
    setState(prevState => ({ ...prevState, open: false }));
  };

  const {
    selectedmessage,
    CollabStatus,
    RequestStatus,
    open,
    windowWidth,
    isMobileDevice,
    active
  } = state;

  const getTabs = () => {
    return [
      {
        id: 1,
        label: "Inbox",
        active: window?.location?.hash == "#/inbox-messages",
        value: "/inbox-messages"
      },
      {
        id: 2,
        label: "Send",
        active: window?.location?.hash == "#/sent-messages",
        value: "/sent-messages"
      }
    ];
  };

  return (
    <PageWrapper className="collaboration">
      {dialogMessage && (
        <ConfirmDialog testId="company-confirm-diloag">
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              testId="company-delete-yes"
              onClick={handleYesClick}
            >
              {labels?.companyDeleteCurtainYESBtnText}
            </ConfirmDialog.Button>
            <ConfirmDialog.Button
              className="dialog-btn"
              testId="company-delete-no"
              onClick={handleNoClick}
            >
              {labels?.companyDeleteCurtainNOBtnText}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      <CreateMessageModal
        open={open}
        handleContentChange={handleContentChange}
        messageForm={messageForm}
        onSubmitMessage={handleSubmitMessage}
        handleReplyModalClose={handleReplyModalClose}
      />

      <div className="h-full new-design-search w-full flex flex-col">
        <div className="tabs-header-col">
          {isMobileDevice ? (
            <div
              className="flex items-center gap-2 cursor-pointer"
              onClick={() => {
                setState(prevState => ({
                  ...prevState,
                  isMobileDevice: false
                }));
              }}
            >
              <div>
                <img style={{ height: "16px" }} src={ArrowLeft} alt="" />
              </div>
              <Title level={5} className="!m-0">
                {" "}
                Messages
              </Title>
            </div>
          ) : (
            <div className="flex gap-4 justify-between items-center">
              <div className="flex gap-4 items-center main-header">
                {windowWidth > 480 && (
                  <button
                    className={`tab-btn-coll w-full ${
                      window?.location?.hash == "#/create-message"
                        ? "active-url"
                        : "inactive-url"
                    }`}
                    onClick={() => {
                      navigate("/create-message");
                    }}
                  >
                    + New Message
                  </button>
                )}
                {windowWidth < 767 ? (
                  <Select
                    className="dropdown-callooration"
                    size="medium"
                    bordered={false}
                    onChange={e => {
                      setState(prevState => ({ ...prevState, active: e }));

                      navigate(`${e}`);
                    }}
                    value={active}
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      option?.props?.label
                        ?.toLowerCase()
                        ?.indexOf(input?.toLowerCase()) >= 0
                    }
                    options={getTabs() || []}
                  />
                ) : (
                  <>
                    {getTabs()?.map(single => (
                      <button
                        className={`tab-btn-coll ${
                          single?.active ? "active-url" : "inactive-url"
                        }`}
                        key={single?.id}
                        style={{
                          background: single?.active ? "#8E81F5" : "",
                          color: single?.active ? "#fff" : "#000"
                        }}
                        onClick={() => {
                          navigate(single?.value);
                        }}
                      >
                        {single?.label}
                      </button>
                    ))}
                  </>
                )}
              </div>
            </div>
          )}
        </div>
        <Row className=" h-full" style={{ overflow: "auto" }}>
          <Col xs={24} md={8} className="collboration-column1">
            {isMobileDevice ? (
              <div
                className=" resume-list h-full"
                style={{ background: "#fff", borderRadius: "12px" }}
              >
                <div className="new-collaboration-detail-component message-detail-new">
                  {isEmpty(selectedMessage) ? (
                    <EmptyInfo>{labels.Message_Sent}</EmptyInfo>
                  ) : (
                    <MessageDetail
                      selectedMessage={selectedMessage}
                      labels={labels}
                      handleVisitRequest={handleVisitRequest}
                      IsFreelancer={IsFreelancer}
                      isReply={isReply}
                      handleOpen={handleReplyModal}
                      deleteMessage={handleDeleteMessage}
                    />
                  )}
                </div>
              </div>
            ) : (
              <div className="h-full flex flex-col">
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: "12px",
                    borderBottom: "1px solid #F3F3F3",
                    gap: "10px"
                  }}
                >
                  <Input
                    placeholder={labels?.searchInputPlaceholderMessage}
                    size="medium"
                    bordered={false}
                    style={{ border: "1px solid #F3F3F3" }}
                    onChange={handleSearchChange}
                  />
                  <div>
                    <img style={{ height: "30px" }} src={SearchIcon} alt="" />
                  </div>
                </div>
                <div
                  style={{
                    overflow: "auto",
                    padding: "12px",
                    flex: 1
                  }}
                >
                  {filterMessages?.length > 0 ? (
                    <Space size={[6, 6]} wrap className="short-list-new">
                      {filterMessages?.map(item => (
                        <div
                          onClick={() => handleMessageSelect(item)}
                          key={item?.UserCompanyId}
                          className={`flex gap-2 justify-between items-center pointer ${
                            item.MessageId === selectedMessage.MessageId
                              ? "background-shortlist short-list-item "
                              : "short-list-item"
                          }`}
                        >
                          <div className="flex  gap-2 w-full justify-content-betwee items-center">
                            <div className="text-clamp" style={{ flex: 1 }}>
                              <label style={{ fontSize: "14px" }}>
                                {item?.SenderName}
                              </label>
                              <p
                                className="text-clamp"
                                style={{ fontSize: "12px", marginBottom: 0 }}
                              >
                                {item?.Subject}
                              </p>
                            </div>
                            {item.IsRead == false && (
                              <div className="">
                                <div className="chip-new-msg">New</div>
                                <p className="time-message">
                                  {moment(item?.DateCreated)?.format("hh:mm A")}
                                </p>
                              </div>
                            )}
                            {item.MessageId === selectedMessage.MessageId && (
                              <div
                                onClick={e => {
                                  e.stopPropagation();
                                  handleDeleteMessage(item);
                                }}
                              >
                                <img
                                  src={DeleteIcon}
                                  alt=""
                                  style={{ height: "20px" }}
                                />
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </Space>
                  ) : (
                    <EmptyInfo>{labels.EMBARK_NO_ITEM_TO_DISPLAY}</EmptyInfo>
                  )}
                </div>
                {windowWidth < 480 && (
                  <div style={{ margin: "auto" }}>
                    <div className="tabs-header-col mt-3">
                      <button
                        className="tab-btn-coll"
                        style={{ background: "#6C63FF", color: "#fff" }}
                        onClick={() => {
                          navigate("/create-message");
                        }}
                      >
                        + New Message
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </Col>
          <Col md={16} xs={0} offset={0.5} className={"h-full"}>
            <div
              className=" resume-list h-full"
              style={{
                marginLeft: "16px",
                background: "#fff",
                borderRadius: "12px"
              }}
            >
              <div className="new-collaboration-detail-component message-detail-new">
                {isEmpty(selectedMessage) ? (
                  <EmptyInfo>{labels.Message_Sent}</EmptyInfo>
                ) : (
                  <MessageDetail
                    selectedMessage={selectedMessage}
                    labels={labels}
                    handleVisitRequest={handleVisitRequest}
                    IsFreelancer={IsFreelancer}
                    isReply={isReply}
                    handleOpen={handleReplyModal}
                    deleteMessage={handleDeleteMessage}
                  />
                )}
              </div>
            </div>
          </Col>
        </Row>
      </div>
    </PageWrapper>
  );
};

export default MessageInbox;
