import { compact, find, map } from "lodash";
import moment from "moment";
import { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Carousel } from "react-responsive-carousel";
import { useLocation, useNavigate } from "react-router-dom";
import { notificationAction } from "../../../actions/notification";
import DatePicker from "../../../common/DatePicker/DatePicker";
import LoadingMask from "../../../common/LoadingMask/LoadingMask";
import {
  getLoginFeaturesApi,
  getSearcherPhillipSocialMediaStatusApi,
  saveParsedResume,
  updateResumeAvailabilityApi,
  uploadLinkedinPDF
} from "../../../components/Phillip/phillipApi";
import { privateRoutes } from "../../../Routes/routing";
import { htmlParser } from "../../../utilities/helpers";
import "./phillip.scss";

const Slide = ({ className, children, testId }) => (
  <div className={`js_slide ${className}`} data-testid={testId}>
    {children}
  </div>
);

const CustomInput = ({ onClick, value }) => (
  <button className="custom-date-picker" onClick={onClick}>
    {value}
  </button>
);

const ResumeValues = [
  {
    id: 0,
    name: "Resume",
    notFound: "iProOnboardingResumeCreated",
    found: "iProOnboardingResumeCreated",
    active: true
  },
  {
    id: 1,
    name: "Keywords",
    notFound: "iProOnboardingKeywordNotFound",
    found: "iProOnboardingKeywordFound"
  },
  {
    id: 2,
    name: "Certifications",
    notFound: "iProOnboardingCertificationNotFound",
    found: "iProOnboardingCertificationFound"
  },
  {
    id: 3,
    name: "Skills",
    notFound: "iProOnboardingSkillNotFound",
    found: "iProOnboardingSkillFound"
  },
  {
    id: 4,
    name: "Profiles",
    notFound: "iProOnboardingRoleNotFound",
    found: "iProOnboardingRoleFound"
  },
  {
    id: 5,
    name: "Educations",
    notFound: "iProOnboardingEducationNotFound",
    found: "iProOnboardingEducationFound"
  },
  {
    id: 6,
    name: "Experiences",
    notFound: "iProOnboardingExperienceNotFound",
    found: "iProOnboardingExperienceFound"
  },
  {
    id: 7,
    name: "Countries",
    notFound: "iProOnboardingCountryNotFound",
    found: "iProOnboardingCountryFound"
  },
  {
    id: 8,
    name: "Languages",
    notFound: "iProOnboardingLanguageNotFound",
    found: "iProOnboardingLanguageFound"
  }
];

const IproPhillip = ({ onCloseModal }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { labels } = useSelector(state => state.systemLabel);
  const user = useSelector(state => state.userInfo.user);

  const [currentSlide, setCurrentSlide] = useState(0);
  const [date, setDate] = useState(moment());
  const [isFacebookConnected, setIsFacebookConnected] = useState(false);
  const [isLinkedInConnected, setIsLinkedInConnected] = useState(false);
  const [resumeValues, setResumeValues] = useState(ResumeValues);
  const [foundResumeValues, setFoundResumeValues] = useState({});
  const [dateValue, setDateValue] = useState("select a date");
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [LoginWithFb, setLoginWithFb] = useState(false);
  const [LoginWithLinkedin, setLoginWithLinkedin] = useState(false);
  const [availabilities, setAvailabilities] = useState([]);
  const [selectedAvailability, setSelectedAvailability] = useState("notice");
  const [otherResumePdfName, setOtherResumePdfName] = useState("");
  const [linkedinPdfName, setLinkedinPdfName] = useState("");
  const [resumeData, setResumeData] = useState(null);

  useEffect(() => {
    if (labels) {
      setAvailabilities([
        {
          id: 1,
          name: "now",
          isActive: false,
          value: labels.iProOnboardingAvailabilityGig
        },
        {
          id: 2,
          name: "notice",
          isActive: false,
          value: labels.iProOnboardingAvailabilityOnNotice
        },
        {
          id: 3,
          name: "date",
          isActive: false,
          value: labels.iProOnboardingAvailabilityUntil
        }
      ]);
    }
  }, [labels]);

  const getLoginFeatures = useCallback(() => {
    getLoginFeaturesApi().then(response => {
      setLoginWithFb(response.items.LoginWithFb);
      setLoginWithLinkedin(response.items.LoginWithLinkedin);
    });
  }, []);

  useEffect(() => {
    const handleTabPress = e => {
      if (e.keyCode === 9) {
        e.preventDefault();
        handleContinueClick();
      }
    };

    window.addEventListener("keydown", handleTabPress);
    getLoginFeatures();

    getSearcherPhillipSocialMediaStatusApi()
      .then(response => {
        if (response.success) {
          setIsFacebookConnected(response.items.FacebookEnabled);
          setIsLinkedInConnected(response.items.LinkedInEnabled);
        }
      })
      .catch(() => {});

    return () => window.removeEventListener("keydown", handleTabPress);
  }, [getLoginFeatures]);

  const handleContinueClick = useCallback(() => {
    setCurrentSlide(prev => prev + 1);
  }, []);

  const handlAvalibalitySelect = useCallback(availability => {
    const { id, name } = availability;
    setAvailabilities(prev =>
      prev.map(item => ({
        ...item,
        isActive: item.id === id
      }))
    );
    setSelectedAvailability(name);
    setIsDatePickerOpen(name === "date");
  }, []);

  const uploadLinkedinPdf = useCallback(
    (e, successName) => {
      const fileData = e.target.files[0];
      if (!fileData) return;

      const fileType = fileData.name.split(".");
      if (fileType[1]?.toLowerCase() !== "pdf") {
        dispatch(
          notificationAction({
            message: "Please select pdf file.",
            status: "error"
          })
        );
        return;
      }

      const reader = new FileReader();
      reader.onload = e => {
        const fileDataInBytes = e.target.result.split(",")[1];
        setIsLoading(true);

        uploadLinkedinPDF(fileDataInBytes)
          .then(res => {
            dispatch(
              notificationAction({
                message: htmlParser(labels[successName]),
                status: "success"
              })
            );

            const newFoundResumeValues = {
              Profiles: res.items.Profiles?.length || 0,
              Skills: res.items.Skills?.length || 0,
              Certifications: res.items.Certifications?.length || 0,
              Keywords: res.items.Keywords?.length || 0,
              Countries: res.items.Countries?.length || 0,
              Languages: res.items.Languages?.length || 0,
              Industries: res.items.Industries?.length || 0
            };

            setResumeValues(prev =>
              prev.map(item => ({
                ...item,
                active:
                  newFoundResumeValues[item.name] > 0 || item.name === "Resume"
              }))
            );

            setFoundResumeValues(newFoundResumeValues);
            setResumeData(res.items);
            setIsLoading(false);

            if (successName === "OtherResumeSuccessMessage") {
              setOtherResumePdfName(fileData.name);
            } else {
              setLinkedinPdfName(fileData.name);
            }

            handleContinueClick();
          })
          .catch(() => {
            dispatch(
              notificationAction({
                message: "Unable to upload file.",
                status: "error"
              })
            );
            setIsLoading(false);
          });
      };
      reader.readAsDataURL(fileData);
    },
    [dispatch, labels, handleContinueClick]
  );

  const handleFinishClick = useCallback(() => {
    setIsLoading(true);
    if (!resumeData) {
      setIsLoading(false);
      return;
    }

    saveParsedResume(resumeData)
      .then(() => {
        const selectedAvailability = find(availabilities, "isActive");
        let data = {};

        if (selectedAvailability) {
          const now = moment();
          switch (selectedAvailability.name) {
            case "notice":
              data = {
                IsAvailable: false,
                AvailabilityDate: now.add(1, "M").format("MM/DD/YYYY"),
                AvailablityType: 2
              };
              break;
            case "date":
              data = {
                IsAvailable: false,
                AvailabilityDate: date.format("MM/DD/YYYY"),
                AvailablityType: 3
              };
              break;
            case "now":
              data = {
                IsAvailable: true,
                AvailabilityDate: now.format("MM/DD/YYYY"),
                AvailablityType: 1
              };
              break;
            default:
              data = {
                IsAvailable: false,
                AvailabilityDate: date.format("MM/DD/YYYY")
              };
          }

          return updateResumeAvailabilityApi(data).then(({ data: resData }) => {
            dispatch(
              notificationAction({
                message: resData.message,
                status: "success"
              })
            );
          });
        }
      })
      .then(() => {
        dispatch(
          notificationAction({
            message: "Resume saved successfully",
            status: "success"
          })
        );
        onCloseModal();
        if (otherResumePdfName || linkedinPdfName) {
          navigate(privateRoutes.resumeBuilder.path);
        }
      })
      .catch(err => console.error("Error:", err))
      .finally(() => setIsLoading(false));
  }, [
    resumeData,
    availabilities,
    date,
    otherResumePdfName,
    linkedinPdfName,
    onCloseModal,
    navigate,
    dispatch
  ]);

  const renderResumeValues = useCallback(
    item => {
      const count = foundResumeValues[item.name] || 0;
      if (count > 0) {
        return labels[item.found].replace("@count", count);
      }
      return labels[item.notFound];
    },
    [foundResumeValues, labels]
  );

  const generateCards = useCallback(() => {
    if (!labels) return [];

    return compact([
      <Slide key={1} className="js_slide_3">
        <div className="slide-heading">
          <b>{htmlParser(labels.iProOnboardingResumeUploadTitle)}</b>
        </div>
        <br />
        <br />
        {htmlParser(labels.iProOnboardingResumeUploadMessage)}
        <br />

        <div className="resume-upload-row">
          <div
            onChange={e => uploadLinkedinPdf(e, "OtherResumeSuccessMessage")}
            className="js_upload_btn"
          >
            {labels.iProOnboardingResumeOtherBtnText}
            <input className="linkedInPdf" type="file" title=" " />
          </div>

          <div className={`resume-name ${otherResumePdfName || "blur"}`}>
            {otherResumePdfName || " No file selected "}
          </div>
        </div>
      </Slide>,
      <Slide key={2} className="js_slide_2">
        {resumeValues.map(item => (
          <div
            key={item.id}
            className={`js_item ${item.active ? "active" : ""}`}
          >
            <span>{renderResumeValues(item)}</span>
          </div>
        ))}
        <br />
        <div className="js_query">
          {labels.iProOnboardingAvailabilityQuestion}
          <br />
          <div className="js_selection">
            {map(availabilities, item => (
              <div
                key={item.id}
                className={`js_item js_sel_item ${item.isActive && "active"}`}
                onClick={() => handlAvalibalitySelect(item)}
                data-testid={`ipro-phillip-avalibility-${item.name}`}
              >
                <span>{htmlParser(item.value)}</span>
                {item.id === 3 && (
                  <DatePicker
                    name="DatePicker"
                    minDate={moment()}
                    todayButton="Today"
                    dateFormat="MM/DD/YYYY"
                    selected={date}
                    value={
                      dateValue
                        ? dateValue
                        : moment(date)
                            .format("L")
                            .toString()
                    }
                    onChange={(date, e) => {
                      e.stopPropagation();
                      setDateValue("");
                      setDate(date);
                      setIsDatePickerOpen(false);
                    }}
                    onBlur={() => {
                      setIsDatePickerOpen(false);
                    }}
                    customInput={<CustomInput />}
                    open={isDatePickerOpen}
                    stop
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </Slide>
    ]);
  }, [
    labels,
    availabilities,
    date,
    isDatePickerOpen,
    otherResumePdfName,
    resumeValues,
    renderResumeValues
  ]);

  return (
    <div
      className="phillip-component ipro-phillip"
      data-testid="ipro-phillip-popup"
    >
      {isLoading && <LoadingMask text="" />}
      <Carousel
        swipeable
        showStatus={false}
        showArrows={false}
        showThumbs={false}
        selectedItem={currentSlide}
        onChange={setCurrentSlide}
      >
        {generateCards()}
      </Carousel>
      <div className="phillip-footer">
        {currentSlide !== 1 && (
          <button
            type="button"
            className="continue-button"
            onClick={handleContinueClick}
            data-testid="ipro-phillip-continue-button"
          >
            {labels?.searcherOnboardingContinue}
          </button>
        )}
        {(otherResumePdfName || linkedinPdfName) && currentSlide === 1 && (
          <button
            type="button"
            className="finish-button"
            onClick={handleFinishClick}
            data-testid="ipro-phillip-finish-button"
          >
            {labels?.searcherOnboardingFinished}
          </button>
        )}
      </div>
    </div>
  );
};

export default IproPhillip;
