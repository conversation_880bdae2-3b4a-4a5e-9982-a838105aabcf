import { useSelector, useDispatch } from "react-redux";
import NewCollaboration from "../common/newCollaboration";
import { notificationAction } from "../../../actions/notification";
import { useLocation } from "react-router-dom";

const NewIproActiveCollaboration = () => {
  const { userId, labels, IsFreelancer, isHelpActive } = useSelector(state => ({
    labels: state.systemLabel.labels,
    userId: state.userInfo.user.UserId,
    IsFreelancer: state.userInfo.user.IsFreelancer,
    isHelpActive: state.navigation.isHelpActive
  }));
  const dispatch = useDispatch();
  const location = useLocation();

  return (
    <NewCollaboration
      IsFreelancer={IsFreelancer}
      isHelpActive={isHelpActive}
      isIproCollaboration
      isIproActiveCollaboration
      notificationAction={notificationAction}
      url={`GetAllCollaboration`}
      // list section system label
      searchInputPlaceholder={labels.IproCollaborationSearchPlaceholder}
      toolTipExpandList={labels.TooltipIproCollaborationListExpand}
      helpTooltipExpandList={labels.HlpTooltipIproCollaborationListExpand}
      toolTipCollapseList={labels.TooltipIproCollaborationListCollapse}
      helpTooltipCollapseList={labels.HlpTooltipIproCollaborationListCollapse}
      listColumnText={labels.IproCollaborationListColumnText}
      // detail section system label
      detailHeadingText={labels.IproCollaborationDetailHeadingText}
      toolTipExpandDetail={labels.TooltipIproCollaborationDetailExpand}
      helpTooltipExpandDetail={labels.HlpTooltipIproCollaborationDetailExpand}
      toolTipCollapseDetail={labels.TooltipIproCollaborationDetailCollapse}
      helpTooltipCollapseDetail={
        labels.HlpTooltipIproCollaborationDetailCollapse
      }
      detailsColumnText={labels.IproCollaborationDetailColumnText}
      // selected user section system label
      userDetailHeadingText={labels.IproCollaborationCompanyDetailHeaderText}
      toolTipExpandUserDetail={
        labels.TooltipIproCollaborationCompanyDetailExpand
      }
      helpTooltipExpandUserDetail={
        labels.HlpTooltipIproCollaborationCompanyDetailExpand
      }
      toolTipCollapseUserDetail={
        labels.TooltipIproCollaborationCompanyDetailCollapse
      }
      helpTooltipCollapseUserDetail={
        labels.HlpTooltipIproCollaborationCompanyDetailCollapse
      }
      userDetailsColumnText={labels.IproCollaborationCompanyDetailColumnText}
      emptyCollaorationInfo={labels?.emptyCollaborationInfo}
      startDateLabel={labels.IproCollaborationStartDateLabel}
      durationLabel={labels.IproCollaborationDurationLabel}
      compensationLabel={labels.IproCollaborationCompensationLabel}
      companyLabel={labels.IproCollaborationCompanyLabel}
      descriptionLabel={labels.IproCollaborationDescriptionLabel}
      acceptedCollaboration={labels.IproCollaborationAcceptedLabel}
      selectInterest={labels.IproCollaborationInterestOrNotLabel}
      // onCollaborationDelete={handleCollaborationDelete}
      companyAddressLabel={labels.IproCollaborationCompanyAddressLabel}
      comapnyIndustryLabel={labels.IproCollaborationCompanyIndustryLabel}
      companyCountryLabel={labels.IproCollaborationCompanyCountryLabel}
      companyPhoneLabel={labels.IproCollaborationCompanyPhoneLabel}
      companyWebUrlLabel={labels.IproCollaborationCompanyWebUrlLabel}
      companyVatLabel={labels.IproCollaborationCompanyVatLabel}
      collaborationAcceptedMsg={labels.InfoIProCollaborationAccepted}
      collaborationDeclinedMsg={labels.InfoIProCollaborationDeclined}
      collaborationDeletedMsg={labels.InfoIProCollaborationDeleted}
      listCollapsedTestId={"listCollapsedTestId1"}
      listCollapsedHelpTestId={"listCollapsedTestId2"}
      listExpandTestId={"listCollapsedTestId3"}
      listExpandHelpTestId={"listCollapsedTestId4"}
      detailCollapsedTestId={"listCollapsedTestId5"}
      detailCollapsedHelpTestId={"listCollapsedTestId6"}
      detailExpandTestId={"listCollapsedTestId7"}
      detailExpandHelpTestId={"listCollapsedTestId8"}
      userDetailCollapsedTestId={"listCollapsedTestId9"}
      userDetailCollapsedHelpTestId={"listCollapsedTestId10"}
      userDetailExpandTestId={"listCollapsedTestId11"}
      userDetailExpandHelpTestId={"listCollapsedTestId12"}
      searchInputTestId="search-input"
      locationProp={location}
      collaborationClosedMsg={labels.InfoSearcherCollaborationClosed}
      likeToRateCollaborationMessage={labels.likeToRateCollaborationMessage}
      collaborationDeleteConfirmation={
        labels.InfoSearcherSentCollaborationDeleteConfirmationMsg
      }
      collaborationCloseConfirmation={
        labels.InfoSearcherSentCollaborationCloseConfirmationMsg
      }
      popupYesText={"Yes"}
      popupNoText={"No"}
    />
  );
};

export default NewIproActiveCollaboration;
