import { Carousel } from "antd";
import { useSelector } from "react-redux";

const Slider = () => {
  const labels = useSelector(state => state.systemLabel.labels);

  const sliderData = [
    {
      id: 1,
      title: labels?.Landing_Slider_Title1,
      data: labels?.Landing_Slider_Description1
    },
    {
      id: 2,
      title: labels?.Landing_Slider_Title2,
      data: labels?.Landing_Slider_Description2
    },
    {
      id: 3,
      title: labels?.Landing_Slider_Title3,
      data: labels?.Landing_Slider_Description3
    },
    {
      id: 4,
      title: labels?.Landing_Slider_Title4,
      data: labels?.Landing_Slider_Description4
    }
  ];

  return (
    <Carousel
      autoplay
      rootClassName="
        [&_.slick-dots]:!mt-12
        [&_.slick-dots>li]:!w-6
        [&_.slick-dots>li>button]:!bg-[#343333]
        [&_.slick-dots>li.slick-active>button]:!bg-[#8E81F5]
      "
    >
      {sliderData.map(item => (
        <div key={item.id}>
          <div className="min-h-[200px] flex flex-col justify-center items-center text-center px-4 pb-16">
            <h1 className="text-2xl font-bold mb-4">{item.title}</h1>
            <p className="text-base text-gray-700 max-w-xl">{item.data}</p>
          </div>
        </div>
      ))}
    </Carousel>
  );
};

export default Slider;
