import React, { useState, useEffect } from "react";
import { connect, useSelector } from "react-redux";
import PageWrapper from "../../components/PageWrapper/PageWrapper";
import { notificationAction } from "../../actions/notification";
import "./snapshot.scss";
import {
  tictellLogin,
  getCurrenciesApi,
  getExpensesCategoriesApi,
  tictellAppLoginApi
} from "../Tictell/tictellApi";
import { privateRoutes } from "../../Routes/routing";
import {
  GetAcceptedCollaborationCount,
  getChildFeatureByName
} from "../Snapshot/snapshotApi";
import { StorageService } from "../../api/storage";
import SnapshotNavigation from "./Navigation/Navigation";
import Reports from "./views/Reports/Reports";
import ConfirmDialog from "../../common/ConfirmDialog/ConfirmDialog";
import Collaborations from "./views/Collaborations/Collaborations";
import Dashboard from "./views/Dashboard/Dashboard";
import Pools from "./views/Pools/Pools";
import { useNavigate, useLocation } from "react-router-dom";

const Snapshot = props => {
  const [userFeatures, setUserFeatures] = useState([]);
  const [token, setToken] = useState("");
  const [expenseCategories, setExpenseCategories] = useState([]);
  const [currencies, setCurrencies] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [userId, setUserId] = useState(-1);
  const [dialogMessage, setDialogMessage] = useState("");
  const navigate = useNavigate();
  const location = useLocation();
  const user =
    useSelector(state => state.userInfo.user) || StorageService.getUser();

  useEffect(() => {
    getChildFeatureByName({ featureName: "snapshot" })
      .then(res => {
        setUserFeatures(res.items);
        const { items } = res;
        const firstUrl = items.find(a => a.Url);
        var queryString = window.location.href;
        queryString = queryString.toLowerCase().split("snapshot")[1];
        if (queryString == "") {
          if (firstUrl) {
            const isApp =
              window.location.hash.toLowerCase().indexOf("apps") > -1;

            navigate((isApp ? "/apps" : "") + "/snapshot/" + firstUrl.Url);
          }
        }
      })
      .catch(err => console.log("Err ", err));

    let Email = user?.UserEmail || user?.userEmail || user?.Email;
    if (!Email) {
      const User = StorageService.getUser();
      if (User) {
        Email = User.Email || User.UserEmail || User.userEmail;
      }
    }

    setUserId(props.User.UserId);
    if (Email && Email !== "undefined") {
      tictellAppLoginApi(Email)
        .then(res => {
          if (res.success) {
            tictellLogin(Email)
              .then(res => {
                if (res.success) {
                  const { access_token } = res.items;
                  getCurrencies(access_token);
                  getAllactivecollaborations(access_token);
                  setToken(access_token);
                }
              })
              .catch(err => console.log(err));
          }
        })
        .catch(Err => console.log("Error ", Err));
    } else {
      console.warn("No valid email found for Snapshot Tictell login");
    }
  }, []);

  useEffect(() => {
    onRouteChanged();
  }, [location]);

  const onRouteChanged = () => {
    const firstUrl = userFeatures.find(a => a.Url);
    var queryString = window.location.href;
    queryString = queryString.toLowerCase().split("snapshot")[1];
    if (queryString == "") {
      if (firstUrl) {
        const isApp = window.location.hash.toLowerCase().indexOf("apps") > -1;

        navigate((isApp ? "/apps" : "") + "/snapshot/" + firstUrl.Url);
      }
    }
  };

  const getAllactivecollaborations = access_token => {
    GetAcceptedCollaborationCount(access_token).then(res => {
      if (res.success) {
        if (res.items.acceptedColl === 0) {
          setDialogMessage(props.labels.Snapshot_Collaboration_Dialog_Msg);
          setIsLoading(true);
        } else {
          setIsLoading(false);
        }
      }
    });
  };

  const getCurrencies = access_token => {
    getCurrenciesApi()
      .then(res => {
        if (res.success) {
          setCurrencies(
            res.items.map(item => ({
              ...item,
              value: item.CurrencyId,
              label: item.Name
            }))
          );
          getExpenseCategories(access_token);
        }
      })
      .catch(err => console.log("Err ", err));
  };

  const getExpenseCategories = access_token => {
    getExpensesCategoriesApi(access_token)
      .then(res => {
        if (res.success) {
          setExpenseCategories(
            res.items.map(item => ({
              ...item,
              value: item.ExpenseCategoryId,
              label: item.Title
            }))
          );
        }
      })
      .catch(err => console.log("Err ", err));
  };

  const handleOkClick = () => {
    navigate(privateRoutes.dashboard.path);
  };

  const { labels, isHelpActive } = props;
  const {
    pendingtimesheet,
    Rejectedtimesheet,
    Rejectedexpense,
    Pendingexpense
  } = {}; //state;
  //check later
  const hashesList = [
    "#/apps/snapshot/",
    "#/snapshot/",
    "#/apps/snapshot",
    "#/snapshot"
  ];
  const currentViewHash =
    window.location.hash.toLowerCase().split("/")[1] == "apps"
      ? window.location.hash.toLocaleLowerCase().split("/")[3]
      : window.location.hash.toLocaleLowerCase().split("/")[2];
  console.log("pool");
  return (
    <PageWrapper className={`snapshot-page`}>
      {isLoading && (
        <div id="loader-wrapper">
          <div className="loader-container">
            <div className="loader">
              <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
                <defs>
                  <filter id="goo">
                    <feGaussianBlur
                      in="SourceGraphic"
                      stdDeviation="6"
                      result="blur"
                    />
                    <feColorMatrix
                      in="blur"
                      mode="matrix"
                      values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                      result="goo"
                    />
                    <feBlend in="SourceGraphic" in2="goo" />
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </div>
      )}
      <SnapshotNavigation
        labels={labels}
        disabled={`${isLoading ? "disabled" : ""}`}
        isHelpActive={isHelpActive}
        UserFeatures={userFeatures}
      />
      {dialogMessage && (
        <ConfirmDialog testId="confirm-diloag">
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              onClick={handleOkClick}
            >
              {"Ok"}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      {!isLoading && (
        <React.Fragment>
          {currentViewHash == "home" &&
            userFeatures.findIndex(
              a => a.Url && a.Url.toLocaleLowerCase().trim() == currentViewHash
            ) > -1 && (
              <Dashboard
                token={token}
                Readyforapproval={pendingtimesheet}
                Rejected={Rejectedtimesheet}
                asOwnerCount={"12"}
                Rejectedexpense={Rejectedexpense}
                Pendingexpense={Pendingexpense}
                UserFeatures={userFeatures}
              />
            )}
          {currentViewHash == "reports" &&
            userFeatures.findIndex(
              a => a.Url && a.Url.toLocaleLowerCase().trim() == currentViewHash
            ) > -1 && (
              <Reports
                token={token}
                Currencies={currencies}
                ExpenseCategories={expenseCategories}
              />
            )}
          {currentViewHash == "collaborations" &&
            userFeatures.findIndex(
              a => a.Url && a.Url.toLocaleLowerCase().trim() == currentViewHash
            ) > -1 && (
              <Collaborations
                token={token}
                Currencies={currencies}
                ExpenseCategories={expenseCategories}
                UserId={userId}
                locationProp={location}
              />
            )}
          {currentViewHash == "pools" &&
            userFeatures.findIndex(
              a => a.Url && a.Url.toLocaleLowerCase().trim() == currentViewHash
            ) > -1 && (
              <Pools
                token={token}
                Currencies={currencies}
                ExpenseCategories={expenseCategories}
                UserId={userId}
              />
            )}
        </React.Fragment>
      )}
    </PageWrapper>
  );
};

const mapStateToProps = ({ systemLabel, navigation, userInfo }) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  const User = user ? user : StorageService.getUser();
  const { isHelpActive } = navigation;
  return { labels, isHelpActive, User };
};

export default connect(mapStateToProps, { notificationAction })(Snapshot);
