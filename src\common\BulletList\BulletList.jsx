import "./bulletlist.scss";

const ListItem = ({
  children,
  onClick,
  className,
  isSelected,
  isActive,
  testId
}) => (
  <li
    data-testid={testId}
    onClick={onClick}
    className={`list-item animated fadeIn ${className} ${
      isSelected ? "selected-item" : ""
    } ${isActive ? "activeItem" : ""}`}
  >
    {children}
  </li>
);

const Close = ({ onClick, testId }) => (
  <button onClick={onClick} className="closeBtn" data-testid={testId} />
);

const BulletList = ({ children, className, testId, isFetching }) => (
  <div className={`bullet-list-wrapper ${className}`}>
    <ul
      className={`bullet-list-ul ${isFetching ? "loading-list-mask" : ""}`}
      data-testid={testId}
    >
      {children}
    </ul>
  </div>
);

BulletList.ListItem = ListItem;
BulletList.Close = Close;

BulletList.defaultProps = {
  className: ""
};
BulletList.ListItem.defaultProps = {
  className: ""
};

export default BulletList;
