
const Experience = props => {
  return (
    <svg
      width="33"
      height="35"
      viewBox="0 0 33 35"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M17.099 1.41101C17.4235 1.48401 17.748 1.54079 18.0643 1.63813C19.7434 2.15726 20.9357 3.21985 21.6414 4.83402C21.6901 4.94758 21.8118 5.07736 21.9253 5.10981C23.1826 5.46671 23.9288 6.41574 23.9369 7.7379C23.9451 8.6626 23.9288 9.57919 23.9451 10.5039C23.9451 10.6499 24.0262 10.8202 24.1316 10.9257C24.8373 11.6314 25.1293 12.475 24.8779 13.4483C24.6183 14.4704 23.9369 15.103 22.9149 15.3464C22.7284 15.3869 22.631 15.4681 22.5742 15.6465C22.3228 16.4252 21.9091 17.1147 21.3413 17.6987C20.6356 18.4287 19.9218 19.1506 19.1918 19.8644C18.7051 20.343 18.1049 20.6107 17.4154 20.6188C16.5394 20.6269 15.6552 20.6188 14.7792 20.6026C14.1222 20.5945 13.5625 20.3106 13.1001 19.8563C12.3539 19.1182 11.6157 18.38 10.8938 17.6257C10.3666 17.0741 9.98536 16.4252 9.75013 15.7033C9.67713 15.4924 9.57979 15.3951 9.35267 15.3383C8.33875 15.0949 7.67362 14.4622 7.42216 13.4483C7.17882 12.4831 7.4465 11.6476 8.16841 10.9581C8.32253 10.8121 8.37931 10.6742 8.37119 10.4714C8.35497 9.32773 8.33064 8.18403 8.67943 7.08088C9.52301 4.37167 11.2913 2.58716 14.0005 1.74358C14.4953 1.58946 15.0225 1.52457 15.5335 1.42723C16.0527 1.41101 16.5718 1.41101 17.099 1.41101ZM10.4639 11.2745C10.4639 12.2722 10.4234 13.2293 10.472 14.1783C10.5288 15.2977 10.9993 16.2549 11.778 17.0579C12.4431 17.7392 13.1245 18.4125 13.7977 19.0776C14.1303 19.4102 14.5358 19.5805 15.0144 19.5724C15.7607 19.5643 16.4988 19.5643 17.2451 19.5724C17.7723 19.5805 18.2022 19.394 18.5672 19.0208C19.1918 18.3882 19.8245 17.7636 20.4572 17.1309C20.952 16.6361 21.3332 16.0683 21.5765 15.4113C22.0794 14.0486 21.7955 12.6372 21.8848 11.2339C20.4166 11.0068 19.3621 10.2443 18.6564 9.00328C18.6159 9.01139 18.5915 9.0195 18.5753 9.02761C18.5429 9.05194 18.5104 9.07628 18.478 9.10061C16.6124 10.6418 14.4547 11.315 12.0538 11.2745C11.5265 11.2664 10.9993 11.2745 10.4639 11.2745ZM9.4419 10.2281C10.4883 10.2281 11.5184 10.2362 12.5404 10.2281C14.7549 10.2038 16.661 9.42507 18.2671 7.90013C18.4536 7.72168 18.6078 7.50267 18.843 7.21877C19.7109 9.00328 20.7167 10.4309 22.8825 10.2362C22.8906 10.2119 22.8987 10.1794 22.8987 10.1551C22.8987 9.35207 22.9068 8.54093 22.8987 7.7379C22.8906 6.7402 22.2741 6.11562 21.2683 6.09129C21.0331 6.08317 20.9357 6.02639 20.8627 5.79116C20.4409 4.38789 19.573 3.37397 18.2103 2.80617C17.2207 2.39249 16.1906 2.4006 15.1523 2.55472C11.7455 3.03329 8.99577 6.10751 9.4419 10.2281ZM9.40945 14.2513C9.40945 13.7809 9.40945 13.3104 9.40945 12.8481C9.40945 12.3695 9.40945 11.899 9.40945 11.4124C8.8011 11.5746 8.37119 12.1748 8.36308 12.8156C8.36308 13.4727 8.8011 14.0891 9.40945 14.2513ZM22.8987 14.2676C23.58 14.0242 24.0018 13.3753 23.9288 12.7021C23.8639 12.0694 23.4178 11.5097 22.8987 11.4367C22.8987 12.3695 22.8987 13.3104 22.8987 14.2676Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.4"
      />
      <path
        d="M11.5104 20.1078C11.843 20.1078 12.1594 20.1078 12.5081 20.1078C12.4433 21.5111 13.0354 22.7683 13.2625 24.0986C13.295 24.1229 13.3274 24.1473 13.368 24.1716C13.4085 24.0905 13.441 24.0013 13.4977 23.9282C14.0655 23.2388 14.6414 22.5574 15.2011 21.8761C15.3309 21.722 15.4607 21.6327 15.6716 21.649C16.0772 21.6733 16.5395 21.5435 16.8802 21.7057C17.2209 21.8598 17.4318 22.2898 17.6913 22.598C18.0645 23.036 18.4295 23.4821 18.7945 23.9282C18.8513 24.0013 18.9162 24.058 19.0054 24.1554C19.1838 23.4578 19.3623 22.8089 19.5083 22.1437C19.6543 21.4867 19.7597 20.8216 19.8895 20.1159C20.1248 20.1159 20.4492 20.1159 20.7656 20.1159C20.7818 20.1321 20.8061 20.1565 20.798 20.1646C20.7088 20.6756 20.9846 20.8378 21.4307 20.9757C22.6636 21.3488 23.8722 21.7787 25.0889 22.1843C27.2222 22.9062 28.6174 24.3419 29.0554 26.5644C29.5015 28.8437 29.8422 31.1393 30.2235 33.4348C30.2316 33.4753 30.2235 33.5159 30.2235 33.5808C20.8386 33.5808 11.4618 33.5808 2.04443 33.5808C2.13366 33.013 2.22288 32.4614 2.32022 31.9099C2.61223 30.1902 2.8718 28.4625 3.19625 26.7429C3.62616 24.4555 4.98887 22.9468 7.18706 22.2005C8.493 21.7544 9.79894 21.3164 11.113 20.8946C11.3969 20.8054 11.5348 20.6756 11.4861 20.3755C11.478 20.2862 11.5023 20.2051 11.5104 20.1078ZM11.6321 21.795C10.1072 22.3141 8.61467 22.7683 7.15462 23.3199C5.59723 23.9039 4.61575 25.0638 4.26696 26.7023C4.03984 27.773 3.88572 28.8681 3.69916 29.9469C3.55315 30.7986 3.41526 31.6503 3.26926 32.5344C4.47785 32.5344 5.65401 32.5344 6.8545 32.5344C7.01672 30.9608 7.17084 29.4034 7.33307 27.8379C7.70619 27.8785 8.02254 27.9109 8.36322 27.9434C8.2091 29.4927 8.0631 31.0014 7.90898 32.5263C9.83949 32.5263 11.7295 32.5263 13.6275 32.5263C13.9358 30.3606 14.244 28.2192 14.5522 26.0697C14.5603 26.0048 14.5603 25.9155 14.5279 25.8588C14.3494 25.5749 14.1629 25.3072 13.952 24.999C13.5545 25.4694 13.1814 25.9155 12.7839 26.3941C12.3946 24.8367 12.0215 23.3442 11.6321 21.795ZM25.4458 32.5182C26.6625 32.5182 27.8387 32.5182 29.0311 32.5182C28.7147 30.6039 28.4308 28.7221 28.0739 26.8565C27.7333 25.0395 26.6382 23.8228 24.8943 23.2144C23.6532 22.7845 22.3959 22.379 21.1468 21.9572C21.0008 21.9085 20.8548 21.8598 20.6763 21.8031C20.2951 23.3361 19.9139 24.8367 19.5245 26.3941C19.1108 25.9074 18.7458 25.4613 18.3565 24.999C18.1861 25.2504 18.0563 25.4694 17.9022 25.6641C17.7724 25.8344 17.74 25.9967 17.7724 26.2157C17.959 27.4567 18.1293 28.7059 18.2997 29.9469C18.4214 30.8067 18.5511 31.6584 18.6728 32.5182C20.5871 32.5182 22.4771 32.5182 24.3914 32.5182C24.2372 30.977 24.0831 29.4602 23.9371 27.9272C24.294 27.8947 24.6104 27.8623 24.9673 27.8298C25.1376 29.4115 25.2917 30.9608 25.4458 32.5182ZM17.594 32.5263C17.594 32.4209 17.6021 32.3316 17.594 32.2586C17.2939 30.1578 16.9856 28.0569 16.7017 25.9561C16.6774 25.802 16.7261 25.6073 16.8072 25.4694C17.0749 25.0314 17.3669 24.6177 17.667 24.1716C17.2777 23.7092 16.9289 23.2226 16.499 22.8089C16.3773 22.6872 15.9555 22.6791 15.8338 22.7927C15.4039 23.2144 15.0389 23.7011 14.6496 24.1716C14.9578 24.6339 15.2498 25.0638 15.5256 25.5019C15.5905 25.6073 15.6473 25.7533 15.631 25.875C15.4039 27.5297 15.1606 29.1844 14.9172 30.8473C14.8361 31.3988 14.7631 31.9585 14.682 32.5425C15.6716 32.5263 16.6206 32.5263 17.594 32.5263Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.4"
      />
    </svg>
  );
};

export default Experience;
