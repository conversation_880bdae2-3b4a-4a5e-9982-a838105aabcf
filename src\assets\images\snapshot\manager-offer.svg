<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="507.921" height="398.127" viewBox="0 0 507.921 398.127">
  <defs>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0.01" stop-color="#1a2257"/>
      <stop offset="1" stop-color="#5e60f2"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-33.391" y1="0.5" x2="-32.391" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#3076a1"/>
      <stop offset="1" stop-color="#48c9b6"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="-17.061" y1="0.5" x2="-16.061" y2="0.5" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-4" x1="-22.837" y1="0.5" x2="-21.837" y2="0.5" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-5" x1="-16.484" y1="0.5" x2="-15.484" y2="0.5" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-6" x1="0.983" y1="0.962" x2="0.233" y2="0.108" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-7" x1="0.079" y1="2.027" x2="0.485" y2="0.07" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#c9c4c1"/>
      <stop offset="1" stop-color="#fff6de"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="-0.105" y1="0.5" x2="0.895" y2="0.5" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-9" x1="0.571" y1="1.795" x2="0.493" y2="0.379" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#d1d7e0"/>
      <stop offset="1" stop-color="#ebebff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="0.5" y1="1.147" x2="0.5" y2="0.24" xlink:href="#linear-gradient-9"/>
    <linearGradient id="linear-gradient-11" x1="0.5" y1="2.165" x2="0.5" y2="0.322" xlink:href="#linear-gradient-9"/>
    <linearGradient id="linear-gradient-12" x1="0.5" y1="1.147" x2="0.5" y2="0.24" xlink:href="#linear-gradient-9"/>
    <linearGradient id="linear-gradient-13" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-9"/>
    <linearGradient id="linear-gradient-14" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-9"/>
    <linearGradient id="linear-gradient-15" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-9"/>
    <linearGradient id="linear-gradient-16" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-9"/>
    <linearGradient id="linear-gradient-17" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-9"/>
    <linearGradient id="linear-gradient-19" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-9"/>
    <linearGradient id="linear-gradient-20" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-9"/>
    <linearGradient id="linear-gradient-22" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-9"/>
  </defs>
  <g id="hire_manager_illustration" data-name="hire manager illustration" transform="translate(-32.598 -0.147)">
    <path id="Path_292" data-name="Path 292" d="M733.131,292.111a256.837,256.837,0,0,1-35.578,130.9H260.788a256.781,256.781,0,0,1-35.578-130.9c0-141.556,113.7-256.311,253.963-256.311S733.131,150.555,733.131,292.111Z" transform="translate(-192.612 -35.653)" fill="#fff"/>
    <path id="Path_293" data-name="Path 293" d="M834.148,1840.355c0,5.371-93.022,9.724-207.769,9.724s-207.769-4.356-207.769-9.724,93.02-9.724,207.769-9.724S834.148,1834.982,834.148,1840.355Z" transform="translate(-338.014 -1451.804)" fill="url(#linear-gradient)"/>
    <path id="Path_294" data-name="Path 294" d="M740.974,1362.94c6.208-4.366,13.472-34.789,13.906-52.908.22-9.284,2.965-13.151,5.385-16.567,2.307-3.263,4.312-6.082,3.146-12.545-2.223-12.361,3.626-17.7,3.685-17.748l-.677-.772c-.26.229-6.352,5.731-4.016,18.7,1.086,6.044-.707,8.571-2.976,11.769-2.382,3.355-5.346,7.535-5.576,17.131-.466,19.447-7.891,48.171-13.47,52.093Z" transform="translate(-631.318 -1005.171)" fill="url(#linear-gradient-2)"/>
    <path id="Path_295" data-name="Path 295" d="M567.354,1511.811c-.585-1.328-1.191-2.7-1.887-4.15-3.544-7.484-6.686-8.718-11.891-10.76-1.271-.5-2.714-1.065-4.295-1.776-7.455-3.355-6.013-8.288-4.737-12.644.176-.608.354-1.216.51-1.818.654-2.561-.6-3.846-2.517-5.792a21.846,21.846,0,0,1-4.54-6.082c-1.487-3.11-.73-4.779.17-5.631,1.887-1.791,6.252-1.468,10.368.787a15.025,15.025,0,0,1,3.448,2.967c2.624,2.749,5.891,6.17,14.151,6.208,4.71.023,7.292.673,8.628,2.175,1.516,1.7,1.231,4.255.839,7.787-.136,1.221-.292,2.6-.386,4.1a23.913,23.913,0,0,0,4.758,15.422c3.146,3.747,7.023,11.522,5.35,16.307a5.72,5.72,0,0,1-4.662,3.752,13.256,13.256,0,0,1-2.626.285C572.266,1522.941,570.15,1518.149,567.354,1511.811Zm-19.294-46.976c-3.683-1.992-7.625-2.412-9.177-.944-1.179,1.118-.629,3.032.052,4.442a20.571,20.571,0,0,0,4.347,5.811c1.994,2.036,3.565,3.645,2.772,6.767-.157.612-.338,1.231-.52,1.852-1.258,4.337-2.464,8.435,4.175,11.419,1.558.7,2.989,1.258,4.249,1.755,5.253,2.062,8.724,3.425,12.444,11.278.692,1.468,1.3,2.839,1.887,4.175,3.211,7.277,5.151,11.673,12.163,10.253a4.767,4.767,0,0,0,3.9-3.085c1.468-4.217-2.053-11.6-5.169-15.309a24.524,24.524,0,0,1-4.993-16.148c.094-1.52.252-2.919.39-4.152.365-3.265.629-5.627-.583-6.99-1.107-1.244-3.534-1.808-7.864-1.829-8.7-.042-12.132-3.636-14.89-6.526a14.058,14.058,0,0,0-3.2-2.775Z" transform="translate(-470.711 -1162.917)" fill="url(#linear-gradient-3)"/>
    <path id="Path_296" data-name="Path 296" d="M610.609,1559.747l1.026-.054c-.05-.963-1.344-23.666-10.461-30.31-10-7.292-13.155-15.125-14.657-20.384-1.768-6.195-10.775-7.852-11.159-7.919l-.178,1.011c.088.015,8.758,1.613,10.351,7.191,1.544,5.4,4.781,13.453,15.039,20.929C609.294,1536.572,610.6,1559.517,610.609,1559.747Z" transform="translate(-500.762 -1193.797)" fill="url(#linear-gradient-4)"/>
    <path id="Path_297" data-name="Path 297" d="M706.012,1265.628c-2.143-1.068-2.066-5.362-1.948-11.863.149-8.217.352-19.466-3.949-29.815-4.568-10.993-.493-14.206,3.446-17.314,4.414-3.479,8.581-6.765.583-21.6-3.674-6.814-4.842-11.423-3.672-14.506,1.156-3.045,4.4-4.232,7.835-5.49,5.927-2.168,12.644-4.626,11.777-17.171-.266-3.846.185-6.212,1.422-7.443,1.344-1.338,3.324-1.193,5.616-1.025,3.464.254,7.388.539,10.806-3.92.354-1.114,3.622-11.06,7.531-12.763a2.765,2.765,0,0,1,2.393.013h0c2.7,1.29,4.263,2.821,4.788,4.679.91,3.234-1.468,6.879-4.194,11.1-4.308,6.635-9.192,14.154-4.127,23.1,6.7,11.83,3.16,15.972-1.736,21.7-2.8,3.284-5.983,7-8.107,13.073-1.709,4.88,1.206,9.536,4.026,14.036,3.536,5.645,7.193,11.484-.508,16.548-12.73,8.376-25.3,35.718-25.428,35.993l-.052.113-.1.078c-2.345,1.837-4.054,2.726-5.339,2.726a2.351,2.351,0,0,1-1.063-.252Zm31.411-39.518c6.862-4.513,3.775-9.437.21-15.146-2.812-4.488-6-9.573-4.125-14.921,2.189-6.25,5.434-10.05,8.3-13.4,4.8-5.614,7.969-9.324,1.623-20.529-5.371-9.49-.086-17.629,4.161-24.165,2.6-4.014,4.855-7.478,4.07-10.263-.438-1.558-1.825-2.875-4.242-4.031h0a1.749,1.749,0,0,0-1.537,0c-3.626,1.581-6.956,12.111-6.99,12.216l-.027.086-.055.071c-3.775,4.979-8.194,4.654-11.744,4.4-2.124-.155-3.8-.279-4.823.73s-1.363,3.165-1.122,6.644c.921,13.315-6.5,16.028-12.449,18.207-3.355,1.225-6.237,2.284-7.227,4.891-1.049,2.783.092,7.13,3.615,13.654,8.409,15.594,3.7,19.306-.839,22.9-3.808,3-7.4,5.839-3.146,16.112,4.383,10.547,4.177,21.921,4.026,30.226-.1,5.662-.182,10.148,1.382,10.928.914.455,2.665-.315,5.205-2.29C712.7,1260.207,724.737,1234.459,737.423,1226.11Z" transform="translate(-597.902 -894.571)" fill="url(#linear-gradient-5)"/>
    <path id="Path_298" data-name="Path 298" d="M612.052,1235.961c6.48-2.221,5.134,10.276,7.873,19.5s30.457,4.031,18.822,27.168,14.714,7.736,6.843,27.927-.684,41.785-9.24,36.293-4.794-30.914-13.7-33.936-10.349-6.545-1.541-14.757-23.083-16.358-15.213-32.952S604.572,1238.528,612.052,1235.961Z" transform="translate(-524.041 -984.072)" fill="url(#linear-gradient-6)"/>
    <path id="Path_299" data-name="Path 299" d="M677.339,1454.3c.816-3.456,7.774-33.873,1.281-37.551-5.723-3.24-5.583-15.844-4.689-27.747.791-10.536-12.742-15.462-13.319-15.666l-.342.967c.134.048,13.369,4.869,12.637,14.621-.917,12.224-1.015,25.2,5.207,28.718,4.928,2.791.39,27.238-1.774,36.423Z" transform="translate(-568.008 -1092.846)" fill="#fff"/>
    <path id="Path_300" data-name="Path 300" d="M485.4,1671.96l-9.253,35.1H455.471l-9.25-35.1Z" transform="translate(-351.617 -1321.331)" fill="url(#linear-gradient-7)"/>
    <path id="Path_301" data-name="Path 301" d="M710.6,1707.76s-20.365,3.825-20.365,35.1H680.68l-9.25-35.1Z" transform="translate(-576.827 -1357.133)" fill="url(#linear-gradient-8)"/>
    <rect id="Rectangle_98" data-name="Rectangle 98" width="110.615" height="110.615" rx="55.308" transform="translate(201.204 97.392)" opacity="0.95" fill="url(#linear-gradient-9)"/>
    <rect id="Rectangle_99" data-name="Rectangle 99" width="51.214" height="2.529" transform="translate(230.906 182.825)" fill="#96bfe6"/>
    <rect id="Rectangle_100" data-name="Rectangle 100" width="36.706" height="2.529" transform="translate(238.16 190.798)" fill="#96bfe6"/>
    <rect id="Rectangle_101" data-name="Rectangle 101" width="151.39" height="177.698" rx="69.12" transform="translate(330.018 135.757)" opacity="0.95" fill="url(#linear-gradient-10)"/>
    <rect id="Rectangle_102" data-name="Rectangle 102" width="12.008" height="2.223" transform="translate(398.659 240.588)" fill="#4598e6"/>
    <rect id="Rectangle_103" data-name="Rectangle 103" width="69.868" height="4.305" transform="translate(353.074 260.966)" fill="#96bfe6"/>
    <rect id="Rectangle_104" data-name="Rectangle 104" width="56.499" height="4.305" transform="translate(353.074 281.635)" fill="#96bfe6"/>
    <rect id="Rectangle_105" data-name="Rectangle 105" width="33.122" height="4.305" transform="translate(353.074 271.301)" fill="#96bfe6"/>
    <path id="Path_302" data-name="Path 302" d="M1996.542,767.67v2.166h12.8v12.017h2.175V767.67ZM1909.6,781.861h2.168V769.836h12.792V767.67H1909.58Zm99.748,62v12.021h-12.8v2.175h14.978v-14.2Zm-97.579,0h-2.175v14.2h14.978v-2.175h-12.792Z" transform="translate(-1555.321 -614.192)" fill="#4598e6"/>
    <path id="Path_303" data-name="Path 303" d="M2315.1,1304.31a12.805,12.805,0,1,1,12.805-12.805,12.805,12.805,0,0,1-12.805,12.805Zm0-23.131a10.326,10.326,0,1,0,10.326,10.326,10.326,10.326,0,0,0-10.326-10.326Z" transform="translate(-1865.675 -1018.053)" fill="#4598e6"/>
    <path id="Path_304" data-name="Path 304" d="M2112.327,1293.3l-5.817-5.817,1.753-1.753,4.064,4.066,5.7-5.7,1.753,1.753Z" transform="translate(-1663.723 -1014.81)" fill="#4598e6"/>
    <rect id="Rectangle_106" data-name="Rectangle 106" width="74.559" height="63.971" transform="translate(219.233 101.53)" opacity="0.95" fill="url(#linear-gradient-11)"/>
    <path id="Path_305" data-name="Path 305" d="M1330.513,652.187c-2.456-6.5-8.355-9.477-11.14-10.406-.992-.329-2.762-.908-4.628-1.512L1307.707,638h-11.6s-4.018,1.346-8.29,2.844c-3.932,1.378-8.076,2.884-9.473,3.586-2.917,1.455-7.824,11.868-8.808,13.061a92.338,92.338,0,0,0-5.033,9.2H1277c1.321-2.269,2.338-4.031,2.338-4.031s.791,1.736,1.749,4.031h40.055c.629-2.666,1.028-6.021,1.028-6.021s2.145,2.917,4.488,6.021h11.534C1335.381,661.985,1331.9,655.855,1330.513,652.187Z" transform="translate(-1045.523 -511.716)" fill="#385369"/>
    <path id="Path_306" data-name="Path 306" d="M1408.545,485.672s9.072,1.428,7.692,15.626-9.391,13.032-9.926,13.017-8.041.26-8.624-17.073C1397.688,497.232,1397.432,484.414,1408.545,485.672Z" transform="translate(-1150.779 -391.265)" fill="#eea886"/>
    <path id="Path_307" data-name="Path 307" d="M1484.817,539.569s.992-1.061,1.357.015-1.355,5.872-1.418,6.082c0,0-.18.419-.419.187S1484.817,539.569,1484.817,539.569Z" transform="translate(-1219.218 -433.558)" fill="#eea886"/>
    <path id="Path_308" data-name="Path 308" d="M1393.5,538.657s-.973-1.08-1.359-.013,1.227,5.889,1.3,6.115c0,0,.174.419.419.2S1393.5,538.657,1393.5,538.657Z" transform="translate(-1146.352 -432.824)" fill="#eea886"/>
    <path id="Path_309" data-name="Path 309" d="M1412.424,491.745l-1.3,1.736a34.4,34.4,0,0,0-.172-4.4c-.21-.6-.526-2.422-.7-2.852s-7.793-2.565-14.521.629c0,0,.3,2.726-.35,2.98s-.327,4.459-.327,4.459l-1.07-1.2s-1.634-10.932-1.229-12.065,1.69-7.984,15.5-3.92c0,0,4.3-.912,5.167,2.059S1412.424,491.745,1412.424,491.745Z" transform="translate(-1146.832 -383.555)" fill="#434343"/>
    <path id="Path_310" data-name="Path 310" d="M1402.569,603.968c0-.013-1.164-.573-1.164-.573-.566-1.258-.147-6.6-.147-6.6s-11.555-2.936-11.226-.692-1,7.474-1,7.474l-5.927,2.936s6.317,16.962,13.575,14.772,10-15.382,10-15.382Z" transform="translate(-1139.259 -477.94)" fill="#eea886"/>
    <path id="Path_311" data-name="Path 311" d="M1416.91,610.238a6.854,6.854,0,0,0,5.094,2.4c2.126,0,3.653-.6,5.769-2.661a6.08,6.08,0,0,1-5.754,4.286S1418.51,614.713,1416.91,610.238Z" transform="translate(-1165.971 -489.572)" fill="#a77860"/>
    <path id="Path_312" data-name="Path 312" d="M1193.232,877.25s6.667,3.261,5.019,12.134-2.378,10.066-.533,13.535,4.02,18.256,2.359,21.057-20.552-12.317-23.161-25,1.659-18.329,3.173-20.432S1187.345,874.261,1193.232,877.25Z" transform="translate(-975.654 -699.634)" fill="#0e2d3d"/>
    <path id="Path_313" data-name="Path 313" d="M1239.908,1795.125h1.5v-7.13a20.238,20.238,0,0,1,4.823,4.708c1.86,2.733,4.417,2.5,8.311,2.323s3.72-1.743,3.372-3.951-4.3-2.907-5.58-3.255-4.182-5.35-5.115-6.568a16.654,16.654,0,0,1-1.946-3.255s-.726-2.963-4.331,0-2.557,8.659-2.15,9.183S1239.908,1795.125,1239.908,1795.125Z" transform="translate(-1024.846 -1411.6)" fill="#163342"/>
    <path id="Path_314" data-name="Path 314" d="M1254.306,1789.853c1.336.872,4.823,0,4.823,0-.988-1.279-7.061-9.823-7.061-9.823-3.691-2.149-5.148.81-5.148.81C1248.426,1781.292,1252.97,1788.981,1254.306,1789.853Z" transform="translate(-1031.63 -1413.629)" fill="#eea886"/>
    <path id="Path_315" data-name="Path 315" d="M1466.642,1187.777s2.42.464,3.246-.673a7.9,7.9,0,0,1,2.8-1.94c.661-.306,2.339-2,2.727-1.174s-2.632,2.936-3.18,3.5,2.968.818,3.565,1.37-.141,6.352-2.533,5.9-4.65-2.516-5.088-2.516a14.9,14.9,0,0,1-1.753-.26Z" transform="translate(-1205.105 -943.026)" fill="#eea886"/>
    <path id="Path_316" data-name="Path 316" d="M1501.629,1206.93s7.233,1.869,7.61,2.4.254.914-.42.767-7.04-1.6-7.04-1.6,8.189,3.649,8.6,4.014c.235.21.04.975-.333.931s-8.7-3.2-8.7-3.2,7.64,3.422,7.885,3.827a.576.576,0,0,1-.723.795c-.583-.21-7.854-2.991-7.854-2.991s5.4,2.84,5.411,3.307c0,.285-.134.952-1.506.174s-4.8-2.267-4.8-2.267Z" transform="translate(-1231.446 -961.334)" fill="#eea886"/>
    <path id="Path_317" data-name="Path 317" d="M1150.907,1817.422h.929v-5.243a11.366,11.366,0,0,1,2.41,4.194c.755,2.5,3.46,3.24,7.587,3.125s4.194-1.745,3.836-4.194-3.836-3.72-3.836-3.72c-1.569-1.394-5.453-7.178-5.453-7.178a5.447,5.447,0,0,0-4.461.917c-1.961,1.583-1.351,5.159-1.294,6.082S1150.907,1817.422,1150.907,1817.422Z" transform="translate(-955.424 -1433.4)" fill="#163342"/>
    <path id="Path_318" data-name="Path 318" d="M1134.162,1265a36.478,36.478,0,0,0-6.252,13.977c-1.334,7.585.789,33.04.73,36.788s-3.546,13.078-2.848,20.133,3.45,42.207,3.45,42.207l7.535-1.5s-.436-35.829,0-38.8,1.311-13.77,1.485-14.99,11.069-33.269,11.069-38.736S1146.017,1256.077,1134.162,1265Z" transform="translate(-935.83 -1005.592)" fill="#1d3b54"/>
    <path id="Path_319" data-name="Path 319" d="M1220.121,1278.657s8.078,30.828,9.036,35.1,2.349,7.8,2.349,9.609a63.535,63.535,0,0,1-1.562,10.966l-10.259,49.073h-9.771s5.453-29.433,5.591-35.19,4.586-13.961,3.714-16.4-14.034-30.6-14.034-30.6S1195.217,1269.7,1220.121,1278.657Z" transform="translate(-997.09 -1016.753)" fill="#1d3b54"/>
    <path id="Path_320" data-name="Path 320" d="M1167.213,1812.189l-5.453-7.178-4.461.916a20.2,20.2,0,0,0,3.368,5.629C1163.227,1814.418,1167.213,1812.189,1167.213,1812.189Z" transform="translate(-960.804 -1433.989)" fill="#eea886"/>
    <path id="Path_321" data-name="Path 321" d="M1256.863,1034.85a10.082,10.082,0,0,1,5.847,3.011,38.531,38.531,0,0,1,5.538,8.435c.658,1.438,9.9,14.764,10.377,15.064s5.125,1.852,8.2,2.888c4.748,1.6,14.26,5.058,14.26,5.058l-1.887,5.985s-17.9-5.857-22.383-6.962c-5.482-1.35-18.1-17.538-18.1-17.538S1249.314,1039.764,1256.863,1034.85Z" transform="translate(-1037.169 -825.341)" fill="#285173"/>
    <path id="Path_322" data-name="Path 322" d="M1153.063,1022.858a45.251,45.251,0,0,0,5.813,2.336c1.5.3,7.311,7.85,8.33,10.607s0,6.889-2.1,11.744-.122,6.472-1.08,11.324-.65,23.012-.47,25.707-28.047,6.784-33.619,1.871c0,0,3.775-16.358,8.24-21.034.965-1.011.862-7.74.21-9.418s-8.089-14.2-8.089-14.2-.545-12.7,2.7-15.221c2.814-2.187,10.486-2.965,11.87-3.446A23.488,23.488,0,0,1,1153.063,1022.858Z" transform="translate(-939.182 -815.686)" fill="#ffbf55"/>
    <path id="Path_323" data-name="Path 323" d="M1211.323,977.758s-.13,1.187-.279,2.793c-.057.629-.12,1.336-.176,2.055a48.235,48.235,0,0,0-.227,5.643s-4.484,4.492-9.582-5.243a13.378,13.378,0,0,0,.528-9.481C1199.974,968.061,1211.323,977.758,1211.323,977.758Z" transform="translate(-995.387 -775.558)" fill="#eea886"/>
    <path id="Path_324" data-name="Path 324" d="M1301.847,1106.534s1.08-7.176-.067-11.534a27.866,27.866,0,0,1-2.059,9.085Z" transform="translate(-1073.357 -872.877)" fill="#183c59"/>
    <path id="Path_325" data-name="Path 325" d="M1184.66,1025.263s3.085,9.108,11.551,8.439,4.014-7.692,4.014-7.692a18.61,18.61,0,0,0-2.7-1.323c-.593-.113-9.362-.226-9.362-.226Z" transform="translate(-982.426 -817.13)" fill="#eea886"/>
    <path id="Path_326" data-name="Path 326" d="M1258.88,1031.85s4.429,12.268,4.429,24.777.357,32.868,1.9,39.3,5.956,26.321,5.956,26.321l3.454-1.787s-8.307-31.742-8.932-34.032.476-26.47,1.072-28.494,3.127-11.039,2.726-13.539c-.5-3.146-3.448-7.816-8.21-11.826Z" transform="translate(-1041.081 -822.97)" fill="#285173"/>
    <path id="Path_327" data-name="Path 327" d="M1122.115,1026.46a127.6,127.6,0,0,1,4.152,20.132c1.4,11.3-4.287,63.77-8.1,75.681l-14.77-2.378s3.179-37.086,7.5-43.83,5.689-12.719,1.03-26.805,2.043-21.064,2.043-21.064S1120.821,1026.607,1122.115,1026.46Z" transform="translate(-918.208 -818.711)" fill="#285173"/>
    <path id="Path_328" data-name="Path 328" d="M900.913,1124.113l-17.907,31.014L863.1,1143.635l17.129-31.465Z" transform="translate(-681.072 -878.936)" fill="#616161"/>
    <path id="Path_329" data-name="Path 329" d="M906.563,1134.237l-15.9,27.546-17.679-10.209,15.213-27.944Z" transform="translate(-688.88 -887.993)" fill="#e0cec7"/>
    <path id="Path_330" data-name="Path 330" d="M977.156,1139.571l1.116-1.934-7.147-4.127-1.395,1.772Z" transform="translate(-765.34 -895.801)" fill="#616161"/>
    <path id="Path_331" data-name="Path 331" d="M1195.332,1266.836a3.847,3.847,0,0,1-1.172-.168s-.629-.365-1.084-.6a3.865,3.865,0,0,0-.363-.163,6.838,6.838,0,0,1-2-.839,45.034,45.034,0,0,1-3.6-3.8l1.325-2.013.971-2.061s5.887.38,6.683.805a2.05,2.05,0,0,1,.21.134,8.913,8.913,0,0,1,1.246,1.273c.113.1,1.174,1.136,1.139,1.579a1.449,1.449,0,0,1-.7,1.22,3.471,3.471,0,0,1-.745,1.757,2.159,2.159,0,0,1-1.036,1.6C1196.219,1265.561,1195.987,1266.675,1195.332,1266.836Z" transform="translate(-984.37 -1001.062)" fill="#eea886"/>
    <path id="Path_332" data-name="Path 332" d="M1103.388,1034.163s-5.438-1.558-8.554,4.436a27.321,27.321,0,0,0-3.517,11.473c-.394,2.764-2.168,10.307-2.349,11.085s-4.194,10.066-2.357,13.315,20.676,12.87,20.552,13.587,3.259-7.262,3.259-7.262-7.615-4.245-8.863-5.551c-2.026-2.122-6.291-4.4-5.423-7.044,1.032-3.093,4.131-13.531,4.851-15.06S1107.97,1036.111,1103.388,1034.163Z" transform="translate(-904.56 -824.654)" fill="#285173"/>
    <path id="Path_333" data-name="Path 333" d="M1205.1,886.393s9.68,3.043,4.425,19.749-18.476-1.936-17.094-12.115S1205.1,886.393,1205.1,886.393Z" transform="translate(-988.488 -707.794)" fill="#eea987"/>
    <path id="Path_334" data-name="Path 334" d="M1207.6,891.088a21.846,21.846,0,0,1-12.86,5.312s-3.1,3.626-3.332,5.05c0,0-7.183-12.243,1.646-19.581S1210.854,888.064,1207.6,891.088Z" transform="translate(-985.581 -702.741)" fill="#0e2d3d"/>
    <path id="Path_335" data-name="Path 335" d="M1205.517,948.25c-.063-1.216-.812-3.984-2.619-3.209s.111,6.409,1.308,6.063C1204.206,951.1,1205.663,951.127,1205.517,948.25Z" transform="translate(-996.253 -754.26)" fill="#eea987"/>
    <path id="Path_336" data-name="Path 336" d="M1221.166,996.117c-.057.629-.12,1.336-.176,2.055,0,0-4.274-.256-7.13-6.612C1214.047,991.839,1217.178,996.438,1221.166,996.117Z" transform="translate(-1005.503 -791.13)" fill="#a77860"/>
    <rect id="Rectangle_107" data-name="Rectangle 107" width="81.27" height="73.194" rx="36.597" transform="translate(322.687 56.431)" opacity="0.95" fill="url(#linear-gradient-12)"/>
    <rect id="Rectangle_108" data-name="Rectangle 108" width="6.447" height="1.193" transform="translate(359.791 115.78)" fill="#4598e6"/>
    <path id="Path_337" data-name="Path 337" d="M1925.482,269.038s5.195,2.85,3.546,9.857-2.278,7.938-.937,10.771,2.516,14.709,1.088,16.88-15.915-10.595-17.524-20.81,2.013-14.552,3.3-16.171S1920.9,266.434,1925.482,269.038Z" transform="translate(-1556.642 -219.018)" fill="#344052"/>
    <path id="Path_338" data-name="Path 338" d="M1969.27,425.532h-5.56c-.268-2.741-.518-4.651-.468-4.932.082-.468-5.532-15.194-6.057-16.412s-2.586-4.482-2.854-7.55,3.739-4.318,3.739-4.318c5.415.969,7.248,8.46,7.248,8.46s2.273,15.185,3.425,19.459A27.076,27.076,0,0,1,1969.27,425.532Z" transform="translate(-1590.677 -317.558)" fill="#eea886"/>
    <path id="Path_339" data-name="Path 339" d="M1966.819,399.369l-8.279,3.49s-7.611-18.885-1.231-16.777S1966.53,392.136,1966.819,399.369Z" transform="translate(-1591.027 -312.498)" fill="#eb4747"/>
    <path id="Path_340" data-name="Path 340" d="M1913.773,404.153c-.241,1.875-1.233,8.516-1.011,10.31.038.3.159.977.344,1.9h-19.965c.531-1.887,1.015-3.565,1.307-4.549.969-3.215.042-4.077-.449-6.648-.191-1.009-.862-2.921-1.594-5.081-.243-.721-.495-1.468-.738-2.229a46.163,46.163,0,0,1-1.537-7.13c-1.2-7.68,4.7-8.418,4.7-8.418,2.2-.4,6.031-1.59,6.031-1.59l6.006,1.258a6.517,6.517,0,0,1,1.405.839h0a11.136,11.136,0,0,1,1.35,1.185,35.938,35.938,0,0,1,3.267,4.723c.4.65.8,1.317,1.179,1.973,1.22,2.114,2.1,3.819,2.061,5.742C1916.109,398.321,1914.015,402.279,1913.773,404.153Z" transform="translate(-1539.822 -308.391)" fill="#eb4747"/>
    <path id="Path_341" data-name="Path 341" d="M1935.542,382.187s3.762,6.43,2.364,6.921-8.774-1.143-11.656-7.218C1926.25,381.879,1931.09,379.679,1935.542,382.187Z" transform="translate(-1568.496 -308.602)" fill="#eea886"/>
    <path id="Path_342" data-name="Path 342" d="M1939.781,354.042s-3.343,1.416-6.369-3.242c0,0,.813-5.287-.906-8.345s7.514,3.666,7.514,3.666-.149.629-.3,1.562c-.08.476-.162,1.03-.222,1.617-.052.491-.092,1-.109,1.518A8.184,8.184,0,0,0,1939.781,354.042Z" transform="translate(-1573.27 -277.521)" fill="#eea886"/>
    <path id="Path_343" data-name="Path 343" d="M1904.441,435.06c-.688,3.538-5.625,7.969-5.625,7.969-.243-.723-.495-1.468-.736-2.227A32.209,32.209,0,0,0,1904.441,435.06Z" transform="translate(-1546.233 -351.335)" fill="#393d63"/>
    <path id="Path_344" data-name="Path 344" d="M1889.75,404.937c-.312,1.29-1.753,14.353-1.6,14.8.092.256.789,3.278,1.487,6.635H1883.2a15.883,15.883,0,0,1-.956-4.976c.419-4.4-1.082-18.455-1.082-18.455s-1.338-8.5,5.713-9.559c0,0,4.165.549,4.417,3.624S1890.063,403.65,1889.75,404.937Z" transform="translate(-1532.78 -318.396)" fill="#eea886"/>
    <path id="Path_345" data-name="Path 345" d="M1877.852,403.035l8.749,2.045s6.942-13.478.69-15.938C1880.7,386.547,1876.273,395.676,1877.852,403.035Z" transform="translate(-1529.993 -314.69)" fill="#eb4747"/>
    <path id="Path_346" data-name="Path 346" d="M1935.539,276.576s7.6,2.791,2.785,15.915-14.661-2.242-13.174-10.305S1935.539,276.576,1935.539,276.576Z" transform="translate(-1567.506 -225.81)" fill="#eea987"/>
    <path id="Path_347" data-name="Path 347" d="M1938.442,280.445a17.441,17.441,0,0,1-10.456,3.75s-1.5,3.67-1.747,4.8c0,0-6.365-10.926.952-16.446S1941.147,278.151,1938.442,280.445Z" transform="translate(-1566.458 -221.701)" fill="#344052"/>
    <path id="Path_348" data-name="Path 348" d="M1938.5,327.234c0-.971-.5-3.207-1.967-2.657s-.153,5.115.816,4.884C1937.347,329.461,1938.508,329.535,1938.5,327.234Z" transform="translate(-1576.1 -263.954)" fill="#eea987"/>
    <path id="Path_349" data-name="Path 349" d="M1947.308,367.279a8.142,8.142,0,0,1-5-4.509c2.907,3.756,5.1,3.215,5.1,3.215C1947.356,366.476,1947.352,366.782,1947.308,367.279Z" transform="translate(-1581.187 -294.205)" fill="#a77860"/>
    <path id="Path_350" data-name="Path 350" d="M1869.014,368.06v1.168h6.872v6.451h1.168V368.06Zm-46.674,7.619h1.168v-6.451h6.872V368.06h-8.04Zm53.546,33.286v6.453h-6.872v1.166h8.04v-7.619Zm-52.378,0h-1.168v7.619h8.04v-1.166h-6.872Z" transform="translate(-1486.376 -298.386)" fill="#4598e6"/>
    <rect id="Rectangle_109" data-name="Rectangle 109" width="8.609" height="1.594" transform="translate(252.208 170.467)" fill="#4598e6"/>
    <path id="Path_351" data-name="Path 351" d="M1331.542,555v1.56h9.181v8.615h1.558V555ZM1269.2,565.175h1.558v-8.611h9.181V555H1269.2Zm71.512,44.459v8.619h-9.181v1.558h10.739V609.639Zm-69.964,0h-1.558v10.177h10.739v-1.558h-9.181Z" transform="translate(-1049.229 -446.122)" fill="#4598e6"/>
    <path id="Path_352" data-name="Path 352" d="M2013.641,915.549h-7.281c-.134-.292-.241-.545-.331-.749-2.16-5.1-13.3-16.8-13.3-16.8v.025a.019.019,0,0,0,0,.013v.013c0,.019-.013.031-.019.05s-.013.059-.025.1a.226.226,0,0,0-.013.063c0,.019-.012.032-.019.05-.457,1.7-2.7,10.2-3.823,17.241h-49.282c-.113-.654-.245-1.275-.4-1.852a39.826,39.826,0,0,1-.95-5.1c0-.082-.019-.157-.025-.241-.04-.3-.071-.6-.1-.887a46.819,46.819,0,0,1-.082-8.156c-.138.3-1.134,3.949-2.441,8.454-.715,2.472-1.527,5.205-2.332,7.778H1921.59l14.6-42.129c.323-.254,4.113-2.085,6.876-3.4,2.821-1.344,6.224-3.316,9.3-5,1.573-.862,3.056-1.64,4.343-2.229l.195-.09h0c2.124-.969,3.538-1.609,3.538-1.609h0l.918-.09c2.965-.285,12.163-1.147,15.248-1.323.254-.019.47-.025.629-.031a2.508,2.508,0,0,1,.31,0,38.694,38.694,0,0,1,4.777,1.634c.1.031.191.071.285.109l.357.132c.21.084.436.166.658.254.445.178.906.354,1.37.539l.489.191c4.987,1.978,10.7,4.429,12.061,5.406.9.64,3.7,5.906,6.933,12.421C2007.975,891.489,2012.24,907.6,2013.641,915.549Z" transform="translate(-1564.812 -686.873)" fill="#decbc4"/>
    <path id="Path_353" data-name="Path 353" d="M2123.136,795.844c-.132,6.623-12.958,10.247-12.958,10.247s-5.365-6.725-4.083-7.369c.5-.254.709-1.258.772-2.382a23.563,23.563,0,0,0-.073-3.005c-.038-.468-.076-.77-.076-.77s1.833-1.934,4.165-4.236c4.073-4.029,9.674-9.188,9.682-7.13a1.825,1.825,0,0,1-.048.39C2119.533,786.2,2123.136,795.844,2123.136,795.844Z" transform="translate(-1710.468 -624.513)" fill="#eea886"/>
    <path id="Path_354" data-name="Path 354" d="M2116.767,806.52h0c-.025.04-.57.854-1.5,2.022-1.969,2.477-5.662,6.545-9.831,8.179a23.519,23.519,0,0,0-.073-3.005,6.6,6.6,0,0,1-1.275-.153,39.512,39.512,0,0,1,2.789-4.083l2.575-.77Z" transform="translate(-1709.04 -644.895)" fill="#a77860"/>
    <path id="Path_355" data-name="Path 355" d="M2086.635,699.471c-1.11,2.414-2.162,4.163-2.462,4.857h0a8.6,8.6,0,0,1-1.5,2.022c-2.114,2.275-6.044,5.278-9.9,5.174a6.594,6.594,0,0,1-1.275-.153,6.063,6.063,0,0,1-4.415-3.049c-8-15.666-2.208-28.475-2.208-28.475s21.879-6.189,25.3.4C2092.576,684.864,2089.242,693.8,2086.635,699.471Z" transform="translate(-1676.446 -542.703)" fill="#eea886"/>
    <path id="Path_356" data-name="Path 356" d="M2062.443,638.836s-2.03-.113-2.533-3.966c-.5-3.806.508-4.861.508-4.861s9.032-3.775,12.274-4.272,11.219-.965,13.09,1.174c0,0,4.541-1.049,5.78,6.484,1.963,11.954-6.69,23.578-6.69,23.578s.073-7.816-1.65-8.85-3.211,1.426-3.211,1.426l-3.525.629s5.354-11.561-1.07-11.989S2066.121,639.511,2062.443,638.836Z" transform="translate(-1674.015 -501.693)" fill="#e8734d"/>
    <path id="Path_357" data-name="Path 357" d="M2156.314,713.571c.335-3.443,6.562-4.758,6.145-1.149s-3.483,7.076-4.9,6.954S2156.058,716.2,2156.314,713.571Z" transform="translate(-1750.222 -568.832)" fill="#eea886"/>
    <path id="Path_358" data-name="Path 358" d="M2172.44,886.8c-2.028-6.5-3.775-11.637-4.563-12.195-1.363-.975-12.164-3.947-17.289-5.956l-.489-.191c-.464-.185-.925-.361-1.37-.539-.222-.088-.445-.17-.658-.254l-.356-.132c-.094-.038-.189-.078-.285-.109a.09.09,0,0,1-.025.025,84.724,84.724,0,0,0-5.662,7.239c-5.3,7.537-12.193,19.73-11.144,30.991.342,3.613.76,8.808,1.468,14.63.051.461.107.925.17,1.395h23.907c.291-6.1.812-14.128,1.6-17.241,0-.019.013-.031.019-.05a.223.223,0,0,1,.013-.063.587.587,0,0,1,.025-.1.181.181,0,0,1,.019-.05v-.013a.022.022,0,0,1,0-.013s5.786,9.045,6.081,17.526h15.047C2178.22,913.821,2175.919,897.95,2172.44,886.8Z" transform="translate(-1729.904 -693.023)" fill="#3c4145"/>
    <path id="Path_359" data-name="Path 359" d="M1947.919,874.684s-1.806,6.008-4.14,21.011h0c-.082.5-.157,1.013-.239,1.539,0,.057-.019.119-.025.185-.059.342-.109.7-.159,1.049l-.019.128c-.038.235-.076.474-.109.721-.088.589-.176,1.2-.264,1.814-.876,6.122-2.042,15.85-3.227,26.233h-9.45a44.079,44.079,0,0,1-.956-7.308.9.9,0,0,0-.044.119c-.755,2.1-1.585,4.368-2.428,6.579-.076.21-.151.407-.229.61H1910.34l16.226-42.446c.323-.254,4.5-2.5,7.264-3.817,2.884-1.374,6.526-3.066,9.76-4.557,1.545-.715,3-1.38,4.228-1.944Z" transform="translate(-1555.922 -698.698)" fill="#3c4145"/>
    <path id="Path_360" data-name="Path 360" d="M2143.895,883.879l-6.243,3.523,2.667,2.726-5.855,31.6h-2.206c-.812-6.39-1.273-12.126-1.634-16.024-1.629-17.406,15.75-37.056,16.825-38.256.839.317,1.736.667,2.67,1.034Z" transform="translate(-1729.924 -693.047)" fill="#51565c"/>
    <path id="Path_361" data-name="Path 361" d="M2050.786,874.22s-2.332,7.613-5.043,26.621c-.874,6.115-2.049,15.85-3.234,26.233h-.918l-1.21-29.859,2.972-3.219-2.642-3.3,5.662-14.439c1.546-.715,3-1.38,4.228-1.944Z" transform="translate(-1658.691 -698.397)" fill="#51565c"/>
    <path id="Path_362" data-name="Path 362" d="M2144.442,855.926l-5.173-3.146s-.145,2.835-12.958,10.247l3.638,10.249Z" transform="translate(-1726.6 -681.454)" fill="#f5e2d9"/>
    <path id="Path_363" data-name="Path 363" d="M2094.207,859.61s.21,3.062,3.408,8.808l-7.1,9.257-.514-15Z" transform="translate(-1697.905 -686.851)" fill="#f5e2d9"/>
    <path id="Path_364" data-name="Path 364" d="M2110.122,901.66s1.376,5.5.88,7.046c0,0-3.026,1.1-5.2-.275A24.339,24.339,0,0,1,2110.122,901.66Z" transform="translate(-1710.391 -720.083)" fill="#d44040"/>
    <path id="Path_365" data-name="Path 365" d="M2090.8,941.114a138.4,138.4,0,0,0-.912,28.383h-6.065c-.084-1.2-.159-2.447-.235-3.754-1.128-19.577,5.792-37.071,5.792-37.071l3.087-.772S2091.477,936.551,2090.8,941.114Z" transform="translate(-1692.742 -740.82)" fill="#d44040"/>
    <path id="Path_366" data-name="Path 366" d="M1990.98,1005.087c-.231,4.7-.2,8.869-.2,8.869h0c0,.023,0,.151-.015.361a2.309,2.309,0,0,0-.029-.237c-.753,2.1-1.586,4.364-2.424,6.577a69.453,69.453,0,0,1-.189-7.166c.336-13.172,4.479-20.08,4.479-20.08C1991.647,995.708,1991.2,1000.635,1990.98,1005.087Z" transform="translate(-1617.378 -792.592)" fill="#2d3133"/>
    <path id="Path_367" data-name="Path 367" d="M1359.911,984.63l-19.38,10.651-19.461-30.624,23.538-8.177Z" transform="translate(-1043 -755.897)" fill="#292c47"/>
    <path id="Path_368" data-name="Path 368" d="M1363.534,987.115l-18.123,9.961-18.2-28.64,22.014-7.646Z" transform="translate(-1047.852 -759.303)" fill="#fff"/>
    <path id="Path_369" data-name="Path 369" d="M1670.689,1072.6s-3.372-1.615-4.853-2.458-5.289-2-5.6-2.275-11.289-.952-11.954-1.527-1.313,1.346,1.533,1.971,6.807,2.147,7.023,3.259-2.923,1.376-3.24,2.292.088.711.539.908,3.735-1.623,5.18-1.107,3.651-.2,4.758.18,6.5,2.192,6.5,2.192Z" transform="translate(-1348.371 -850.144)" fill="#eea886"/>
    <path id="Path_370" data-name="Path 370" d="M1578.687,1085.823s-2.833-2.552-4.194-3.617-3.945-4.194-4.324-4.389-5.293-10.276-6.094-10.672.757-1.762,2.454.677,4.674,5.578,5.807,5.35.159-3.29.9-3.947.7-.193,1.065.155-.073,4.15.975,5.312,1.235,3.519,2.024,4.4,4.591,5.264,4.591,5.264Z" transform="translate(-1282.101 -850.279)" fill="#eea886"/>
    <path id="Path_371" data-name="Path 371" d="M1925.57,1806.933s.615,4.255.69,4.895,6.82-.377,6.82-.377l.545-5.065S1926.413,1806.336,1925.57,1806.933Z" transform="translate(-1567.958 -1435.075)" fill="#424773"/>
    <path id="Path_372" data-name="Path 372" d="M1884.46,1828.711a18.307,18.307,0,0,1-4.194,3.68c-2.057,1.118-5.312,1.56-6.535,2.726-.881.839-3.213,4.429,5.369,4.356s12.719.508,13.631-.87-1.33-10.173-1.33-10.173S1889.367,1829.154,1884.46,1828.711Z" transform="translate(-1526.229 -1452.497)" fill="#292c47"/>
    <path id="Path_373" data-name="Path 373" d="M1695.21,1806.933s.614,4.255.69,4.895,6.82-.377,6.82-.377l.545-5.065S1696.053,1806.336,1695.21,1806.933Z" transform="translate(-1385.907 -1435.075)" fill="#424773"/>
    <path id="Path_374" data-name="Path 374" d="M1639.163,1828.679a18.308,18.308,0,0,1-4.194,3.68c-2.057,1.118-9.67,2.036-10.89,3.192-.883.839-2.349,4.194,9.724,3.882,8.579-.222,12.719.507,13.631-.87s-1.33-10.173-1.33-10.173S1644.188,1829.172,1639.163,1828.679Z" transform="translate(-1329.242 -1452.466)" fill="#292c47"/>
    <path id="Path_375" data-name="Path 375" d="M1840.739,1287.69s6.335,10.066,5.872,19.164,2.3,19.033,3.248,28.538c1.061,10.594,5.4,35.68,5.56,41.347s-.673,23.767-.673,23.767l-8.693.2s-7.3-43.853-9.189-48.328-11.266-36.375-11.58-39.955S1840.739,1287.69,1840.739,1287.69Z" transform="translate(-1488.698 -1025.157)" fill="#292c47"/>
    <path id="Path_376" data-name="Path 376" d="M1700.187,1282.528s-4.775,24.937-5.406,30.5-1.049,26.948-1.365,30.429-2.1,45.543-3.775,50.715h11.115s8.529-30.578,8.529-45.046,6.589-31.457,10.842-41,13.992-9.772,16.605-27.472C1736.723,1280.649,1703.726,1276.909,1700.187,1282.528Z" transform="translate(-1381.505 -1018.63)" fill="#292c47"/>
    <path id="Path_377" data-name="Path 377" d="M1791.469,881.227s-8.5-3.181-12.423,3.3-3.544,19.432.742,21.026,10.725.759,12.92-3.291,4.176-9.9,4.079-13.818S1793.7,882.357,1791.469,881.227Z" transform="translate(-1450.011 -703.317)" fill="#eea886"/>
    <path id="Path_378" data-name="Path 378" d="M1796.29,971.15a7.345,7.345,0,0,1-.231,3.427c-.585,1.12,11.016,1.162,11.016,1.162a76.444,76.444,0,0,1,2.726-11.35C1811.587,959.192,1796.29,971.15,1796.29,971.15Z" transform="translate(-1465.59 -768.622)" fill="#eea886"/>
    <path id="Path_379" data-name="Path 379" d="M1795.6,895.451s-.8,1.321-4.068.807-7.256-4.861-8.621-5.337-1.378-1.279-1.378-1.279l-.854,1.258-1.451.092s1.323-3.89-.671-5.478,2.07-12.583,12.551-8.925c0,0,8.051,2.219,7.759,6.8S1797.405,893.171,1795.6,895.451Z" transform="translate(-1451.39 -699.684)" fill="#3a3f47"/>
    <path id="Path_380" data-name="Path 380" d="M1792.86,1005.275s.036-2.16,1.015-2.368,10.813-1.151,12.015-.679v2.517S1803.572,1006.171,1792.86,1005.275Z" transform="translate(-1463.079 -799.458)" fill="#e2d2ca"/>
    <path id="Path_381" data-name="Path 381" d="M1794.658,927.39c.185-.767,2.137-2.963,2.884-.612s-1.128,5.5-2.177,5.239S1794.192,929.345,1794.658,927.39Z" transform="translate(-1464.296 -739.098)" fill="#eea886"/>
    <path id="Path_382" data-name="Path 382" d="M1606.639,1138.994a.021.021,0,0,1,.017,0C1606.589,1139.021,1606.578,1139.023,1606.639,1138.994Z" transform="translate(-1315.879 -907.644)" fill="#323657"/>
    <path id="Path_383" data-name="Path 383" d="M1675.335,1095.914a90.067,90.067,0,0,1-14.107,2.516c-8.892.982-31.111-.354-31.247-1.405s4.779-39.12,4.5-41.081-.419-8.946-.419-8.946a49.411,49.411,0,0,1-4.4,13.994c-.851,1.541-3.7,6.367-5.079,5.27-6.656-5.316-16.964-25.342-17.616-25.511.55-.239,4.886-2.236,5.495-2.437.533-.178,5.335,4.905,8.244,8.057,1.3,1.405,2.219,2.426,2.219,2.426.839-1.888,3.9-13.8,4.851-17.922,2.145-9.295,1.816-9.68,1.816-9.68,6.5-1.969,16.381-5.975,16.381-5.975l13.029-.531s11.954,2.632,16.668,4.76c.447.21.449,10.668,1.678,13.422l-3.9,8.237s-2.257,16.318-2.726,23.574C1670.474,1068.022,1675.335,1095.914,1675.335,1095.914Z" transform="translate(-1316.165 -809.409)" fill="#ffdf69"/>
    <path id="Path_384" data-name="Path 384" d="M1890.3,1064.421c-.34,1.1-1.887,2.026-2.888,2.433a3.733,3.733,0,0,1-2.1.21l-.51-.094h0l-8.929-1.657s-1.955-22.5,12.2-27.892C1888.464,1037.542,1893.432,1054.346,1890.3,1064.421Z" transform="translate(-1528.612 -827.372)" fill="#ffdf69"/>
    <path id="Path_385" data-name="Path 385" d="M1921.437,1134.873h0a3.732,3.732,0,0,1-2.1.21l-.51-.094h0l.881-11.5Z" transform="translate(-1562.631 -895.392)" fill="#ab9546"/>
    <path id="Path_386" data-name="Path 386" d="M1674.631,1168.185l-1.09,4.614a36.557,36.557,0,0,1-1.128-7.048C1673.7,1167.163,1674.631,1168.185,1674.631,1168.185Z" transform="translate(-1367.89 -928.79)" fill="#ab9546"/>
    <path id="Path_387" data-name="Path 387" d="M1860.09,887.721s11.144-1.395.189,17.22Z" transform="translate(-1516.21 -709.048)" fill="#3a3f47"/>
    <path id="Path_388" data-name="Path 388" d="M1359.523,968.695l-1.132-1.946,8.921-3.078,1.132,1.936Z" transform="translate(-1072.494 -761.579)" fill="#292c47"/>
    <g id="Group_243" data-name="Group 243" transform="translate(431.563 116.929)" opacity="0.95">
      <path id="Path_389" data-name="Path 389" d="M2168,574.814h6.321l-1.934-4.014h-6.321Z" transform="translate(-2142.356 -568.028)" fill="url(#linear-gradient-13)"/>
      <path id="Path_390" data-name="Path 390" d="M2153.9,574.814h1.506l-1.936-4.014h-1.506Z" transform="translate(-2131.205 -568.028)" fill="url(#linear-gradient-14)"/>
      <path id="Path_391" data-name="Path 391" d="M2053.842,559.585h25.6l-.849-2h-25.6Z" transform="translate(-2052.99 -557.58)" fill="url(#linear-gradient-15)"/>
    </g>
    <rect id="Rectangle_110" data-name="Rectangle 110" width="10.846" height="3.269" rx="1.635" transform="translate(284.695 51.685)" fill="url(#linear-gradient-16)"/>
    <rect id="Rectangle_111" data-name="Rectangle 111" width="10.846" height="3.269" rx="1.635" transform="translate(284.695 59.522)" fill="url(#linear-gradient-17)"/>
    <rect id="Rectangle_112" data-name="Rectangle 112" width="10.846" height="3.269" rx="1.635" transform="translate(284.695 67.361)" fill="url(#linear-gradient-16)"/>
    <rect id="Rectangle_113" data-name="Rectangle 113" width="3.087" height="3.269" rx="1.543" transform="translate(300.021 51.685)" fill="url(#linear-gradient-19)"/>
    <rect id="Rectangle_114" data-name="Rectangle 114" width="3.087" height="3.269" rx="1.543" transform="translate(300.021 59.522)" fill="url(#linear-gradient-20)"/>
    <rect id="Rectangle_115" data-name="Rectangle 115" width="3.087" height="3.269" rx="1.543" transform="translate(300.021 67.361)" fill="url(#linear-gradient-19)"/>
    <path id="Path_392" data-name="Path 392" d="M1224.3,1076.664h11.348l-3.473-7.2H1220.83Z" transform="translate(-963.781 -845.183)" fill="url(#linear-gradient-22)"/>
    <path id="Path_393" data-name="Path 393" d="M1198.993,1076.664h2.7l-3.473-7.2h-2.7Z" transform="translate(-943.779 -845.183)" fill="url(#linear-gradient-22)"/>
  </g>
</svg>
