@import "../../../../assets/sass/importFiles";
.landing-header {
  @extend %clearfix;
  background: $darkBlue;
  position: fixed;
  width: 100%;
  z-index: 20;
  height: 80px;
  > div {
    display: flex;
  }
  .bgUpdate & {
    background: #fafafa;
  }
  .loginScreen {
    .bgUpdate & {
      display: none;
    }
  }
  .otherScreen {
    display: none;
    .bgUpdate & {
      display: block;
    }
  }
  @include breakpoint(screen767) {
    // position: relative;
  } //MobileLandscape
  &:before {
    content: "";
  }
  .TopBar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: url("../../../../assets/images/topbar.png");
    height: 10px;
    background-size: contain;
    img {
      display: none;
      max-width: 100%;
      float: left;
    }
  }

  .Logo {
    float: right;
    margin-top: 15px; // background: #fff;
    // border-bottom: 7px solid #00C9A7;
    // border-left: 7px solid #00C9A7;
    width: 150px;
    padding: 15px 20px 0 0;
    @include breakpoint(screen1023) {
      width: 175px;
      max-width: 45%;
    }
    img {
      max-width: 100%;
    }
  }

  .btnWrap {
    float: left;
    margin: 29px 0 0 30px;
    @include breakpoint(screen640) {
      display: inline-block;
      margin-left: 20px;
      float: none;
    }
  }

  .loginBtnClick {
    font-family: $RubikRegular;
    background: $green;
    color: $darkBlue;
    font-size: 14px;
    padding: 6px 10px;
    display: inline-block;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    outline: none;
    margin-right: 10px;
    @include transition(0.3s all);
    &:hover {
      background: darken($green, 5);
    }
  }

  .SignUpHeader {
    float: left;
  }

  .signUpLink {
    background-color: #2b2342;
    padding: 32px 30px;
    height: 90px;
    margin-top: 10px;
    font-size: 20px;
    float: left;
    color: #fff;
    @include transition(background 0.3s, color 0.3s);
    @include breakpoint(screen1023) {
      padding: 16px 0px;
      height: 67px;
      font-size: 18px;
      text-align: center;
      width: 85px;
      line-height: 1;
    }
    &:hover {
      // color: darken(#fff,20);
    }
  }
  .top-navigation {
    flex: 1;
    align-self: center;
    margin-top: 25px;
    justify-content: flex-end;
    display: flex;
    position: relative;
    a,
    > div {
      margin: 5px;
      color: $white;
      text-decoration: none;
    }
    .sub-nav {
      display: flex;
      flex-flow: column;
      position: absolute;
      top: 25px;
      background: #413760;
      padding: 5px 10px 10px 0px;
      border-radius: 10px;
    }
  }
} //header

.mobile-header {
  display: none;
}
.tab-header {
  display: none;
}
@include breakpoint(screen991) {
  .main-header,
  .mobile-header {
    display: none;
  }
  .tab-header {
    display: flex;
    height: 100px;
    padding-bottom: 10px;
    .top-wrapper {
      display: flex;
      flex-flow: column;
      width: 100%;
      align-items: center;
      .logo-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        align-self: normal;
        .btnWrap {
          display: inline-block;
          margin-left: 10px;
          float: none;
          margin-top: 10px;
        }
        .Logo {
          margin-top: 20px;
          width: 100px;
          max-width: 100px;
          margin: 10px;
          img {
            margin-top: 10px;
          }
        }
      }
      .top-navigation,
      .landing-search-component {
        margin-top: 10px;
      }
      .Logo {
        margin: 0px;
        padding: 0px;
      }
      > * {
        flex-basis: 30%;
      }
    }
  }
}
@include breakpoint(screen640) {
  .main-header,
  .tab-header {
    display: none;
  }
  .mobile-header {
    display: flex;
    height: 120px;
    padding-bottom: 10px;
    .top-wrapper {
      display: flex;
      flex-flow: column;
      width: 100%;
      align-items: center;
      .logo-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        align-self: normal;
        .btnWrap {
          display: inline-block;
          margin-left: 10px;
          float: none;
          margin-top: 10px;
        }
        .Logo {
          margin-top: 20px;
          width: 100px;
          max-width: 100px;
          margin: 10px;
          img {
            margin-top: 10px;
          }
        }
      }
      .top-navigation,
      .landing-search-component {
        margin-top: 0px;
        margin: 5px 15px 0;
      }
      .landing-search-component {
        margin-top: 0px;
        margin: 5px 10px 0;
        flex: 1;
        height: 40px;
        width: 250px;
        min-width: 250px;
        .SearchIcon {
          width: 25px;
          height: 25px;
          background-size: 25px;
        }
        .Select-placeholder {
          line-height: 40px;
        }
      }
      .Logo {
        margin: 0px;
        padding: 0px;
      }
      > * {
        flex-basis: 30%;
      }
      .top-navigation {
        flex: 1 1;
        align-self: center;
        margin-top: 5px;
        justify-content: flex-end;
        display: flex;
        position: relative;
        flex-flow: column;
        background: #3e335e;
        width: 100%;
        span,
        a {
          width: 100%;
          padding: 5px 15px;
          display: block;
          &:hover {
            background: $green;
          }
        }
        .sub-nav {
          display: flex !important;
          flex-flow: column !important;
          position: relative !important;
          background: #413760 !important;
          padding-left: 10px !important;
          top: 0;
          padding: 0;
        }
      }
      .menu-wrapper {
        display: flex;
        justify-content: space-between;
        width: 100%;
        .ToggleMenu {
          align-self: center;
          border: none;
          background: url("../../../../assets/images/Icons/menu.png") no-repeat
            0 0;
          width: 35px;
          margin: 5px 5px 0;
          height: 35px;
          float: left;
          background-size: 35px 35px;
        }
      }
    }
  }
}
