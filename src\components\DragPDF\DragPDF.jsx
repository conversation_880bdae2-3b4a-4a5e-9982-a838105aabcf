import { useEffect } from "react";
import Dropzone from "react-dropzone";
import "./dragpdf.scss";
import { notificationAction } from "../../actions/notification";
import { connect } from "react-redux";
import { onStateChangeAction } from "../../views/Resume/resumeAction";

const DragPDF = props => {
  useEffect(() => {
    if (!props.PDFFileName) {
      props.onStateChangeAction({
        PDFFileName: props.labels.iproResumeFileFormate
      });
    }
  }, [
    props.PDFFileName,
    props.labels.iproResumeFileFormate,
    props.onStateChangeAction
  ]);

  const handleDropAccepted = files => {
    let types = ["pdf"];
    const file = files[0];
    const filename = file.name.split(".")[0];
    let fileType = file.name.split(".");
    if (types.includes(fileType[fileType.length - 1].toLowerCase())) {
      let reader = new FileReader();
      reader.onload = (function(theFile) {
        return function(e) {
          let resumeFile = e.target.result.split(",")[1];
          if (resumeFile) {
            props.getPDF(resumeFile, false);
            props.onStateChangeAction({
              isPDFFileUploaded: true,
              PDFFileName: filename
            });
          }
        };
      })(file);
      reader.readAsDataURL(file);
    } else {
      const info = {
        message: props.labels.resumeImportPDFFileInfo,
        status: "error"
      };
      props.notificationAction(info);
      props.onStateChangeAction({
        isPDFFileUploaded: false,
        PDFFileName: props.labels.iproResumeFileFormate
      });
      props.getPDF(null, true);
    }
  };

  const handleCloseClick = () => {
    props.onStateChangeAction({
      isPDFFileUploaded: false,
      PDFFileName: props.labels.iproResumeFileFormate
    });
    props.getPDF(null, true);
  };

  const { labels, isPDFFileUploaded, PDFFileName } = props;

  return (
    <div className="drag-pdf-component">
      {isPDFFileUploaded && (
        <div>
          <button
            className="closeBtn"
            onClick={handleCloseClick}
            data-testid="resumeDropZoneCloseButton"
          />
        </div>
      )}
      <Dropzone
        className="feedDrag demo-droppable"
        acceptClassName="drag-accepted"
        rejectClassName="drag-rejected"
        onDropAccepted={handleDropAccepted}
      >
        <p className="pdfFormatText" data-testid="resumeDropZonepdffromate">
          {PDFFileName}
        </p>
      </Dropzone>
    </div>
  );
};

const mapStateToProps = ({ systemLabel, resume }) => {
  const { labels } = systemLabel;
  return { labels, ...resume };
};

export default connect(mapStateToProps, {
  notificationAction,
  onStateChangeAction
})(DragPDF);
