import { Drawer } from "antd";
import isEmpty from "lodash/isEmpty";
import map from "lodash/map";
import moment from "moment";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { notificationAction } from "../../../actions/notification";
import {
  addCollaborationApi,
  getAllOpportunitiesApi,
  getCurrenciesApi,
  getOpportunityIprosResumeApi
} from "../common/collaborationApi";
import NewUserDetail from "../common/components/newUserDetail";
import { onStateChangeAction } from "../searcher/SearcherCollaborationCreateAction";
import CreateNewCollaborationForm from "./components/createNewCollobration/createForm";
import ConfirmDialog from "../../../common/ConfirmDialog/ConfirmDialog";
const NewCreateCollaboration = ({
  setCreateNewCollaboration,
  modalOpened,
  availableDetail,
  setAvailableDetail,
  Currencies,
  setCurrencies,
  activeShowMore,
  setActiveShowMore,
  showUserDetail,
  setShowUserDetail,
  filteredCollaborations,
  setRefresh,
  jobInviteData,
  setJobInviteData
}) => {
  const [mobileModal, setMobileModal] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {
    labels,
    isHelpActive,
    collaborationForm,
    allCurrenciesList,
    formCollapsed,
    detailCollapsed,
    dialogMessage,
    isLoading,
    opportunityList,
    iprosList,
    selectedIpro
  } = useSelector(state => ({
    labels: state.systemLabel.labels,
    isHelpActive: state.navigation.isHelpActive,
    ...state.createCollaboration
  }));
  useEffect(() => {
    if (selectedIpro && !isEmpty(selectedIpro)) {
      setAvailableDetail(selectedIpro);
    }
  }, [selectedIpro]);

  useEffect(() => {
    getCurrencies();
    getOpportunities();
    networkToCollaboration();
    return () => {
      localStorage.removeItem("OpportunityId");
      localStorage.removeItem("ResumeId");
      dispatch(onStateChangeAction({ selectedIpro: {}, iprosList: [] }));
    };
  }, []);

  const networkToCollaboration = () => {
    const RequestId = JSON.parse(localStorage.getItem("OpportunityId"));
    const ResumeId = JSON.parse(localStorage.getItem("ResumeId"));
    if (RequestId && ResumeId) {
      const selectedOpportunity = {
        RequestId: RequestId,
        value: RequestId
      };
      handleFormSelectChange("OpprtunityId", selectedOpportunity);
      dispatch(
        onStateChangeAction({
          collaborationForm: {
            ...collaborationForm,
            invalidRequestId: false,
            Request: RequestId,
            RequestId,
            invalidResumeId: false,
            ResumeId: ResumeId
          }
        })
      );
    }
  };

  const getCurrencies = () => {
    getCurrenciesApi()
      .then(res => {
        if (res.success) {
          setCurrencies(
            res.items.map(item => ({
              ...item,
              value: item.CurrencyId,
              label: item.Name
            }))
          );
        }
      })
      .catch(err => console.log("Err ", err));
  };

  const getOpportunities = () => {
    getAllOpportunitiesApi(0).then(data => {
      if (data.success) {
        const opportunityList = data.items.Sent.map(item => ({
          ...item,
          value: item.RequestId,
          label: item.RequestName
        }));
        dispatch(onStateChangeAction({ opportunityList }));
      }
    });
  };

  const getOpportunityIProsResume = requestId => {
    getOpportunityIprosResumeApi(requestId).then(data => {
      if (data.success) {
        const iprosList = map(data.items.user, item => {
          const newItem = {
            ...item,
            value: item.ResumeId,
            label: item.Title
          };
          const newData = opportunityList.filter(item => {
            return item?.RequestId === requestId;
          });
          setJobInviteData(newData);
          return newItem;
        });
        dispatch(onStateChangeAction({ iprosList }));
      }
    });
  };

  const handleFormSelectChange = (name, selectedOption) => {
    if (!selectedOption) return;
    const { value, RequestId } = selectedOption;
    if (name === "OpprtunityId") {
      if (RequestId) {
        getOpportunityIProsResume(RequestId);
        dispatch(
          onStateChangeAction({
            collaborationForm: {
              ...collaborationForm,
              invalidRequestId: false,
              Request: RequestId,
              RequestId
            }
          })
        );
        return;
      }
    }
    const { ResumeId } = selectedOption;
    if (name === "ResumeId") {
      if (ResumeId) {
        dispatch(
          onStateChangeAction({
            collaborationForm: {
              ...collaborationForm,
              invalidResumeId: false,
              Resume: ResumeId,
              ResumeId
            },
            selectedIpro: iprosList.find(item => item.ResumeId === ResumeId)
          })
        );
        return;
      }
    }
    if (name === "HourlyRateType") {
      if (value) {
        dispatch(
          onStateChangeAction({
            collaborationForm: {
              ...collaborationForm,
              invalidHourlyRateType: false,
              HourlyRateType: value,
              value
            }
          })
        );
        return;
      }
    }
    if (name === "DurationType") {
      if (value) {
        dispatch(
          onStateChangeAction({
            collaborationForm: {
              ...collaborationForm,
              invalidDurationType: false,
              DurationType: value,
              value
            }
          })
        );
        return;
      }
    }
    if (name === "Duration") {
      if (value) {
        dispatch(
          onStateChangeAction({
            collaborationForm: {
              ...collaborationForm,
              invalidDuration: false,
              Duration: value,
              value
            }
          })
        );
        return;
      }
    }
  };

  const handleDateChange = (date, dateString) => {
    dispatch(
      onStateChangeAction({
        collaborationForm: {
          ...collaborationForm,
          StartDate: dateString || null,
          invalidStartDate: !date
        }
      })
    );
  };

  const handleSliderChange = value => {
    if (value) {
      dispatch(
        onStateChangeAction({
          collaborationForm: {
            ...collaborationForm,
            Duration: value,
            value
          }
        })
      );
    }
  };

  const handleFormFieldChange = e => {
    const { name, value } = e.target;
    if (name === "Title" || name === "Description") {
      validateField({ name, value });
      return;
    }
    if (name === "HourlyRate") {
      if (isNaN(value)) return;
      validateField({ name, value });
    }
    if (name === "Duration") {
      if (isNaN(value)) return;
      validateField({ name, value });
    }
  };

  const validateField = ({ name, value }) => {
    dispatch(
      onStateChangeAction({
        collaborationForm: {
          ...collaborationForm,
          [name]: value,
          [`invalid${name}`]: !value
        }
      })
    );
  };

  const makeMandatoryFieldRed = () => {
    dispatch(
      onStateChangeAction({
        collaborationForm: {
          ...collaborationForm,
          invalidTitle: true,
          Title: "",
          invalidStartDate: true,
          StartDate: "",
          invalidDurationType: true,
          DurationType: "",
          invalidHourlyRate: true,
          HourlyRate: "",
          invalidRequestId: true,
          RequestId: "",
          invalidResumeId: true,
          ResumeId: "",
          invalidHourlyRateType: true,
          HourlyRateType: ""
        }
      })
    );
  };

  const handleCollaborationSave = () => {
    const { collTitleRequired } = labels;
    const selectedJob = jobInviteData?.[0] || {};
    const {
      RequestName = "",
      RequestId = "",
      StartDate = "",
      DurationType = "",
      Duration = "",
      HourlyRate = "",
      HourlyRateType = "",
      Decription = ""
    } = selectedJob;

    const defaultCurrency = Currencies?.find(item => item.Name === "USD");
    const finalHourlyRateType =
      HourlyRateType || (defaultCurrency ? defaultCurrency.CurrencyId : "");
    const finalDurationType = DurationType ? DurationType : "Months";

    var collaboration = {
      Title: RequestName,
      HourlyRate,
      HourlyRateType: finalHourlyRateType,
      RequestId,
      ResumeId: collaborationForm.ResumeId,
      Description: Decription,
      StartDate,
      DurationType: finalDurationType,
      Duration
    };

    // ---- VALIDATIONS ----
    if (!RequestName && !StartDate && !Duration && !HourlyRate && !RequestId) {
      const info = {
        message: labels.COLLABORATION_CREATE_REQURIED_FIELD_MESSAGE,
        status: "error"
      };
      dispatch(notificationAction(info));
      return;
    }

    if (!RequestName) {
      dispatch(
        notificationAction({ message: collTitleRequired, status: "error" })
      );
      return;
    }

    const isValidStartDate = StartDate && moment(StartDate).isValid();

    if (!isValidStartDate) {
      dispatch(
        notificationAction({
          message: labels.collStartDateRequired,
          status: "error"
        })
      );
      return;
    }

    if (!HourlyRate) {
      dispatch(
        notificationAction({ message: "Hourly rate required", status: "error" })
      );
      return;
    }

    if (!Duration) {
      dispatch(
        notificationAction({ message: "Duration required", status: "error" })
      );
      return;
    }

    if (!RequestId) {
      dispatch(
        notificationAction({ message: labels.collOppRequired, status: "error" })
      );
      return;
    }

    if (!collaboration.ResumeId) {
      dispatch(
        notificationAction({
          message: labels.collIproRequired,
          status: "error"
        })
      );
      return;
    }

    // ---- API CALL ----
    dispatch(onStateChangeAction({ isLoading: true }));
    collaboration.Duration = Duration + " " + DurationType;
    collaboration.StartDate = collaborationForm.StartDate;

    addCollaborationApi({ collaboration })
      .then(() => {
        setJobInviteData("");
        setRefresh(prev => !prev);
        setAvailableDetail(false);
        setCreateNewCollaboration(false);
        dispatch(
          notificationAction({
            message: labels.collSuccessfullySent,
            status: "success"
          })
        );
        handleCreateNewClick();
      })
      .catch(() => {
        dispatch(onStateChangeAction({ isLoading: false }));
        dispatch(notificationAction({ message: "Error", status: "error" }));
      });
  };

  const handleCreateNewClick = () => {
    dispatch(
      onStateChangeAction({
        isLoading: false,
        collaborationForm: {
          Title: "",
          HourlyRate: "",
          RequestId: "",
          ResumeId: "",
          Description: "",
          StartDate: null,
          DurationType: "",
          Duration: "",
          HourlyRateType: "",
          invalidTitle: false,
          invalidStartDate: false,
          invalidDurationType: false,
          invalidHourlyRate: false,
          invalidRequestId: false,
          invalidResumeId: false,
          invalidHourlyRateType: false
        },
        selectedIpro: {},
        iprosList: []
      })
    );
  };
  const handleClearSelectedIpro = () => {
    setAvailableDetail(false);
    dispatch(
      onStateChangeAction({
        collaborationForm: {
          ...collaborationForm,
          RequestId: undefined,
          ResumeId: undefined,
          Request: undefined,
          Resume: undefined,
          invalidRequestId: false,
          invalidResumeId: false
        },
        selectedIpro: null,
        iprosList: []
      })
    );
  };
  const handleListOpenMobile = () => {
    setMobileModal(true);
  };

  const handleMobileModalClose = () => {
    setMobileModal(false);
  };
  return (
    <div className="!rounded-[20px] md:!p-[40px] !p-0 max-h-[90vh] overflow-auto">
      {dialogMessage && (
        <ConfirmDialog>
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button className="dialog-btn" />
            <ConfirmDialog.Button className="dialog-btn" />
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}

      <div className="h-full w-full flex flex-col">
        <>
          <div className="flex w-full gap-10">
            <div
              className={`${
                isEmpty(selectedIpro) ? "w-full" : "w-full md:w-[47%]"
              }`}
            >
              <div>
                <CreateNewCollaborationForm
                  collaborationForm={collaborationForm}
                  onCollaborationSave={handleCollaborationSave}
                  onFormSelectChange={handleFormSelectChange}
                  labels={labels}
                  isLoading={isLoading}
                  onFormFieldChange={handleFormFieldChange}
                  onDateChange={handleDateChange}
                  onSliderChange={handleSliderChange}
                  opportunityList={opportunityList}
                  iprosList={iprosList}
                  Currencies={Currencies}
                  handleListOpenMobile={handleListOpenMobile}
                  modalOpened={modalOpened}
                  activeShowMore={activeShowMore}
                  setActiveShowMore={setActiveShowMore}
                  setCreateNewCollaboration={setCreateNewCollaboration}
                  selectedIpro={selectedIpro}
                  setShowUserDetail={setShowUserDetail}
                  showUserDetail={showUserDetail}
                  onClearSelectedIpro={handleClearSelectedIpro}
                  jobInviteData={jobInviteData}
                  selectedCurrency={selectedCurrency}
                  setSelectedCurrency={setSelectedCurrency}
                />
              </div>
            </div>
            {!isEmpty(selectedIpro) && (
              <div className="hidden md:block w-[53%] border-l-[0.5px] border-[#C6C6C6]">
                <div className="h-full">
                  {isEmpty(selectedIpro) ? (
                    ""
                  ) : (
                    <NewUserDetail
                      selectedUser={selectedIpro}
                      onCollaborationSave={handleCollaborationSave}
                      handleSliderChange={handleSliderChange}
                      labels={labels}
                      setCreateNewCollaboration={setCreateNewCollaboration}
                      onClearSelectedIpro={handleClearSelectedIpro}
                      setActiveShowMore={setActiveShowMore}
                      setShowUserDetail={setShowUserDetail}
                      jobInviteData={jobInviteData}
                      selectedCurrency={selectedCurrency}
                      setSelectedCurrency={setSelectedCurrency}
                    />
                  )}
                </div>
              </div>
            )}
          </div>
          <div>
            <Drawer
              getContainer={document.body}
              placement="right"
              closable={true}
              open={showUserDetail}
              key="placement"
              headerStyle={{ display: "none" }}
              onClose={() => {
                setShowUserDetail(false);
              }}
              bodyStyle={{
                padding: 0,
                maxHeight: "955px"
              }}
              rootClassName=" 
                  md:[&_.ant-drawer-content-wrapper]:!m-[8px]
                  [&_.ant-drawer-content-wrapper]:!mt-[8px]
                  md:[&_.ant-drawer-content]:!rounded-[16px]
                  [&_.ant-drawer-content]:!rounded-tl-[16px]
                  [&_.ant-drawer-content]:!rounded-tr-[16px]
                  md:[&_.ant-drawer-content-wrapper]:!w-[497px]
                  [&_.ant-drawer-content-wrapper]:!w-[100%]
                  [&_.ant-drawer-content-wrapper]:!rounded-[16px]
                  [&_.ant-drawer-body]:p-[32px_24px]
                  [&_.ant-drawer-body]:!scrollbar-width-none"
            >
              <NewUserDetail
                selectedUser={selectedIpro}
                onCollaborationSave={handleCollaborationSave}
                handleSliderChange={handleSliderChange}
                labels={labels}
                setCreateNewCollaboration={setCreateNewCollaboration}
                onClearSelectedIpro={handleClearSelectedIpro}
                setActiveShowMore={setActiveShowMore}
                setShowUserDetail={setShowUserDetail}
                jobInviteData={jobInviteData}
                selectedCurrency={selectedCurrency}
                setSelectedCurrency={setSelectedCurrency}
              />
            </Drawer>
          </div>
        </>
      </div>
    </div>
  );
};

export default NewCreateCollaboration;
