import { Modal } from "antd";
import { useSelector } from "react-redux";

const TooltipModal = ({ title, description, open, setOpen }) => {
  const labels = useSelector(state => state.systemLabel.labels);

  return (
    <div>
      <Modal
        open={open}
        title={null}
        footer={null}
        closeIcon={null}
        closable={false}
        onCancel={() => {
          setOpen(false);
        }}
        centered
        rootClassName="
        [&_.ant-modal-content]:!p-0 
        [&_.ant-modal]:!max-w-[315px]               
        "
      >
        <div className="p-6">
          <h1 className="!m-0 text-[#343333] !text-[16px] font-medium leading-[100%]">
            {title}
          </h1>
          <p className="!mt-3 !leading-[21px] !text-[#878787] !text-xs !font-normal">
            {description}
          </p>
          <span
            className="mt-[30px] font-medium text-[14px] text-[#8E81F5] flex w-full justify-end cursor-pointer"
            onClick={() => {
              setOpen(false);
            }}
          >
            {labels?.okLabel}
          </span>
        </div>
      </Modal>
    </div>
  );
};

export default TooltipModal;
