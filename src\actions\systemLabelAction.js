import axios from "axios";
import { ActionTypes } from "./ActionsTypes";
import { StorageService } from "../api/storage";
import { ApiUrl } from "../api/apiUrls";
import { set } from "lodash";
const transformedJson = json =>
  json.reduce((acc, item) => {
    const { Identifier, Message } = item;

    // Split Identifier into parts using ONLY dots
    const path = Identifier.split(".");

    // Use Lodash to safely create nested structure
    set(acc, path, Message);

    return acc;
  }, {});
export const systemLabelAction = () => dispatch => {
  axios
    .get(ApiUrl.SystemLabel.systemLabel)
    .then(({ data }) => {
      const labels = transformedJson(data.items);
      StorageService.saveSystemLabels(labels);
      dispatch({
        type: ActionTypes.SYSTEM_LABEL,
        payload: labels
      });
    })
    .catch(error => {
      console.log("error", error);
    });
};
