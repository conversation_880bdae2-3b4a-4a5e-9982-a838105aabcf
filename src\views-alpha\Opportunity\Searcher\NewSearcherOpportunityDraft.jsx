import { Drawer, Dropdown, Modal, Space } from "antd";
import { filter, head, includes, isEmpty, map, toLower } from "lodash";
import moment from "moment";
import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { notificationAction } from "../../../actions/notification";
import ConfirmDialog from "../../../common/ConfirmDialog/ConfirmDialog";
import LoadingMask from "../../../common/LoadingMask/LoadingMask";
import UpdateTitleModal from "../../../components/updateTitleModal/updateTitleModal";
import DeleteIcon from "../../../assets-alpha/images/svg/trash.svg?react";
import EditIcon from "../../../assets-alpha/images/svg/edit.svg?react";
import {
  formatDate,
  formatTime,
  isNullOrUndefined
} from "../../../utilities/helpers";
import { getCurrenciesApi } from "../../Collaboration/common/collaborationApi";
import {
  deleteOpportunityApi,
  getAllLanguagesApi,
  getAllLocationsApi,
  getDraftOpportunitesApi,
  getSentOpportunitesApi,
  getDraftOpportunityDetailApi,
  getSavedSearchesApi,
  getShortlistResumesApi,
  getShortlistsApi,
  saveOpportunity
} from "../../Opportunity/opportunityApi";


import DetailModalCreateOpportunity from "./NewCreateOpportunity/component/detailModal";
import NewSearcherOpportunityForm from "./NewCreateOpportunity/component/NewSearcherOpportunityForm";
import { onStateChangeAction } from "./searcherOpportunityDraftAction";
import { MoreOutlined } from "@ant-design/icons";
import DeleteModal from "../../../common-alpha/DeleteModal/DeleteModal";
import { getCompaniesApi } from "../../Workplace/WorkplaceApi";
const NewSearcherOpportunityDraft = props => {
  const {
    setCreateJobInvitation,
    createJobInvitation,
    setDraftActive,
    setSentInviteActive,
    isLoading,
    setShortlistId,
    draftActive,
    setDraftLength,
    setSentLength,
    editModalOpen,
    setEditModalOpen,
    loadDraft,
    setLoadDraft,
    refresh,
    setRefresh,
    resetFormState
  } = props;
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const location = useLocation();
  const [state, setState] = useState({
    isSearchFocus: false,
    searchListHover: false,
    isLoading: false,
    isResumeDetail: false,
    showDetail: false,
    active: "",
    windowWidth: window.innerWidth,
    updateTitleModal: {
      open: false
    }
  });
  const items = [
    {
      key: "1",
      label: (
        <span className="flex items-center justify-between gap-2">
          Delete <DeleteIcon className="w-5 h-5" />
        </span>
      ),
      onClick: e => {
        setDeleteModalOpen(true);
        handleOpportunityDelete({ selectedOpportunity, e });
      }
    },
    {
      key: "2",
      label: (
        <span className="flex items-center justify-between gap-2">
          Edit <EditIcon className="w-5 h-5" />
        </span>
      ),
      onClick: () => {
        // setCreateJobInvitation(true);
        setEditModalOpen(true);
      }
    }
  ];
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    labels,
    isHelpActive,
    dialogMessage,
    filterOpportunires,
    selectedOpportunity,
    filterShortlists,
    shortlistResumes,
    selectedSaveSearch,
    selectedCompany,
    savedSearches,
    userCompanies,
    languages,
    locations,
    opportunitySaveLoading,
    allCurrenciesList,
    opportunities,
    yesClickCount,
    opportunityToDelete,
    selectedShortlists,
    selectedShortlist,
    shortlists,
    ...rest
  } = useSelector(state => ({
    labels: state.systemLabel.labels,
    isHelpActive: state.navigation.isHelpActive,
    ...state.searcherOpportunityDraft
  }));
  const user = useSelector(state => state.userInfo.user);
  const isFreelancer = user?.IsFreelancer;
  useEffect(() => {
    getShotlists();
    getLanguages();
    getLocations();
    getCurrencies();
    getCompaniesApi().then(response => {
      if (response.success) {
        const { items } = response;
        dispatch(
          onStateChangeAction({
            userCompanies: map(items, ({ CompanyName, UserCompanyId }) => {
              return { label: CompanyName, value: UserCompanyId };
            })
          })
        );
      }
    });
    getSavedSearchesApi().then(response => {
      if (response.success) {
        const { items } = response;
        dispatch(
          onStateChangeAction({
            savedSearches: map(items, ({ SearchName, SavedSearchId }) => {
              return { label: SearchName, value: SavedSearchId };
            })
          })
        );
      }
    });
    if (location.pathname.split("/").length > 2) {
      setState(prevState => ({
        ...prevState,
        updateTitleModal: { open: true, title: "Opportunity" }
      }));
    }
    setState(prevState => ({
      ...prevState,
      active: window.location.hash?.slice(1)
    }));
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);
  useEffect(() => {
    getOpportunities();
  }, [loadDraft, refresh]);
  const handleResize = () => {
    setState(prevState => ({ ...prevState, windowWidth: window.innerWidth }));
  };

  const getOpportunities = () => {
    dispatch(onStateChangeAction({ isFetching: true, shortlistResumes: [] }));
    setState(prevState => ({ ...prevState, isLoading: true }));
    getDraftOpportunitesApi({ isFreelancer })
      .then(response => {
        const { success, items } = response;
        if (success) {
          setState(prevState => ({
            ...prevState,
            updateTitleModal: {
              ...prevState.updateTitleModal,
              id: items?.Draft?.[0]?.RequestId,
              value: items?.Draft?.[0]?.RequestName
            }
          }));
          const opportunities = Array.isArray(items?.Draft) ? items.Draft : [];
          const filterData = items.Draft?.map(single => {
            return {
              ...single,
              label: single?.RequestName,
              value: single?.RequestId
            };
          });
          dispatch(
            onStateChangeAction({
              opportunities: filterData,
              filterOpportunires: filterData,
              isFetching: false
            })
          );
          setDraftLength(items?.Draft?.length);
        }
        setState(prevState => ({ ...prevState, isLoading: false }));
      })
      .catch(() => {
        setState(prevState => ({ ...prevState, isLoading: false }));
      });
    getSentOpportunitesApi({ isFreelancer })
      .then(response => {
        const {
          success,
          items: { Sent }
        } = response;
        if (success) {
          setSentLength(response?.items?.Sent?.length);
        }
      })
      .catch(() => {
        dispatch(onStateChangeAction({ isLoading: false }));
      });
  };

  const getShotlists = () => {
    getShortlistsApi()
      .then(response => {
        if (response.success) {
          const shortlist = response?.items?.map(item => ({
            ...item,
            value: item?.ShortlistId,
            label: item?.ShortlistName
          }));
          dispatch(
            onStateChangeAction({
              shortlists: shortlist,
              filterShortlists: shortlist
            })
          );
        }
      })
      .catch(response => response);
  };

  const getLocations = () => {
    getAllLocationsApi().then(response => {
      if (response.success) {
        const locations = response.items.map(item => ({
          ...item,
          value: item.CountryId,
          label: item.CountryName
        }));
        dispatch(onStateChangeAction({ locations }));
      }
    });
  };

  const getLanguages = () => {
    getAllLanguagesApi().then(response => {
      if (response.success) {
        const languages = response.items.map(item => ({
          ...item,
          value: item.LanguageId,
          label: item.LanguageValue
        }));
        dispatch(onStateChangeAction({ languages }));
      }
    });
  };

  const getCurrencies = () => {
    getCurrenciesApi()
      .then(res => {
        if (res.success) {
          const allCurrenciesList = res.items.map(item => ({
            ...item,
            value: item.CurrencyId,
            label: item.Name
          }));
          dispatch(onStateChangeAction({ allCurrenciesList }));
        }
      })
      .catch(err => console.log("Err ", err));
  };

  const handleOpportunityClick = ({ selectedOpportunity }) => {
    setState(prevState => ({
      ...prevState,
      // isLoading: true,
      showDetail: true
    }));
    const { RequestId } = selectedOpportunity;
    let selectedShortlist = null;
    dispatch(
      onStateChangeAction({
        isFetchingShortlist: true,
        currentOpportunityId: RequestId,
        isFetchingShortlistResume: true,
        selectedShortlist: [],
        selectedOpportunity: {
          ...selectedOpportunity
        },
        shortlistResumes: [],
        selectedResume: {}
      })
    );
    getDraftOpportunityDetailApi({ RequestId })
      .then(response => {
        if (response.success) {
          const { items } = response;
          const {
            ShortLists,
            UserCompanyId,
            SavedSearchedId,
            DurationType,
            Languages,
            Countries,
            StartDate,
            ...rest
          } = items;
          const UserCompany = filter(
            userCompanies,
            company => company.value === UserCompanyId
          )[0];
          const SavedSearches = filter(
            savedSearches,
            search => search.value === SavedSearchedId
          )[0];
          if (isEmpty(ShortLists)) {
            selectedOpportunity = {
              ...selectedOpportunity,
              Language: Languages,
              selectedLanguages: Languages?.map(single => single?.LanguageId),
              selectedLocation: Countries?.map(single => single?.CountryId),
              DurationType: DurationType || "Months",
              Countries,
              StartDate: moment(StartDate),
              FeeCurrencyType: allCurrenciesList.find(
                x => x.CurrencyId == items.FeeCurrencyType
              ),
              ...rest
            };
            setState(prevState => ({ ...prevState, isLoading: false }));
            dispatch(
              onStateChangeAction({
                selectedOpportunity,
                selectedShortlists: [],
                selectedShortlist: null,
                shortlistResumes: [],
                selectedResume: {},
                selectedCompany: UserCompany ? UserCompany : {},
                selectedSaveSearch: SavedSearches ? SavedSearches : {},
                isFetchingShortlist: false
              })
            );
            return;
          }
          selectedShortlist = head(ShortLists);

          dispatch(
            onStateChangeAction({
              selectedOpportunity: {
                ...selectedOpportunity,
                Language: Languages,
                selectedLanguages: Languages?.map(single => single?.LanguageId),
                selectedLocation: Countries?.map(single => single?.CountryId),
                selectedShortlists: ShortLists?.map(
                  single => single?.ShortlistId
                ),
                Countries,
                StartDate: moment(StartDate),
                DurationType: DurationType || "Months",
                FeeCurrencyType: allCurrenciesList.find(
                  x => x.CurrencyId == items.FeeCurrencyType
                ),
                ...rest
              },
              selectedCompany: UserCompany ? UserCompany : {},
              selectedSaveSearch: SavedSearches ? SavedSearches : {},
              selectedShortlist: ShortLists,
              isFetchingShortlist: false
            })
          );
          getShortlistResumesApi({ ShortlistId: selectedShortlist.ShortlistId })
            .then(response => {
              if (response.success) {
                const { items } = response;
                if (isEmpty(items)) {
                  const info = {
                    message: labels.InfoSearcherOpportunityDraftEmptyShortlist,
                    status: "info"
                  };
                  dispatch(notificationAction(info));
                  setState(prevState => ({ ...prevState, isLoading: false }));
                  return;
                }
                dispatch(
                  onStateChangeAction({
                    shortlistResumes: items,
                    selectedResume: head(items),
                    isFetchingShortlistResume: false
                  })
                );
              }
              setState(prevState => ({ ...prevState, isLoading: false }));
            })
            .catch(response => {
              setState(prevState => ({ ...prevState, isLoading: false }));
            });
        }
      })
      .catch(response => {
        setState(prevState => ({ ...prevState, isLoading: false }));
        dispatch(
          onStateChangeAction({
            isFetchingShortlist: false,
            isFetchingShortlistResume: false
          })
        );
      });
  };

  const handleDateChange = date => {
    try {
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            invalidStartDate: false,
            StartDate: date
          }
        })
      );
    } catch (e) {
      console.log(e);
    }
  };

  const handleSearchChange = e => {
    const { value } = e.target;
    const filterOpportunires = filter(opportunities, opportunity =>
      includes(toLower(opportunity.RequestName), toLower(value))
    );
    dispatch(onStateChangeAction({ filterOpportunires, searchKey: value }));
  };

  const handleOpportunityDelete = ({ selectedOpportunity, e }) => {
    dispatch(
      onStateChangeAction({
        opportunityToDelete: selectedOpportunity
      })
    );
  };

  const handleYesClick = () => {
    const { RequestId } = opportunityToDelete;
    deleteOpportunity({
      RequestId: RequestId
    });
  };

  const deleteOpportunity = ({ RequestId }) => {
    setState(prevState => ({ ...prevState, isLoading: true }));
    setLoading(true);
    deleteOpportunityApi({ RequestId })
      .then(response => {
        if (response.success) {
          setLoading(false);
          setDeleteModalOpen(false);
          const info = {
            message: labels.oppDeletedSuccessfully,
            status: "success"
          };
          dispatch(notificationAction(info));
          getOpportunities();
          dispatch(
            onStateChangeAction({
              isLoading: false,
              selectedOpportunity: null,
              opportunity: null
            })
          );
        }
        setState(prevState => ({ ...prevState, isLoading: false }));
        dispatch(
          onStateChangeAction({
            isLoading: false,
            selectedOpportunity: null,
            opportunity: null
          })
        );
      })
      .catch(response => {
        setLoading(false);
        setDeleteModalOpen(false);
        setState(prevState => ({ ...prevState, isLoading: false }));
        dispatch(
          onStateChangeAction({
            isLoading: false,
            selectedOpportunity: null,
            opportunity: null
          })
        );
      });
  };

  const handleNoClick = () => {
    if (
      dialogMessage ===
      labels.InfoSearcherOpportunityDraftOpportunityDeleteAssociationsConformationMsg
    ) {
      const { RequestId } = opportunityToDelete;
      dispatch(onStateChangeAction({ isLoading: true }));
      deleteOpportunity({ RequestId });
    }
    dispatch(onStateChangeAction({ dialogMessage: "", yesClickCount: 0 }));
  };

  const handleSearchBlur = () => {
    if (state.searchListHover) return;
    setState(prevState => ({ ...prevState, isSearchFocus: false }));
  };

  const handleShortListSelect = ({ selectedShortlist }) => {
    const alreadySelected = filter(
      selectedShortlists,
      shortlist => shortlist.ShortlistId === selectedShortlist.ShortlistId
    );
    if (isEmpty(alreadySelected)) {
      dispatch(
        onStateChangeAction({
          selectedShortlists: [...selectedShortlists, selectedShortlist]
        })
      );
      setState(prevState => ({ ...prevState, isSearchFocus: false }));
      return;
    }
    setState(prevState => ({ ...prevState, isSearchFocus: false }));
  };

  const handleFormSelectChange = (name, selectedOption) => {
    if (name === "selectedSavedSearch") {
      dispatch(
        onStateChangeAction({
          selectedSaveSearch: isNullOrUndefined(selectedOption)
            ? null
            : selectedOption,
          selectedOpportunity: {
            ...selectedOpportunity,
            invalidSelectedSavedSearch: false
          }
        })
      );
      return;
    }
    if (!selectedOption) return;
    if (name === "SelectedCompany") {
      dispatch(
        onStateChangeAction({
          selectedCompany: selectedOption,
          selectedOpportunity: {
            ...selectedOpportunity,
            invalidSelectedCompany: false
          }
        })
      );
      return;
    }
    if (name === "FeeCurrencyType") {
      setState(prevState => ({
        ...prevState,
        validation: {
          ...prevState.validation,
          feeCurrencyType: false
        }
      }));
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            invalidFeeCurrencyType: false,
            FeeCurrencyType: selectedOption
          }
        })
      );
      return;
    }
    if (name === "DurationType") {
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            invalidDurationType: false,
            DurationType: selectedOption
          }
        })
      );
      return;
    }
    dispatch(
      onStateChangeAction({
        selectedOpportunity: {
          ...selectedOpportunity,
          [name]: selectedOption
        }
      })
    );
  };

  const handleLanguageSelect = (e, name, option) => {
    dispatch(
      onStateChangeAction({
        selectedOpportunity: {
          ...selectedOpportunity,
          selectedLanguages: e,
          Language: option
        }
      })
    );
  };

  const handleLocationSelect = (e, name, option) => {
    dispatch(
      onStateChangeAction({
        selectedOpportunity: {
          ...selectedOpportunity,
          selectedLocation: e,
          Countries: option
        }
      })
    );
  };

  const handleShortlistSearchChange = e => {
    const { value } = e.target;
    const filterShortlists = filter(shortlists, shortlist =>
      includes(toLower(shortlist.ShortlistName), toLower(value))
    );
    dispatch(onStateChangeAction({ filterShortlists }));
  };

  const handleSelectedShortlistClick = option => {
    setState(prevState => ({ ...prevState, isLoading: true }));
    getShortlistResumesApi({ ShortlistId: option?.ShortlistId })
      .then(response => {
        if (response.success) {
          const { items } = response;
          const selectedResume = head(items);
          if (isEmpty(items)) {
            const info = {
              message: labels.InfoSearcherOpportunityDraftEmptyShortlist,
              status: "error"
            };
            dispatch(notificationAction(info));
          }
          dispatch(
            onStateChangeAction({
              selectedShortlist: [option],
              selectedOpportunity: {
                ...selectedOpportunity,
                selectedShortlists: option?.ShortlistId,
                [`invalidShortList`]: false
              },
              shortlistResumes: items,
              selectedResume,
              isFetchingShortlistResume: false
            })
          );
        }
        setState(prevState => ({ ...prevState, isLoading: false }));
      })
      .catch(response => {
        setState(prevState => ({ ...prevState, isLoading: false }));
      });
  };

  const validateField = ({ name, value }) => {
    dispatch(
      onStateChangeAction({
        selectedOpportunity: {
          ...selectedOpportunity,
          [`invalid${name}`]: !value,
          [name]: value
        }
      })
    );
  };

  const setFieldValue = ({ name, value }) => {
    dispatch(
      onStateChangeAction({
        selectedOpportunity: {
          ...selectedOpportunity,
          [name]: value
        }
      })
    );
  };

  const handleFormFieldChange = e => {
    const { name, value } = e.target;
    if (name === "RequestName") {
      validateField({ name, value });
      return;
    }
    if (name === "HourlyFee") {
      if (isNaN(value)) {
        return;
      }
      setState(prevState => ({
        ...prevState,
        validation: {
          ...prevState.validation,
          hourlyRate: false
        }
      }));
    }
    setFieldValue({ name, value });
  };

  const handleSliderChange = value => {
    if (value) {
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            Duration: value
          }
        })
      );
    }
  };

  const handleOkClick = () => {
    handleOpportunityUpdate({ IsSent: true });
    dispatch(onStateChangeAction({ dialogMessage: "" }));
  };

  const handleOpportunitySend = () => {
    const {
      RequestName,
      StartDate,
      DurationType,
      HourlyFee,
      FeeCurrencyType
    } = selectedOpportunity;
    if (!RequestName) {
      const info = {
        message: labels.InfoSearcherOpportunityDraftTitleRequiredMsg,
        status: "error"
      };
      validateField({ name: "RequestName", value: RequestName });
      dispatch(notificationAction(info));
      return;
    }
    if (!StartDate) {
      const info = {
        message: labels.InfoSearcherOpportunityDraftStartDateRequiredMsg,
        status: "error"
      };
      dispatch(notificationAction(info));
      validateField({ name: "StartDate", value: StartDate });
      return;
    }
    if (!DurationType) {
      const info = {
        message: labels.InfoSearcherOpportunityDraftDurationTypeRequiredMsg,
        status: "error"
      };
      dispatch(notificationAction(info));
      validateField({ name: "DurationType", value: DurationType });
      return;
    }
    if (!HourlyFee) {
      const info = {
        message: labels.searcher_phillip_automate_oppertunity_hourlyrate_alert,
        status: "error"
      };
      dispatch(notificationAction(info));
      setState(prevState => ({
        ...prevState,
        validation: {
          ...prevState.validation,
          hourlyRate: true
        }
      }));
      return;
    }
    if (!FeeCurrencyType) {
      const info = {
        message: labels.collCurrancyTypeRequired,
        status: "error"
      };
      dispatch(notificationAction(info));
      setState(prevState => ({
        ...prevState,
        validation: {
          ...prevState.validation,
          feeCurrencyType: true
        }
      }));
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            [`invalidFeeCurrencyType`]: !FeeCurrencyType,
            FeeCurrencyType: FeeCurrencyType
          }
        })
      );
      return;
    }
    if (isEmpty(selectedCompany)) {
      const info = {
        message: labels.InfoSearcherOpportunityDraftUserCompanyRequiredMsg,
        status: "error"
      };
      dispatch(notificationAction(info));
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            [`invalidSelectedCompany`]: true,
            selectedCompany: selectedCompany
          }
        })
      );
      return;
    }

    if (isEmpty(selectedShortlist)) {
      const info = {
        message: labels.InfoSearcherOpportunityDraftSendEmptyShortlistMsg,
        status: "error"
      };
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            [`invalidShortList`]: true
          }
        })
      );
      dispatch(notificationAction(info));
      return;
    }
    dispatch(
      onStateChangeAction({
        dialogMessage: labels.InfoSearcherOpportunityDraftSendConformationMsg
      })
    );
  };

  const handleOpportunityUpdate = ({ IsSent = false }) => {
    const { RequestName } = selectedOpportunity;

    if (!RequestName) {
      validateField({ name: "RequestName", value: RequestName });
      const info = {
        message: labels.InfoSearcherOpportunityDraftTitleRequiredMsg,
        status: "error"
      };
      dispatch(notificationAction(info));
      return;
    }

    if (IsSent) {
      if (selectedCompany && !selectedCompany.value) {
        const info = {
          message: labels.InfoSearcherOpportunityDraftCompanyRequiredMsg,
          status: "error"
        };
        dispatch(notificationAction(info));
        dispatch(
          onStateChangeAction({
            selectedOpportunity: {
              ...selectedOpportunity,
              invalidSelectedCompany: true
            }
          })
        );
        return;
      }
    }

    const LanguageIds = map(
      selectedOpportunity.Language,
      language => language.LanguageId
    );
    const LocationsIds = map(
      selectedOpportunity.Countries,
      country => country.CountryId
    );

    const ShortListIds = map(
      selectedShortlist,
      shortlist => shortlist.ShortlistId
    );

    const opportunity = {
      Countries: [],
      CreateDate: selectedOpportunity.CreateDate,
      Description: selectedOpportunity.Description,
      Duration: selectedOpportunity.Duration,
      FeeCurrencyType: selectedOpportunity.FeeCurrencyType
        ? typeof selectedOpportunity.FeeCurrencyType === "string"
          ? selectedOpportunity.FeeCurrencyType
          : selectedOpportunity.FeeCurrencyType.value
        : null,
      DurationType: selectedOpportunity.DurationType
        ? typeof selectedOpportunity.DurationType === "string"
          ? selectedOpportunity.DurationType
          : selectedOpportunity.DurationType.value
        : null,
      EndDate: selectedOpportunity.EndDate,
      HourlyFee: selectedOpportunity.HourlyFee,
      IsSent,
      Language: [],
      LanguageIds,
      LocationsIds,
      RequestCountries: null,
      RequestId: selectedOpportunity.RequestId,
      RequestName: selectedOpportunity.RequestName,
      SavedSearchedId: selectedSaveSearch ? selectedSaveSearch.value : null,
      ShortListIds,
      StartDate: selectedOpportunity.StartDate,
      UserCompanyId: selectedCompany ? selectedCompany.value : null
    };
    dispatch(onStateChangeAction({ opportunitySaveLoading: true }));
    saveOpportunity({ opportunity }).then(response => {
      if (response.success) {
        setCreateJobInvitation(false);
        setEditModalOpen(false);
        if (IsSent) {
          dispatch(onStateChangeAction({ selectedOpportunity: null }));
          const info = {
            message: labels.InfoSearcherOpportunityDraftSendSuccessfullyMsg,
            status: "success"
          };
          dispatch(notificationAction(info));
        } else {
          const info = {
            message: labels.InfoSearcherOpportunityDraftSavedSuccessfullyMsg,
            status: "success"
          };
          dispatch(notificationAction(info));
        }
        getOpportunities();
        dispatch(onStateChangeAction({ opportunitySaveLoading: false }));
        return;
      }

      const info = {
        message: response.message,
        status: "error"
      };
      dispatch(notificationAction(info));
      dispatch(onStateChangeAction({ opportunitySaveLoading: false }));
    });
  };

  const handleModalClick = data => {
    setState(prevState => ({
      ...prevState,
      data: data,
      detailModal: true
    }));
  };

  const handleCloseModal = () => {
    setState(prevState => ({
      ...prevState,
      data: "",
      detailModal: false
    }));
  };

  const handleListOpenMobile = () => {
    setState(prevState => ({ ...prevState, isResumeDetail: true }));
  };

  const handleUpdateTitle = () => {
    const opportunity = {
      RequestId: state.updateTitleModal.id,
      RequestName: state.updateTitleModal.value
    };
    saveOpportunity({ opportunity })
      .then(response => {
        setCreateJobInvitation(false);
        editModalOpen(false);
        if (response.success) {
          const info = {
            message:
              labels.InfoSearcherOpportunityDraftSavedUpdatedSuccessfullyMsg,
            status: "success"
          };
          dispatch(notificationAction(info));
          getOpportunities();
          setState(prevState => ({
            ...prevState,
            updateTitleModal: { open: false }
          }));
          return;
        }
        const info = {
          message: response.message,
          status: "error"
        };
        dispatch(notificationAction(info));
        setState(prevState => ({
          ...prevState,
          updateTitleModal: { open: false }
        }));
      })
      .catch(err => {
        console.log("Err", err);
      });
  };

  const updateTitle = e => {
    if (typeof e == "object") {
      setState(prevState => ({
        ...prevState,
        updateTitleModal: { open: false }
      }));
    } else {
      setState(prevState => ({
        ...prevState,
        updateTitleModal: { ...prevState.updateTitleModal, value: e()?.value }
      }));
    }
  };
  return (
    <div className="w-full h-full bg-white">
      {state?.isLoading && <LoadingMask />}
      {dialogMessage && (
        <ConfirmDialog testId="confirm-diloag">
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            {dialogMessage ===
            labels.InfoSearcherOpportunityDraftSendConformationMsg ? (
              <ConfirmDialog.Button
                testId="searcher-opportunity-draft-ok-btn"
                onClick={handleOkClick}
              >
                Yes
              </ConfirmDialog.Button>
            ) : (
              <ConfirmDialog.Button
                testId="searcher-opportunity-draft-yes-btn"
                onClick={handleYesClick}
              >
                Yes
              </ConfirmDialog.Button>
            )}
            <ConfirmDialog.Button
              testId="searcher-opportunity-draft-no-btn"
              onClick={handleNoClick}
            >
              No
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      <DeleteModal
        open={deleteModalOpen}
        onCancel={() => {
          setDeleteModalOpen(false);
        }}
        onConfirm={handleYesClick}
        isLoading={loading}
        title={labels?.Cancel_Invitation_Title}
        description={labels?.Cancel_Invitation_Description}
        cancelText={labels?.companyDeleteCurtainNOBtnText}
        confirmText={labels?.labels.delete}
      />
      {state?.detailModal == true && (
        <DetailModalCreateOpportunity
          handleCloseModal={handleCloseModal}
          open={state}
          selected={state?.selected}
        />
      )}
      <UpdateTitleModal
        open={state?.updateTitleModal}
        setOpen={updateTitle}
        handleUpdateTitle={handleUpdateTitle}
      />
      {window.innerWidth > 767 && draftActive ? (
        <Modal
          title={null}
          footer={null}
          closeIcon={null}
          closable={false}
          open={editModalOpen}
          centered
          onCancel={() => {
            setEditModalOpen(false);
            dispatch(onStateChangeAction({ selectedOpportunity: null }));
            dispatch(
              onStateChangeAction({
                shortlistResumes: null,
                selectedResume: null,
                isFetchingShortlistResume: false
              })
            );
          }}
          rootClassName={`
         ${
           shortlistResumes?.length > 0
             ? "md:[&_.ant-modal]:!w-[80%]"
             : "md:[&_.ant-modal]:!w-[40%]"
         }
         [&_.ant-modal-content]:!p-[0px]
       `}
        >
          <NewSearcherOpportunityForm
            state={state}
            shortlist={filterShortlists}
            handleSelectedShortlistClick={handleSelectedShortlistClick}
            selectedOpportunity={selectedOpportunity}
            selectedCompany={selectedCompany}
            selectedSaveSearch={selectedSaveSearch}
            userCompanies={userCompanies}
            savedSearches={savedSearches}
            languages={languages}
            labels={labels}
            isLoading={opportunitySaveLoading}
            allLocations={locations}
            allLanguages={languages}
            onFormSelectChange={handleFormSelectChange}
            onFormFieldChange={handleFormFieldChange}
            onDateChange={handleDateChange}
            onSliderChange={handleSliderChange}
            onLanguageSelect={handleLanguageSelect}
            onLocationSelect={handleLocationSelect}
            onOpportunityUpdate={handleOpportunityUpdate}
            onOpportunitySend={handleOpportunitySend}
            allCurrencies={allCurrenciesList}
            handleListOpenMobile={handleListOpenMobile}
            resumeList={shortlistResumes}
            score={0}
            setRefresh={setRefresh}
            setCreateJobInvitation={setCreateJobInvitation}
            handleModalClick={handleModalClick}
            setShortlistId={setShortlistId}
            name="opportunity"
            label="No Resume found"
            draftActive={draftActive}
            setEditModalOpen={setEditModalOpen}
            resetFormState={resetFormState}
          />
        </Modal>
      ) : (
        <Drawer
          placement="right"
          closable={true}
          open={editModalOpen}
          key="placement"
          headerStyle={{ display: "none" }}
          onClose={() => {
            editModalOpen(false);
            dispatch(onStateChangeAction({ selectedOpportunity: null }));
            dispatch(
              onStateChangeAction({
                shortlistResumes: null,
                selectedResume: null,
                isFetchingShortlistResume: false
              })
            );
          }}
          bodyStyle={{
            padding: 0,
            maxHeight: "955px"
          }}
          rootClassName=" 
          [&_.ant-drawer-content-wrapper]:!mt-[8px]
          [&_.ant-drawer-content]:!rounded-tl-[16px]
          [&_.ant-drawer-content]:!rounded-tr-[16px]
          [&_.ant-drawer-content-wrapper]:!w-[100%]
          [&_.ant-drawer-content-wrapper]:!rounded-[16px]
          [&_.ant-drawer-body]:p-[0px]
          [&_.ant-drawer-body]:!scrollbar-width-none"
        >
          <NewSearcherOpportunityForm
            state={state}
            shortlist={filterShortlists}
            handleSelectedShortlistClick={handleSelectedShortlistClick}
            selectedOpportunity={selectedOpportunity}
            selectedCompany={selectedCompany}
            selectedSaveSearch={selectedSaveSearch}
            userCompanies={userCompanies}
            savedSearches={savedSearches}
            languages={languages}
            labels={labels}
            isLoading={opportunitySaveLoading}
            allLocations={locations}
            allLanguages={languages}
            onFormSelectChange={handleFormSelectChange}
            onFormFieldChange={handleFormFieldChange}
            onDateChange={handleDateChange}
            onSliderChange={handleSliderChange}
            onLanguageSelect={handleLanguageSelect}
            onLocationSelect={handleLocationSelect}
            onOpportunityUpdate={handleOpportunityUpdate}
            onOpportunitySend={handleOpportunitySend}
            allCurrencies={allCurrenciesList}
            handleListOpenMobile={handleListOpenMobile}
            resumeList={shortlistResumes}
            score={0}
            setRefresh={setRefresh}
            setCreateJobInvitation={setCreateJobInvitation}
            handleModalClick={handleModalClick}
            setShortlistId={setShortlistId}
            draftActive={draftActive}
            name="opportunity"
            label="No Resume found"
            setEditModalOpen={setEditModalOpen}
            resetFormState={resetFormState}
          />
        </Drawer>
      )}
      <div className="h-full new-design-search w-full flex flex-col">
        {/* <div className="flex w-full flex-col h-full bg-white overflow-y-auto [scrollbar-width:'none'] [-ms-overflow-style:'none'] [&::-webkit-scrollbar]:hidden"> */}
        <div className="flex w-full flex-col h-full bg-white overflow-y-auto">
          <div className="w-full h-full">
            <div className="h-full flex flex-col">
              <>
                <div>
                  {filterOpportunires?.length > 0 ? (
                    <div className="w-full flex flex-col gap-1 mb-4">
                      {filterOpportunires?.map((item, id) => (
                        <div
                          key={id}
                          onClick={e => {
                            dispatch(onStateChangeAction({ isLoading: false }));
                            e.stopPropagation();
                            handleOpportunityClick({
                              selectedOpportunity: item
                            });
                            // setCreateJobInvitation(true);
                          }}
                          className="flex justify-between w-full rounded-2xl border-[0.5px] min-h-[60px] border-[#EAE5FC] px-4 py-4 items-center pointer hover:bg-gray-50"
                        >
                          <div className="flex gap-2">
                            <div className="flex flex-col gap-2">
                              <label className="text-[#343333] text-sm font-medium leading-[16px]">
                                {item.label}
                              </label>
                              <p className="flex gap-3 text-[#878787] text-xs font-normal leading-[100%]">
                                Date: {formatDate(item?.UpdatedOn)}
                                <span>Time: {formatTime(item?.UpdatedOn)}</span>
                              </p>
                            </div>
                          </div>
                          <Dropdown menu={{ items }} trigger={["click"]}>
                            <a onClick={e => e.preventDefault()}>
                              <Space>
                                <MoreOutlined
                                // onClick={(e) => {
                                //   handleOpportunityClick({
                                //     selectedOpportunity: items,
                                //   });
                                // }}
                                />
                              </Space>
                            </a>
                          </Dropdown>
                        </div>
                      ))}
                    </div>
                  ) : (
                    ""
                  )}
                </div>
              </>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewSearcherOpportunityDraft;
