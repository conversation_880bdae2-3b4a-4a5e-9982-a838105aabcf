{"name": "prodoo-web-latest", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "build": "vite build", "lint": "eslint 'src/**/*.{js,jsx}'", "preview": "vite preview", "deadfile": "deadfile src/Routes/Routes.jsx --dir src --output report.json --exclude assets", "depcheck": "depcheck", "unused-sass": "find-unused-sass-variables src"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@microsoft/signalr": "^9.0.6", "@react-oauth/google": "^0.12.2", "@reduxjs/toolkit": "^2.5.1", "@szhsin/react-accordion": "^1.4.0", "@tailwindcss/vite": "^4.0.14", "@tanstack/react-query": "^5.66.8", "@tippy.js/react": "^2.2.3", "antd": "^5.24.2", "antd-phone-input": "^0.3.13", "apexcharts": "^4.4.0", "axios": "^0.27.2", "blueimp-load-image": "^5.16.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "html-react-parser": "^5.2.2", "jwt-decode": "^4.0.0", "lodash": "^4.17.10", "lottie-react": "^2.4.1", "lucide-react": "^0.525.0", "moment": "^2.24.0", "pace-progressbar": "^1.0.8", "prettier": "^1.11.1", "rc-menu": "^7.0.5", "rc-slider": "^8.6.3", "rc-time-picker": "^3.7.3", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-datepicker": "^8.7.0", "react-device-detect": "^2.2.3", "react-dom": "^19.0.0", "react-dropzone": "^7.0.1", "react-hook-form": "^7.54.2", "react-infinite-scroll-component": "^6.1.0", "react-multi-carousel": "^2.8.2", "react-overlays": "^1.1.1", "react-quill-new": "^3.3.3", "react-redux": "^9.2.0", "react-responsive-carousel": "^3.1.46", "react-router-dom": "^6.28.2", "react-router-hash-link": "^2.4.3", "react-select": "^5.10.0", "react-star-ratings": "^2.3.0", "react-timer-hook": "^3.0.5", "react-to-print": "^3.1.1", "redux": "^5.0.1", "redux-logger": "^3.0.6", "redux-thunk": "^3.1.0", "rollup-plugin-visualizer": "^6.0.3", "sanitize-html": "^2.14.0", "sass": "^1.83.4", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.4", "uuid": "^11.1.0"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@eslint/js": "^9.17.0", "@tanstack/eslint-plugin-query": "^5.66.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "deadfile": "^2.1.1", "depcheck": "^1.4.7", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "eslint-plugin-unused-imports": "^4.2.0", "find-unused-sass-variables": "^6.0.0", "globals": "^15.14.0", "react-codemod": "^5.4.4", "vite": "^6.2.1", "vite-plugin-svgr": "^4.3.0"}}