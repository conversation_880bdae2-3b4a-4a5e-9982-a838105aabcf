import { useDispatch, useSelector } from "react-redux";
import { resumeChangeAction } from "../../actions/resumeActions";
import { logoutApi } from "../../components/Navigation/navigationApi";
import { logoutAction } from "../../store/initialConfig";
import axios from "axios";
import { StorageService } from "../../api/storage";

export const useLogout = () => {
  const dispatch = useDispatch();
  const {
    systemLabel,
    resume: { isResumeChange }
  } = useSelector(state => state);
  const { labels } = systemLabel;

  const handleLogout = () => {
    if (isResumeChange) {
      dispatch(
        resumeChangeAction({
          isResumeChange,
          message: labels.RESUME_EDIT_UNSAVED_CHANGES_MESSAGE,
          isLogoutTrigger: true
        })
      );
      return;
    }
    logout();
  };

  const logout = () => {
    logoutApi().then(response => {
      if (response.success) {
        delete axios.defaults.headers.common[".ASPXAUTH"];
        dispatch(logoutAction());
        let windowLocation = window.location.href;
        StorageService.clearAll();
        if (
          windowLocation.indexOf("/apps/") >= 0 ||
          windowLocation.indexOf("app-login") >= 0 ||
          windowLocation.indexOf("/Apps/") >= 0
        ) {
          window.location.href = windowLocation;
        } else {
          // window.location.href = RESET_LANDINGPAGE_URL;
        }
      }
    });
  };
  return {
    handleLogout
  };
};
