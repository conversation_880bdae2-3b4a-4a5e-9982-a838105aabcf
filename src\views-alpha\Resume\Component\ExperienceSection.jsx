import { useSelector } from "react-redux";
import { getYear } from "../../../utilities-alpha/helper";
import { htmlParser } from "../../../utilities/helpers";
import { Profiles } from "../constant";
import { ShowMoreSection } from "./LanguagesSection";
import { twMerge } from "tailwind-merge";
// Experience Section
const ExperienceSection = ({ userData, openDrawer }) => {
  const resume = useSelector(state => state.systemLabel.labels?.resume);
  return (
    <ShowMoreSection
      title={resume?.experience}
      items={userData?.items?.ResumeExperience || []}
      openDrawer={openDrawer}
      addEditAction={Profiles.experience}
      itemKeyId="ResumeExperienceId"
      viewMore
      itemValueKey="JobTitle" // Using JobTitle for the main display value
      emptyMessage={resume?.experienceEmptyMessage}
      addButtonAction={Profiles.experience}
      renderItem={exp => {
        const experienceFields = [
          {
            label: resume?.experienceYear,
            value: `${getYear(exp.StartDate)}-${getYear(exp.EndDate) ||
              resume?.ongoing}`
          },
          { label: resume?.experienceCompany, value: exp.IndustryValue },
          { label: resume?.experiencePosition, value: exp.ProfileValue }
        ];
        return (
          <InfoItemCard
            fields={experienceFields}
            description={exp.Description}
            itemKey={exp.ResumeExperienceId}
          />
        );
      }}
    />
  );
};

export default ExperienceSection;

export const InfoItemCard = ({
  fields = [],
  description,
  itemKey,
  labelClassName
}) => {
  return (
    <div
      key={itemKey}
      className="border bg-[#F3F1FD] border-[#EAE5FC] w-full rounded-lg overflow-hidden text-[#878787]"
    >
      <div className="flex flex-col gap-x-4">
        {fields.map(({ label, value }, idx) =>
          value ? (
            <span
              key={idx}
              className="border-b border-b-[#EAE5FC] px-4 py-3 text-sm flex items-center"
            >
              <label
                className={twMerge(
                  "block min-w-[80px] border-r border-r-[#EAE5FC] mr-3 ",
                  labelClassName
                )}
              >
                {label}
              </label>
              <span className="text-[#343333] truncate">{value}</span>
            </span>
          ) : null
        )}
      </div>
      {description && (
        <div className="px-4 py-3">
          <p className="text-sm text-[#343333]">
            {typeof description === "string"
              ? htmlParser(description)
              : description}
          </p>
        </div>
      )}
    </div>
  );
};
