@use "../../../assets/sass/importFiles" as *;

.product-menus {
  margin-top: 10px;

  @include breakpoint(screen767) {
    // position: absolute;
    top: 35px;
    left: 0;
    width: 100%;
    height: 250px;
    background: #f1edff;
    z-index: 2;
    padding-top: 10px;
    padding-bottom: 10px;
    margin-bottom: 20px;
    min-height: 60px;
  }
  > div {
    align-self: center;
  }
  .help-icon {
    bottom: 10px;
  }
  .common-background-img {
    background-size: contain !important;
    content: "";
    min-height: 100%;
    width: 20px;
    height: 20px;
    margin-left: 15px;
  }
  .newMessage {
    left: 0px;
  }

  .leftNavIcon {
    border-radius: 50%;
    min-width: 50px;
    text-align: center;
    height: 50px;
    display: flex;
    align-items: center;
    margin-right: 10px;
    background-color: transparent;
    border: 1px solid $purple4;
    &:before {
      /* use !important to prevent issues with browser extensions that change fonts */
      font-family: "icomoon" !important;
      speak: none;
      font-style: normal;
      font-weight: normal;
      font-variant: normal;
      text-transform: none;
      line-height: 1;

      /* Better Font Rendering =========== */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      content: "";
      display: inline-block;
      margin: 0 auto;
      // @extend %fi;
      font-size: 30px;
      color: $purple4; // content: $fi-profile;
      @media not all and (min-resolution: 0.001dpcm) {
        @supports (-webkit-appearance: none) {
          font-size: 35px;
          margin-top: 4px;
        }
      }
    }
    &:hover,
    &.activeBtn {
      background-color: $purple4;
    }
    @media (max-width: 330px) {
      min-width: 35px;
      height: 35px;
    }
  }

  .snapshot-home-icon {
    @extend .leftNavIcon;
    &:before {
      content: $fi-Snapshot-filled;
    }
  }
  .snapshot-reports-icon {
    @extend .leftNavIcon;
    &:before {
      content: $fi-Approve;
    }
  }
  .snapshot-collaborations-icon {
    @extend .leftNavIcon;
    &:before {
      content: $fi-Collaboration;
    }
  }
  .snapshot-pools-icon {
    @extend .leftNavIcon;
    &:before {
      content: $fi-Pool;
    }
  }
}
