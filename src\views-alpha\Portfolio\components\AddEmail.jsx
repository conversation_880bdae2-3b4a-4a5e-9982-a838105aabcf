import { Button, Input, Modal } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { useState } from "react";
import { AddUserEmail } from "../portfolioApi";
import { notificationAction } from "../../../actions/notification";

const AddEmail = ({ open, setOpen, hideAddEmailModal, title, setRefresh }) => {
  const [error, setError] = useState(false);
  const [value, setValue] = useState("");
  const dispatch = useDispatch();
  const labels = useSelector(state => state.systemLabel.labels);
  const handleCancel = () => {
    setOpen(false);
  };

  const handleAddEmail = () => {
    AddUserEmail({
      UserEmailValue: value
    })
      .then(response => {
        setRefresh(prev => !prev);
        hideAddEmailModal();
        const info = {
          message: response?.message,
          status: "info"
        };
        setValue("");
        getNumber();
        dispatch(notificationAction(info));
      })
      .catch(response => response);
  };

  return (
    <>
      <Modal
        zIndex={9999}
        open={open}
        setOpen={setOpen}
        onCancel={hideAddEmailModal}
        centered
        footer={null}
        width={448}
        closable={false}
        rootClassName="[&_.ant-modal-content]:!p-0"
      >
        <div className="border-b border-[#2f29292f] px-[18px] py-[14px] flex justify-between items-center text-center h-[66px]">
          <h5 className="!m-0 text-[18px] font-semibold">{title}</h5>
        </div>
        <div className="pl-6 pr-6 pt-5 pb-6">
          <div className="w-full mt-0">
            <Input
              className="!rounded-[99px] !text-[14px] font-normal !bg-[#F3F1FD] pl-4 placeholder:!text-[#878787] placeholder:!text-[14px] placeholder:!font-normal !h-10"
              placeholder="<EMAIL>"
              value={value || ""}
              autoFocus={true}
              maxLength={50}
              size="large"
              onChange={e => {
                const input = e.target.value;
                setValue(input);

                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(input)) {
                  setError(true);
                } else {
                  setError(false);
                }
              }}
            />
            {error && (
              <p className="text-red-500 mt-[4px] !text-[12px]">
                {labels?.Email_Error_Message}
              </p>
            )}
          </div>

          <div className="flex w-full gap-2 justify-end">
            <Button
              type="default"
              block
              size="large"
              htmlType="submit"
              className=" !text-[14px] !w-[107px] !bg-[#FFFFFF] !border !border-[#EAE5FC] !text-[#343333] mt-4 !rounded-[8px] !h-10"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              block
              size="large"
              htmlType="submit"
              className="border !w-[93px] !text-[14px] border-[#EAE5FC] text-white mt-4 !rounded-[8px] !h-10"
              onClick={handleAddEmail}
            >
              Save
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};
export default AddEmail;
