import HappycustomerImage from "../../../../assets-alpha/images/svg/happy-customers.svg?react";
import HappycustomerImageMbl from "../../../../assets-alpha/images/svg/happy-customer-mobile.svg?react";
import { Button } from "antd";
import { useNavigate } from "react-router-dom";
import { publicRoutes } from "../../../../Routes/routing";
import { useEffect, useState } from "react";

const Home = ({ labels }) => {
  const navigate = useNavigate();
  const [isLarge, setIsLarge] = useState(false);
  const [isXL, setIsXL] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsLarge(window.innerWidth > 1376);
      setIsXL(window.innerWidth > 2200);
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div className="!pt-[56px] md:!pt-[102px] flex flex-col items-start justify-between min-h-[100vh] md:items-center w-full md:p-0 p-4">
      <div className="flex flex-col w-full items-center">
        <div className="hidden md:flex items-center justify-center w-[543px] h-[38px] bg-white rounded-[99px]">
          <span className="flex gap-1 font-medium text-xs text-[#343333]">
            💸 <p className="text-[#8E81F5]">{labels?.Big_News_Label}</p>{" "}
            {labels?.News_Desc}
          </span>
        </div>

        <div className="!pt-3 w-full md:!w-[82%] md:text-center text-start">
          {/* Desktop heading */}
          <h1 className="hidden md:block !m-0 !font-bold !text-[60px] text-[#343333] !leading-[70px]">
            {labels?.Hero_Heading_1}{" "}
            <span className="text-[#02CAA8]">{labels?.Hero_Heading_2}</span>{" "}
            {labels?.Hero_Heading_3}{" "}
            <span className="text-[#8E81F5]">{labels?.Hero_Heading_4}</span>,{" "}
            <br /> {labels?.Hero_Heading_5}
          </h1>

          {/* Mobile heading */}
          <h2 className="md:hidden block !m-0 !font-bold !text-[40px] text-[#343333] !leading-[52px] !tracking-[-2%]">
            {labels?.Hero_Heading_1}{" "}
            <span className="text-[#02CAA8]">{labels?.Hero_Heading_2}</span>{" "}
            <br /> {labels?.Hero_Heading_3}{" "}
            <span className="text-[#8E81F5]">{labels?.Hero_Heading_4}</span>,{" "}
            <br />
            {labels?.Hero_Heading_Mbl_4} <br /> {labels?.Hero_Heading_Mbl_5}{" "}
            <br /> {labels?.Hero_Heading_Mbl_6}
          </h2>
        </div>

        <div className="mt-3 w-full md:w-[45%]">
          <p className="!m-0 text-start md:text-center text-[13px] md:text-[16px] font-normal text-[#878787] leading-[28px]">
            {labels?.Hero_Desc}
          </p>
        </div>

        {/* Customer Images */}
        <div className={`mt-[24px] ${isLarge ? "block" : "hidden"}`}>
          <HappycustomerImage />
        </div>
        <div className="mt-[18px] md:hidden w-full flex justify-center">
          <HappycustomerImageMbl />
        </div>
      </div>

      {/* Hero Image */}
      <div className="hidden md:flex justify-center w-full gap-4">
        <div className="w-full flex justify-center px-[50px]">
          <img
            src="/assets/images/hero-image.webp"
            alt="hero image"
            style={{ width: isXL ? "1600px" : "1400px" }}
            className="w-[800px] lg:w-[900px] xl:w-[1000px] xxl:w-[1200px] 2xl:w-[1400px]"
          />
        </div>
      </div>

      {/* Mobile Buttons */}
      <div className="flex flex-col w-full justify-center md:hidden gap-3 pb-[82px]">
        <Button
          className="w-full !h-10 !rounded-[12px]"
          type="primary"
          onClick={() => navigate(publicRoutes.signup.path)}
        >
          {labels?.Get_Started_Lbl}
        </Button>
        <Button
          className="w-full !h-10 !rounded-[12px] !bg-white !text-[#343333]"
          type="default"
          onClick={() => navigate(publicRoutes.login.path)}
        >
          {labels?.loginLoginButton}
        </Button>
      </div>
    </div>
  );
};

export default Home;
