import "./column.scss";
import Button from "../Button/Button";
import { Tooltip } from "../Tooltip/Tooltip";

const Head = ({ children, className }) => (
  <div className={`column-head ${className}`}>{children}</div>
);

const HeaderTitle = ({ isActive, children, testId }) => (
  <div className="heading" data-testid={testId}>
    {isActive && children}
  </div>
);

const Body = ({ children, className }) => (
  <div className={`column-body ${className}`}>{children}</div>
);

const Collapsed = ({
  onClick,
  text,
  tooltipButton,
  children,
  isHelpActive,
  tooltipHelp,
  tooltipPlace,
  testId,
  testIdHelp,
  className
}) => (
  <div className={`column-collapsed`}>
    <Button
      onClick={onClick}
      tooltipButton={tooltipButton}
      className={`collapseBtn expandBtn ${className}`}
      isHelpActive={isHelpActive}
      tooltipHelp={tooltipHelp}
      tooltipPlace={tooltipPlace}
      testId={testId}
      testIdHelp={testIdHelp}
    />

    {isHelpActive ? (
      <div className="expand-heading" onClick={onClick}>
        {text}
      </div>
    ) : (
      <Tooltip
        content={tooltipButton}
        // trigger={!isMobile ? "mouseenter" : "focus"}
        followCursor
      >
        <div className="expand-heading" onClick={onClick}>
          {text}
        </div>
      </Tooltip>
    )}

    {children}
  </div>
);

const Column = props => {
  const { className, children, collapse, tooltip, testId } = props;
  return (
    <div
      data-testid={testId}
      className={`page-column ${className} ${
        collapse ? "page-column-collapse" : ""
      }`}
    >
      {children}
    </div>
  );
};

Column.Head = Head;
Column.Body = Body;
Column.Collapsed = Collapsed;
Column.HeaderTitle = HeaderTitle;

Column.defaultProps = {
  className: ""
};
Column.Head.defaultProps = {
  className: ""
};
Column.Body.defaultProps = {
  className: ""
};

export default Column;
