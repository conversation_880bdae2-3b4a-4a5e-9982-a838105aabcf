import { Button, Input } from "antd";
import {
  useScrollAnimation,
  useStaggeredAnimation
} from "../../hooks/useScrollAnimation";

import {
  Send,
  Clock,
  CheckCircle2,
  Shield,
  Heart,
  Award
} from "lucide-react";
import ArrowRight from "../../../../assets-alpha/images/svg/arrow-right.svg?react";
import { isValidEmail } from "../../../../utilities/helpers";
import { visitorContactSubmitApi } from "../../landingPageApi";
import { useDispatch, useSelector } from "react-redux";
import { useState } from "react";
import { notificationAction } from "../../../../actions-alpha/notification";
import VideoSection from "../Video/VideoSection";
import Apps from "../Apps/Apps";

const Contact = ({
  setPrivacyPolicyDrawerOpen,
  setTermsAndServicesDrawerOpen
}) => {
  const labels = useSelector(state => state.systemLabel.labels);
  const dispatch = useDispatch();
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    description: ""
  });
  const submitContact = ({ formData }) => {
    const { firstName, lastName, email, description } = formData;
    const validEmail = isValidEmail(email);
    if (
      firstName !== "" &&
      lastName !== "" &&
      description !== "" &&
      validEmail
    ) {
      visitorContactSubmitApi({
        firstName,
        lastName,
        email,
        description
      }).then(res => {
        setFormData("");
        if (res.success) {
          const info = {
            message: labels?.Msg_Sent_success,
            status: "success"
          };
          dispatch(notificationAction(info));
        }
      });
    } else {
      const info = {
        message:
          !validEmail &&
          firstName !== "" &&
          lastName !== "" &&
          description !== ""
            ? labels?.ValidEmail_lbl
            : labels?.Fill_Fields_err,
        status: "error"
      };
      dispatch(notificationAction(info));
    }
  };
  const {
    elementRef: headerRef,
    isVisible: headerVisible
  } = useScrollAnimation();
  const {
    elementRef: benefitsRef,
    isVisible: benefitsVisible
  } = useScrollAnimation();
  const {
    containerRef: statsRef,
    visibleItems: statItems
  } = useStaggeredAnimation(3, 400, 150);
  const { elementRef: formRef, isVisible: formVisible } = useScrollAnimation();

  const stats = [
    { number: "", label: labels?.Stat_1, icon: Clock },
    { number: "", label: labels?.Stat_2, icon: Award },
    { number: "", label: labels?.Stat_3, icon: Shield },
    { number: "", label: labels?.Stat_4, icon: Heart }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <section className="pt-8 lg:pt-24 bg-[#FCFCFF] relative overflow-hidden">
      <div className="absolute inset-0 opacity-[0.03]">
        <div
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(to right, var(--company) 1px, transparent 1px),
              linear-gradient(to bottom, var(--company) 1px, transparent 1px),
              linear-gradient(to right, var(--primary) 1px, transparent 1px),
              linear-gradient(to bottom, var(--primary) 1px, transparent 1px)
            `,
            backgroundSize: "40px 40px, 40px 40px, 80px 80px, 80px 80px",
            backgroundPosition: "0 0, 0 0, 20px 20px, 20px 20px"
          }}
        ></div>
      </div>
      <div className="max-w-7xl mx-auto relative z-10 pb-6 lg:pb-[64px] px-4">
        <div
          ref={headerRef}
          className={`text-center !mb-8 md:mb-20 animate-on-scroll ${
            headerVisible ? "animate-visible" : ""
          }`}
        >
          <h2 className="flex w-full gap-3 justify-center text-2xl md:text-5xl mb-4 md:mb-6 leading-tight">
            <span className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
              {labels?.Contact_h1}
            </span>
            <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
              {labels?.Contact_h2}
            </span>
          </h2>

          <div className="flex w-full justify-center text-center">
            <p className="text-xs md:text-[16px] text-[#878787] leading-relaxed">
              {labels?.Contact_Desc}
            </p>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 items-start">
          <div className="space-y-8 md:block hidden">
            <div
              ref={benefitsRef}
              className={`bg-transparent animate-on-scroll ${
                benefitsVisible ? "animate-visible" : ""
              }`}
            >
              <div className="absolute inset-0 opacity-5">
                <div
                  className="w-full h-full"
                  style={{
                    backgroundImage: `radial-gradient(circle at 3px 3px, var(--primary) 1px, transparent 0)`,
                    backgroundSize: "30px 30px"
                  }}
                ></div>
              </div>

              <div className="relative z-10">
                <div className="flex flex-col items-center gap-3 mb-6">
                  <div className="w-12 h-11 md:h-12 bg-gradient-to-r from-primary to-primary/80 rounded-xl flex items-center justify-center">
                    <CheckCircle2 className="w-6 h-6 text-primary-foreground" />
                  </div>
                  <div>
                    <h3 className="text-[20px] text-[#343333] font-medium">
                      {labels?.Contact_h3}
                    </h3>
                    <p className="text-sm font-normal text-muted-foreground">
                      {labels?.Contact_h4}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex w-full gap-4 flex-wrap">
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className="flex justify-center items-center flex-col w-[48%] h-[235px] bg-gradient-to-br from-card to-card/80 border border-primary/10 rounded-2xl text-center group hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                >
                  <div className="w-12 h-11 md:h-12 bg-[#F4F2FE] rounded-xl flex items-center justify-center mx-auto mb-3  transition-all duration-300">
                    <stat.icon className="w-6 h-6 text-[#8E81F5] group-hover:text-primary transition-colors duration-300" />
                  </div>
                  <div className="text-2xl font-bold text-primary mb-1">
                    {stat.number}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div
            ref={formRef}
            className={`bg-gradient-to-br from-card to-card/80 border border-primary/10 rounded-3xl p-4 md:p-6 shadow-xl relative overflow-hidden animate-on-scroll ${
              formVisible ? "animate-visible" : ""
            }`}
          >
            <div className="absolute inset-0 opacity-5">
              <div
                className="w-full h-full"
                style={{
                  backgroundImage: `linear-gradient(45deg, var(--primary) 1px, transparent 1px), linear-gradient(-45deg, var(--primary) 1px, transparent 1px)`,
                  backgroundSize: "30px 30px"
                }}
              ></div>
            </div>

            <div className="relative z-10">
              <div className="mb-8">
                <div className="flex items-center gap-4 mb-4">
                  <div className="!w-9 !h-9 md:!w-14 md:!h-14 bg-gradient-to-r from-primary to-primary/80 rounded-[7px] md:rounded-2xl flex items-center justify-center shadow-lg">
                    <Send className="w-4 h-4  md:w-7 md:h-7 text-primary-foreground" />
                  </div>
                  <div>
                    <h3 className="txt-[13px] font-medium md:text-2xl text-[#343333]">
                      {labels?.Send_US_Msg_Lbl}
                    </h3>
                    <p className="text-[10px] font-normal md:text-sm text-[#878787]">
                      {labels?.Respond_Time_Lbl}
                    </p>
                  </div>
                </div>
              </div>

              <form className="space-y-6">
                <div className="grid sm:grid-cols-2 gap-4">
                  <div className="group">
                    <label className="flex items-center text-[13px] md:text-sm font-mormal mb-2">
                      {labels?.FIRSTNAME_LABEL}
                    </label>
                    <Input
                      placeholder={labels?.USER_SETTING_FIRSTNAME_PLACEHOLDER}
                      value={formData.firstName}
                      onChange={e =>
                        handleInputChange("firstName", e.target.value)
                      }
                      className="bg-input-background border-primary/20 rounded-xl h-11 md:h-12 focus:border-primary transition-all duration-300 focus:shadow-lg focus:shadow-primary/10 placeholder:text-[12px] md:placeholder:text-[13px]"
                    />
                  </div>
                  <div className="group">
                    <label className="flex items-center text-[13px] md:text-sm font-mormal mb-2">
                      {labels?.LASTNAME_LABEL}
                    </label>
                    <Input
                      placeholder={labels?.USER_SETTING_LASTNAME_PLACEHOLDER}
                      value={formData.lastName}
                      onChange={e =>
                        handleInputChange("lastName", e.target.value)
                      }
                      className="bg-input-background border-primary/20 rounded-xl h-11 md:h-12 focus:border-primary transition-all duration-300 focus:shadow-lg focus:shadow-primary/10 placeholder:text-[12px] md:placeholder:text-[13px]"
                    />
                  </div>
                </div>

                <div className="group">
                  <label className="flex items-center text-[13px] md:text-sm font-mormal mb-2">
                    {labels?.USER_SETTING_LIST_PROFILE_EMAIL_HEADING}
                  </label>
                  <Input
                    type="email"
                    placeholder={labels?.LANDING_EMAIL_LBL_PLACEHOLDER}
                    value={formData.email}
                    onChange={e => handleInputChange("email", e.target.value)}
                    className="bg-input-background border-primary/20 rounded-xl h-11 md:h-12 focus:border-primary transition-all duration-300 focus:shadow-lg focus:shadow-primary/10 placeholder:text-[12px] md:placeholder:text-[13px]"
                  />
                </div>

                <div className="group">
                  <label className="flex items-center text-[13px] md:text-sm font-mormal mb-2">
                    {labels?.Your_Message_Lbl}
                  </label>
                  <Input.TextArea
                    placeholder={labels?.Message_Desc_Contact}
                    value={formData.description}
                    onChange={e =>
                      handleInputChange("description", e.target.value)
                    }
                    className="!h-[124px] !pt-2 !rounded-[8px] bg-input-background border-primary/20 min-h-[120px] resize-none focus:border-primary transition-all duration-300 focus:shadow-lg focus:shadow-primary/10 placeholder:text-[12px] md:placeholder:text-[13px]"
                  />
                </div>

                <Button
                  type="primary"
                  className="w-full !h-10 lg:!h-11 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary text-primary-foreground py-4 rounded-xl group shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02] relative overflow-hidden"
                  onClick={() => {
                    submitContact({ formData });
                  }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>
                  <span className="relative flex items-center justify-center gap-3">
                    {labels?.SEND_MESSAGE_LBL}
                    <ArrowRight />
                  </span>
                </Button>

                <p className=" text-xs text-muted-foreground text-center leading-relaxed">
                  {labels?.Privacy_1}{" "}
                  <span
                    className="text-primary hover:underline cursor-pointer font-medium"
                    onClick={() => {
                      setPrivacyPolicyDrawerOpen(true);
                    }}
                  >
                    {labels?.Footer_Privacy_Lbl}
                  </span>{" "}
                  {labels?.And_Lbl}{" "}
                  <span
                    className="text-primary hover:underline cursor-pointer font-medium"
                    onClick={() => {
                      setTermsAndServicesDrawerOpen(true);
                    }}
                  >
                    {labels?.Footer_Terms_Lbl}
                  </span>
                  .
                  <br className="hidden md:block" /> {labels?.Privacy_p1}
                  {labels?.Privacy_p2}
                </p>
              </form>
            </div>
          </div>
        </div>
      </div>
      <div className="w-full bg-[#F4F2FE]">
        <div className="absolute top-1/4 left-1/6 w-64 h-64 bg-gradient-radial from-company/15 via-company/5 to-transparent rounded-full blur-3xl animate-pulse"></div>
        <Apps />
        <VideoSection />
      </div>
    </section>
  );
};
export default Contact;
