import Slider from "rc-slider";
import { Button, Input, Select } from "antd";
const { TextArea } = Input;
import CalendarIcon from "../../../../../assets-alpha/images/svg/calendar-icon.svg?react";
import ArrowDown from "../../../../../assets-alpha/images/svg/arrow-down-1.svg?react";
import LocationIcon from "../../../../../assets-alpha/images/svg/location-dark.svg?react";
import CloseIcon from "../../../../../assets-alpha/images/svg/close-modal.svg";
import ShortListResume from "./resumeResumeList/index";
import { useEffect, useRef, useState } from "react";
import { DatePicker } from "antd";
import dayjs from "dayjs";
import { onStateChangeAction } from "../../createNewOpportunityAction";
import { useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
const NewSearcherOpportunityForm = props => {
  const {
    state,
    selectedOpportunity,
    labels,
    handleSelectedShortlistClick,
    selectedCompany,
    userCompanies,
    shortlist,
    onFormFieldChange,
    onFormSelectChange,
    onOpportunityUpdate,
    onDateChange,
    onLocationSelect,
    onOpportunitySend,
    allLocations,
    allCurrencies,
    resumeList,
    handleModalClick,
    setCreateJobInvitation,
    setShortlistId,
    DeleteResumeFromShortlist,
    setEditModalOpen,
    refresh,
    setRefresh,
    resetFormState
  } = props;
  const location = useLocation();
  const selectRef = useRef();
  const handleDropdownVisibleChange = open => {
    if (open && window.innerWidth < 768 && selectRef.current) {
      setTimeout(() => {
        selectRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start"
        });
      }, 100);
    }
  };
  const dispatch = useDispatch();
  const Handle = Slider.Handle;
  const [viewResumeActive, setViewResumeActive] = useState(false);
  useEffect(() => {
    const curr = allCurrencies?.find(item => item?.Name === "USD");
    if (curr) {
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            StartDate: "",
            FeeCurrencyType: curr
          }
        })
      );
    }
  }, [allCurrencies, refresh]);
  const durationTypeList = [
    { value: "Days", label: "Days" },
    { value: "Weeks", label: "Weeks" },
    { value: "Months", label: "Months" },
    { value: "Years", label: "Years" }
  ];

  const handle = props => {
    const { value, dragging, index, ...restProps } = props;
    return (
      <Handle value={value} key={index} {...restProps}>
        {value}
      </Handle>
    );
  };
  useEffect(() => {
    setShortlistId(selectedOpportunity?.selectedShortlists);
  }, [selectedOpportunity]);
  const CurrType = allCurrencies?.find(item => {
    return (
      selectedOpportunity?.FeeCurrencyType?.CurrencyId == item?.CurrencyId ||
      String(selectedOpportunity?.FeeCurrencyType) === String(item?.CurrencyId)
    );
  });

  return (
    <div className="flex md:!p-10  p-0 overflow-auto max-h-[90vh]">
      {viewResumeActive ? (
        <div className="md:p-4 p-0 w-full">
          <ShortListResume
            resumeList={resumeList}
            score={0}
            handleModalClick={handleModalClick}
            name="opportunity"
            labels
            onOpportunityUpdate={onOpportunityUpdate}
            onOpportunitySend={onOpportunitySend}
            setCreateJobInvitation={setCreateJobInvitation}
            setEditModalOpen={setEditModalOpen}
            setViewResumeActive={setViewResumeActive}
            DeleteResumeFromShortlist={DeleteResumeFromShortlist}
            isEditable={true}
            hideActionBtns={true}
            resetFormState={resetFormState}
          />
        </div>
      ) : (
        <div
          className={` flex flex-col ${
            resumeList?.length > 0
              ? "w-[100%] md:w-[50%] md:border-r-[0.5px] md:border-[#C6C6C6] md:pr-10 pr-0"
              : "w-[100%]"
          }`}
        >
          <div className="relative w-full md:w-[497px] h-[64px] md:hidden block">
            <div className="fixed md:w-[497px] w-full h-[64px] border-b border-[#EAE5FC] z-[999] bg-white rounded-tl-2xl rounded-tr-2xl">
              <div className="flex w-full h-[64px] justify-between items-center md:p-6 p-4 border-b border-[#EAE5FC]">
                <div className="flex md:justify-start justify-between w-full items-center gap-4 ">
                  <img
                    src={CloseIcon}
                    alt=""
                    onClick={() => {
                      dispatch(onStateChangeAction(resetFormState()));
                      setCreateJobInvitation(false);
                      setEditModalOpen(false);
                      setRefresh(prev => !prev);
                      dispatch(
                        onStateChangeAction({
                          selectedOpportunity: {},
                          shortlistResumes: null,
                          selectedResume: null,
                          selectedCompany: null,
                          selectedSaveSearch: null,
                          isFetchingShortlistResume: false
                        })
                      );
                      setViewResumeActive(false);
                    }}
                    className="pointer order-2 md:order-1"
                  />
                  <h1 className="!m-0 flex-1 order-1 md:order-2 !text-[16px] !font-semibold md:!text-2xl md:font-medium">
                    {labels?.New_Invitation_Title}
                  </h1>
                </div>
              </div>
            </div>
          </div>
          <div className="hidden md:flex justify-between w-full items-center h-[29px]">
            <h1 className="!m-0  !text-[16px] !font-semibold md:!text-2xl md:font-medium">
              {labels?.New_Invitation_Title}
            </h1>
            {!resumeList?.length > 0 && (
              <img
                src={CloseIcon}
                alt=""
                onClick={() => {
                  dispatch(onStateChangeAction(resetFormState()));
                  setRefresh(prev => !prev);
                  setCreateJobInvitation(false);
                  setEditModalOpen(false);
                  dispatch(onStateChangeAction({ selectedOpportunity: {} }));
                  dispatch(
                    onStateChangeAction({
                      selectedOpportunity: {},
                      shortlistResumes: null,
                      selectedResume: null,
                      isFetchingShortlistResume: false
                    })
                  );
                  setViewResumeActive(false);
                }}
                className="pointer"
              />
            )}
          </div>
          <div className="!p-4 md:!p-0 md:mt-9 mt-[64px]">
            <div className="!mb-6">
              <p className="!font-medium md:!text-[14px] !text-[13px] !text-[#878787] !mb-[6px]">
                {labels.Job_Title_Label}
              </p>

              <Input
                type="text"
                autoComplete="off"
                name="RequestName"
                className={`!bg-[#F3F1FD] !rounded-[99px] md:!text-[13px] !text-[12px] md:!h-11 !h-10 placeholder:!text-[#878787] md:placeholder:!text-[13px] placeholder:!text-sm placeholder:!font-normal ${
                  selectedOpportunity?.invalidRequestName ? "inValid" : ""
                }`}
                value={
                  selectedOpportunity?.RequestName
                    ? selectedOpportunity?.RequestName
                    : ""
                }
                placeholder={labels?.Write_Title_Label}
                onChange={onFormFieldChange}
                onBlur={onFormFieldChange}
                bordered={false}
                autoFocus={selectedOpportunity?.invalidRequestName}
                data-testid="input-txt-opportunity-request-name"
              />
            </div>
            <div className="flex gap-2 w-full">
              <div className="!mb-6 w-full">
                <p className="!font-medium md:!text-[14px] !text-[13px] !text-[#878787] !mb-[6px]">
                  {labels.collStartDate}
                </p>
                <DatePicker
                  name="StartDate"
                  format="DD-MM-YYYY"
                  value={
                    dayjs(selectedOpportunity?.StartDate).isValid()
                      ? dayjs(selectedOpportunity?.StartDate)
                      : null
                  }
                  onChange={onDateChange}
                  placeholder={labels?.Select_date_label}
                  className={`
                 w-full md:!text-[14px] !text-[12px]
                 !bg-[#F3F1FD] 
                 !rounded-[99px] 
                 !h-10 md:!h-11
                 [&_.ant-picker-input>input::placeholder]:!text-[#878787]
                  [&_.ant-picker-input>input]:!text-[13px]
                 [&_.ant-picker-input>input::placeholder]:!font-normal
               `}
                  popupClassName="[&_.ant-picker-cell-in-view]:text-[#F3F1FD]"
                  suffixIcon={<CalendarIcon className="!w-4 !h-4" />}
                  disabledDate={current =>
                    current && current < dayjs().startOf("day")
                  }
                />
              </div>
              <div className="!mb-6 w-full min-w-[50%]">
                <p className="!font-medium md:!text-[14px] !text-[13px] !text-[#878787] !mb-[6px]">
                  {labels?.oppResumeDetailLocation}
                </p>
                <div>
                  <Select
                    placeholder="Select Location"
                    onChange={(e, option) =>
                      onLocationSelect(e, "Languages", option)
                    }
                    showSearch
                    showArrow
                    className={`flex items-center !rounded-[99px] bg-[#F3F1FD] w-full pr-[70px] !h-10 md:!h-11 pl-3 !min-w-[49%]`}
                    bordered={false}
                    value={selectedOpportunity?.selectedLocation || []}
                    mode="multiple"
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      option?.props?.label
                        ?.toLowerCase()
                        ?.indexOf(input?.toLowerCase()) >= 0
                    }
                    options={allLocations}
                    suffixIcon={<LocationIcon className="!w-4 !h-4" />}
                    rootClassName="
                    [&_.ant-select-selection-overflow]:!flex [&_.ant-select-selection-overflow]:!flex-nowrap
                    [&_.ant-select-selection-overflow]:!overflow-hidden
                    [&_.ant-select-selection-item]:!pl-[6px]
                    [&_.ant-select-selection-placeholder]:!pl-[6px]
                    [&_.ant-select-selection-search]:!pl-[12px]
                    [&_.ant-select-selection-search]:!w-[100%]
                    [&_.ant-select-selection-item]:!overflow-hidden
                    [&_.ant-select-selection-item]:!text-ellipsis
                    [&_.ant-select-selection-item]:!whitespace-nowrap
                    md:[&_.ant-select-selection-placeholder]:!text-[13px]
                    [&_.ant-select-selection-placeholder]:!text-[12px]
                    [&_.ant-select-selection-placeholder]:!text-[#878787]
                    [&_.ant-select-selector]:!text-[12px]
                    md:[&_.ant-select-selector]:!text-[14px]
                    [&_.ant-select-selector]:!text-[#343333]
                    [&_.ant-select-selection-item]:!bg-transparent
                    [&_.ant-select-selector]:!overflow-hidden
                    md:[&_.ant-select-selection-item-content]:!text-[13px]
                    [&_.ant-select-selection-item-content]:!text-[12px]
                    [&_.ant-select-selection-wrap]:!h-[100%]
                    [&_.ant-select-selection-wrap]:!items-center
                  "
                  ></Select>
                </div>
              </div>
            </div>
            <div className="flex gap-2 w-full ">
              <div className="!mb-6 !w-full">
                <p className="!font-medium md:!text-[14px] !text-[13px] !text-[#878787] !mb-[6px]">
                  {labels?.SearcherOpportunityDraftDetailHourlyFee}
                </p>
                <div className="relative w-full mt-[3px]">
                  <div className="flex items-center bg-[#F3F1FD] rounded-[99px]">
                    <Input
                      autoFocus={state?.validation?.hourlyRate}
                      type="text"
                      name="HourlyFee"
                      className={`
                     !bg-[#F3F1FD] !rounded-[99px] md:!text-[13px] !text-[12px] md:!h-11 !h-10 placeholder:!text-[#878787] md:placeholder:!text-[13px] placeholder:!text-sm placeholder:!font-normal
                     ${state?.validation?.hourlyRate ? "inValid" : ""}
                    `}
                      value={
                        selectedOpportunity?.HourlyFee
                          ? selectedOpportunity?.HourlyFee
                          : ""
                      }
                      placeholder="0"
                      onChange={onFormFieldChange}
                      onBlur={onFormFieldChange}
                      data-testid="input-txt-opportunity-hourlyRate"
                      bordered={false}
                    />

                    <div className="absolute right-[2px] md:!h-[40px] !h-[36px] top-[2px] w-[70px] flex items-center bg-white rounded-r-[99px] border-l border-l-[#F3F1FD]">
                      <Select
                        bordered={false}
                        size="small"
                        className="w-full !rounded-[99px] !h-full [&_.ant-select-selector]:!h-full [&_.ant-select-selection-item]:!flex [&_.ant-select-selection-item]:!items-center"
                        dropdownStyle={{ fontSize: 12 }}
                        dropdownClassName="!rounded-lg"
                        name="FeeCurrencyType"
                        value={CurrType?.label}
                        onChange={(e, selectedOption) =>
                          onFormSelectChange("FeeCurrencyType", selectedOption)
                        }
                        options={allCurrencies}
                        suffixIcon={<ArrowDown className="w-[20px]" />}
                        popupMatchSelectWidth={false}
                        rootClassName="
                          [&_.ant-select-selection-item]:!text-[12px]
                          [&_.ant-select-selection-item]:!font-normal
                          "
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="!mb-6 !w-full">
                <p className="!font-medium md:!text-[14px] !text-[13px] !text-[#878787] !mb-[6px]">
                  {labels?.SearcherOpportunityDraftDetailDuration}
                </p>
                <div className="relative w-full mt-[3px]">
                  <div className="flex gap-2 items-center bg-[#F3F1FD] rounded-[99px] md:!h-11 !h-10 group">
                    <Input
                      type="text"
                      name="Duration"
                      className="!bg-[#F3F1FD] !rounded-[99px] md:!text-[13px] !text-[12px] md:!h-11 !h-10 placeholder:!text-[#878787] md:placeholder:!text-[13px] placeholder:!text-sm placeholder:!font-normal"
                      value={
                        selectedOpportunity?.Duration
                          ? selectedOpportunity?.Duration
                          : ""
                      }
                      placeholder="0"
                      onChange={onFormFieldChange}
                      onBlur={onFormFieldChange}
                      bordered={false}
                    />

                    <div className="absolute right-[2px] md:!h-[40px] !h-[36px] top-[2px] w-[82px] flex items-center bg-white rounded-r-[99px] border-l border-l-[#F3F1FD]">
                      <Select
                        name="DurationType"
                        size="small"
                        bordered={false}
                        className="w-[82px] !h-full [&_.ant-select-selector]:!h-full [&_.ant-select-selection-item]:!flex [&_.ant-select-selection-item]:!items-center"
                        dropdownStyle={{ fontSize: 12 }}
                        dropdownClassName="!rounded-lg"
                        value={selectedOpportunity?.DurationType || "Months"}
                        onChange={(value, selectedOption) =>
                          onFormSelectChange("DurationType", selectedOption)
                        }
                        options={durationTypeList}
                        suffixIcon={<ArrowDown className="w-[20px]" />}
                        showSearch
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          (option?.label ?? "")
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        rootClassName="
                         [&_.ant-select-selection-item]:!text-[12px]
                         [&_.ant-select-selection-item]:!font-normal
                       "
                        autoFocus={selectedOpportunity?.invalidDurationType}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="!mb-6 ">
              <p className="!font-medium md:!text-[14px] !text-[13px] !text-[#878787] !mb-[6px]">
                {labels?.Section2HiringShortlistTitle}
              </p>
              <div className="relative" ref={selectRef}>
                <Select
                  name="selectedShortlists"
                  placeholder={labels?.Shortlist_Placeholder}
                  options={shortlist || []}
                  value={
                    selectedOpportunity?.selectedShortlists &&
                    selectedOpportunity?.selectedShortlists
                  }
                  onChange={(value, option) =>
                    handleSelectedShortlistClick(option)
                  }
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                  onDropdownVisibleChange={handleDropdownVisibleChange}
                  className={`!h-10 md:!h-11 !rounded-[99px] bg-[#F3F1FD] w-full pr-[70px]`}
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option?.props?.label
                      ?.toLowerCase()
                      ?.indexOf(input?.toLowerCase()) >= 0
                  }
                  suffixIcon={<ArrowDown className="w-[20px]" />}
                  bordered={false}
                  rootClassName="
                    [&_.ant-select-selection-item]:!pl-[6px]
                    [&_.ant-select-selection-placeholder]:!pl-[6px]
                    [&_.ant-select-selection-search]:!pl-[6px]
                    [&_.ant-select-selection-item]:!max-w-[180px]
                    md:[&_.ant-select-selection-placeholder]:!text-[13px]
                    [&_.ant-select-selection-placeholder]:!text-[12px]
                    [&_.ant-select-selection-placeholder]:!text-[#878787]
                    [&_.ant-select-selector]:!text-[12px]
                    md:[&_.ant-select-selector]:!text-[14px]
                    [&_.ant-select-selector]:!text-[#343333]"
                />
                {resumeList?.length > 0 && window.innerWidth < 767 && (
                  <span
                    className="absolute right-[38px] top-1/2 -translate-y-1/2 text-[#8E81F5] text-xs font-medium cursor-pointer z-10"
                    onClick={() => setViewResumeActive(true)}
                  >
                    {labels?.Workplace_View_Label}
                  </span>
                )}
              </div>
            </div>
            <div className="!mb-6">
              <p className="!font-medium md:!text-[14px] !text-[13px] !text-[#878787] !mb-[6px]">
                {labels?.SearcherOpportunityDraftDetailCompany}
              </p>
              <div>
                <Select
                  showArrow
                  className={`!h-10 md:!h-11 !rounded-[99px] bg-[#F3F1FD] w-full pr-[70px]`}
                  bordered={false}
                  value={selectedCompany && selectedCompany}
                  placeholder={
                    labels?.SearcherOpportunityDraftDetailCompanyPlaceholder
                  }
                  onChange={(e, selectedOption) =>
                    onFormSelectChange("SelectedCompany", selectedOption)
                  }
                  options={userCompanies}
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option?.props?.label
                      ?.toLowerCase()
                      ?.indexOf(input?.toLowerCase()) >= 0
                  }
                  suffixIcon={<ArrowDown className="w-[20px]" />}
                  rootClassName="
                  [&_.ant-select-selection-item]:!pl-[6px]
                  [&_.ant-select-selection-placeholder]:!pl-[6px]
                  [&_.ant-select-selection-search]:!pl-[6px]
                  [&_.ant-select-selection-item]:!max-w-[180px]
                  md:[&_.ant-select-selection-placeholder]:!text-[13px]
                  [&_.ant-select-selection-placeholder]:!text-[12px]
                  [&_.ant-select-selection-placeholder]:!text-[#878787]
                  [&_.ant-select-selector]:!text-[12px]
                  md:[&_.ant-select-selector]:!text-[14px]
                  [&_.ant-select-selector]:!text-[#343333]"
                ></Select>
              </div>
            </div>
            <div>
              <p className="!font-medium md:!text-[14px] !text-[13px] !text-[#878787] !mb-[6px]">
                {labels?.collDescription}
              </p>
              <div>
                <TextArea
                  name="Description"
                  placeholder={labels?.companyDescriptionPlaceholder}
                  value={selectedOpportunity?.Description || ""}
                  onChange={onFormFieldChange}
                  className="!bg-[#F3F1FD] md:!text-[14px] !text-[12px] placeholder:!text-[12px] md:placeholder:!text-[13px] placeholder:!text-[#878787] !rounded-2xl !h-[176px] md:!h-[182px] !resize-none !pt-[10px]"
                  bordered={false}
                />
              </div>
            </div>
          </div>
          <div
            className={`w-full justify-end mt-4 ${
              selectedOpportunity?.selectedShortlists
                ? "hidden"
                : "md:flex hidden"
            }`}
          >
            <Button
              type="primary"
              className="w-full md:w-[112px] !h-10 !text-sm"
              onClick={onOpportunityUpdate}
            >
              {labels?.save_draft_oppportunity}
            </Button>
          </div>
        </div>
      )}
      <div className="flex md:hidden w-full  h-[64px] bg-white fixed bottom-0 justify-center items-center  border-t-[0.5px] border-[#EAE5FC]">
        <div
          className={`w-full justify-end ml-4 mr-4 ${
            resumeList?.length > 0 ? "hidden" : "flex"
          }`}
        >
          <Button
            type="primary"
            className="w-full md:w-[112px] !h-10 !text-sm"
            onClick={onOpportunityUpdate}
          >
            {labels?.save_draft_oppportunity}
          </Button>
        </div>
        {resumeList?.length > 0 && (
          <div className="flex w-full justify-between gap-[9px] ml-4 mr-4">
            <Button
              type="default"
              className="w-full !h-10 !text-sm"
              onClick={onOpportunityUpdate}
            >
              {labels?.save_draft_oppportunity}
            </Button>
            <Button
              type="primary"
              className="w-full !h-10 !text-sm"
              onClick={onOpportunitySend}
            >
              {labels?.send_opportunity}
            </Button>
          </div>
        )}
      </div>
      {resumeList?.length > 0 && window.innerWidth > 767 && (
        <div className="w-[53%] ml-4">
          <ShortListResume
            resumeList={resumeList}
            score={0}
            handleModalClick={handleModalClick}
            name="opportunity"
            labels
            onOpportunityUpdate={onOpportunityUpdate}
            onOpportunitySend={onOpportunitySend}
            setCreateJobInvitation={setCreateJobInvitation}
            setEditModalOpen={setEditModalOpen}
            isEditable={true}
            DeleteResumeFromShortlist={DeleteResumeFromShortlist}
            resetFormState={resetFormState}
          />
        </div>
      )}
    </div>
  );
};

export default NewSearcherOpportunityForm;
