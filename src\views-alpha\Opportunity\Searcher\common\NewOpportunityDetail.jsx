import { useEffect, useState } from "react";
import moment from "moment";
import { <PERSON><PERSON>, Drawer } from "antd";
import { formatDate } from "../../../../utilities/helpers";
import CloseIcon from "../../../../assets-alpha/images/svg/close-modal.svg";
import ResumeListComponentNewDesign from "../NewCreateOpportunity/component/resumeResumeList";
import ResumeStatusDetail from "./ResumeStatusDetail";
import { useSelector } from "react-redux";

const NewOpportunityDetail = props => {
  const {
    selectedOpportunity,
    onAcceptedResumes,
    onDeclinedResumes,
    onNoActionResumes,
    labels,
    onSelectedActionChange,
    declinedRequests,
    acceptedRequests,
    pendingRequests,
    selectedShortlists,
    handleListOpenMobile,
    handleStatusChange,
    status,
    setDetailDrawerOpen,
    resumeList,
    score,
    handleModalClick,
    handleOpportunityDelete,
    name,
    setCreateJobInvitation,
    overviewActive,
    setOverviewActive,
    resetFormState
  } = props;
  const [acceptedResume, setAcceptedResume] = useState(false);
  const [pendingResume, setPendingResume] = useState(false);
  const [rejectedResume, setRejectedResume] = useState(false);
  const { allCurrenciesList } = useSelector(state => ({
    ...state.createNewOpportunity
  }));
  const CurrType = allCurrenciesList?.find(item => {
    return (
      selectedOpportunity?.FeeCurrencyType == item?.CurrencyId ||
      String(selectedOpportunity?.FeeCurrencyType) === String(item?.CurrencyId)
    );
  });
  useEffect(() => {
    if (props.selectedOpportunity !== selectedOpportunity) {
      onSelectedActionChange({ selectedAction: "" });
    }
  }, [selectedOpportunity]);

  const renderDate = date => {
    return date ? moment(date).format("MM/DD/YY") : "N/A";
  };

  const renderDuration = (duration, durationType) => {
    return duration ? `${duration} ${durationType}` : "N/A";
  };

  const renderHourlyFee = (fee, currencyType) => {
    return fee ? `${fee} ${currencyType}` : "N/A";
  };
  return (
    <>
      <Drawer
        getContainer={document.body}
        placement="right"
        closable={true}
        open={
          (acceptedResume && acceptedRequests > 0) ||
          (pendingResume && pendingRequests > 0) ||
          (rejectedResume && declinedRequests > 0)
        }
        key="placement"
        headerStyle={{ display: "none" }}
        onClose={() => {
          if (acceptedResume) {
            setAcceptedResume(false);
          } else if (pendingResume) {
            setPendingResume(false);
          } else {
            setAcceptedResume(false);
          }
        }}
        rootClassName=" 
         sm:[&_.ant-drawer-content-wrapper]:!m-[8px]
         [&_.ant-drawer-content-wrapper]:!mt-[8px] 
         md:[&_.ant-drawer-content]:!rounded-[16px]
         [&_.ant-drawer-content]:!rounded-tl-[16px]
         [&_.ant-drawer-content]:!rounded-tr-[16px]
         sm:[&_.ant-drawer-content-wrapper]:!w-[497px]
         [&_.ant-drawer-content-wrapper]:!w-[100%]
         [&_.ant-drawer-content-wrapper]:!rounded-[16px]
         [&_.ant-drawer-body]:!p-[0px]"
      >
        <ResumeStatusDetail
          resumeList={resumeList}
          acceptedResume={acceptedResume}
          pendingResume={pendingResume}
          rejectedResume={rejectedResume}
          setAcceptedResume={setAcceptedResume}
          setRejectedResume={setRejectedResume}
          setPendingResume={setPendingResume}
        />
      </Drawer>
      <div>
        <div className="relative w-full sm:w-[497px] h-[64px] ">
          <div className="fixed sm:w-[497px] w-full h-[64px] border-b border-[#EAE5FC] z-[999] bg-white rounded-tl-2xl rounded-tr-2xl">
            <div className="flex w-full h-[64px] justify-between items-center p-6">
              <div className="flex md:justify-start justify-between w-full items-center gap-4 ">
                <img
                  src={CloseIcon}
                  alt=""
                  onClick={() => {
                    setDetailDrawerOpen(false);
                  }}
                  className="pointer order-2 sm:order-1"
                />
                <h1
                  level={3}
                  className="!m-0 flex-1 order-1 sm:order-2 !text-[18px] !font-semibold md:!text-2xl md:font-medium"
                >
                  {labels?.Invitation_Details_Label}
                </h1>
              </div>
              <div className="hidden sm:flex order-3">
                <Button
                  type="primary"
                  className=" w-[136px] !bg-[#FF3B30] text-white !text-sm font-medium leading-4 !h-9 pl-4 pr-4"
                  onClick={e => {
                    e.stopPropagation();
                    handleOpportunityDelete({
                      selectedOpportunity: selectedOpportunity,
                      e
                    });
                  }}
                >
                  {labels?.Cancel_Invitation_Label}
                </Button>
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-col">
          <div className="flex w-full justify-center md:justify-start">
            <div className="flex !h-10 md:!h-[44px] w-[343px] rounded-[99px] bg-[#F3F1FD] items-center justify-center md:gap-4 gap-[9px] ml-6 mr-6 mt-[22px]">
              <Button
                className={`${
                  overviewActive
                    ? "w-[165px] md:w-[162px] !rounded-[99px] !h-9 md:!h-10 !bg-white !text-[#343333] !text-sm !font-medium m-[2px]"
                    : "w-[165px] md:w-[162px] !rounded-[99px] !h-9 md:!h-10 !text-sm !font-medium !text-[#878787] m-[2px]"
                }`}
                onClick={() => {
                  setOverviewActive(true);
                }}
              >
                {labels?.Job_Overview_Label}
              </Button>
              <Button
                className={`${
                  overviewActive
                    ? "w-[165px] md:w-[162px] !rounded-[99px] !h-9 md:!h-10 !text-sm !font-medium !text-[#878787] m-[2px]"
                    : "w-[165px] md:w-[162px] !rounded-[99px] !h-9 md:!h-10 !bg-white !text-[#343333] !text-sm !font-medium m-[2px]"
                }`}
                onClick={() => {
                  setOverviewActive(false);
                }}
              >
                {labels?.Shorlist_Resume_Label}
              </Button>
            </div>
          </div>
          {overviewActive ? (
            <div className="flex flex-col">
              <div className="flex h-[45px] rounded-[99px] bg-[#F3F1FD] items-center justify-center gap-4 ml-6 mr-6 mt-3">
                <h1
                  className="!m-0 flex gap-1"
                  onClick={() => {
                    {
                      acceptedRequests > 0 && setDetailDrawerOpen(false);
                    }
                    setTimeout(() => {
                      setAcceptedResume(true);
                      setRejectedResume(false);
                      setPendingResume(false);
                    }, 300);
                    {
                      acceptedRequests > 0 ? onAcceptedResumes() : "";
                    }
                    onSelectedActionChange({ selectedAction: "accepted" });
                    handleStatusChange("accepted");
                  }}
                >
                  <span className="!text-sm font-normal text-[#34C759] underline cursor-pointer">
                    {labels?.SearcherOpportunitySentDetailAccepted}
                  </span>
                  <span className="!text-sm font-normal text-[#343333] cursor-pointer">
                    {acceptedRequests || 0}
                  </span>
                </h1>
                <h1
                  className="!m-0 flex gap-1 cursor-pointer"
                  onClick={() => {
                    {
                      pendingRequests > 0 && setDetailDrawerOpen(false);
                    }
                    setTimeout(() => {
                      setAcceptedResume(false);
                      setPendingResume(true);
                      setRejectedResume(false);
                    }, 300);
                    {
                      pendingRequests > 0 ? onNoActionResumes() : "";
                    }
                    onSelectedActionChange({ selectedAction: "pending" });
                    handleStatusChange("pending");
                  }}
                >
                  <span className="!text-sm font-normal text-[#FF9500] underline">
                    {labels?.SearcherOpportunitySentDetailPending}
                  </span>
                  <span className="!text-sm font-normal text-[#343333]">
                    {pendingRequests <= 0 ? 0 : pendingRequests}
                  </span>
                </h1>
                <h1
                  className="!m-0 flex gap-1 cursor-pointer"
                  onClick={() => {
                    {
                      declinedRequests > 0 && setDetailDrawerOpen(false);
                    }
                    setTimeout(() => {
                      setAcceptedResume(false);
                      setPendingResume(false);
                      setRejectedResume(true);
                    }, 300);
                    {
                      declinedRequests > 0 ? onDeclinedResumes() : "";
                    }
                    onSelectedActionChange({ selectedAction: "declined" });
                    handleStatusChange("declined");
                  }}
                >
                  <span className="!text-sm font-normal text-[#FF3B30] underline ">
                    {labels?.collaborationsRejected}:
                  </span>
                  <span className="!text-sm font-normal text-[#343333]">
                    {declinedRequests || 0}
                  </span>
                </h1>
              </div>
              <div className="flex flex-col bg-[#F3F1FD] rounded-xl mb-[78px] ml-6 mr-6 mt-1">
                <div className="flex pl-4 pr-4 h-auto min-h-10 !border-b !border-[#EAE5FC]">
                  <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
                    {labels?.collFormTitle}
                  </label>
                  <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
                  <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
                    {selectedOpportunity?.RequestName
                      ? selectedOpportunity?.RequestName
                      : "N/A"}
                  </p>
                </div>
                <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
                  <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
                    {labels?.SearcherSentCollaborationStartDateLabel}
                  </label>
                  <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
                  <p className="!ml-[12.5px] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
                    {formatDate(selectedOpportunity?.StartDate)}
                  </p>
                </div>
                <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
                  <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
                    {labels?.collDuration}
                  </label>
                  <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
                  <p className="!ml-[12.5px] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
                    {renderDuration(
                      selectedOpportunity?.Duration,
                      selectedOpportunity?.DurationType
                    )}
                  </p>
                </div>
                <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
                  <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
                    {labels?.collHourlyFee}
                  </label>
                  <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
                  <p className="!ml-[12.5px] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
                    {renderHourlyFee(
                      selectedOpportunity?.HourlyFee,
                      CurrType?.label
                    )}
                  </p>
                </div>
                <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
                  <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
                    {labels?.oppResumeDetailLocation}
                  </label>
                  <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
                  <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
                    {selectedOpportunity?.Countries || "N/A"}
                  </p>
                </div>
                <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
                  <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
                    {labels?.SearcherOpportunityDraftViewTitleShortlist}
                  </label>
                  <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
                  <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
                    {selectedShortlists[0]?.ShortlistName}
                  </p>
                </div>
                <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
                  <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
                    {labels?.SearcherSentCollaborationCompanyLabel}
                  </label>
                  <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
                  <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
                    {selectedOpportunity?.CompanyName || "N/A"}
                  </p>
                </div>
                <div className="flex pl-4 pr-4 pt-[14px] min-h-10 h-auto pb-3">
                  <label className="flex  !w-[72px] h-full text-[#878787] text-[13px] font-normal ">
                    {labels?.collDescription}
                  </label>
                  <span className="h-[22.5px] border border-[#EAE5FC]  ml-[21.5px]"></span>
                  <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal  text-[#343333]">
                    {selectedOpportunity?.Description || "N/A"}
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div>
              <ResumeListComponentNewDesign
                resumeList={resumeList}
                score={0}
                handleModalClick={handleModalClick}
                name="opportunity"
                isEditable={false}
                setCreateJobInvitation={setCreateJobInvitation}
                resetFormState={resetFormState}
              />
            </div>
          )}
        </div>
        <div className="sm:hidden flex w-full h-[64px] bg-white fixed bottom-0 justify-center items-center  border-t-[0.5px] border-[#EAE5FC]">
          <Button
            type="primary"
            className=" w-full  !bg-[#FF3B30] text-white !text-sm font-medium leading-4 !h-10 sm:!h-9 ml-4 mr-4"
            onClick={e => {
              e.stopPropagation();
              handleOpportunityDelete({
                selectedOpportunity: selectedOpportunity,
                e
              });
            }}
          >
            {labels?.Cancel_Invitation_Label}
          </Button>
        </div>
      </div>
    </>
  );
};

export default NewOpportunityDetail;
