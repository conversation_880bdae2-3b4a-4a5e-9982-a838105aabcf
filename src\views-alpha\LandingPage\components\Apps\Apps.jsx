import { Button } from "antd";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import ImageSlider from "./Components/ImageSlider";
const Apps = () => {
  const labels = useSelector(state => state.systemLabel.labels);
  const [previewImages, setPreviewImages] = useState(false);
  useEffect(() => {
    if (previewImages) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }

    return () => {
      document.body.style.overflow = "auto";
    };
  }, [previewImages]);

  return (
    <>
      {previewImages ? (
        <div className="fixed top-0 left-0 w-full h-[100vh] bg-[#343333] z-[9999] overflow-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
          <ImageSlider setPreviewImages={setPreviewImages} />
        </div>
      ) : (
        <div className="flex flex-col w-full items-center bg-[url('/assets/images/appintro-bg.webp')] bg-cover bg-no-repeat bg-center">
          <h2 className="flex w-full gap-3 justify-center text-2xl md:text-5xl mb-4 md:mb-6 leading-tight mt-[60px]">
            <span className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
              {labels?.AppIntro_h1}
            </span>
            <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
              {labels?.AppIntro_h2}
            </span>
          </h2>

          <div className="flex w-full justify-center text-center">
            <p className="text-xs md:text-[16px] text-[#878787] leading-relaxed">
              {labels?.AppIntro_Desc}
            </p>
          </div>
          <div className="relative sm:hidden block">
            <div className="absolute w-full h-full">
              <Button
                type="primary"
                className="absolute z-[999] top-1/2 left-1/3  h-[34px] -translate-x-1/2 -translate-y-1/2 !rounded-[99px]"
                onClick={() => {
                  setPreviewImages(true);
                }}
              >
                Click to preview
              </Button>
            </div>
            <img src="/assets/images/app-intro-main-mbl.png" alt="app-intro" />
          </div>
          <div className="relative w-full sm:flex justify-center hidden min-h-[700px]">
            <div className="absolute  w-full h-full">
              <Button
                type="primary"
                className="absolute z-[999] top-1/2 left-[40%] 2xl:left-[42%] -translate-x-1/2 -translate-y-1/2 !h-11 !rounded-[99px]"
                onClick={() => {
                  setPreviewImages(true);
                }}
              >
                Click to preview
              </Button>
            </div>
            <img src="/assets/images/app-intro-main.webp" alt="app-intro" />
          </div>
        </div>
      )}
    </>
  );
};

export default Apps;
