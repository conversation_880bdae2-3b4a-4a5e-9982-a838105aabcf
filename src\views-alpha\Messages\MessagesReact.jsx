import { useEffect, useState, useCallback } from "react";
import PageWrapper from "../../components-alpha/PageWrapper/PageWrapper";
import EmptyView from "../../common-alpha/EmptyViewUpdated/EmptyView";
import EmptyInboxIcon from "../../assets-alpha/images/view/empty-inbox.svg?react";
import AvatarIcon from "../../assets-alpha/images/svg/workplace-avatar.svg";
import { Input, Button, message as antMessage } from "antd";
import { Send } from "lucide-react";
import { HubConnectionBuilder, LogLevel } from "@microsoft/signalr";
import { useFetchWithInterceptor } from "../../api-alpha/api-service";

const { TextArea } = Input;

const MessagesReact = () => {
  // Use the same BASE_URL pattern as your existing API service
  const BASE_URL = `${import.meta.env.VITE_API_URL ?? ""}/webapi/api/`;
  const SIGNALR_HUB_URL = "https://prodoov2-gqg9esa9g9b3d9bd.canadacentral-01.azurewebsites.net/hubs/messages";
  const fetchWithInterceptor = useFetchWithInterceptor();

  // State management
  const [connection, setConnection] = useState(null);
  const [currentUserId, setCurrentUserId] = useState(null);
  const [currentConversationId, setCurrentConversationId] = useState(null);
  const [conversations, setConversations] = useState([]);
  const [messages, setMessages] = useState([]);
  const [userStatuses, setUserStatuses] = useState({});
  const [connectionStatus, setConnectionStatus] = useState({
    connected: false,
    message: "Enter your User ID to connect"
  });
  const [userIdInput, setUserIdInput] = useState("");
  const [messageInput, setMessageInput] = useState("");
  const [loading, setLoading] = useState(false);

  // Validate user before connecting
  const validateUser = async (userId) => {
    try {
      console.log(`🔍 Validating user ${userId} at: ${BASE_URL}users/${userId}/validate`);

      const result = await fetchWithInterceptor(`${BASE_URL}users/${userId}/validate`);
      console.log('✅ Validation result:', result);
      return result;
    } catch (error) {
      console.error('❌ User validation failed:', error);
      console.error('❌ Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      return { userId: userId, isValid: false, error: error.message };
    }
  };

  // Initialize SignalR connection
  const initializeSignalR = useCallback(async () => {
    const newConnection = new HubConnectionBuilder()
      .withUrl(SIGNALR_HUB_URL)
      .withAutomaticReconnect()
      .configureLogging(LogLevel.Information)
      .build();

    // Connection events
    newConnection.onclose(() => {
      setConnectionStatus({ connected: false, message: "Disconnected" });
    });

    newConnection.onreconnecting(() => {
      setConnectionStatus({ connected: false, message: "Reconnecting..." });
    });

    newConnection.onreconnected(() => {
      setConnectionStatus({ connected: true, message: "Connected" });
      if (currentUserId) {
        joinUserGroup(currentUserId);
      }
    });

    // SignalR event handlers
    newConnection.on("ConversationCreated", (data) => {
      console.log("New conversation created:", data);
      handleNewConversation(data);
    });

    newConnection.on("NewMessage", (data) => {
      console.log("New message received:", data);
      handleNewMessage(data);
    });

    newConnection.on("UserStatusChanged", (data) => {
      console.log("👤 Received UserStatusChanged notification:", data);
      updateUserStatus(parseInt(data.UserId), data.IsOnline);
    });

    setConnection(newConnection);
    return newConnection;
  }, [API_BASE_URL, currentUserId]);

  // Join user group for notifications
  const joinUserGroup = async (userId) => {
    if (connection && connection.state === "Connected") {
      try {
        await connection.invoke("JoinUserGroup", userId.toString());
        console.log(`Joined user group for User ${userId}`);
        await loadCurrentUserStatus(userId);
      } catch (err) {
        console.error("Failed to join user group:", err);
      }
    }
  };

  // Load current user's status
  const loadCurrentUserStatus = async (userId) => {
    try {
      const status = await fetchWithInterceptor(`${BASE_URL}users/${userId}/status`);
      setUserStatuses(prev => ({
        ...prev,
        [userId]: {
          isOnline: status.isOnline,
          lastSeen: new Date(status.lastSeenUtc)
        }
      }));
      console.log(`📊 Current user status loaded - User ${userId}: ${status.isOnline ? 'Online' : 'Offline'}`);
    } catch (error) {
      console.error('Error loading current user status:', error);
    }
  };

  // Load conversations for a user
  const loadConversations = async (userId) => {
    setLoading(true);
    try {
      const conversationsData = await fetchWithInterceptor(`${BASE_URL}conversations/initiator/${userId}`);
      setConversations(conversationsData);
      await loadUserStatuses(conversationsData);
    } catch (error) {
      console.error("Error loading conversations:", error);
      antMessage.error('Unable to connect to server. Please check your connection.');
    } finally {
      setLoading(false);
    }
  };

  // Load user statuses for all conversation participants
  const loadUserStatuses = async (conversationsData) => {
    if (conversationsData.length === 0) return;

    const userIds = [...new Set(
      conversationsData.flatMap(conv => [conv.initiatorId, conv.recipientId])
        .filter(id => id !== currentUserId)
    )];

    if (userIds.length === 0) return;

    try {
      const statuses = await fetchWithInterceptor(`${BASE_URL}users/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ userIds })
      });

      const statusMap = {};
      statuses.forEach(status => {
        statusMap[status.userId] = {
          isOnline: status.isOnline,
          lastSeen: new Date(status.lastSeenUtc)
        };
      });
      setUserStatuses(prev => ({ ...prev, ...statusMap }));
      console.log('📊 User statuses loaded:', statusMap);
    } catch (error) {
      console.error('Error loading user statuses:', error);
    }
  };

  // Update user status and refresh UI
  const updateUserStatus = (userId, isOnline) => {
    setUserStatuses(prev => ({
      ...prev,
      [userId]: {
        isOnline: isOnline,
        lastSeen: new Date()
      }
    }));
    console.log(`👤 User ${userId} is now ${isOnline ? 'online' : 'offline'}`);
  };

  // Load messages for a conversation
  const loadMessages = async (conversationId) => {
    try {
      const messagesData = await fetchWithInterceptor(`${BASE_URL}messages/conversation/${conversationId}`);
      setMessages(messagesData);
    } catch (error) {
      console.error("Error loading messages:", error);
      antMessage.error('Unable to load messages');
    }
  };

  // Send a message
  const sendMessage = async () => {
    const content = messageInput.trim();
    if (!content || !currentConversationId || !currentUserId) {
      return;
    }

    try {
      await fetchWithInterceptor(`${BASE_URL}messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          conversationId: currentConversationId,
          senderId: currentUserId,
          content: content,
          type: 'Text'
        })
      });

      // Add message to UI immediately for the sender
      const newMessage = {
        senderId: currentUserId,
        content: content,
        sentDate: new Date().toISOString(),
        messageType: 'Text'
      };

      setMessages(prev => [...prev, newMessage].sort((a, b) => new Date(a.sentDate) - new Date(b.sentDate)));
      setMessageInput('');
    } catch (error) {
      console.error("Error sending message:", error);
      antMessage.error('Failed to send message. Please try again.');
    }
  };

  // Handle new conversation from SignalR
  const handleNewConversation = (data) => {
    if (data.initiatorId === currentUserId || data.recipientId === currentUserId) {
      const existingConv = conversations.find(c => c.conversationId === data.conversationId);
      if (!existingConv) {
        const newConversation = {
          conversationId: data.conversationId,
          title: data.title,
          initiatorId: data.initiatorId,
          recipientId: data.recipientId,
          createdDate: data.timestamp,
          lastMessage: 'New conversation started'
        };
        setConversations(prev => [newConversation, ...prev]);
      }
    }
  };

  // Handle new message from SignalR
  const handleNewMessage = (data) => {
    // Update conversation list with latest message
    setConversations(prev => {
      const updatedConversations = prev.map(conv => {
        if (conv.conversationId === data.conversationId) {
          return {
            ...conv,
            lastMessage: data.content,
            lastMessageTime: data.timestamp
          };
        }
        return conv;
      });

      // Move updated conversation to top
      const updatedConv = updatedConversations.find(c => c.conversationId === data.conversationId);
      if (updatedConv) {
        const otherConversations = updatedConversations.filter(c => c.conversationId !== data.conversationId);
        return [updatedConv, ...otherConversations];
      }
      return updatedConversations;
    });

    // If this message is for the currently open conversation and not from current user
    if (currentConversationId === data.conversationId && data.senderId !== currentUserId) {
      const newMessage = {
        senderId: data.senderId,
        content: data.content,
        sentDate: data.timestamp,
        messageType: data.messageType || 'Text'
      };
      setMessages(prev => [...prev, newMessage].sort((a, b) => new Date(a.sentDate) - new Date(b.sentDate)));
    }
  };

  // Main connection handler
  const handleLoadConversations = async () => {
    const userId = parseInt(userIdInput);
    if (!userId) {
      antMessage.error('Please enter a valid User ID');
      return;
    }

    try {
      // Step 1: Validate user first
      setConnectionStatus({ connected: false, message: "Validating user..." });
      const validation = await validateUser(userId);

      if (!validation.isValid) {
        setConnectionStatus({ connected: false, message: "❌ User validation failed" });
        antMessage.error(`User ${userId} is not valid or not found in the system. Please check the user ID and make sure the user exists in the Users module.`);
        return;
      }

      console.log('✅ User validated:', validation);

      // Step 2: Initialize SignalR connection
      setConnectionStatus({ connected: false, message: `Initializing connection for ${validation.displayName || `User ${userId}`}...` });

      const newConnection = await initializeSignalR();

      // Step 3: Connect to hub
      setConnectionStatus({ connected: false, message: `Connecting to messaging hub...` });
      await newConnection.start();

      // Step 4: Join user group and load conversations
      setCurrentUserId(userId);
      setConnection(newConnection);
      await joinUserGroup(userId);
      await loadConversations(userId);

      // Step 5: Update status to connected
      setConnectionStatus({ connected: true, message: `Connected as ${validation.displayName || `User ${userId}`}` });

    } catch (error) {
      console.error('❌ Connection failed:', error);
      setConnectionStatus({ connected: false, message: "❌ Connection failed" });
      antMessage.error(`Failed to connect: ${error.message}`);
    }
  };

  // Select conversation
  const selectConversation = async (conversationId) => {
    setCurrentConversationId(conversationId);
    await loadMessages(conversationId);
  };

  // Utility functions
  const getUserInitials = (displayName) => {
    if (!displayName) return '?';
    return displayName.split(' ')
      .map(name => name.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('');
  };

  const getOtherUserInfo = (conversation) => {
    const isInitiator = conversation.initiatorId === currentUserId;
    return isInitiator ? conversation.recipientInfo : conversation.initiatorInfo;
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Handle Enter key for sending messages
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Cleanup connection on unmount
  useEffect(() => {
    return () => {
      if (connection) {
        connection.stop();
      }
    };
  }, [connection]);

  return (
    <PageWrapper className={"!p-0"}>
      <div className="flex w-full h-full">
        {/* Left Sidebar - User Setup & Conversations */}
        <div className="flex flex-col w-[350px] bg-white border-r border-[#e0e0e0]">
          {/* Header with User Setup */}
          <div className="p-5 border-b border-[#e0e0e0] bg-[#f8f9fa]">
            <div className="mb-4">
              <Input
                type="number"
                placeholder="Enter Your User ID (e.g., 101, 103)"
                value={userIdInput}
                onChange={(e) => setUserIdInput(e.target.value)}
                className="mb-3"
              />
              <Button
                type="primary"
                onClick={handleLoadConversations}
                loading={loading}
                className="w-full"
                disabled={!userIdInput}
              >
                Load My Conversations
              </Button>
            </div>
            <div className={`px-3 py-2 rounded text-center text-xs font-bold ${connectionStatus.connected
              ? 'bg-[#d4edda] text-[#155724]'
              : 'bg-[#f8d7da] text-[#721c24]'
              }`}>
              {connectionStatus.message}
            </div>
          </div>

          {/* Conversations List */}
          <div className="flex-1 overflow-y-auto">
            {loading ? (
              <div className="p-5 text-center text-gray-500">Loading conversations...</div>
            ) : conversations.length === 0 ? (
              <div className="p-5 text-center">
                <h3 className="text-lg font-semibold mb-2">Welcome to ProDoo Messaging</h3>
                <p className="text-gray-600">Enter your User ID above and click "Load My Conversations" to start</p>
              </div>
            ) : (
              conversations.map(conv => {
                const otherUserInfo = getOtherUserInfo(conv);
                const otherUserId = otherUserInfo?.userId || (conv.initiatorId === currentUserId ? conv.recipientId : conv.initiatorId);
                const userStatus = userStatuses[otherUserId] || { isOnline: false };
                const displayName = otherUserInfo?.displayName || `User ${otherUserId}`;
                const profilePicture = otherUserInfo?.profilePicture;
                const initials = getUserInitials(displayName);

                return (
                  <div
                    key={conv.conversationId}
                    className={`p-4 border-b border-[#f0f0f0] cursor-pointer transition-colors hover:bg-[#f8f9fa] ${currentConversationId === conv.conversationId ? 'bg-[#e3f2fd] border-r-3 border-r-[#007bff]' : ''
                      }`}
                    onClick={() => selectConversation(conv.conversationId)}
                  >
                    <div className="flex items-center gap-3">
                      <div className="relative w-12 h-12 flex-shrink-0">
                        {profilePicture ? (
                          <img
                            src={profilePicture}
                            alt={displayName}
                            className="w-12 h-12 rounded-full object-cover border-2 border-[#e0e0e0]"
                            onError={(e) => {
                              e.target.style.display = 'none';
                              e.target.nextElementSibling.style.display = 'flex';
                            }}
                          />
                        ) : null}
                        <div
                          className={`w-12 h-12 rounded-full bg-gradient-to-br from-[#667eea] to-[#764ba2] text-white flex items-center justify-center font-semibold text-base border-2 border-[#e0e0e0] ${profilePicture ? 'hidden' : 'flex'
                            }`}
                        >
                          {initials}
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-center mb-1">
                          <div className="font-semibold text-[#333] truncate">{conv.title}</div>
                          <div className="text-xs text-[#999] whitespace-nowrap ml-2">
                            {formatTime(conv.createdDate)}
                          </div>
                        </div>
                        <div className="text-sm text-[#666] truncate mb-1">
                          {conv.lastMessage || 'No messages yet'}
                        </div>
                        <div className="flex items-center gap-1 text-xs text-[#666]">
                          <span className={`w-2 h-2 rounded-full ${userStatus.isOnline ? 'bg-[#4caf50] shadow-sm' : 'bg-[#9e9e9e]'}`}></span>
                          <span>{displayName} • {userStatus.isOnline ? 'Online' : 'Offline'}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>

        {/* Right Side - Chat Area */}
        <div className="flex-1 flex flex-col bg-white">
          {currentConversationId ? (
            <>
              {/* Chat Header */}
              <div className="p-5 border-b border-[#e0e0e0] bg-[#f8f9fa]">
                {(() => {
                  const conversation = conversations.find(c => c.conversationId === currentConversationId);
                  if (!conversation) return null;

                  const otherUserInfo = getOtherUserInfo(conversation);
                  const otherUserId = otherUserInfo?.userId || (conversation.initiatorId === currentUserId ? conversation.recipientId : conversation.initiatorId);
                  const userStatus = userStatuses[otherUserId] || { isOnline: false };
                  const displayName = otherUserInfo?.displayName || `User ${otherUserId}`;
                  const profilePicture = otherUserInfo?.profilePicture;
                  const initials = getUserInitials(displayName);

                  return (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="relative w-10 h-10 flex-shrink-0">
                          {profilePicture ? (
                            <img
                              src={profilePicture}
                              alt={displayName}
                              className="w-10 h-10 rounded-full object-cover border-2 border-[#e0e0e0]"
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextElementSibling.style.display = 'flex';
                              }}
                            />
                          ) : null}
                          <div
                            className={`w-10 h-10 rounded-full bg-gradient-to-br from-[#667eea] to-[#764ba2] text-white flex items-center justify-center font-semibold text-sm border-2 border-[#e0e0e0] ${profilePicture ? 'hidden' : 'flex'
                              }`}
                          >
                            {initials}
                          </div>
                        </div>
                        <div>
                          <div className="text-lg font-semibold text-[#333]">{conversation.title}</div>
                          <div className="text-sm text-[#666]">Conversation with {displayName}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-1 text-sm text-[#666]">
                        <span className={`w-2 h-2 rounded-full ${userStatus.isOnline ? 'bg-[#4caf50] shadow-sm' : 'bg-[#9e9e9e]'}`}></span>
                        <span>{userStatus.isOnline ? 'Online' : 'Offline'}</span>
                      </div>
                    </div>
                  );
                })()}
              </div>

              {/* Messages Area */}
              <div className="flex-1 p-5 overflow-y-auto bg-[#f9f9f9]">
                {messages.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full text-center">
                    <h3 className="text-lg font-semibold mb-2">No messages yet</h3>
                    <p className="text-gray-600">Start the conversation by sending a message!</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {messages.map((msg, idx) => (
                      <div
                        key={idx}
                        className={`flex items-end gap-3 ${msg.senderId === currentUserId ? 'justify-end' : 'justify-start'
                          }`}
                      >
                        {msg.senderId !== currentUserId && (
                          <img
                            src={AvatarIcon}
                            alt="Avatar"
                            className="w-8 h-8 rounded-full"
                          />
                        )}
                        <div
                          className={`max-w-[70%] px-4 py-3 rounded-2xl ${msg.senderId === currentUserId
                            ? 'bg-[#007bff] text-white rounded-br-sm'
                            : 'bg-[#e9ecef] text-[#333] rounded-bl-sm'
                            }`}
                        >
                          <div className="text-sm leading-5">{msg.content}</div>
                          <div className={`text-xs mt-1 ${msg.senderId === currentUserId ? 'text-[#c6c6c6]' : 'text-[#878787]'
                            }`}>
                            {formatTime(msg.sentDate)}
                          </div>
                        </div>
                        {msg.senderId === currentUserId && (
                          <img
                            src={AvatarIcon}
                            alt="Avatar"
                            className="w-8 h-8 rounded-full"
                          />
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Message Input Area */}
              <div className="p-5 border-t border-[#e0e0e0] bg-white">
                <div className="flex gap-3 items-end">
                  <TextArea
                    value={messageInput}
                    onChange={(e) => setMessageInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Type your message..."
                    autoSize={{ minRows: 1, maxRows: 4 }}
                    className="flex-1"
                  />
                  <Button
                    type="primary"
                    icon={<Send size={16} />}
                    onClick={sendMessage}
                    disabled={!messageInput.trim()}
                    className="flex items-center justify-center"
                  >
                    Send
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <EmptyView
                icon={<EmptyInboxIcon />}
                title={"No conversation selected"}
                description={"Select a conversation from the left sidebar to start chatting"}
              />
            </div>
          )}
        </div>
      </div>
    </PageWrapper>
  );
};

export default MessagesReact;
