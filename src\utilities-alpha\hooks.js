import { useEffect, useRef } from "react";

let drawerStack = [];

export function useHistoryStateHandler(isOpen, onClose) {
  const onCloseRef = useRef(onClose);
  onCloseRef.current = onClose;

  useEffect(() => {
    if (isOpen) {
      const stateId = Date.now(); // unique per drawer
      window.history.pushState({ isDrawer: true, id: stateId }, "");

      drawerStack.push({ id: stateId, close: onCloseRef.current });

      const handlePopState = event => {
        const last = drawerStack[drawerStack.length - 1];
        if (last && last.id === stateId) {
          drawerStack.pop();
          onCloseRef.current();
        }
      };

      window.addEventListener("popstate", handlePopState);

      return () => {
        window.removeEventListener("popstate", handlePopState);
        // If manually closed, remove from stack
        drawerStack = drawerStack.filter(d => d.id !== stateId);
      };
    }
  }, [isOpen]);
}
