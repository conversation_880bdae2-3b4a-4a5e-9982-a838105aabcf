import "./navigation.scss";
import NavigationItem from "../../../common/ProductsNavigation/NavigationItem";
import { Col, Row, Select } from "antd";
import { useNavigate, useLocation } from "react-router-dom";
const { Option } = Select;

const PhilipNavigation = props => {
  const navigate = useNavigate();
  const location = useLocation();
  const hash = window.location.hash.toLowerCase();
  const app =
    hash.indexOf("apps") > -1 ? "/Apps/searcher-philip/" : "/searcher-philip/";
  const { isHelpActive, labels, UserFeatures, disabled } = props;
  let features = [];
  const url = location?.pathname?.split("/");
  UserFeatures.forEach(element => {
    if (element.Url) features.push(element);
  });
  const hashesList = [
    "#/apps/searcher-philip/",
    "#/searcher-philip/",
    "#/apps/searcher-philip",
    "#/searcher-philip"
  ];

  return (
    <Row>
      <Col xs={0} sm={24}>
        <div className={`${disabled ? "disabled" : ""} product-menus`}>
          {features.map(item => (
            <NavigationItem
              Id={3}
              tooltipLabel={`${labels[`${item.Name}_TOOLTIP`]}`}
              isHelpActive={isHelpActive}
              IsActive={
                item.Url && window.location.href.indexOf(item.Url) > -1
                  ? true
                  : hashesList.indexOf(window.location.hash.toLowerCase()) > -1
                  ? true
                  : false
              }
              CssClass={"approve-menu"}
              CssClassIcon={item.CssClassIcon}
              Url={`${app}${item.Url}`}
              testId={`${item.Name}-button`}
              key={item.Name}
              menuText={""}
              isNavShrink={false}
              tooltipHelp={`${labels[`${item.Name}_HELP_TOOLTIP`]}`}
              NotificationBadge={false}
              NotificationCount={5}
              mobileName={item?.mobileName}
            />
          ))}
        </div>
      </Col>
      <Col xs={24} sm={0} className="aant-section-column-mobile">
        <Select
          style={{ width: "100%" }}
          defaultValue={`${app}automate`}
          onChange={e => {
            navigate(e);
          }}
        >
          {features.map(item => (
            <Option key={item.Url} value={`${app}${item.Url}`}>
              <div className="product-menus-mobile">
                <i className={item?.mobileIcon}></i>
                <p>{`${labels[`${item.Name}_TOOLTIP`]}`}</p>
              </div>
            </Option>
          ))}
        </Select>
      </Col>
    </Row>
  );
};

export default PhilipNavigation;
