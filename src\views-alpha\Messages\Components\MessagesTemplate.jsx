import { Button, Input } from "antd";
import AttachmentIcon from "../../../assets-alpha/images/svg/attachment.svg?react";
import SendIcon from "../../../assets-alpha/images/svg/send-message.svg?react";
import AvatarIcon from "../../../assets-alpha/images/svg/workplace-avatar.svg?react";
const MessagesTemplate = () => {
  return (
    <div className="flex w-full h-full justify-center !mx-6 py-4">
      <div className="flex flex-col w-full mb-[66px] h-[calc(100vh-162px)] overflow-y-auto">
        {[1, 2, 3, 4, 5, 6, 7, 8, 9].map(item => {
          return (
            <>
              <div className="flex w-full justify-end items-end pt-2 pb-2 pr-[72px] gap-4">
                <div className="flex flex-col max-w-[58%] items-center px-4 py-2 bg-[#8E81F5] rounded-2xl rounded-br-none ">
                  <span className="!font-normal text-[13px] text-[#FFFFFF] !leading-5">
                    Hey! How's everything going for you?
                  </span>
                  <span className="flex w-full justify-end !font-normal text-[11px] text-[#C6C6C6] leading-[100%]">
                    10:13 AM
                  </span>
                </div>
                <div>
                  <AvatarIcon className="!w-8 !h-8" />
                </div>
              </div>
              <div className="flex w-full justify-start items-end pt-2 pb-2 pl-[72px] gap-4">
                <div>
                  <AvatarIcon className="!w-8 !h-8" />
                </div>
                <div className="flex flex-col max-w-[58%] items-center px-4 py-2 bg-[#F4F2FE] rounded-2xl rounded-bl-none ">
                  <span className="font-normal text-[13px] text-[#343333] !leading-5">
                    Hey! How's everything going for you? Hope you are doing
                    well. Let me know if you need any help regarding the
                    project.
                  </span>
                  <span className="flex w-full justify-end !font-normal text-[11px] text-[#878787] leading-[100%]">
                    10:13 AM
                  </span>
                </div>
              </div>
            </>
          );
        })}
      </div>
      <div className="absolute w-full flex justify-center items-center bottom-0 border-t-[0.5px] border-[#EAE5FC] min-h-[60px]">
        <Input
          placeholder="Type a message"
          size="medium"
          bordered={false}
          className="!border !m-3 !border-[#F3F3F3] !bg-[#F3F1FD] !h-11 placeholder:text-[#878787] ![--ant-input-padding-inline:12px] [&_.ant-input-affix-wrapper]:!p-[3px_3px_3px_16px] [&_.ant-input-prefix]:!border-0 [&_.ant-input-prefix]:!mr-4 [&_.ant-input-suffix]:!-mr-1 hover:!border-[#8E81F5]"
          rootClassName="[&_.ant-input]:!ml-[6px]"
          prefix={<AttachmentIcon />}
          suffix={
            <Button
              type="primary"
              icon={<SendIcon />}
              className="flex justify-center items-center w-8 h-8 bg-[#8E81F5] !rounded-[50%] cursor-pointer"
            ></Button>
          }
        />
      </div>
    </div>
  );
};

export default MessagesTemplate;
