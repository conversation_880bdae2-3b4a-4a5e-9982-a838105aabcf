import {
  Upload,
  BarChart3
} from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "antd";
import { useScrollAnimation } from "../../hooks/useScrollAnimation";
import ResumeAnalysisIcon from "../../../../assets-alpha/images/svg/resume-analysis.svg?react";
import DirectSendIcon from "../../../../assets-alpha/images/svg/direct-send.svg?react";
import ResumeATS from "../../../../assets-alpha/images/svg/resume-analysis-score.svg?react";
import TickIcon from "../../../../assets-alpha/images/svg/tick-circle.svg?react";
import {
  CloseOutlined,
  PlusOutlined
} from "@ant-design/icons";
import {
  GetMarketTopSalariesBasedOnUserRolesApi,
  GetSuggestedProfilesForNewResumeApi,
  GetSuggestedSkillsForNewResumeApi,
  UploadAndGetRolesAndSkillsFromRandomResumeApi
} from "./ResumeAnalysisApi";
import { publicRoutes } from "../../../../Routes/routing";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

const ResumeAnalysis = () => {
  const labels = useSelector(state => state.systemLabel.labels);
  const navigate = useNavigate();
  const [fileData, setFileData] = useState("");
  const [loading, setLoading] = useState(false);
  const [showGraph, setShowGraph] = useState(false);
  const [fileName, setFileName] = useState("");
  const [roleId, setRoleId] = useState([]);
  const [salaries, setSalaries] = useState([]);
  const [suggestion, setSuggestion] = useState([]);
  const [skills, setSkills] = useState([]);
  const [selectedRole, setSelectedRole] = useState(null);
  const [animationStarted, setAnimationStarted] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);
  const [animatedValue, setAnimatedValue] = useState(0);
  const fileInputRef = useRef(null);

  const handleFileClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = e => {
    const file = e.target.files[0];
    if (file) {
      setFileName(file?.name);
      const reader = new FileReader();
      reader.onloadend = () => {
        const fullBase64 = reader.result;
        const base64Only = fullBase64.split(",")[1];
        setFileData(base64Only);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleClose = () => {
    setShowGraph(false);
    setSelectedIds([]);
    setFileName("");
    setFileData("");
    setSelectedRole(null);
    setSalaries([]);
    setAnimationStarted(false);
    setAnimatedValue(0);
  };
  const handleClick = id => {
    setSelectedIds(prev =>
      prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]
    );
  };
  useEffect(() => {
    if (fileData) {
      UploadResume();
    }
  }, [fileData]);

  const UploadResume = async () => {
    setLoading(true);
    try {
      const res = await UploadAndGetRolesAndSkillsFromRandomResumeApi(fileData);
      if (res?.success) {
        const roleIds = res?.items?.Profiles?.map(p => p.ProfileId) || [];
        setRoleId(roleIds);

        const data = {
          CountryId: 46, // pakistan
          CurrencyId: 2, // usd
          DurationType: 1, // year
          RoleIds: roleIds
        };

        const [resSalary, resSuggestion, resSkills] = await Promise.all([
          GetMarketTopSalariesBasedOnUserRolesApi(data),
          GetSuggestedProfilesForNewResumeApi(roleIds),
          GetSuggestedSkillsForNewResumeApi(roleIds)
        ]);

        if (resSalary?.success) setSalaries(resSalary);
        if (resSuggestion?.success) setSuggestion(resSuggestion);
        if (resSkills?.success) setSkills(resSkills);

        setShowGraph(true);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (salaries.items?.Top3Salaries?.length > 0 && !selectedRole) {
      const firstRole = salaries.items.Top3Salaries[0];
      setSelectedRole({
        level: firstRole.Lookupvalue,
        entry: firstRole.Entry,
        average: firstRole.Average,
        senior: firstRole.Senior
      });
    }
  }, [salaries, selectedRole]);

  useEffect(() => {
    if (showGraph) {
      setAnimationStarted(true);
      const interval = setInterval(() => {
        setAnimatedValue(prev => (prev < 94 ? prev + 2 : 94));
      }, 100);
      return () => clearInterval(interval);
    } else {
      setAnimationStarted(false);
      setAnimatedValue(0);
    }
  }, [showGraph]);

  const {
    elementRef: sectionRef,
    isVisible: sectionVisible
  } = useScrollAnimation({ delay: 200 });
  return (
    <section className="bg-[#F4F2FE] relative overflow-hidden pb-8 lg:pb-[120px]">
      {/* Background Circles */}
      <div className="absolute top-1/4 left-1/6 w-64 h-64 bg-gradient-radial from-company/15 via-company/5 to-transparent rounded-full blur-3xl animate-pulse"></div>
      <div
        className="absolute bottom-1/4 right-1/6 w-48 h-48 bg-gradient-radial from-primary/15 via-primary/5 to-transparent rounded-full blur-3xl animate-pulse"
        style={{ animationDelay: "2s" }}
      ></div>

      <div className="absolute inset-0 opacity-[0.03] pointer-events-none z-0">
        <div
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(to right, var(--company) 1px, transparent 1px),
              linear-gradient(to bottom, var(--company) 1px, transparent 1px),
              linear-gradient(to right, var(--primary) 1px, transparent 1px),
              linear-gradient(to bottom, var(--primary) 1px, transparent 1px)
            `,
            backgroundSize: "40px 40px, 40px 40px, 80px 80px, 80px 80px",
            backgroundPosition: "0 0, 0 0, 20px 20px, 20px 20px"
          }}
        ></div>
      </div>

      <div className="max-w-6xl mx-auto relative z-10 pt-[64px]">
        {/* Section Header */}
        <div
          ref={sectionRef}
          className={`text-center mb-8 md:mb-12 transition-all duration-800 ease-out ${
            sectionVisible
              ? "opacity-100 translate-y-0"
              : "opacity-0 translate-y-8"
          }`}
        >
          <h2 className="!m-0 font-medium text-[#343333] text-2xl lg:text-[40px] leading-[100%] lg:leading-[58px]">
            {labels?.Section2ProfessionalResumeTitle}{" "}
            <span className="text-[#8E81F5]">
              {labels?.landingWidgetNextstepTitle2}
            </span>
          </h2>

          <p className="!m-0 flex w-full justify-center text-center text-xs lg:!text-[16px] text-muted-foreground pt-3 px-6">
            {labels?.Resume_Analysis_Desc}
          </p>
        </div>

        {showGraph ? (
          <div className="flex lg:flex-row flex-col  w-full justify-center gap-6 px-4">
            <div className="flex flex-col justify-between w-full lg:w-[50%] bg-white border border-[#EAE5FC] rounded-3xl !p-6">
              <div className="mb-4">
                <div className="flex flex-col gap-2 w-full items-center">
                  <h1 className="!m-0 !text-sm lg:!text-[16px] font-semibold text-[#343333]">
                    {labels?.Resume_Graph_h1}
                  </h1>
                  <p className="text-[#878787] font-normal text-sm">
                    {labels?.Resume_Graph_p}
                  </p>
                </div>
                <div className="mt-4 lg:mt-11 w-full h-auto flex gap-3 flex-wrap overflow-auto">
                  {skills?.items?.map((item, id) => {
                    const isSelected = selectedIds.includes(id);

                    return (
                      <div
                        key={id}
                        onClick={() => handleClick(id)}
                        className={`cursor-pointer h-10 px-4 py-2  border border-[#EAE5FC] rounded-lg flex items-center gap-2 transition-all duration-200 ${
                          isSelected ? "bg-[#8E81F5]" : "bg-white"
                        }`}
                      >
                        <span
                          className={`${
                            isSelected ? "text-white" : "text-[#878787]"
                          }`}
                        >
                          <PlusOutlined />
                        </span>
                        <span
                          className={`text-sm font-normal !text-[12px] lg:text-sm ${
                            isSelected ? "text-white" : "text-[#878787]"
                          }`}
                        >
                          {item?.SkillValue}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>
              <div className="mt-auto">
                <Button
                  type="primary"
                  className="w-full md:!h-11 h-10"
                  onClick={() => {
                    navigate(publicRoutes.signup.path);
                  }}
                >
                  {labels?.Landing_Proceed_Label}
                </Button>
              </div>
            </div>
            <div className="flex flex-col gap-6 md:gap-6 w-full lg:w-[50%] justify-center items-center transition-all duration-800 ease-out delay-200 ">
              <div className="bg-card/95 rounded-3xl p-6  border border-border shadow-xl backdrop-blur-md relative overflow-hidden w-full">
                {/* Results Header */}
                <div className="flex items-center gap-4 mb-8">
                  <div className="w-9 h-9 md:w-14 md:h-14 bg-gradient-to-br from-company to-company/80 rounded-[7px] md:rounded-xl flex items-center justify-center shadow-lg shadow-company/30">
                    <BarChart3 className="w-4 h-4 md:w-6 md:h-6 text-company-foreground" />
                  </div>
                  <div>
                    <h4 className="text-sm md:text-2xl font-medium text-[#343333]">
                      {labels?.Salary_Insights}
                    </h4>
                    <p className="text-xs md:text-sm text-muted-foreground">
                      {labels?.Market_Data_Lbl}
                    </p>
                  </div>
                </div>

                {/* Salary Bars */}
                <div className="space-y-6 mb-8">
                  {selectedRole ? (
                    <>
                      {/* Entry Level */}
                      <div className="space-y-1">
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-[#343333] md:text-sm font-medium">
                            {labels?.Entry_Level_Lbl}
                          </span>
                          <span className="text-xs md:text-sm font-medium text-[#343333]">
                            ${selectedRole.entry}
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 md:h-3 overflow-hidden relative">
                          <div
                            className="absolute top-0 left-0 h-full rounded-full bg-[#02CAA8] transition-all duration-1000 ease-out"
                            style={{
                              width: animationStarted
                                ? `${Math.min(
                                    100,
                                    (selectedRole.entry / selectedRole.senior) *
                                      100
                                  )}%`
                                : "0%"
                            }}
                          />
                        </div>
                      </div>

                      {/* Average */}
                      <div className="space-y-1">
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-[#343333] md:text-sm font-medium">
                            {labels?.Average_Lbl}
                          </span>
                          <span className="text-xs text-[#343333] md:text-sm font-medium">
                            ${selectedRole.average}
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 md:h-3 overflow-hidden relative">
                          <div
                            className="absolute top-0 left-0 h-full rounded-full bg-[#02CAA8] transition-all duration-1000 ease-out"
                            style={{
                              width: animationStarted
                                ? `${Math.min(
                                    100,
                                    (selectedRole.average /
                                      selectedRole.senior) *
                                      100
                                  )}%`
                                : "0%"
                            }}
                          />
                        </div>
                      </div>

                      {/* Senior Level */}
                      <div className="space-y-1">
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-[#343333] md:text-sm font-medium">
                            {labels?.Senior_Level_Lbl}
                          </span>
                          <span className="text-xs md:text-sm font-medium text-[#343333]">
                            ${selectedRole.senior}
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 md:h-3 overflow-hidden relative">
                          <div
                            className="absolute top-0 left-0 h-full rounded-full bg-[#02CAA8] transition-all duration-1000 ease-out"
                            style={{
                              width: animationStarted ? "100%" : "0%"
                            }}
                          />
                        </div>
                      </div>
                    </>
                  ) : (
                    <p>{labels?.Load_Salaray_Data}</p>
                  )}
                </div>

                {/* Role Selection */}
                <div className="flex flex-col gap-[10px] w-full mt-6">
                  {salaries.items?.Top3Salaries?.map((role, index) => (
                    <div
                      key={index}
                      onClick={() =>
                        setSelectedRole({
                          level: role.Lookupvalue,
                          entry: role.Entry,
                          average: role.Average,
                          senior: role.Senior
                        })
                      }
                      className={`flex w-full text-xs md:text-sm font-medium bg-[#F4F2FE] justify-center items-center !h-11 rounded-lg cursor-pointer transition-all ${selectedRole?.level ===
                        role.Lookupvalue &&
                        "border border-[#8E81F5] text-[#8E81F5] "}`}
                    >
                      <p>
                        {role.Lookupvalue}: ${role.Average}/{labels?.Annual_Lbl}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
              <div
                onClick={handleFileClick}
                className="relative flex pl-4 gap-4 items-center w-full  h-[89px] rounded-[16px] border border-dashed border-[#C6C6C6] bg-white cursor-pointer"
              >
                <div className="flex justify-center items-center w-14 h-14 bg-[#F4F2FE] rounded-[12px]">
                  <DirectSendIcon />
                </div>
                <div className="flex flex-col justify-between h-11">
                  <h1 className="!m-0 !text-[16px] font-medium text-[#343333]">
                    {fileName}
                  </h1>
                  <p className="!m-0 font-normal !text-[16px] text-[#8E81F5]">
                    {labels?.Upload_Lbl}
                  </p>
                </div>
                <div
                  className="absolute top-[20px] right-[20px]"
                  onClick={e => {
                    e.stopPropagation();
                    handleClose();
                  }}
                >
                  <CloseOutlined />
                </div>
                <input
                  type="file"
                  accept=".pdf,.doc,.docx"
                  className="hidden"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col lg:flex-row w-full items-center lg:justify-between px-6 gap-8">
            {/* Left Column - Upload Section */}
            <div className="order-2 lg:order-1 space-y-8 w-full lg:w-[50%]">
              {/* Upload Card */}
              <div className="bg-card/95 rounded-3xl p-4 border border-border shadow-xl backdrop-blur-md relative overflow-hidden">
                <div className="text-center space-y-4 lg:space-y-6">
                  <div className="flex w-full h-[52px] lg:h-[102px] justify-center">
                    <div className="flex justify-center items-center w-[52px] h-[52px] lg:w-[102px] lg:h-[102px] mb-6 bg-[#8E81F5] rounded-[12px] animate-pulse">
                      <ResumeAnalysisIcon className="w-7 h-6 lg:w-auto lg:h-auto" />
                    </div>
                  </div>

                  <div className="flex flex-col gap-4">
                    <h3 className="text-sm lg:text-2xl text-[#343333] font-semibold">
                      {labels?.Resume_Upload_Lbl}
                    </h3>
                    <p className="text-muted-foreground text-xs lg:!text-[16px]">
                      {labels?.Resume_Upload_Desc}
                    </p>
                  </div>

                  {/* Drop Zone */}
                  <div
                    className="flex items-center justify-center h-[144px] lg:h-[275px] border-[0.8px] border-dashed border-muted-foreground/30 rounded-xl hover:border-primary/50 hover:bg-primary/5 transition-all duration-300 cursor-pointer group relative overflow-hidden"
                    onClick={handleFileClick}
                  >
                    <div className="flex flex-col gap-[13px] lg:gap-6">
                      <div className="w-[30px] h-[30px] lg:w-12 lg:h-12 mx-auto bg-[#F4F2FE] rounded-[8px] lg:rounded-[12px] flex items-center justify-center group-hover:bg-primary/10 group-hover:text-primary transition-all duration-300">
                        <DirectSendIcon className="w-4 h-4 mg:w-auto lg:h-auto" />
                      </div>
                      <div className="flex flex-col gap-[6px] lg:gap-3">
                        <p className="font-semibold text-[10px] lg:text-[20px] text-[#343333]">
                          {labels?.Drop_Resume_Lbl}
                        </p>
                        <p className="text-[9px] lg:text-[16px] font-normal text-muted-foreground">
                          {labels?.Resume_Type_Lbl}
                        </p>
                      </div>
                    </div>
                  </div>

                  <input
                    type="file"
                    accept=".pdf,.doc,.docx"
                    className="hidden"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                  />

                  {/* Upload button */}
                  <Button
                    type="primary"
                    size="lg"
                    loading={loading}
                    onClick={handleFileClick}
                    className="w-full !h-10 md:!h-11 !text-xs md:!text-[16px] bg-gradient-to-r from-primary to-company hover:from-primary/90 hover:to-company/90 text-primary-foreground shadow-lg shadow-primary/30 hover:shadow-primary/40 hover:-translate-y-0.5 transition-all duration-300 relative overflow-hidden"
                  >
                    <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-800 -translate-x-full hover:translate-x-full"></span>
                    <Upload className="w-5 h-5 mr-2" />
                    {labels?.Choose_File_Lbl}
                  </Button>
                </div>
              </div>
            </div>

            {/* Right Column - Analysis Results */}
            <div className="order-1 lg:order-2 flex flex-col gap-4 justify-center items-center w-full lg:w-[50%]">
              <ResumeATS />
              <div className="pt-[10px] lg:pt-0">
                <div className="relative z-10 flex items-center justify-center gap-2 !bg-white border border-[#EAE5FC] h-[42px] lg:h-[60px] rounded-[12px] px-2 lg:px-4 shadow-xl rotate-[-5deg]">
                  <TickIcon className="w-4 h-4 lg:w-auto lg:h-auto" />
                  <p className="!m-0 text-[#343333] text-[10px] lg:text-sm font-medium">
                    {labels?.Resume_Skills_Suggestion}
                  </p>
                </div>
                <div className="relative w-[90%] left-[5%] z-0 flex items-center justify-center gap-2 !bg-white border border-[#EAE5FC] h-[42px] lg:h-[60px] rounded-[12px] px-2 lg:px-4 shadow-xl rotate-[-1.2deg]">
                  <TickIcon className="w-4 h-4 lg:w-auto lg:h-auto" />
                  <p className="!m-0 text-[#343333] text-[10px] lg:text-sm font-medium">
                    {labels?.Achievement_Lbl}
                  </p>
                </div>
                <div className="relative w-[90%] z-10 flex items-center justify-center gap-2 !bg-white border border-[#EAE5FC] h-[42px] lg:h-[60px] rounded-[12px] px-2 lg:px-4 shadow-xl rotate-[-5deg]">
                  <TickIcon className="w-4 h-4 lg:w-auto lg:h-auto" />
                  <p className="!m-0 text-[#343333] text-[10px] lg:text-sm font-medium">
                    {labels?.Add_Industry_Lbl}
                  </p>
                </div>
                <div className="relative w-[90%] left-[8%] z-10 flex items-center justify-center gap-2 !bg-white border border-[#EAE5FC] h-[42px] lg:h-[60px] rounded-[12px] px-2 lg:px-4 shadow-xl rotate-[-1.2deg]">
                  <TickIcon className="w-4 h-4 lg:w-auto lg:h-auto" />
                  <p className="!m-0 text-[#343333] text-[10px] lg:text-sm font-medium">
                    {labels?.Optimize_Keywords_Lbl}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default ResumeAnalysis;
