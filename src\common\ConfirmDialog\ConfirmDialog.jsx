import "./confirmdialog.scss";

const Message = ({ children, testId, className }) => (
  <div className={`confirm-message ${className}`} data-testid={testId}>
    {children}
  </div>
);

const ActionButtons = ({ children, testId }) => (
  <div className="confirm-action-buttons" data-testid={testId}>
    {children}
  </div>
);

const Button = ({ children, className, testId, onClick }) => (
  <button
    className={`dialog-btn ${className}`}
    data-testid={testId}
    onClick={onClick}
  >
    {children}
  </button>
);

const ConfirmDialog = ({ children, testId, className }) => (
  <div
    className={`${className} confirm-dialog animated fadeInDown`}
    data-testid={testId}
  >
    {children}
  </div>
);

ConfirmDialog.Message = Message;
ConfirmDialog.ActionButtons = ActionButtons;
ConfirmDialog.Button = Button;

ConfirmDialog.Button.defaultProps = {
  className: ""
};

export default ConfirmDialog;
