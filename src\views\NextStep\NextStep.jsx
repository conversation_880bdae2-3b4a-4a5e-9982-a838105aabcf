import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import PageWrapper from "../../components/PageWrapper/PageWrapper";
import { notificationAction } from "../../actions/notification";
import "./nextstep.scss";
import { privateRoutes } from "../../Routes/routing";
import { StorageService } from "../../api/storage";
import { updatePhillipPopupStatusApi } from "../../components/Phillip/phillipApi";
import ResumeParser from "./components/ResumeParser/ResumeParser";
import { getChildFeatureByName } from "../Snapshot/snapshotApi";
import ResumeSuggestor from "./components/ResumeSuggestor/ResumeSuggestor";
import MarketAnalyzer from "./components/Market Analyzer/MarketAnalyzer";
import { GetUserExpectedSalaryApi } from "./nextstepApi";
import ConfirmDialog from "../../common/ConfirmDialog/ConfirmDialog";

const NextStep = (props) => {
  const [state, setState] = useState({
    UserFeatures: [],
    token: "",
    ExpenseCategories: [],
    Currencies: [],
    UserId: -1,
    dialogMessage: "",
    showPhillip: true,
    facebookClientId: "",
    linkedInClientId: "",
    isLoading: false,
  });

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { labels, isHelpActive, User } = useSelector((state) => ({
    labels: state.systemLabel.labels,
    isHelpActive: state.navigation.isHelpActive,
    User: state.userInfo.user ? state.userInfo.user : StorageService.getUser(),
  }));

  useEffect(() => {
    getChildFeatureByName({ featureName: "nextstep" })
      .then((res) => {
        setState((prevState) => ({
          ...prevState,
          UserFeatures: res.items,
        }));
        const { items } = res;
        const firstUrl = items[0];
        var queryString = window.location.href;
        queryString = queryString.toLowerCase().split("next-step")[1];
        if (queryString == "") {
          if (firstUrl) {
            const isApp =
              window.location.hash.toLowerCase().indexOf("apps") > -1;

            navigate((isApp ? "/apps" : "") + "/next-step/" + firstUrl.Url);
          }
        }
      })
      .catch((err) => console.log("Err ", err));

    let { UserEmail } = User;
    let Email = UserEmail;
    if (!Email) {
      const User = StorageService.getUser();
      Email = User.Email;
    }
    setState((prevState) => ({
      ...prevState,
      UserId: User.UserId,
    }));
  }, []);

  useEffect(() => {
    const firstUrl = state.UserFeatures[0];
    var queryString = window.location.href;
    queryString = queryString.toLowerCase().split("next-step")[1];
    if (queryString == "") {
      if (firstUrl) {
        const isApp = window.location.hash.toLowerCase().indexOf("apps") > -1;

        navigate((isApp ? "/apps" : "") + "/next-step/" + firstUrl.Url);
      }
    }
  }, [props.location]);

  const handleOkClick = () => {
    navigate(privateRoutes.resumeEdit.path);
  };

  const handleClosePhillip = () => {
    updatePhillipPopupStatusApi({ isFreelancer: true }).then((response) => {});
    setState((prevState) => ({ ...prevState, showPhillip: false }));
  };

  const handleShowPhillip = () => {
    setState((prevState) => ({ ...prevState, showPhillip: true }));
  };

  const handleSearcherPhillipSkip = () => {
    updatePhillipPopupStatusApi({ isFreelancer: true }).then((response) => {
      if (response.success) {
        const info = {
          message: props.searcherPhillipSkip,
          status: "success",
        };
        dispatch(notificationAction(info));
      }
    });
    setState((prevState) => ({ ...prevState, showPhillip: false }));
  };

  const handleUpdateCompanyPresentationsWidget = () => {
    setState((prevState) => ({
      ...prevState,
      isReloadCompanyPresentationsWidget: !prevState.isReloadCompanyPresentationsWidget,
    }));
  };

  const handleUpdateResumeShortlistWidgetWidget = () => {
    setState((prevState) => ({
      ...prevState,
      isReloadResumeShortlistWidget: !prevState.isReloadResumeShortlistWidget,
    }));
  };

  const reloadResumeWidgetHandler = () => {
    setState((prevState) => ({
      ...prevState,
      isReloadResumeWidget: !prevState.isReloadResumeWidget,
    }));
  };

  const GetUserExpectedSalary = () => {
    setState((prevState) => ({
      ...prevState,
      isLoading: true,
    }));
    GetUserExpectedSalaryApi(46, 22, 1)
      .then((res) => {
        if (res.success) {
          if (res.items.Top3Salaries.length < 1) {
            setState((prevState) => ({
              ...prevState,
              dialogMessage: labels.NextStep_No_Role_Message,
              isLoading: false,
            }));
          } else {
            setState((prevState) => ({
              ...prevState,
              isLoading: false,
            }));
          }
        }
        setState((prevState) => ({
          ...prevState,
          isLoading: false,
        }));
      })
      .catch((err) => {
        console.log(err);
        setState((prevState) => ({
          ...prevState,
          isLoading: false,
        }));
      });
  };

  const { showPhillip, dialogMessage, UserFeatures, isLoading } = state;
  const currentViewHash =
    window.location.hash.toLowerCase().split("/")[1] == "apps"
      ? window.location.hash.toLocaleLowerCase().split("/")[3]
      : window.location.hash.toLocaleLowerCase().split("/")[2];

  return (
    <PageWrapper>
      {isLoading && (
        <div id="loader-wrapper">
          <div className="loader-container">
            <div className="loader">
              <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
                <defs>
                  <filter id="goo">
                    <feGaussianBlur
                      in="SourceGraphic"
                      stdDeviation="6"
                      result="blur"
                    />
                    <feColorMatrix
                      in="blur"
                      mode="matrix"
                      values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                      result="goo"
                    />
                    <feBlend in="SourceGraphic" in2="goo" />
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </div>
      )}
      {dialogMessage && (
        <ConfirmDialog testId="confirm-diloag">
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              onClick={handleOkClick}
            >
              {"Ok"}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      {!isLoading &&
        !dialogMessage &&
        currentViewHash == "home" &&
        UserFeatures.findIndex(
          (a) => a.Url && a.Url.toLocaleLowerCase().trim() == currentViewHash
        ) > -1 && (
          <MarketAnalyzer
            open={showPhillip}
            onCloseModal={handleClosePhillip}
            onSkip={handleSearcherPhillipSkip}
            onResumeUpdate={reloadResumeWidgetHandler}
          />
        )}
      {currentViewHash == "resume-parser" &&
        UserFeatures.findIndex(
          (a) => a.Url && a.Url.toLocaleLowerCase().trim() == currentViewHash
        ) > -1 && (
          <ResumeParser
            open={showPhillip}
            onCloseModal={handleClosePhillip}
            onSkip={handleSearcherPhillipSkip}
            onResumeUpdate={reloadResumeWidgetHandler}
          />
        )}
      {currentViewHash == "resume-suggestor" &&
        UserFeatures.findIndex(
          (a) => a.Url && a.Url.toLocaleLowerCase().trim() == currentViewHash
        ) > -1 && (
          <ResumeSuggestor
            open={showPhillip}
            onCloseModal={handleClosePhillip}
            onSkip={handleSearcherPhillipSkip}
            onResumeUpdate={reloadResumeWidgetHandler}
          />
        )}
    </PageWrapper>
  );
};

export default NextStep;
