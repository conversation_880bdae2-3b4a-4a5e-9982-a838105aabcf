@use "../../../assets/sass/importFiles" as *;
.product-menus {
  margin-top: 10px;
  > div {
    align-self: center;
  }
  .help-icon {
    bottom: 10px;
  }

  .leftNavIcon {
    border-radius: 50%;
    min-width: 50px;
    text-align: center;
    height: 50px;
    display: flex;
    align-items: center;
    margin-right: 10px;
    background-color: transparent;
    border: 1px solid $purple4;
    &:before {
      /* use !important to prevent issues with browser extensions that change fonts */
      font-family: "icomoon" !important;
      speak: none;
      font-style: normal;
      font-weight: normal;
      font-variant: normal;
      text-transform: none;
      line-height: 1;

      /* Better Font Rendering =========== */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      content: "";
      display: inline-block;
      margin: 0 auto;
      // @extend %fi;
      font-size: 30px;
      color: $purple4; // content: $fi-profile;
    }
    &:hover,
    &.activeBtn {
      background-color: $purple4;
    }
    @media (max-width: 330px) {
      min-width: 35px;
      height: 35px;
    }
  }

  .tictell-home-icon {
    @extend .leftNavIcon;
    &:before {
      content: $fi-Phillip;
    }
  }

  .searcherphilip-statistic-icon {
    @extend .leftNavIcon;
    &:before {
      content: $fi-Phillip;
    }
  }

  .searcherphilip-automate-icon {
    @extend .leftNavIcon;
    &:before {
      content: $fi-Search;
    }
  }

  .searcherphilip-monitoring-icon {
    @extend .leftNavIcon;
    &:before {
      content: $fi-Monitoring;
    }
  }

  .assort-start-new-search-icon {
    @extend .leftNavIcon;
    &:before {
      content: $fi-Phillip;
    }
  }

  .assort-approve-time-and-expenses-icon {
    @extend .leftNavIcon;
    &:before {
      content: $fi-Tictell;
    }
  }
}

.product-menus-mobile {
  width: 100%;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
  height: 100%;
  p {
    margin: 0;
    font-family: "Inter";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: -0.5px;
    width: 100%;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
  }
  display: flex;
  align-items: center;
  .leftNavIconMobile {
    padding-right: 8px;
    &:before {
      /* use !important to prevent issues with browser extensions that change fonts */
      font-family: "icomoon" !important;
      speak: none;
      font-style: normal;
      font-weight: normal;
      font-variant: normal;
      text-transform: none;
      line-height: 1;

      /* Better Font Rendering =========== */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      content: "";
      display: inline-block;
      margin: 0 auto;
      // @extend %fi;
      font-size: 18px;
      color: $purple4; // content: $fi-profile;
    }
  }

  .icon-phillips-mobile-4 {
    @extend .leftNavIconMobile;
    &:before {
      content: $fi-Opportunity;
      display: flex;
    }
  }

  .icon-phillips-mobile-5 {
    @extend .leftNavIconMobile;
    &:before {
      content: $fi-Phillip;
      display: flex;
    }
  }

  .icon-phillips-mobile-0 {
    @extend .leftNavIconMobile;
    &:before {
      content: $fi-Search;
      display: flex;
    }
  }

  .icon-phillips-mobile-1 {
    @extend .leftNavIconMobile;
    &:before {
      content: $fi-Monitoring;
      display: flex;
    }
  }

  .icon-phillips-mobile-2 {
    @extend .leftNavIconMobile;
    &:before {
      content: $fi-Phillip;
      display: flex;
    }
  }

  .icon-phillips-mobile-3 {
    @extend .leftNavIconMobile;
    &:before {
      content: $fi-Tictell;
      display: flex;
    }
  }
}
.aant-section-column-mobile {
  margin-top: 4px;
  .ant-select-selection-item {
    line-height: 50px;
    padding-left: 15px;
  }
  .ant-select-selector {
    height: 50px !important;
  }
  .ant-select-show-search {
    height: 50px !important;
    line-height: 50px;
  }
  .ant-select-selection-placeholder {
    line-height: 50px !important;
    font-family: "Inter";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    letter-spacing: -0.5px;
    width: 100%;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
  }
  .ant-select-item-option-content {
    font-family: "Inter";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
  }
  .ant-select-selection-search-input {
    font-family: "Inter";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
  }
  .ant-select-selection-item {
    line-height: 50px !important;
    font-family: "Inter";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
  }
}
