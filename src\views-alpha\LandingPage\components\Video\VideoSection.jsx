import { useSelector } from "react-redux";

const VideoSection = () => {
  const labels = useSelector(state => state.systemLabel.labels);
  return (
    <div className="pb-8 md:pb-24 flex flex-col w-full justify-center items-center">
      <div className="pt-11 md:pt-[64px] flex flex-col w-full items-center text-center">
        <h1 className="!m-0 font-medium !text-2xl md:!text-[40px] text-[#343333] leading-[100%] md:leading-[58px] md:px-0 !px-2">
          {labels?.VideoSection_h1}{" "}
          <span className="text-[#8E81F5]">{labels?.VideoSection_h2}</span>
        </h1>
        <p className="flex w-full max-w-[676px] text-center !mt-4 !text-xs px-4 md:!text-[16px] font-normal text-[#878787] leading-6 pb-8 md:pb-[62px]">
          {labels?.VideoSection_Desc}
        </p>
      </div>

      <div className="relative w-full h-[240px] md:h-auto pb-0 lg:pb-[114px] flex justify-center items-center md:bg-[url('/assets/images/video-bg.webp')] bg-contain bg-right bg-no-repeat">
        <div className="md:hidden absolute top-[69px] left-[32px]">
          <img src="/assets/images/video-bg.webp" alt="" />
        </div>
        <video
          className="w-[69.5%] min-w-[343px] h-auto z-10 rounded-[6px]"
          controls
          poster="/assets/images/maxresdefault.jpg"
        >
          <source src="/video/homepage.mp4" type="video/mp4" />
        </video>
      </div>
    </div>
  );
};

export default VideoSection;
