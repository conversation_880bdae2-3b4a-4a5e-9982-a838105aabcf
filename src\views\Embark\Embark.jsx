import React, { useState, useEffect } from "react";
import { connect } from "react-redux";
import PageWrapper from "../../components/PageWrapper/PageWrapper";
import { notificationAction } from "../../actions/notification";
import "./embark.scss";
import { privateRoutes } from "../../Routes/routing";
import { getChildFeatureByName } from "../Snapshot/snapshotApi";
import { StorageService } from "../../api/storage";
import SnapshotNavigation from "./Navigation/Navigation";
import { embarkLogin, embarkAppLoginApi } from "./embarkApi";
import ManageFlows from "./views/Flows/ManageFlows";
import ManageIPros from "./views/ManageIPros/ManageIPros";
import MyWork from "./views/MyWork/MyWork";
import Dashboard from "./views/Dashboard/Dashboard";
import { useNavigate } from "react-router-dom";
import ConfirmDialog from "../../common/ConfirmDialog/ConfirmDialog";

const Snapshot = ({ labels, isHelpActive, location, User, dispatch }) => {
  const [state, setState] = useState({
    UserFeatures: [],
    token: "",
    ExpenseCategories: [],
    Currencies: [],
    isLoading: true,
    UserId: -1,
    dialogMessage: "",
  });

  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getChildFeatureByName({ featureName: "embark" });
        setState((prevState) => ({
          ...prevState,
          UserFeatures: res.items,
        }));
        const { items } = res;
        const firstUrl = items.find((a) => a.Url);
        let queryString = window.location.href.toLowerCase().split("embark")[1];
        if (queryString === "" && firstUrl) {
          const isApp = window.location.hash.toLowerCase().indexOf("apps") > -1;

          navigate((isApp ? "/apps" : "") + "/embark/" + firstUrl.Url);
        }
      } catch (err) {
        console.log("Err ", err);
      }

      let Email = User.UserEmail || StorageService.getUser().Email;
      setState((prevState) => ({
        ...prevState,
        UserId: User.UserId,
      }));

      try {
        const res = await embarkAppLoginApi(Email);
        if (res.success) {
          const loginRes = await embarkLogin(Email);
          if (loginRes.success) {
            const { access_token } = loginRes.items;
            setState((prevState) => ({
              ...prevState,
              token: access_token,
              isLoading: false,
            }));
          }
        }
      } catch (err) {
        console.log("Error ", err);
      }
    };

    fetchData();
  }, [User, navigate]);

  const handleOkClick = () => {
    navigate(privateRoutes.dashboard.path);
  };

  const {
    UserFeatures,
    token,
    Currencies,
    dialogMessage,
    ExpenseCategories,
    isLoading,
    UserId,
    pendingtimesheet,
    Rejectedtimesheet,
    Rejectedexpense,
    Pendingexpense,
  } = state;

  const currentViewHash =
    window.location.hash.toLowerCase().split("/")[1] === "apps"
      ? window.location.hash.toLocaleLowerCase().split("/")[3]
      : window.location.hash.toLocaleLowerCase().split("/")[2];

  return (
    <PageWrapper className={`snapshot-page`}>
      {isLoading && (
        <div id="loader-wrapper">
          <div className="loader-container">
            <div className="loader">
              <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
                <defs>
                  <filter id="goo">
                    <feGaussianBlur
                      in="SourceGraphic"
                      stdDeviation="6"
                      result="blur"
                    />
                    <feColorMatrix
                      in="blur"
                      mode="matrix"
                      values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                      result="goo"
                    />
                    <feBlend in="SourceGraphic" in2="goo" />
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </div>
      )}
      <SnapshotNavigation
        disabled={`${isLoading ? "disabled" : ""}`}
        labels={labels}
        isHelpActive={isHelpActive}
        UserFeatures={UserFeatures}
      />
      {dialogMessage && (
        <ConfirmDialog testId="confirm-diloag">
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              onClick={handleOkClick}
            >
              {"Ok"}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      {!isLoading && (
        <React.Fragment>
          {currentViewHash === "home" &&
            UserFeatures.findIndex(
              (a) =>
                a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
            ) > -1 && (
              <Dashboard
                token={token}
                Readyforapproval={pendingtimesheet}
                Rejected={Rejectedtimesheet}
                asOwnerCount={"12"}
                Rejectedexpense={Rejectedexpense}
                Pendingexpense={Pendingexpense}
                UserFeatures={UserFeatures}
              />
            )}
          {currentViewHash === "mywork" &&
            UserFeatures.findIndex(
              (a) =>
                a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
            ) > -1 && (
              <MyWork
                token={token}
                Currencies={Currencies}
                ExpenseCategories={ExpenseCategories}
              />
            )}
          {currentViewHash === "manageipros" &&
            UserFeatures.findIndex(
              (a) =>
                a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
            ) > -1 && (
              <ManageIPros
                token={token}
                Currencies={Currencies}
                ExpenseCategories={ExpenseCategories}
                UserId={UserId}
                locationProp={location}
              />
            )}
          {currentViewHash === "manageflows" &&
            UserFeatures.findIndex(
              (a) =>
                a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
            ) > -1 && (
              <ManageFlows
                token={token}
                Currencies={Currencies}
                ExpenseCategories={ExpenseCategories}
                UserId={UserId}
              />
            )}
        </React.Fragment>
      )}
    </PageWrapper>
  );
};

const mapStateToProps = ({ systemLabel, navigation, userInfo }) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  const User = user ? user : StorageService.getUser();
  const { isHelpActive } = navigation;
  return { labels, isHelpActive, User };
};

export default connect(mapStateToProps, { notificationAction })(Snapshot);
