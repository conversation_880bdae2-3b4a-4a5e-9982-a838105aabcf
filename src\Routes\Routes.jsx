import includes from "lodash/includes";
import { lazy, Suspense, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Navigate,
  Route,
  HashRouter as Router,
  Routes,
  useLocation,
  useNavigate
} from "react-router-dom";
import { useStopwatch } from "react-timer-hook";
import { StorageService } from "../api/storage";
import LoadingMain, {
  LoadingMask
} from "../common-alpha/LoadingMain/LoadingMain";
import { logoutApi } from "../components/Navigation/navigationApi";
import { logoutAction } from "../store/initialConfig";
import { roles } from "../utilities/helpers";
import { appMetaData } from "../utilities/metaData";
import MobileLogin from "../views/LeadPage/components/Auth/MobileLogin";
import { appsRoutes, privateRoutes, publicRoutes } from "./routing";
const Cookie = lazy(() => import("../common/Cookie/Cookie"));
const AppViewWrapper = lazy(() =>
  import("../components/AppViewWrapper/AppViewWrapper")
);
const ViewWrapper = lazy(() =>
  import("../components-alpha/ViewWrapper/ViewWrapper")
);
const { ipro, searcher } = roles;

const RoutesComponent = () => {
  return (
    <Router>
      <Suspense fallback={<LoadingMain />}>
        <Routes>
          <Route
            path={publicRoutes.default.path}
            element={<PublicRoute component={publicRoutes.default.component} />}
          />
          <Route
            path={publicRoutes.ipro.path}
            element={<PublicRoute component={publicRoutes.ipro.component} />}
          />
          <Route
            path={publicRoutes.login.path}
            element={<PublicRoute component={publicRoutes.login.component} />}
          />

          <Route
            path={publicRoutes.signup.path}
            element={<PublicRoute component={publicRoutes.signup.component} />}
          />
          <Route
            path={publicRoutes.forgetPassword.path}
            element={
              <PublicRoute component={publicRoutes.forgetPassword.component} />
            }
          />

          <Route
            path={`${publicRoutes.bottomFeature.path}/:pageId`}
            element={
              <PublicRoute component={publicRoutes.bottomFeature.component} />
            }
          />
          {/* <Route
          path={publicRoutes.mobileLogin.path}
          element={
            <PublicRoute component={publicRoutes.mobileLogin.component} />
          }
        /> */}
          <Route
            path={publicRoutes.redirectedLogin.path}
            element={
              <PublicRoute component={publicRoutes.redirectedLogin.component} />
            }
          />
          <Route
            path={`${publicRoutes.emailverification.path}/:token`}
            element={
              <PublicRoute
                component={publicRoutes.emailverification.component}
              />
            }
          />
          <Route
            path={`${publicRoutes.redirectedfrom.path}`}
            element={
              <PublicRoute component={publicRoutes.redirectedfrom.component} />
            }
          />

          <Route
            path={appsRoutes.tictell.path}
            element={
              <AppRoute
                component={appsRoutes.tictell.component}
                roles={privateRoutes.dashboard.roles}
              />
            }
          />
          <Route
            path={appsRoutes.embark.path}
            element={
              <AppRoute
                component={appsRoutes.embark.component}
                roles={privateRoutes.dashboard.roles}
              />
            }
          />
          <Route
            path={appsRoutes.sense.path}
            element={
              <AppRoute
                component={appsRoutes.sense.component}
                roles={privateRoutes.dashboard.roles}
              />
            }
          />
          <Route
            path={appsRoutes.headsup.path}
            element={
              <AppRoute
                component={appsRoutes.headsup.component}
                roles={privateRoutes.dashboard.roles}
              />
            }
          />
          <Route
            path={appsRoutes.snapshot.path}
            element={
              <AppRoute
                component={appsRoutes.snapshot.component}
                roles={privateRoutes.dashboard.roles}
              />
            }
          />
          <Route
            path={appsRoutes.assort.path}
            element={
              <AppRoute
                component={appsRoutes.assort.component}
                roles={privateRoutes.dashboard.roles}
              />
            }
          />
          <Route
            path={appsRoutes.philip.path}
            element={
              <AppRoute
                component={appsRoutes.philip.component}
                roles={privateRoutes.dashboard.roles}
              />
            }
          />
          <Route
            path={appsRoutes.nextstep.path}
            element={
              <AppRoute
                component={appsRoutes.nextstep.component}
                roles={privateRoutes.dashboard.roles}
              />
            }
          />
          <Route
            path={appsRoutes.iproPhilip.path}
            element={
              <AppRoute
                component={appsRoutes.iproPhilip.component}
                roles={privateRoutes.dashboard.roles}
              />
            }
          />
          <Route
            path={privateRoutes.dashboard.path}
            element={
              <PrivateRoute
                component={privateRoutes.dashboard.component}
                roles={privateRoutes.dashboard.roles}
              />
            }
          />
          <Route
            path={privateRoutes.Messages.path}
            element={
              <PrivateRoute
                component={privateRoutes.Messages.component}
                roles={privateRoutes.Messages.roles}
              />
            }
          />
          {/* <Route
            path={privateRoutes.createMessage.path}
            element={
              <PrivateRoute
                component={privateRoutes.createMessage.component}
                roles={privateRoutes.createMessage.roles}
              />
            }
          />
          <Route
            path={privateRoutes.inboxMessages.path}
            element={
              <PrivateRoute
                component={privateRoutes.inboxMessages.component}
                roles={privateRoutes.inboxMessages.roles}
              />
            }
          />
          <Route
            path={privateRoutes.sentMessages.path}
            element={
              <PrivateRoute
                component={privateRoutes.sentMessages.component}
                roles={privateRoutes.sentMessages.roles}
              />
            }
          /> */}
          <Route
            path={privateRoutes.iproNewOpportunity.path}
            element={
              <PrivateRoute
                component={privateRoutes.iproNewOpportunity.component}
                roles={privateRoutes.iproNewOpportunity.roles}
              />
            }
          />
          <Route
            path={privateRoutes.iproNewOpportunityWithId.path}
            element={
              <PrivateRoute
                component={privateRoutes.iproNewOpportunityWithId.component}
                roles={privateRoutes.iproNewOpportunity.roles}
              />
            }
          />

          <Route
            path={privateRoutes.searcherCreateCollaboration.path}
            element={
              <PrivateRoute
                component={privateRoutes.searcherCreateCollaboration.component}
                roles={privateRoutes.searcherCreateCollaboration.roles}
              />
            }
          />
          <Route
            path={privateRoutes.resume.path}
            element={
              <PrivateRoute
                component={privateRoutes.resume.component}
                roles={privateRoutes.resume.roles}
              />
            }
          />
          <Route
            path={privateRoutes.resumeBuilder.path}
            element={
              <PrivateRoute
                component={privateRoutes.resumeBuilder.component}
                roles={privateRoutes.resumeBuilder.roles}
              />
            }
          />
          <Route
            path={privateRoutes.resumeEdit.path}
            element={
              <PrivateRoute
                component={privateRoutes.resumeEdit.component}
                roles={privateRoutes.resumeEdit.roles}
              />
            }
          />

          <Route
            path={privateRoutes.presentations.path}
            element={
              <PrivateRoute
                component={privateRoutes.presentations.component}
                roles={privateRoutes.presentations.roles}
              />
            }
          />
          <Route
            path={privateRoutes.statistics.path}
            element={
              <PrivateRoute
                component={privateRoutes.statistics.component}
                roles={privateRoutes.statistics.roles}
              />
            }
          />
          <Route
            path={privateRoutes.feedback.path}
            element={
              <PrivateRoute
                component={privateRoutes.feedback.component}
                roles={privateRoutes.feedback.roles}
              />
            }
          />

          <Route
            path={privateRoutes.about.path}
            element={
              <PrivateRoute
                component={privateRoutes.about.component}
                roles={privateRoutes.about.roles}
              />
            }
          />

          <Route
            path={privateRoutes.faq.path}
            element={
              <PrivateRoute
                component={privateRoutes.faq.component}
                roles={privateRoutes.faq.roles}
              />
            }
          />

          <Route
            path={privateRoutes.alpha.companies.path}
            element={
              <PrivateRoute
                component={privateRoutes.alpha.companies.component}
                roles={privateRoutes.alpha.companies.roles}
              />
            }
          />
          <Route
            path={privateRoutes.shortlists.path}
            element={
              <PrivateRoute
                component={privateRoutes.shortlists.component}
                roles={privateRoutes.shortlists.roles}
              />
            }
          />

          <Route
            path={privateRoutes.searcherAcceptedCollaborations.path}
            element={
              <PrivateRoute
                component={
                  privateRoutes.searcherAcceptedCollaborations.component
                }
                roles={privateRoutes.searcherAcceptedCollaborations.roles}
              />
            }
          />

          <Route
            path={privateRoutes.iproActiveCollaborations.path}
            element={
              <PrivateRoute
                component={privateRoutes.iproActiveCollaborations.component}
                roles={privateRoutes.iproActiveCollaborations.roles}
              />
            }
          />

          <Route
            path={privateRoutes.searcherCreateOpportunity.path}
            element={
              <PrivateRoute
                component={privateRoutes.searcherCreateOpportunity.component}
                roles={privateRoutes.searcherCreateOpportunity.roles}
              />
            }
          />
          <Route
            path={privateRoutes.searcherDraftOpportunity.path}
            element={
              <PrivateRoute
                component={privateRoutes.searcherDraftOpportunity.component}
                roles={privateRoutes.searcherDraftOpportunity.roles}
              />
            }
          />
          <Route
            path={privateRoutes.searcherSentOpportunity.path}
            element={
              <PrivateRoute
                component={privateRoutes.searcherSentOpportunity.component}
                roles={privateRoutes.searcherSentOpportunity.roles}
              />
            }
          />
          <Route
            path={privateRoutes.iproPhillip.path}
            element={
              <PrivateRoute
                component={privateRoutes.iproPhillip.component}
                roles={privateRoutes.iproPhillip.roles}
              />
            }
          />
          <Route
            path={privateRoutes.searcherPhillip.path}
            element={
              <PrivateRoute
                component={privateRoutes.searcherPhillip.component}
                roles={privateRoutes.searcherPhillip.roles}
              />
            }
          />

          <Route
            path={privateRoutes.resumeSearch.path}
            element={
              <PrivateRoute
                component={privateRoutes.resumeSearch.component}
                roles={privateRoutes.resumeSearch.roles}
              />
            }
          />
          <Route
            path={privateRoutes.findProfessional.path}
            element={
              <PrivateRoute
                component={privateRoutes.findProfessional.component}
                roles={privateRoutes.findProfessional.roles}
              />
            }
          />
          <Route
            path={privateRoutes.Network.path}
            element={
              <PrivateRoute
                component={privateRoutes.Network.component}
                roles={privateRoutes.Network.roles}
              />
            }
          />
          <Route
            path={privateRoutes.connectedNetwork.path}
            element={
              <PrivateRoute
                component={privateRoutes.connectedNetwork.component}
                roles={privateRoutes.connectedNetwork.roles}
              />
            }
          />
          <Route
            path={privateRoutes.invitedNetwork.path}
            element={
              <PrivateRoute
                component={privateRoutes.invitedNetwork.component}
                roles={privateRoutes.invitedNetwork.roles}
              />
            }
          />
          <Route
            path={privateRoutes.invitationNetwork.path}
            element={
              <PrivateRoute
                component={privateRoutes.invitationNetwork.component}
                roles={privateRoutes.invitationNetwork.roles}
              />
            }
          />
          <Route
            path={privateRoutes.savedSearch.path}
            element={
              <PrivateRoute
                component={privateRoutes.savedSearch.component}
                roles={privateRoutes.savedSearch.roles}
              />
            }
          />
          <Route
            path={privateRoutes.snapshot.path}
            element={
              <PrivateRoute
                component={privateRoutes.snapshot.component}
                roles={privateRoutes.snapshot.roles}
              />
            }
          >
            {privateRoutes.snapshot.children.map(child => (
              <Route
                path={child.path}
                key={child.path}
                element={
                  <PrivateRoute
                    component={privateRoutes.snapshot.component}
                    roles={privateRoutes.snapshot.roles}
                  />
                }
              />
            ))}
          </Route>
          <Route
            path={`${privateRoutes.snapshotAlpha.path}/*`}
            element={
              <PrivateRoute
                component={privateRoutes.snapshotAlpha.component}
                roles={privateRoutes.snapshotAlpha.roles}
              />
            }
          >
            {/* {privateRoutes.snapshotAlpha.children.map(child => (
            <Route
              path={child.path}
              key={child.path}
              element={
                <PrivateRoute
                  component={privateRoutes.snapshotAlpha.component}
                  roles={privateRoutes.snapshotAlpha.roles}
                />
              }
            />
          ))} */}
          </Route>
          <Route
            path={privateRoutes.tictell.path}
            element={
              <PrivateRoute
                component={privateRoutes.tictell.component}
                roles={privateRoutes.tictell.roles}
              />
            }
          >
            {privateRoutes.tictell.children.map(child => (
              <Route
                path={child.path}
                key={child.path}
                element={
                  <PrivateRoute
                    component={privateRoutes.tictell.component}
                    roles={privateRoutes.tictell.roles}
                  />
                }
              />
            ))}
          </Route>
          <Route
            path={privateRoutes.assort.path}
            element={
              <PrivateRoute
                component={privateRoutes.assort.component}
                roles={privateRoutes.assort.roles}
              />
            }
          >
            {privateRoutes.assort.children.map(child => (
              <Route
                path={child.path}
                key={child.path}
                element={
                  <PrivateRoute
                    component={privateRoutes.assort.component}
                    roles={privateRoutes.assort.roles}
                  />
                }
              />
            ))}
          </Route>
          <Route
            path={privateRoutes.embark.path}
            element={
              <PrivateRoute
                component={privateRoutes.embark.component}
                roles={privateRoutes.embark.roles}
              />
            }
          >
            {privateRoutes.embark.children.map(child => (
              <Route
                path={child.path}
                key={child.path}
                element={
                  <PrivateRoute
                    component={privateRoutes.embark.component}
                    roles={privateRoutes.embark.roles}
                  />
                }
              />
            ))}
          </Route>
          <Route
            path={privateRoutes.headsup.path}
            element={
              <PrivateRoute
                component={privateRoutes.headsup.component}
                roles={privateRoutes.headsup.roles}
              />
            }
          >
            {privateRoutes.headsup.children.map(child => (
              <Route
                path={child.path}
                key={child.path}
                element={
                  <PrivateRoute
                    component={privateRoutes.headsup.component}
                    roles={privateRoutes.headsup.roles}
                  />
                }
              />
            ))}
          </Route>
          <Route
            path={privateRoutes.philip.path}
            element={
              <PrivateRoute
                component={privateRoutes.philip.component}
                roles={privateRoutes.philip.roles}
              />
            }
          >
            {privateRoutes.philip.children.map(child => (
              <Route
                path={child.path}
                key={child.path}
                element={
                  <PrivateRoute
                    component={privateRoutes.philip.component}
                    roles={privateRoutes.philip.roles}
                  />
                }
              />
            ))}
          </Route>
          <Route
            path={privateRoutes.iproPhilip.path}
            element={
              <PrivateRoute
                component={privateRoutes.iproPhilip.component}
                roles={privateRoutes.iproPhilip.roles}
              />
            }
          />
          <Route
            path={privateRoutes.sense.path}
            element={
              <PrivateRoute
                component={privateRoutes.sense.component}
                roles={privateRoutes.sense.roles}
              />
            }
          >
            {privateRoutes.sense.children.map(child => (
              <Route
                path={child.path}
                key={child.path}
                element={
                  <PrivateRoute
                    component={privateRoutes.sense.component}
                    roles={privateRoutes.sense.roles}
                  />
                }
              />
            ))}
          </Route>
          <Route
            path={privateRoutes.nextstep.path}
            element={
              <PrivateRoute
                component={privateRoutes.nextstep.component}
                roles={privateRoutes.nextstep.roles}
              />
            }
          >
            {privateRoutes.nextstep.children.map(child => (
              <Route
                path={child.path}
                key={child.path}
                element={
                  <PrivateRoute
                    component={privateRoutes.nextstep.component}
                    roles={privateRoutes.nextstep.roles}
                  />
                }
              />
            ))}
          </Route>

          <Route path="/app-login" element={<MobileLogin />} />
          <Route
            path={publicRoutes.noMatch.path}
            element={<PublicRoute component={publicRoutes.noMatch.component} />}
          />
        </Routes>
      </Suspense>
    </Router>
  );
};
const PublicRoute = ({ component: Component }) => {
  return <Component />;
};
const PrivateRoute = ({ component: Component, roles }) => {
  const userInfo = useSelector(state => state?.userInfo);
  const {
    isAuthenticated,
    user: { IsFreelancer }
  } = userInfo;
  const navigate = useNavigate();
  const dispatch = useDispatch();
  let user = StorageService.getUser();
  const { seconds, minutes, hours, start, pause, reset } = useStopwatch({
    autoStart: false
  });
  const { pathname } = useLocation();
  const role = IsFreelancer ? ipro : searcher;
  useEffect(() => {
    if (!user?.userFeatures) {
      dispatch(logoutAction());
      logoutApi().then(response => {
        if (response.success) {
          StorageService.clearAll();
          navigate("/");
          // window.location.href = RESET_LANDINGPAGE_URL;
          return;
        }
      });
    }
  }, [dispatch, user]);

  useEffect(() => {
    let currentMetaData = appMetaData[pathname];
    if (currentMetaData) {
      document.title = currentMetaData.title;
      document
        .querySelector('meta[name="description"]')
        ?.setAttribute("content", currentMetaData.description);
    } else {
      document.title = appMetaData.prodoo.title;
      document
        .querySelector('meta[name="description"]')
        ?.setAttribute("content", appMetaData.prodoo.description);
    }
  }, [pathname]);

  return isAuthenticated ? (
    <ViewWrapper>
      {includes(roles, role) ? (
        <Suspense fallback={<LoadingMask />}>
          <Component
            startTimer={start}
            pauseTimer={pause}
            stopTimer={reset}
            seconds={seconds}
            minutes={minutes}
            hours={hours}
          />
        </Suspense>
      ) : (
        <Navigate to={privateRoutes.dashboard.path} />
      )}
      <Cookie />
    </ViewWrapper>
  ) : (
    <Navigate
      to={{
        pathname: publicRoutes.default.path,
        state: { from: location }
      }}
    />
  );
};

const AppRoute = ({ component: Component, roles }) => {
  const userInfo = useSelector(state => state?.userInfo);
  const {
    isAuthenticated,
    user: { IsFreelancer }
  } = userInfo;
  const { seconds, minutes, hours, start, pause, reset } = useStopwatch({
    autoStart: false
  });
  const role = IsFreelancer ? ipro : searcher;
  const location = useLocation();
  return isAuthenticated ? (
    <AppViewWrapper>
      {includes(roles, role) ? (
        <Component
          startTimer={start}
          pauseTimer={pause}
          stopTimer={reset}
          seconds={seconds}
          minutes={minutes}
          hours={hours}
        />
      ) : (
        <Navigate to={privateRoutes.dashboard.path} />
      )}
      <Cookie />
    </AppViewWrapper>
  ) : (
    <Navigate
      to={{
        pathname: publicRoutes.default.path,
        state: { from: location }
      }}
    />
  );
};
export default RoutesComponent;
