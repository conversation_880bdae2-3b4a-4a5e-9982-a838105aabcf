import { Typography } from "antd";
import LeftIcon from "../../../assets-alpha/images/svg/arrow-left.svg?react";
import { NavLinks } from "../Settings";
import { useSelector } from "react-redux";
const { Title } = Typography;

const SettingTitle = ({ onSelectedTab, selectedTab }) => {
  const labels = useSelector(state => state.systemLabel.labels?.setting);

  return (
    <div className="">
      <div className="flex justify-between items-center px-6 cursor-pointer">
        <Title
          onClick={() => onSelectedTab(-1)}
          level={5}
          className="font-semibold !mb-0 flex gap-4 !text-[var(--purple)] items-center"
        >
          <LeftIcon />
          {NavLinks(labels)[selectedTab].text}
        </Title>
      </div>
    </div>
  );
};

export default SettingTitle;
