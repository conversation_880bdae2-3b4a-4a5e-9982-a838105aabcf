import clsx from "clsx";
import { replace } from "lodash";
import { useState } from "react";
import { NavLink } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import Icon from "../../../common-alpha/Icon/Icon";
import HelpGuideIcon from "./HelpGuide";

const NavLinkButton = ({
  children,
  classIcon,
  className,
  to,
  testId,
  onClick,
  disabled,
  inActive,
  navIcon,
  childClassName,
  isHelpActive,
  isNavShrink,
  labels,
  Label,
  iconClassName,
  isSubNav
}) => {
  const [isHover, setIsHover] = useState(false);
  return (
    <NavLink
      to={!disabled && to}
      onClick={onClick}
      className={({ isActive }) =>
        twMerge(
          clsx(
            "w-full p-[8px_8px] max-lg:p-[10px_8px] text-[14px] border-none text-left items-center flex flex-row relative no-underline whitespace-nowrap rounded-[10px] gap-2 !text-[inherit]",
            {
              activeBtn: isActive && !disabled,
              "!bg-[var(--light-purple)]":
                (isHover || isActive) && !inActive && !isSubNav,
              "!text-[var(--purple)]": (isHover || isActive) && !inActive,
              "!text-white": isHover && inActive
            },
            className
          )
        )
      }
      data-testid={testId}
      disabled={disabled}
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
    >
      {({ isActive }) => (
        <>
          {navIcon && (
            <Icon
              src={isHover || isActive ? navIcon.active : navIcon.icon}
              renderSvg
              className={clsx(
                {
                  "min-w-[20px]": isSubNav,
                  "min-w-[24px]": !isSubNav
                },
                iconClassName
              )}
            />
          )}
          {!navIcon && <i className={classIcon} />}
          <div
            className={clsx(
              "child-text flex-[1] flex justify-between gap-[10px]",
              childClassName
            )}
          >
            {children}
            {isHelpActive && (
              <HelpGuideIcon
                testId={`${testId}-help`}
                isNavShrink={isNavShrink}
                tooltipHelp={labels[replace(Label, "_LABEL", "_HELP_TOOLTIP")]}
              />
            )}
          </div>
        </>
      )}
    </NavLink>
  );
};

NavLinkButton.defaultProps = {
  className: "",
  to: "/dashboard"
};

export default NavLinkButton;
