import { useState, useEffect } from "react";
import { connect, useDispatch, useSelector } from "react-redux";
import filter from "lodash/filter";
import includes from "lodash/includes";
import map from "lodash/map";
import { notificationAction } from "../../../../actions/notification";
import Messages from "../../../../utilities/Messages";
import toLower from "lodash/toLower";
import {
  getAllOpportunityApi,
  retrieveResumeSearchApi,
  declinedOpportunityApi,
  deleteOpportunityApi,
  getSocialMediaApi,
  opportunityRestoreApi,
  getAllPresenationApi,
  readOpportunityApi,
  getCurrenciesApi,
  acceptOpportunityApi
} from "./opportunityApi";
import { unreadOpportunitiesAction } from "../../../../actions/navigationActions";
import { onStateChangeAction } from "./opportunityAction";
import { Drawer } from "antd";

import moment from "moment";
import { useNavigate } from "react-router-dom";
import ConfirmDialog from "../../../../common/ConfirmDialog/ConfirmDialog";
import JobDetailIpro from "./components/JobDetailIpro";
import { LoadingOutlined } from "@ant-design/icons";
const OpportunityNew = props => {
  const labels = useSelector(state => state.systemLabel.labels);
  const {
    setFilteredJobList,
    setShortlistId,
    isHelpActive,
    currencies
  } = props;
  const [jobInvitationData, setJobInvitationData] = useState([]);
  const [allJobsList, setAllJobsList] = useState([]);
  const [selectedUserDetail, setSelectedUserDetail] = useState(null);
  const [allpresentation, setAllPresentation] = useState([]);
  const [selectedPresentation, setSelectedPresentation] = useState({});
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [requestId, setRequestId] = useState("");
  const [state, setState] = useState({
    opportunity: [],
    selected: null,
    active: "",
    loading: false,
    companyModal: false,
    windowWidth: window.innerWidth,
    showDetail: false,
    isResumeDetail: false,
    presentaionModal: false,
    isMobileDevice: false
  });

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const handleResize = () => {
    setState(st => ({ ...st, windowWidth: window.innerWidth }));
  };
  useEffect(() => {
    getAllPresentation();
    // getAllOpportunityApi();
    dispatch(
      onStateChangeAction({
        opportunityList: [],
        filteredOpportunity: [],
        fetchingOpportunity: false,
        showDetail: false,
        deletedId: null,
        requestName: "",
        selectedOpportunity: {},
        selectedCompany: {},
        showPresentationDetail: false
      })
    );
    const { isAcceptedView, isDeclinedView } = props;
    window.actionName = isAcceptedView
      ? "accepted"
      : isDeclinedView
      ? "declined"
      : "new";
    dispatch(
      onStateChangeAction({
        companyPresentationHeadingText:
          props.labels.iproOpportunityNewCompanyDetailHeading
      })
    );
    getSocialMedia();
    getAllOpportunity();
    window.addEventListener("resize", handleResize);
    setState(st => ({ ...st, active: window?.location?.hash?.slice(1) }));
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const getSocialMedia = () => {
    getSocialMediaApi().then(data => {
      if (data.success) {
        dispatch(onStateChangeAction({ socialMedia: data.items }));
      }
    });
  };
  const handleSubmitPresentDetail = data => {
    setSelectedUserDetail(data);
    getAllPresentation();
    const obj = {
      RequestId: requestId,
      UserDetailId: selectedPresentation,
      ResumeId: 0
    };

    acceptOpportunity(obj);
  };
  const acceptOpportunity = info => {
    acceptOpportunityApi(info).then(data => {
      if (data.success) {
        setIsDrawerOpen(false);
        const info = {
          message: labels.iproOppNewAcceptMessage,
          status: "success"
        };
        dispatch(notificationAction(info));
        getAllOpportunity();
        props.handleOk();
      }
    });
  };
  const getAllOpportunity = () => {
    setState(st => ({ ...st, showDetail: false }));
    dispatch(onStateChangeAction({ isLoading: true }));
    const { url, isAccepted, opportunityType, currencies } = props;
    const loadCurrencies = new Promise(resolve => {
      if (currencies && currencies.length > 0) {
        resolve(currencies);
      } else {
        getCurrenciesApi()
          .then(res => {
            if (res.success) {
              resolve(res.items);
            } else {
              resolve([]);
            }
          })
          .catch(err => {
            console.log("Err ", err);
            resolve([]);
          });
      }
    });

    loadCurrencies.then(currenciesList => {
      dispatch(onStateChangeAction({ fetchingOpportunity: true }));
      setLoading(true);
      getAllOpportunityApi()
        .then(data => {
          setLoading(false);
          let allOpportunity = [];
          if (data.success) {
            setAllJobsList(data?.items?.Requests);
            setJobInvitationData(data?.items?.Requests);
            try {
              allOpportunity = map(data.items?.Requests || [], opp => {
                let currencyName = opp.FeeCurrencyType;

                if (currenciesList && currenciesList.length > 0) {
                  const currencyObj = currenciesList.find(
                    curr =>
                      Number(curr.CurrencyId) === Number(opp.FeeCurrencyType)
                  );
                  if (currencyObj) {
                    currencyName = currencyObj.Name;
                  }
                }

                return {
                  ...opp,
                  FeeCurrencyType: currencyName,
                  label: opp?.RequestName,
                  value: opp?.RequestResumeId
                };
              });
              setFilteredJobList(allOpportunity?.length);
            } catch (err) {
              console.error("Error while mapping opportunities:", err);
            }
            dispatch(
              onStateChangeAction({
                opportunityList: allOpportunity,
                filteredOpportunity: allOpportunity,
                fetchingOpportunity: false,
                deletedId: null,
                requestName: "",
                selectedOpportunity: {},
                showPresentationDetail: false
              })
            );

            if (
              opportunityType === "Requests" &&
              data.items.Requests.length > 0
            ) {
              const spliturl = props.locationProp.pathname.match(/\d+/g);
              if (spliturl != null) {
                let filterrequestid = data.items.Requests.filter(
                  opportunity => opportunity.RequestId == spliturl[0]
                );
                setState(st => ({
                  ...st,
                  opportunity: filterrequestid[0]
                }));
              }
            }
            if (
              props.locationProp.state &&
              props.locationProp.state.visitRequestId
            ) {
              const visitOpportunity = filter(props.filteredOpportunity, [
                "RequestId",
                props.locationProp.state.visitRequestId
              ]);
              dispatch(
                onStateChangeAction({
                  selectedCompany: visitOpportunity[0].Company,
                  selectedOpportunity: visitOpportunity[0]
                })
              );
              retrieveResumeSearch(props.selectedOpportunity);
            }
            dispatch(onStateChangeAction({ isLoading: false }));
          } else {
            dispatch(
              onStateChangeAction({
                fetchingOpportunity: false,
                isLoading: false
              })
            );
          }
        })
        .catch(() => {
          setLoading(false);
        });
    });
  };

  const handleSearchChange = e => {
    const { value } = e.target;
    const { opportunityList } = props;
    const filteredOpportunity = filter(opportunityList, opp =>
      includes(toLower(opp.RequestName), toLower(value))
    );
    dispatch(onStateChangeAction({ filteredOpportunity, searchKey: value }));
  };

  const handleDeleteOpportunityNew = obj => {
    dispatch(
      onStateChangeAction({
        deletedId: obj.RequestId,
        requestName: obj.RequestName,
        dialogMessage: props.labels.iproInvitationDeletionConfirmation
      })
    );
  };

  const handleYesClick = event => {
    const { deletedId, requestName } = props;
    dispatch(onStateChangeAction({ dialogMessage: "" }));
    dispatch(onStateChangeAction({ isLoading: true }));

    const obj = new Object();
    obj.IsRequestToDelete = false;
    obj.RequestId = deletedId;
    obj.RequestName = requestName;
    obj.ResumeId = 0;
    deleteOpportunityApi(obj)
      .then(data => {
        if (data) {
          getAllOpportunity();
          const info = {
            message: props.labels.iproInvitationSuccessfullyDeleted,
            status: "success"
          };
          props.notificationAction(info);
          dispatch(
            onStateChangeAction({
              deletedId: null,
              requestName: "",
              selectedOpportunity: {},
              showPresentationDetail: false,
              showCompanyDetails: false
            })
          );
        }
        dispatch(onStateChangeAction({ isLoading: false }));
      })
      .catch(() => {
        dispatch(onStateChangeAction({ isLoading: false }));
      });
  };

  const handleNoClick = () => {
    dispatch(onStateChangeAction({ dialogMessage: "", deletedId: null }));
  };

  const handleOpportunitySelect = selectedOpportunity => {
    setRequestId(selectedOpportunity?.RequestId);
    setState(st => ({
      ...st,
      selected: selectedOpportunity?.value,
      showDetail: true
    }));
    dispatch(
      onStateChangeAction({
        selectedOpportunity,
        selectedCompany: selectedOpportunity.Company,
        showPresentationDetail: false,
        showCompanyDetails: true
      })
    );
    if (!selectedOpportunity.IsRead) {
      readOpportunityApi({
        requestResumeId: selectedOpportunity.RequestResumeId
      }).then(response => {
        if (response.success) {
          const { opportunityList } = props;
          const {
            isAcceptedView,
            isDeclinedView,
            isNewOpportunityView
          } = props;
          const opportunities = filter(
            opportunityList,
            item =>
              item.RequestId !== selectedOpportunity.RequestId &&
              item.RequestResumeId !== selectedOpportunity.RequestResumeId &&
              !item.IsRead
          );
          if (isNewOpportunityView) {
            dispatch(
              unreadOpportunitiesAction({
                unreadOpportunities: { newOpportunities: opportunities.length }
              })
            );
          } else if (isAcceptedView) {
            dispatch(
              unreadOpportunitiesAction({
                unreadOpportunities: {
                  acceptedOpportunities: opportunities.length
                }
              })
            );
          } else if (isDeclinedView) {
            dispatch(
              unreadOpportunitiesAction({
                unreadOpportunities: {
                  declinedOpportunities: opportunities.length
                }
              })
            );
          }
          const filteredOpportunity = map(opportunityList, oppo => {
            if (oppo.RequestId === selectedOpportunity.RequestId) {
              oppo.IsRead = true;
              return oppo;
            }
            return oppo;
          });
          dispatch(onStateChangeAction({ filteredOpportunity }));
        }
      });
    }

    if (props.isAccepted && selectedOpportunity.SavedSearchedId) {
      retrieveResumeSearch(selectedOpportunity);
    }
  };

  const retrieveResumeSearch = selectedOpportunity => {
    retrieveResumeSearchApi(selectedOpportunity.SavedSearchedId).then(data => {
      if (data.success) {
        dispatch(
          onStateChangeAction({
            selectedOpportunity: {
              ...selectedOpportunity,
              retrieveResumeSearch: {
                Profiles: data.items.filter(a => a.LookupTypeId == 1),
                Skills: data.items.filter(a => a.LookupTypeId == 2),
                Keywords: data.items.filter(a => a.LookupTypeId == 3),
                Industeries: data.items.filter(a => a.LookupTypeId == 4),
                Certifications: data.items.filter(a => a.LookupTypeId == 5),
                Languages: data.items.filter(a => a.LookupTypeId == 6),
                Countries: data.items.filter(a => a.LookupTypeId == 7)
              }
            }
          })
        );
      }
    });
  };

  const handleOpportunityRestore = () => {
    dispatch(
      onStateChangeAction({
        restoreDialogMessage: props.labels.oppRestorationConfiramtion
      })
    );
  };

  const restoreHandleYesClick = () => {
    const { selectedOpportunity } = props;
    dispatch(onStateChangeAction({ isLoading: true }));

    opportunityRestoreApi(selectedOpportunity.RequestResumeId)
      .then(data => {
        if (data) {
          getAllOpportunity();
          const info = {
            message: props.labels.IproOpportunityDeclinedRestoreMessage,
            status: "success"
          };
          props.notificationAction(info);
          dispatch(
            onStateChangeAction({
              deletedId: null,
              requestName: "",
              restoreDialogMessage: "",
              showPresentationDetail: false,
              showCompanyDetails: false,
              selectedOpportunity: {}
            })
          );
        }
        dispatch(onStateChangeAction({ isLoading: false }));
        setState(st => ({ ...st, selected: null }));
      })
      .catch(() => {
        dispatch(onStateChangeAction({ isLoading: false }));
      });
  };

  const restoreHandleNoClick = () => {
    dispatch(
      onStateChangeAction({
        restoreDialogMessage: "",
        deletedId: null
      })
    );
  };

  const handleDeleteOpportunity = obj => {
    dispatch(
      onStateChangeAction({
        deletedId: obj.RequestId,
        requestName: obj.RequestName,
        dialogMessage: props.opportunityDeletedMsg
      })
    );
  };

  const handleOpportunityDetailAcceptClick = () => {
    getAllPresentation();
  };

  const handleOpportunityDetailDeclinedClick = () => {
    setState(st => ({ ...st, selected: null }));
    dispatch(
      onStateChangeAction({
        showPresentationDetail: false,
        showCompanyDetails: true
      })
    );
    const obj = new Object();
    obj.IsRequestToDelete = false;
    obj.RequestId = requestId;
    obj.ResumeId = 0;
    obj.RequestName = props.selectedOpportunity.RequestName;
    declinedOpportunity(obj);
  };

  const declinedOpportunity = info => {
    dispatch(onStateChangeAction({ isLoading: true }));
    declinedOpportunityApi(info)
      .then(data => {
        if (data.success) {
          setIsDrawerOpen(false);
          getAllOpportunity();
          const info = {
            message: props.labels.iproOppNewDeclinedMessage,
            status: "success"
          };
          props.notificationAction(info);

          dispatch(
            onStateChangeAction({
              deletedId: null,
              requestName: "",
              selectedOpportunity: {},
              showPresentationDetail: false,
              showCompanyDetails: false,
              isLoading: false
            })
          );
        }
      })
      .catch(() => {
        dispatch(onStateChangeAction({ isLoading: true }));
      });
  };

  const handleOpportunityDetailCompanyClick = () => {
    dispatch(
      onStateChangeAction({
        showPresentationDetail: false,
        showCompanyDetails: true,
        companyPresentationHeadingText:
          props.labels.iproOpportunityNewCompanyDetailHeading
      })
    );
  };

  const getAllPresentation = () => {
    setState(st => ({ ...st, loading: true }));
    getAllPresenationApi()
      .then(data => {
        if (data.success) {
          if (data.items.length > 0) {
            const presentations = data.items.map(item => ({
              ...item,
              Logo: item.Logo,
              value: item.UserDetailId,
              label: item.Title
            }));
            dispatch(
              onStateChangeAction({
                showPresentationDetail: true,
                showCompanyDetails: false,
                companyPresentationHeadingText:
                  props.labels.IproOpportunityNewUserDetailHeaderText
              })
            );
            setAllPresentation(presentations);
            // setSelectedPresentation(presentations[0]);
            setIsLoading(false);
            if (state.windowWidth < 768) {
              setState(st => ({ ...st, presentaionModal: true }));
            }
          } else {
            const info = {
              message: props.labels.OPPORTUNITY_PRESENT_NOT_AVAILABLE_MESSAGE,
              status: "info"
            };
            props.notificationAction(info);
          }
          setState(st => ({ ...st, loading: false }));
        }
      })
      .catch(() => {
        setState(st => ({ ...st, loading: false }));
      });
  };

  return (
    <div className="flex w-full md:h-[calc(100vh_-_164px)] h-[calc(100vh_-_156px)] bg-white  flex-col gap-2  mb-4 flex-1 ">
      {props.dialogMessage && (
        <ConfirmDialog testId="opportunityIPro-confirm-diloag">
          <ConfirmDialog.Message>{props.dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <button
              className="dialog-btn"
              data-testid="opportunityIPro-delete-yes"
              onClick={handleYesClick}
            >
              {Messages.feedback.confirm.Yes}
            </button>
            <button
              className="dialog-btn"
              data-testid="opportunityIPro-delete-no"
              onClick={handleNoClick}
            >
              {Messages.feedback.confirm.No}
            </button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}

      {props.restoreDialogMessage && (
        <ConfirmDialog>
          <ConfirmDialog.Message>
            {props.restoreDialogMessage}
          </ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <button className="dialog-btn" onClick={restoreHandleYesClick}>
              {Messages.feedback.confirm.Yes}
            </button>
            <button className="dialog-btn" onClick={restoreHandleNoClick}>
              {Messages.feedback.confirm.No}
            </button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      <Drawer
        placement="right"
        closable={true}
        open={isDrawerOpen}
        key="placement"
        headerStyle={{ display: "none" }}
        onClose={() => {
          setIsDrawerOpen(false);
        }}
        rootClassName=" 
                  sm:[&_.ant-drawer-content-wrapper]:!m-[8px]
                  [&_.ant-drawer-content-wrapper]:!mt-[8px] 
                  md:[&_.ant-drawer-content]:!rounded-[16px]
                  [&_.ant-drawer-content]:!rounded-tl-[16px]
                  [&_.ant-drawer-content]:!rounded-tr-[16px]
                  sm:[&_.ant-drawer-content-wrapper]:!w-[497px]
                  [&_.ant-drawer-content-wrapper]:!w-[100%]
                  [&_.ant-drawer-content-wrapper]:!rounded-[16px]
                  [&_.ant-drawer-body]:!p-[0px]"
      >
        <JobDetailIpro
          // presentationview
          selectedOpportunityNeww={jobInvitationData}
          showCompanyDetails={props.showCompanyDetails}
          showPresentationDetail={props.showPresentationDetail}
          selectedPresentation={selectedPresentation}
          setSelectedPresentation={setSelectedPresentation}
          loading={state.loading}
          labels={props.labels}
          socialMedia={props.socialMedia}
          getAllOpportunity={getAllOpportunity}
          allJobsList={allJobsList}
          handleOk={() => {}}
          allpresentation={allpresentation}
          // companyview
          selectedCompanyy={jobInvitationData}
          addressLabel={props.companyAddressLabel}
          industryLabel={props.companyIndustryLabel}
          countryLabel={props.companyCountryLabel}
          phoneLabel={props.companyPhoneLabel}
          webUrlLabel={props.companyWebUrlLabel}
          vatLabel={props.companyVatLabel}
          // others
          handleOpportunityDetailDeclinedClick={
            handleOpportunityDetailDeclinedClick
          }
          handleSubmitPresentDetail={handleSubmitPresentDetail}
          setIsDrawerOpen={setIsDrawerOpen}
          requestId={requestId}
          currencies={currencies}
          isHelpActive={isHelpActive}
        />
      </Drawer>
      <div className="h-full new-design-search w-full flex flex-col">
        <div className="flex w-full flex-col  h-full  bg-white  overflow-y-auto  [scrollbar-width:'none'] [-ms-overflow-style:'none'] [&::-webkit-scrollbar]:hidden">
          <div className="flex w-full overflow-auto">
            <div className="h-full w-full flex flex-col">
              <>
                <div className="flex w-full">
                  {loading ? (
                    <div className="w-full mt-15 flex justify-center items-center">
                      <LoadingOutlined />
                    </div>
                  ) : jobInvitationData?.length > 0 ? (
                    <div className="flex flex-col gap-2 w-full mb-3 flex-1 pt-3">
                      {jobInvitationData?.map(item => (
                        <div
                          onClick={() => {
                            setIsDrawerOpen(true);
                            handleOpportunitySelect(item);
                          }}
                          key={item?.value}
                          className="flex justify-between w-full rounded-2xl border-[0.5px] min-h-[60px] border-[#EAE5FC] px-4 pt-[10px] pb-[10px] items-center pointer hover:bg-gray-50"
                        >
                          <div className="flex flex-col gap-2 flex-1 min-w-0">
                            <label className="text-[#343333] text-sm font-medium truncate w-full leading-[18px]">
                              {item?.RequestName}
                            </label>
                            <p className="flex text-[#878787] font-normal text-xs gap-3">
                              <span>
                                {`Date ${moment(item?.StartDate).format(
                                  "DD-MM-YYYY"
                                )}`}
                              </span>
                              <span>
                                {`Time ${moment(item?.StartDate).format(
                                  "h:mm A"
                                )}`}
                              </span>
                            </p>
                          </div>
                          <div className="flex items-center justify-center flex-shrink-0 ml-2">
                            <span
                              className={`px-4 h-[27px] rounded-[99px] flex items-center justify-center text-xs font-medium
            ${
              item?.Status === "new"
                ? "text-[#8E81F5] bg-[#F3F1FD]"
                : item?.Status === "accepted"
                ? "text-[#34C759] bg-[#E8FEE7]"
                : item?.Status === "declined"
                ? "text-[#FF3B30] bg-[#FEEDED]"
                : ""
            }`}
                            >
                              {item?.Status === "new"
                                ? "New"
                                : item?.Status === "accepted"
                                ? "Accepted"
                                : item?.Status === "declined"
                                ? "Declined"
                                : ""}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    ""
                  )}
                </div>
              </>
              {/* )} */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const mapStateToProps = (
  {
    systemLabel,
    iproOpportunityNew,
    iproOpportunityAccepted,
    iproOpportunityDeclined,
    currency,
    navigation
  },
  { isAcceptedView, isDeclinedView }
) => {
  const { labels } = systemLabel;
  const { currencies } = currency;
  const { isHelpActive } = navigation;
  const opportunityProps = isAcceptedView
    ? iproOpportunityAccepted
    : isDeclinedView
    ? iproOpportunityDeclined
    : iproOpportunityNew;
  return { labels, currencies, isHelpActive, ...opportunityProps };
};

export default connect(mapStateToProps, {
  notificationAction,
  unreadOpportunitiesAction,
  onStateChangeAction
})(OpportunityNew);
