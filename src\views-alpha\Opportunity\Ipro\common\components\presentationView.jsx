import { formatDate } from "../../../../../utilities/helpers";
const PresentationDetailsNew = ({
  labels,
  selectedOpportunityNew,
  allJobsList,
  currType
}) => {
  const currentJob = allJobsList.find(
    job => job.RequestId === selectedOpportunityNew?.RequestId
  );
  return (
    <div>
      <div className="flex flex-col bg-[#F3F1FD] rounded-xl mt-3 md:ml-6 md:mr-6 ml-4 mr-4">
        <div className="flex pl-4 pr-4 h-auto min-h-10 !border-b !border-[#EAE5FC]">
          <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
            {labels?.TITLE_LABEL}
          </label>
          <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
          <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
            {currentJob?.RequestName ? currentJob?.RequestName : "N/A"}
          </p>
        </div>
        <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
          <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
            {labels?.collStartDate}
          </label>
          <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
          <p className="!ml-[12.5px] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
            {currentJob?.StartDate ? formatDate(currentJob?.StartDate) : "N/A"}
          </p>
        </div>
        <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
          <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
            {labels?.SearcherSentCollaborationDurationLabel}
          </label>
          <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
          <p className="flex gap-1 !ml-[12.5px] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
            {currentJob?.Duration ? currentJob?.Duration : "N/A"}
            <span>{currentJob?.DurationType}</span>
          </p>
        </div>
        <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
          <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
            {labels?.collHourlyFee}
          </label>
          <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
          <p className="flex gap-1 !ml-[12.5px] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
            {currentJob?.HourlyFee ? currentJob?.HourlyFee : "N/A"}
            <span> {currType?.Name}</span>
          </p>
        </div>
        <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
          <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
            {labels?.oppResumeDetailLocation}
          </label>
          <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
          <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
            {currentJob?.Countries?.length
              ? currentJob?.Countries?.map(c => c.CountryName)?.join(", ")
              : "N/A"}
          </p>
        </div>
        <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
          <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
            {labels?.Section2HiringShortlistTitle}
          </label>
          <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
          <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
            {currentJob?.ShortlistName ? currentJob?.ShortlistName : "N/A"}
          </p>
        </div>
        <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
          <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
            {labels?.iProOpportunityNewDetailLblCompany}
          </label>
          <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
          <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
            {currentJob?.Company?.CompanyName
              ? currentJob?.Company?.CompanyName
              : "N/A"}
          </p>
        </div>
        <div className="flex pl-4 pr-4 pt-[14px] min-h-10 h-auto pb-3">
          <label className="flex  !w-[72px] h-full text-[#878787] text-[13px] font-normal ">
            {labels?.collDescription}
          </label>
          <span className="h-[22.5px] border border-[#EAE5FC]  ml-[21.5px]"></span>
          <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal  text-[#343333]">
            {currentJob?.Description ? currentJob?.Description : "N/A"}
          </p>
        </div>
      </div>
    </div>
  );
};

export default PresentationDetailsNew;
