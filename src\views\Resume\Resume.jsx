import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import DragPD<PERSON> from "../../components/DragPDF/DragPDF";
import PageWrapper from "../../components/PageWrapper/PageWrapper";
import Button from "../../common/Button/Button";
import Column from "../../common/Column/Column";
import ConfirmDialog from "../../common/ConfirmDialog/ConfirmDialog";
import EmptyInfo from "../../common/EmptyInfo/EmptyInfo";
import LoadingMaskRow from "../../common/LoadingMask/LoadingMaskRow";
import phillipImg from "../../../src/assets/images/svg/phillip-12.svg";
import { notificationAction } from "../../actions/notification";
import {
  saveParsedResume,
  uploadLinkedinPDF
} from "../../components/Phillip/phillipApi";
import { privateRoutes } from "../../Routes/routing";
import { htmlParser } from "../../utilities/helpers";
import "./resume.scss";
import { onStateChangeAction } from "./resumeAction";
import { getResumesApi } from "./resumeApi";
import ResumeDetail from "./ResumeDetail/ResumeDetail";

const ResumeValues = [
  {
    id: 0,
    name: "Resume",
    notFound: "iProOnboardingResumeCreated",
    found: "iProOnboardingResumeCreated",
    active: true
  },
  {
    id: 1,
    name: "Keywords",
    notFound: "iProOnboardingKeywordNotFound",
    found: "iProOnboardingKeywordFound"
  },
  {
    id: 2,
    name: "Certifications",
    notFound: "iProOnboardingCertificationNotFound",
    found: "iProOnboardingCertificationFound"
  },
  {
    id: 3,
    name: "Skills",
    notFound: "iProOnboardingSkillNotFound",
    found: "iProOnboardingSkillFound"
  },
  {
    id: 4,
    name: "Profiles",
    notFound: "iProOnboardingRoleNotFound",
    found: "iProOnboardingRoleFound"
  },
  {
    id: 5,
    name: "Educations",
    notFound: "iProOnboardingEducationNotFound",
    found: "iProOnboardingEducationFound"
  },
  {
    id: 6,
    name: "Experiences",
    notFound: "iProOnboardingExperienceNotFound",
    found: "iProOnboardingExperienceFound"
  },
  {
    id: 7,
    name: "Countries",
    notFound: "iProOnboardingCountryNotFound",
    found: "iProOnboardingCountryFound"
  },
  {
    id: 8,
    name: "Languages",
    notFound: "iProOnboardingLanguageNotFound",
    found: "iProOnboardingLanguageFound"
  }
  // {
  //   id: 9,
  //   name: "Present",
  //   notFound: "iProOnboardingPresentNotFound",
  //   found: "iProOnboardingPresentFound"
  // }
];

const Resume = props => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    isResumeImportCollapsed,
    fetchingResumes,
    selectedResume,
    showLinkedInImportView,
    uploadPDFButtonDisable,
    detailCollapsed,
    importCollapsed,
    dialogMessage,
    dialogResumeUpload,
    dialogResumeEdit,
    labels,
    history,
    imgSrc,
    getImgSrc,
    isHelpActive
  } = useSelector(state => ({
    ...state.resume,
    labels: state.systemLabel.labels,
    isHelpActive: state.navigation.isHelpActive
  }));

  useEffect(() => {
    getResume();
  }, []);

  const getPDFResumeCallback = (pdfData, uploadPDFButtonStatus) => {
    if (pdfData) {
      dispatch(
        onStateChangeAction({
          selectedPDFResume: pdfData,
          uploadPDFButtonDisable: uploadPDFButtonStatus,
          dialogResumeUpload: labels.RESUME_UPLOAD_PDF_FILE_INFO
        })
      );
      return;
    }
    dispatch(
      onStateChangeAction({
        selectedPDFResume: null,
        uploadPDFButtonDisable: true
      })
    );
  };

  const handleUploadPDFResume = () => {
    dispatch(
      onStateChangeAction({
        dialogMessage: labels.resumeImportUserParsingFeedback
      })
    );
  };

  const getResume = () => {
    dispatch(
      onStateChangeAction({
        fetchingResumes: true
      })
    );
    getResumesApi()
      .then(data => {
        if (data.success) {
          dispatch(
            onStateChangeAction({
              selectedResume: data.items,
              fetchingResumes: false
            })
          );
        }
      })
      .catch(response => {
        console.log("error in get resume request ");
      });
  };

  const handleYesClick = () => {
    const info = {
      message: labels.resumeImportResumeParsingInfo,
      status: "success"
    };
    dispatch(notificationAction(info));
    const { selectedPDFResume } = props;
    uploadLinkedinPDF(selectedPDFResume).then(res => {
      getResume();
      dispatch(
        onStateChangeAction({
          dialogMessage: "",
          PDFFileName: labels.iproResumeFileFormate,
          isPDFFileUploaded: false,
          uploadPDFButtonDisable: true
        })
      );
    });
  };

  const handleNoClick = () => {
    dispatch(onStateChangeAction({ dialogMessage: "" }));
  };

  const handleResumeUploadYesClick = () => {
    const { selectedPDFResume } = props;
    uploadLinkedinPDF(selectedPDFResume).then(res => {
      if (res.items) {
        saveParsedResume(res.items)
          .then(() => {
            const info = {
              message: "Resume saved successfully",
              status: "success"
            };
            dispatch(notificationAction(info));
            getResume();
          })
          .catch(err => console.log("Err", err));
      }
      dispatch(
        onStateChangeAction({
          dialogResumeUpload: "",
          isPDFFileUploaded: false,
          uploadPDFButtonDisable: true,
          PDFFileName: labels.iproResumeFileFormate,
          dialogResumeEdit: labels.resumeImportResumeParsingInfo
        })
      );
    });
  };

  const handleResumeUploadNoClick = () => {
    dispatch(onStateChangeAction({ dialogResumeUpload: "" }));
  };

  const handleResumeEditYesClick = () => {
    dispatch(
      onStateChangeAction({
        dialogResumeEdit: ""
      })
    );

    navigate(privateRoutes.resumeBuilder.path);
  };

  return (
    <PageWrapper className="resume-page">
      {dialogResumeEdit && (
        <ConfirmDialog testId="resume-import-confirm-diloag">
          <ConfirmDialog.Message>{dialogResumeEdit}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              testId="resume-import-confirm-yes"
              onClick={handleResumeEditYesClick}
            >
              {labels.presentDeleteCurtainYESBtnText}
            </ConfirmDialog.Button>
            <ConfirmDialog.Button
              className="dialog-btn"
              testId="resume-import-confirm-no"
              onClick={() =>
                dispatch(
                  onStateChangeAction({
                    dialogResumeEdit: ""
                  })
                )
              }
            >
              {labels.presentDeleteCurtainNOBtnText}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}

      {dialogMessage && (
        <ConfirmDialog testId="resume-import-confirm-diloag">
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              testId="resume-import-confirm-yes"
              onClick={handleYesClick}
            >
              {labels.presentDeleteCurtainYESBtnText}
            </ConfirmDialog.Button>
            <ConfirmDialog.Button
              className="dialog-btn"
              testId="resume-import-confirm-no"
              onClick={handleNoClick}
            >
              {labels.presentDeleteCurtainNOBtnText}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}

      {dialogResumeUpload && (
        <ConfirmDialog testId="resume-import-confirm-diloag">
          <ConfirmDialog.Message>{dialogResumeUpload}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              testId="resume-import-confirm-yes"
              onClick={handleResumeUploadYesClick}
            >
              {labels.presentDeleteCurtainYESBtnText}
            </ConfirmDialog.Button>
            <ConfirmDialog.Button
              className="dialog-btn"
              testId="resume-import-confirm-no"
              onClick={handleResumeUploadNoClick}
            >
              {labels.presentDeleteCurtainNOBtnText}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}

      <Column collapse={detailCollapsed} className="col-1">
        <Column.Collapsed
          tooltipButton={labels.ToolTipResumeListExpand}
          text={labels.ViewTitleResumeDetails}
          isHelpActive={isHelpActive}
          tooltipHelp={labels.HelpTooltipResumeListExpand}
          onClick={() =>
            dispatch(onStateChangeAction({ detailCollapsed: false }))
          }
          testId="resumeImportListExpandBtn"
          testIdHelp="resumeImportListExpandHlpBtn"
        />
        <Column.Head>
          <div className="heading">{labels.ResumeHeading}</div>
          <Button
            onClick={() => {
              navigate(privateRoutes.resumeBuilder.path);
            }}
            className="EditBtn"
            tooltipButton={labels.TooltipResumeEdit}
            tooltipPlace="left"
            isHelpActive={isHelpActive}
            tooltipHelp={labels.TooltipResumeEdit}
            testId="resumeImportListEditBtn"
            testIdHelp="resumeImportListEditHlpBtn"
          />
          <Button
            onClick={() =>
              dispatch(onStateChangeAction({ detailCollapsed: true }))
            }
            className="collapseBtn"
            isHelpActive={isHelpActive}
            tooltipPlace="left"
            tooltipButton={labels.ToolTipResumeListCollaspe}
            tooltipHelp={labels.HelpTooltipResumeListCollaspe}
            testId="resumeImportListCollapseBtn"
            testIdHelp="resumeImportListCollapseHlpBtn"
          />
        </Column.Head>
        <Column.Body>
          {fetchingResumes ? (
            <LoadingMaskRow />
          ) : (
            <ResumeDetail resume={selectedResume} />
          )}
        </Column.Body>
      </Column>

      {!showLinkedInImportView && (
        <Column collapse={isResumeImportCollapsed} className="col-2">
          <Column.Collapsed
            tooltipButton={labels.ToolTipResumeImportExpand}
            text={labels.ViewTitleResumeImportDetail}
            onClick={() =>
              dispatch(onStateChangeAction({ isResumeImportCollapsed: false }))
            }
            isHelpActive={isHelpActive}
            tooltipHelp={labels.HelpTooltipResumeImportExpand}
            testId="resumeImportDetailExpandBtn"
            testIdHelp="resumeImportListExpandHlpBtn"
          />
          <Column.Head>
            <div className="heading">
              {" "}
              {labels?.ViewTitleResumeImportDetail}{" "}
            </div>
            <Button
              className="collapseBtn"
              onClick={() =>
                dispatch(onStateChangeAction({ isResumeImportCollapsed: true }))
              }
              tooltipButton={labels.ToolTipResumeImportCollaspe}
              tooltipPlace="left"
              isHelpActive={isHelpActive}
              tooltipHelp={labels.HelpTooltipResumeImportCollaspe}
              testId="resumeImportCollaspeBtn"
              testIdHelp="resumeImportCollaspeHlpBtn"
            />
          </Column.Head>
          <Column.Body className="flex">
            <EmptyInfo
              child={
                <Button
                  className="importBtn"
                  testId="resumeImportBtn"
                  testIdHelp="resumeImportHlpBtn"
                  tooltipButton={labels.resumeImportImportFromLinkedIn}
                  onClick={() =>
                    dispatch(
                      onStateChangeAction({ showLinkedInImportView: true })
                    )
                  }
                >
                  {labels.resumeImportImportFromLinkedIn}
                </Button>
              }
            >
              {labels.Freelance_Resume}
            </EmptyInfo>
          </Column.Body>
        </Column>
      )}

      {showLinkedInImportView && (
        <Column collapse={importCollapsed} className="col-2">
          <Column.Collapsed
            tooltipButton={labels.ToolTipResumeImportUploadExpand}
            text={labels.ViewTitleResumeImportUpload}
            onClick={() =>
              dispatch(onStateChangeAction({ importCollapsed: false }))
            }
            isHelpActive={isHelpActive}
            tooltipHelp={labels.HelpTooltipResumeImportUploadExpand}
            testId="resumeImportUploadExpandBtn"
            testIdHelp="resumeImportUploadExpandHlpBtn"
          />
          <Column.Head>
            <div className="heading">
              {labels.resumeImportImportFromLinkedIn}
            </div>
            <Button
              className="SaveCloudBtn"
              onClick={handleUploadPDFResume}
              tooltipButton={labels.ToolTipResumeSave}
              tooltipPlace="left"
              isHelpActive={isHelpActive}
              tooltipHelp={labels.ToolTipResumeSave}
              disabled={uploadPDFButtonDisable}
              testId="resumeImportUploadSaveBtn"
              testIdHelp="resumeImportUploadSaveHlpBtn"
            />
            <Button
              onClick={() =>
                dispatch(onStateChangeAction({ importCollapsed: true }))
              }
              className="collapseBtn"
              isHelpActive={isHelpActive}
              tooltipPlace="left"
              tooltipButton={labels.ToolTipResumeImportUploadCollaspe}
              tooltipHelp={labels.HelpTooltipResumeImportUploadCollaspe}
              testId="resumeImportUploadCollapseBtn"
              testIdHelp="resumeImportUploadCollapseHlpBtn"
            />
          </Column.Head>
          <Column.Body className="import-resume-body">
            <div className="center-content">
              <div className="companyBigIcon">
                <div className="dashItemImg">
                  <img
                    className="roundeImg"
                    src={phillipImg}
                    alt="phillip"
                    data-testid="resumeImportPhillipImg"
                  />
                </div>
              </div>
              <div className="resumeImportText">
                <b>{labels.iProOnboardingResumeUploadTitle}</b>
                <br />
                {htmlParser(labels.iProOnboardingResumeUploadMessage)}
              </div>
            </div>

            <DragPDF
              getImgSrc={getImgSrc}
              imgSrc={imgSrc}
              labels={labels}
              getPDF={getPDFResumeCallback}
              data-testid="resumeImportDropZone"
            />
          </Column.Body>
        </Column>
      )}
    </PageWrapper>
  );
};

export default Resume;
