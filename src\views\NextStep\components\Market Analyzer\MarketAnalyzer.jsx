import { useState, useEffect } from "react";
import { Steps, Select, Button } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { filter } from "lodash";
import "./market-analyzer.scss";
import { useLocation, useNavigate } from "react-router-dom";
import {
  GetUserExpectedSalaryApi,
  GetSuggestedProfilesForResumeApi,
  GetMarketPopularityIndexApi,
  GetSuggestedSkillsForResumeApi,
  addResumeProfileApi,
  addResumeSkillApi,
  getCurrenciesApi
} from "../../nextstepApi";
import ReactApexChart from "react-apexcharts";

// import React, { useState, useEffect } from "react";
// import { Steps, Select, Button } from "antd";
// import { useDispatch, useSelector } from "react-redux";
// import { useNavigate, useLocation } from "react-router-dom";
// import { notificationAction } from "../../../../actions";
// import { isEmpty, map, filter } from "lodash";
// import "./market-analyzer.scss";
// import {
//   GetUserExpectedSalaryApi,
//   UploadAndGetRolesAndSkillsFromResumeApi,
//   GetSuggestedProfilesForResumeApi,
//   GetMarketPopularityIndexApi,
//   GetSuggestedSkillsForResumeApi,
//   addResumeProfileApi,
//   addResumeSkillApi,
//   getCurrenciesApi,
// } from "../../nextstepApi";
// import "react-circular-progressbar/dist/styles.css";
// import ReactApexChart from "react-apexcharts";
// import { PageWrapper } from "../../../../components";

const { Step } = Steps;
const { Option } = Select;

const LOOKUPTYPES = {
  PROFILE: 1,
  SKILL: 2,
  KEYWORD: 3,
  CERTIFICATION: 4,
  COUNTRY: 5,
  LANGUAGE: 6,
  INDUSTRY: 7
};
const countriesList = [{ value: 46, label: "Pakistan" }];
const durationLists = [
  { value: 1, label: "Yearly" },
  { value: 2, label: "Monthly" },
  { value: 3, label: "Hourly" }
];

const MarketAnalyzer = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { labels } = useSelector(state => state.systemLabel);
  const { user } = useSelector(state => state.userInfo);

  const [allCurrenciesList, setAllCurrenciesList] = useState([]);
  const [selectedCurrency, setSelectedCurrency] = useState(22);
  const [selectedCountry, setSelectedCountry] = useState(46);
  const [selectedDuration, setSelectedDuration] = useState(1);
  const [top3Salaries, setTop3Salaries] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [extractedSearchSettings, setExtractedSearchSettings] = useState({
    Profiles: [],
    Skills: []
  });
  const [resumeUploaded, setResumeUploaded] = useState(false);
  const [popularityIndex, setPopularityIndex] = useState(-1);
  const [selectedSalary, setSelectedSalary] = useState(null);
  const [graphData, setGraphData] = useState([]);
  const [activeStep, setActiveStep] = useState(0);

  const chartOptions = {
    chart: {
      height: 350,
      type: "bar",
      events: {
        click: function(chart, w, e) {}
      }
    },
    bar: {
      distributed: true
    },
    dataLabels: {
      enabled: false,
      align: "left"
    },
    legend: {
      show: false
    },
    xaxis: {
      categories: ["Entry", "Average", "Expert"],
      colors: ["#3e335e", "#66DA26", "#546E7A"]
    },
    labels: {
      style: {
        fontSize: "12px"
      }
    },
    title: {
      text: ""
    },
    responsive: [
      {
        breakpoint: 600,
        options: {
          markers: {
            size: 1.5
          }
        }
      }
    ]
  };

  useEffect(() => {
    getCurrencies();
    GetSuggestedProfilesForResumeApi()
      .then(res => {
        setExtractedSearchSettings(prev => ({
          ...prev,
          Profiles: res.items.map(a => ({
            Id: a.CorrelatedProfiles.ProfileId,
            LookupValue: a.CorrelatedProfiles.ProfileValue
          }))
        }));
      })
      .catch(console.error);

    GetSuggestedSkillsForResumeApi()
      .then(res => {
        setExtractedSearchSettings(prev => ({
          ...prev,
          Skills: res.items.map(a => ({
            Id: a.SkillId,
            LookupValue: a.SkillValue
          }))
        }));
      })
      .catch(console.error);
  }, []);

  const getCurrencies = () => {
    getCurrenciesApi()
      .then(res => {
        if (res.success) {
          const currencies = res.items.map(item => ({
            ...item,
            value: item.CurrencyId,
            label: item.Name
          }));
          setAllCurrenciesList(currencies);
          setSelectedCurrency(currencies[currencies.length - 1].value);
          handleAnalyzeClick();
        }
      })
      .catch(console.error);
  };

  const handleAnalyzeClick = () => {
    setPopularityIndex(-1);
    setIsLoading(true);

    GetMarketPopularityIndexApi()
      .then(res => {
        let progress = 0;
        const interval = setInterval(() => {
          if (progress < res.items) {
            setPopularityIndex(progress);
            progress++;
          } else {
            clearInterval(interval);
            setIsLoading(false);
          }
        }, 10000);
      })
      .catch(console.error);

    GetUserExpectedSalaryApi(
      selectedCountry,
      selectedCurrency,
      selectedDuration
    )
      .then(res => {
        const salaries = res.items.Top3Salaries;
        const selected = salaries.length > 0 ? salaries[0] : {};
        const updatedSalaries = salaries.map(a => ({
          ...a,
          isSelected: a.Lookupvalue === selected.Lookupvalue
        }));

        setTop3Salaries(updatedSalaries);
        setSelectedSalary(selected);
        setGraphData([
          {
            name: `Salary in ${selected.Currency}`,
            data: [selected.Entry, selected.Average, selected.Senior]
          }
        ]);
        setIsLoading(false);
      })
      .catch(console.error);
  };

  const handleSalaryClick = item => {
    setTop3Salaries(prev =>
      prev.map(a => ({
        ...a,
        isSelected: a.Lookupvalue === item.Lookupvalue
      }))
    );
    setSelectedSalary(item);
    setGraphData([
      {
        name: `Salary in ${item.Currency}`,
        data: [item.Entry, item.Average, item.Senior]
      }
    ]);
  };

  const extractedSettingUpdate = (type, item) => {
    const { Profiles, Skills } = extractedSearchSettings;

    switch (type) {
      case LOOKUPTYPES.PROFILE:
        addResumeProfileApi(item.Id)
          .then(res => {
            if (res.success) {
              setExtractedSearchSettings(prev => ({
                ...prev,
                Profiles: prev.Profiles.filter(a => a.Id !== item.Id)
              }));
            }
          })
          .catch(console.error);
        break;

      case LOOKUPTYPES.SKILL:
        addResumeSkillApi(item.Id)
          .then(res => {
            if (res.success) {
              setExtractedSearchSettings(prev => ({
                ...prev,
                Skills: prev.Skills.filter(a => a.Id !== item.Id)
              }));
            }
          })
          .catch(console.error);
        break;

      case LOOKUPTYPES.CERTIFICATION: {
        setExtractedSearchSettings(prev => ({
          ...prev,
          Certifications: filter(prev.Certifications, a => a.Id != item.Id)
        }));
        break;
      }
      case LOOKUPTYPES.COUNTRY: {
        setExtractedSearchSettings(prev => ({
          ...prev,
          Countries: filter(prev.Countries, a => a.Id != item.Id)
        }));
        break;
      }
      case LOOKUPTYPES.INDUSTRY: {
        setExtractedSearchSettings(prev => ({
          ...prev,
          Industries: filter(prev.Industries, a => a.Id != item.Id)
        }));
        break;
      }
      case LOOKUPTYPES.KEYWORD: {
        setExtractedSearchSettings(prev => ({
          ...prev,
          Keywords: filter(prev.Keywords, a => a.Id != item.Id)
        }));
        break;
      }
      case LOOKUPTYPES.LANGUAGE: {
        setExtractedSearchSettings(prev => ({
          ...prev,
          Languages: filter(prev.Languages, a => a.Id != item.Id)
        }));
        break;
      }
    }
  };

  return (
    <div className="analyzer-root ">
      <div className="nextstep-analyzer">
        <Steps className="mobileResponsive" progressDot current={activeStep}>
          <Step title="Step 1" description="Resume Analyzed" />
          <Step title="Step 2" description="Boost your Value" />
          <Step title="Step 3" description="Your Career" />
        </Steps>

        {activeStep === 0 && (
          <>
            <p className="txt1">{labels?.searcherOnboardingResumeTitle}</p>
            <p className="txt2">
              Market is changing rapidly but we are there for you to look what
              roles and skills are trending in the market but first we will need
              to analyze your resume against the market.
            </p>

            <div className="select-warper flex justify-content-around items-center mt-4">
              {/* Country Select */}
              <div className="custom-select">
                <p>Country</p>
                <Select
                  value={selectedCountry}
                  onChange={setSelectedCountry}
                  placeholder="Select Country"
                >
                  {countriesList.map(country => (
                    <Option key={country.value} value={country.value}>
                      {country.label}
                    </Option>
                  ))}
                </Select>
              </div>

              {/* Currency Select */}
              <div className="custom-select">
                <p>Currency</p>
                <Select
                  value={selectedCurrency}
                  onChange={setSelectedCurrency}
                  placeholder="Select Currency"
                >
                  {allCurrenciesList.map(currency => (
                    <Option key={currency.value} value={currency.value}>
                      {currency.label}
                    </Option>
                  ))}
                </Select>
              </div>

              {/* Duration Select */}
              <div className="custom-select">
                <p>Date</p>
                <Select
                  value={selectedDuration}
                  onChange={setSelectedDuration}
                  placeholder="Select Duration"
                >
                  {durationLists.map(duration => (
                    <Option key={duration.value} value={duration.value}>
                      {duration.label}
                    </Option>
                  ))}
                </Select>
              </div>

              <div className="custom-select">
                <Button
                  type="primary"
                  className="analyze-btn"
                  onClick={handleAnalyzeClick}
                  loading={isLoading}
                >
                  Analyze my resume
                </Button>
              </div>
            </div>

            <div className="analyze-box">
              {selectedSalary && (
                <p className="txt1">
                  {`Average Salary for ${selectedSalary?.Lookupvalue} in  ${selectedSalary?.Country} is ${selectedSalary?.Average} ${selectedSalary?.Currency}/${selectedSalary?.DurationType}`}
                </p>
              )}

              {selectedSalary && (
                <div className="chart-warper flex items-center mt-5">
                  <div className="top-salaries">
                    {top3Salaries.map((item, i) => (
                      <div
                        key={i}
                        className={`salary-item ${
                          item.isSelected ? "selected" : ""
                        }`}
                        onClick={() => handleSalaryClick(item)}
                      >
                        <label>{item.Lookupvalue}</label>{" "}
                        <label>{`: ${item.Average} ${item.Currency}`}</label>
                      </div>
                    ))}
                  </div>
                  <div className="bar-chart">
                    <ReactApexChart
                      options={chartOptions}
                      height="180"
                      series={graphData}
                      type="bar"
                    />
                  </div>
                </div>
              )}
            </div>
          </>
        )}

        {activeStep === 1 && (
          <div className="mt-3">
            <p className="txt1">
              Boost your value or better relation to market needs.
            </p>
            <p className="txt2">
              These skills can Boost your value or relation to market needs
            </p>
            <div className="flex mt-3 flex-wrap items-center">
              {extractedSearchSettings.Skills.map(item => (
                <div key={item.Id} className="add-button">
                  <p className="icon-button-text">{item.LookupValue}</p>
                  <p
                    className="plus-button"
                    onClick={() =>
                      extractedSettingUpdate(LOOKUPTYPES.SKILL, item)
                    }
                  >
                    +
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeStep === 2 && (
          <div className="mt-3">
            <p className="txt1">Next step in your career</p>
            <p className="txt2">
              These are potential next step in your career path
            </p>
            <div className="flex mt-3 flex-wrap items-center">
              {extractedSearchSettings.Profiles.map(item => (
                <div key={item.Id} className="add-button">
                  <p className="icon-button-text">{item.LookupValue}</p>
                  <p
                    className="plus-button"
                    onClick={() =>
                      extractedSettingUpdate(LOOKUPTYPES.PROFILE, item)
                    }
                  >
                    +
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div
        className={`steps-btns-warper flex ${
          activeStep > 0 ? "justify-between" : "justify-end"
        }`}
      >
        {activeStep > 0 && (
          <Button
            type="default"
            className="step-btn"
            onClick={() => setActiveStep(prev => prev - 1)}
          >
            Back
          </Button>
        )}

        {activeStep < 2 && (
          <Button
            type="default"
            className="step-btn"
            onClick={() => setActiveStep(prev => prev + 1)}
          >
            Next
          </Button>
        )}
      </div>
    </div>
  );
};

export default MarketAnalyzer;
