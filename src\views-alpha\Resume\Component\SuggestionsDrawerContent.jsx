import { But<PERSON>, Checkbox, Grid } from "antd";
import {
  useGetSuggestedCertifications,
  useGetSuggestedProfile,
  useGetSuggestedSkills,
  useResumeProfile
} from "../resumeApi";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { notificationAction } from "../../../actions-alpha/notification";
import { useQueryClient } from "@tanstack/react-query";
import { ApiUrl } from "../../../api-alpha/apiUrls";
import { LoadingOutlined } from "@ant-design/icons";
import ArrowDownIcon from "../../../assets-alpha/images/svg/arrow-down-icon.svg?react";
import CommonDrawer from "../../../common-alpha/Drawer/Drawer";
import moment from "moment/moment";

// Suggestions Drawer Content Component
const SuggestionsDrawerContent = ({ open, setOpenModal }) => {
  const dispatch = useDispatch();
  const resume = useSelector(state => state.systemLabel.labels?.resume);

  const { md } = Grid.useBreakpoint();
  const queryClient = useQueryClient();
  const {
    data: profileData,
    isLoading: loadingProfile
  } = useGetSuggestedProfile({
    id: open.value,
    type: open.type
  });
  const { data: skillData, isLoading: loadingSkill } = useGetSuggestedSkills({
    id: open.value,
    type: open.type
  });
  const {
    data: certificationData,
    isLoading: loadingCertification
  } = useGetSuggestedCertifications({
    id: open.value,
    type: open.type
  });
  const [selectedProfile, setSelectedProfile] = useState([]);
  const [selectedSkills, setSelectedSkills] = useState([]);
  const [selectedCertifications, setSelectedCertifications] = useState([]);
  const filterProfiles =
    profileData?.items.filter(role => {
      const roleItem = role?.CorrelatedProfiles ?? role?.Profiles;
      return roleItem?.ProfileId != open.value;
    }) || [];
  const { mutateAsync: resumeProfileApi, isPending } = useResumeProfile();
  const onSelectProfile = profile => {
    setSelectedProfile(p =>
      p.includes(profile?.ProfileId)
        ? p.filter(pId => pId !== profile?.ProfileId)
        : [...p, profile?.ProfileId]
    );
  };

  const onSelectSkills = skill => {
    setSelectedSkills(p =>
      p.includes(skill?.SkillId)
        ? p.filter(pId => pId !== skill?.SkillId)
        : [...p, skill?.SkillId]
    );
  };
  const onSelectCertifications = certification => {
    setSelectedCertifications(p =>
      p.includes(certification?.CertificationId)
        ? p.filter(pId => pId !== certification?.CertificationId)
        : [...p, certification?.CertificationId]
    );
  };
  const onAddSuggestion = async () => {
    try {
      const profiles = selectedProfile?.map(profileId =>
        resumeProfileApi({
          suffixUrl: "ResumeProfiles/AddProfile",
          ProfileId: profileId,
          ExperienceLevel: 1
        })
      );
      const skills = selectedSkills?.map(skillId => {
        resumeProfileApi({
          suffixUrl: "ResumeSkills/AddSkill",
          SkillId: skillId,
          ExperienceLevel: 1
        });
      });

      const certifications = selectedCertifications?.map(certificationId => {
        resumeProfileApi({
          suffixUrl: "ResumeCertifications/AddCertification",
          CertificationId: certificationId,
          CertificationDate: moment().format()
        });
      });

      await Promise.all([...profiles, ...skills, ...certifications]);
      const notify = {
        message: resume?.suggestionsAddedSuccessfully,
        status: "success"
      };
      queryClient.invalidateQueries({
        queryKey: [
          ApiUrl.ResumeEdit.RoleSuggestionsLookup({
            Id: open.value,
            type: open.type
          })
        ]
      });
      queryClient.invalidateQueries({
        queryKey: [
          ApiUrl.ResumeEdit.SkillSuggestionsLookup({
            Id: open.value,
            type: open.type
          })
        ]
      });
      queryClient.invalidateQueries({
        queryKey: [ApiUrl.ResumeEdit.GetMyResume]
      });
      queryClient.invalidateQueries({ queryKey: [open?.url] });
      dispatch(notificationAction(notify));
      setOpenModal(false);
    } catch (error) {
      console.error("Error adding suggestions:", error);
    }
  };

  return (
    <CommonDrawer
      drawerKey="suggestions"
      zIndex={2000}
      title={
        <div className="flex items-center justify-between">
          <span className="text-[var(--purple)] font-[500]">
            {resume?.suggestions}
          </span>
          <div className="max-md:bg-white max-md:fixed max-md:px-4 max-md:py-3 max-md:inset-0 max-md:top-[auto] max-md:z-10 max-md:border-[#EAE5FC] max-md:border-t-1">
            <Button
              onClick={onAddSuggestion}
              type="primary"
              loading={isPending}
              disabled={isPending}
              className="rounded-full !px-3 !h-[36px] max-md:w-full"
            >
              {resume?.add}
            </Button>
          </div>
        </div>
      }
      mask={md ? false : true}
      onClose={() => setOpenModal(false)}
      open={open}
      closeIcon={
        <ArrowDownIcon className="rotate-270 max-md:rotate-0 !text-[#343333] w-[28px] h-[28px]" />
      }
      width={400}
      rootClassName="
      [&_.ant-drawer-content-wrapper]:!inset-y-[90px] 
      [&_.ant-drawer-content-wrapper]:!right-[420px]
      [&_.ant-drawer-content-wrapper]:max-md:!inset-y-[8px]
      "
      className="[&_.ant-drawer-header]:md:!border-b-0 [&_.ant-drawer-header]:md:!pb-4.5
      [&_.ant-drawer-body]:!pt-0
      "
    >
      {loadingProfile || loadingSkill || loadingCertification ? (
        <div className="flex items-center justify-center">
          <LoadingOutlined />
        </div>
      ) : (
        <div>
          <div className="mb-5">
            <p className="text-sm text-gray-500 !mb-2">{resume?.newlyAdded}</p>
            <div className="flex items-center mb-4">
              <Checkbox
                checked
                className="!mr-2 [&_.ant-checkbox-inner]:!w-5 [&_.ant-checkbox-inner]:!h-5 [&_.ant-checkbox-inner]:after:!border-white [&_.ant-checkbox-inner]:!border-[#F0ECEC] [&_.ant-checkbox-inner]:!bg-[#F0ECEC]"
                disabled
              />
              <div className="bg-white border border-[#C6C6C6] text-[#878787] rounded-full px-4 py-2 flex-grow">
                {open?.label}
              </div>
            </div>
          </div>

          <div className="mb-4">
            <p className="text-sm text-[var(--purple)] !mb-3">
              {resume?.aiSuggested}
            </p>
            {filterProfiles?.length > 0 && (
              <p className="text-sm text-gray-500 !mb-2">{resume?.roles}</p>
            )}

            {filterProfiles?.map((role, index) => {
              const roleItem = role?.CorrelatedProfiles ?? role?.Profiles;

              return (
                <div key={index} className="flex items-center mb-1.5 gap-2">
                  <Checkbox
                    checked={selectedProfile?.includes(roleItem?.ProfileId)}
                    onChange={() => onSelectProfile(roleItem)}
                    className="mr-2 [&_.ant-checkbox-inner]:!w-5 [&_.ant-checkbox-inner]:!h-5 [&_.ant-checkbox-inner]:!border-[1.5px]"
                  />
                  <div className="bg-white border border-[#EAE5FC] rounded-full px-4 py-2 flex-grow">
                    {roleItem?.ProfileValue}
                  </div>
                </div>
              );
            })}
          </div>

          <div>
            {skillData?.items?.length > 0 && (
              <p className="text-sm text-gray-500 !mb-2">{resume?.skills}</p>
            )}

            {skillData?.items?.map(skill => {
              const skillItem = skill?.Skills ?? skill?.CorrelatedSkills;
              return (
                <div
                  key={skillItem.SkillId}
                  className="flex items-center mb-1.5 gap-2"
                >
                  <Checkbox
                    checked={selectedSkills?.includes(skillItem?.SkillId)}
                    onChange={() => onSelectSkills(skillItem)}
                    className="mr-2 [&_.ant-checkbox-inner]:!w-5 [&_.ant-checkbox-inner]:!h-5 [&_.ant-checkbox-inner]:!border-[1.5px]"
                  />
                  <div className="bg-white border border-[#EAE5FC] rounded-full px-4 py-2 flex-grow">
                    {skillItem?.SkillValue}
                  </div>
                </div>
              );
            })}
          </div>

          <div>
            {certificationData?.items?.length > 0 && (
              <p className="text-sm text-gray-500 !mb-2">
                {resume?.certifications}
              </p>
            )}

            {certificationData?.items?.map(cert => {
              const certItem =
                cert?.CorrelatedCertifications ?? cert?.Certifications;
              return (
                <div
                  key={certItem.CertificationId}
                  className="flex items-center mb-1.5 gap-2"
                >
                  <Checkbox
                    checked={selectedCertifications?.includes(
                      certItem?.CertificationId
                    )}
                    onChange={() => onSelectCertifications(certItem)}
                    className="mr-2 [&_.ant-checkbox-inner]:!w-5 [&_.ant-checkbox-inner]:!h-5 [&_.ant-checkbox-inner]:!border-[1.5px]"
                  />
                  <div className="bg-white border border-[#EAE5FC] rounded-full px-4 py-2 flex-grow">
                    {certItem?.CertificationValue}
                  </div>
                </div>
              );
            })}
          </div>
          {!certificationData?.items?.length &&
            !skillData?.items?.length &&
            !filterProfiles?.length && (
              <div className="text-sm text-gray-500 text-center">
                {resume?.noSuggestionFound}
              </div>
            )}
        </div>
      )}
    </CommonDrawer>
  );
};

export default SuggestionsDrawerContent;
