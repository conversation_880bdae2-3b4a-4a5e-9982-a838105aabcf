import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import PageWrapper from "../../../components/PageWrapper/PageWrapper";
import { notificationAction } from "../../../actions/notification";
import "./messagesent.scss";
import { getSentMessagesApi, updateMessageApi } from "../messageApi";
import MessageDetail from "./components/MessageDetail";
import first from "lodash/first";
import isEmpty from "lodash/isEmpty";
import filter from "lodash/filter";
import toLower from "lodash/toLower";
import includes from "lodash/includes";
import { onStateChangeAction } from "./messageSentAction";
import { Col, Row, Input, Space, Typography, Select } from "antd";
import moment from "moment";
import ArrowLeft from "../../../assets/images/Icons/arrow-left.png";
import DeleteIcon from "../../../assets/images/Icons/delete-modal.png";
import SearchIcon from "../../../assets/images/secrch-icon.png";
import EmptyInfo from "../../../common/EmptyInfo/EmptyInfo";
import ConfirmDialog from "../../../common/ConfirmDialog/ConfirmDialog";

const { Title } = Typography;

const MessageSent = props => {
  const [state, setState] = useState({
    mobileModal: false,
    windowWidth: window.innerWidth,
    isMobileDevice: false,
    active: ""
  });

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    labels,
    isHelpActive,
    selectedMessage,
    filterMessages,
    isFetching,
    dialogMessage,
    searchKey,
    messageForm
  } = useSelector(state => ({
    labels: state.systemLabel.labels,
    isHelpActive: state.navigation.isHelpActive,
    ...state.messageSent
  }));

  useEffect(() => {
    getAllMessages();
    window.addEventListener("resize", handleResize);
    setState(prevState => ({
      ...prevState,
      active: window.location.hash?.slice(1)
    }));
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const handleResize = () => {
    setState(prevState => ({ ...prevState, windowWidth: window.innerWidth }));
  };

  const getAllMessages = () => {
    dispatch(onStateChangeAction({ isFetching: true }));
    getSentMessagesApi().then(data => {
      if (data.success) {
        const messageList = data.items.map(item => ({
          ...item,
          UserRequestMessage: first(item.UserRequestMessage)
        }));
        dispatch(
          onStateChangeAction({
            messageList,
            filterMessages: messageList
          })
        );
      }
      dispatch(onStateChangeAction({ isFetching: false }));
    });
  };

  const handleSearchChange = e => {
    const { value } = e.target;
    const { messageList } = props;
    const filterMessages = filter(messageList, message =>
      includes(toLower(message.Subject), toLower(value))
    );
    dispatch(onStateChangeAction({ filterMessages, searchKey: value }));
  };

  const handleContentChange = e => {
    const { value, name } = e.target;
    const data = {
      ...messageForm,
      [name]: value
    };
    dispatch(onStateChangeAction({ messageForm: data }));
  };

  const handleDelete = message => {
    dispatch(
      onStateChangeAction({
        selectedMessage: message,
        dialogMessage: labels.InfoIProSentMsgDeleteConfirmation
      })
    );
  };

  const handleYesClick = () => {
    const message = {
      MessageId: selectedMessage.MessageId,
      IsDeleteSentMessage: true
    };
    dispatch(
      onStateChangeAction({
        isFetching: true,
        selectedMessage: {},
        dialogMessage: ""
      })
    );
    updateMessageApi({ message }).then(() => {
      dispatch(
        notificationAction({
          status: "success",
          message: labels.InfoIProSentMsgDeleteSuccessfully
        })
      );
      getAllMessages();
    });
  };

  const handleNoClick = () => {
    dispatch(
      onStateChangeAction({
        dialogMessage: "",
        selectedCompany: null
      })
    );
  };

  const renderDate = date => {
    return moment(date).format("MM/DD/YYYY");
  };

  const handleClose = () => {
    setState(prevState => ({ ...prevState, mobileModal: false }));
  };

  const { mobileModal, windowWidth, isMobileDevice, active } = state;

  const getTabs = () => {
    return [
      {
        id: 1,
        label: "Inbox",
        active: window?.location?.hash == "#/inbox-messages",
        value: "/inbox-messages"
      },
      {
        id: 2,
        label: "Send",
        active: window?.location?.hash == "#/sent-messages",
        value: "/sent-messages"
      }
    ];
  };

  return (
    <PageWrapper className="collaboration">
      {dialogMessage && (
        <ConfirmDialog testId="message-sent-confirm-diloag">
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              testId="message-sent-delete-yes"
              onClick={handleYesClick}
            >
              {labels.companyDeleteCurtainYESBtnText}
            </ConfirmDialog.Button>
            <ConfirmDialog.Button
              className="dialog-btn"
              testId="message-sent-delete-no"
              onClick={handleNoClick}
            >
              {labels.companyDeleteCurtainNOBtnText}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      <div className="h-full new-design-search w-full flex flex-col">
        <div className="tabs-header-col">
          {isMobileDevice ? (
            <div
              className="flex items-center gap-2 cursor-pointer"
              onClick={() => {
                setState(prevState => ({
                  ...prevState,
                  isMobileDevice: false
                }));
              }}
            >
              <div>
                <img style={{ height: "16px" }} src={ArrowLeft} alt="" />
              </div>
              <Title level={5} className="!m-0">
                {" "}
                Messages
              </Title>
            </div>
          ) : (
            <div className="flex gap-4 justify-between items-center">
              <div className="flex gap-4 items-center main-header">
                {windowWidth > 480 && (
                  <button
                    className={`tab-btn-coll w-full ${
                      window?.location?.hash == "#/create-message"
                        ? "active-url"
                        : "inactive-url"
                    }`}
                    onClick={() => {
                      navigate("/create-message");
                    }}
                  >
                    + New Message
                  </button>
                )}
                {windowWidth < 767 ? (
                  <Select
                    className="dropdown-callooration"
                    size="medium"
                    bordered={false}
                    onChange={e => {
                      setState(prevState => ({ ...prevState, active: e }));

                      navigate(`${e}`);
                    }}
                    value={active}
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      option?.props?.label
                        ?.toLowerCase()
                        ?.indexOf(input?.toLowerCase()) >= 0
                    }
                    options={getTabs() || []}
                  />
                ) : (
                  <>
                    {getTabs()?.map(single => (
                      <button
                        className="tab-btn-coll"
                        key={single?.id}
                        style={{
                          background: single?.active ? "#8E81F5" : "",
                          color: single?.active ? "#fff" : "#000"
                        }}
                        onClick={() => {
                          navigate(single?.value);
                        }}
                      >
                        {single?.label}
                      </button>
                    ))}
                  </>
                )}
              </div>
            </div>
          )}
        </div>
        <Row className=" h-full" style={{ overflow: "auto" }}>
          <Col xs={24} md={8} className="collboration-column1">
            {isMobileDevice ? (
              <div
                className=" resume-list h-full"
                style={{ background: "#fff", borderRadius: "12px" }}
              >
                <div className="new-collaboration-detail-component message-detail-new">
                  {isEmpty(selectedMessage) ? (
                    <EmptyInfo>{labels.Message_Sent}</EmptyInfo>
                  ) : (
                    <MessageDetail
                      selectedMessage={selectedMessage}
                      labels={labels}
                      deleteMessage={handleDelete}
                    />
                  )}
                </div>
              </div>
            ) : (
              <div className="h-full flex flex-col">
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: "12px",
                    borderBottom: "1px solid #F3F3F3",
                    gap: "10px"
                  }}
                >
                  <Input
                    placeholder={labels?.searchInputPlaceholderMessage}
                    size="medium"
                    bordered={false}
                    style={{ border: "1px solid #F3F3F3" }}
                    onChange={handleSearchChange}
                  />
                  <div>
                    <img style={{ height: "30px" }} src={SearchIcon} alt="" />
                  </div>
                </div>
                <div
                  style={{
                    overflow: "auto",
                    padding: "12px",
                    flex: 1
                  }}
                >
                  {filterMessages?.length > 0 ? (
                    <Space size={[6, 6]} wrap className="short-list-new">
                      {filterMessages?.map(item => (
                        <div
                          onClick={() => {
                            if (windowWidth < 767) {
                              setState(prevState => ({
                                ...prevState,
                                isMobileDevice: true
                              }));
                            }
                            dispatch(
                              onStateChangeAction({
                                selectedMessage: item
                              })
                            );
                          }}
                          key={item?.UserCompanyId}
                          className={`flex gap-2 justify-between items-center pointer ${
                            item.MessageId === selectedMessage.MessageId
                              ? "background-shortlist short-list-item "
                              : "short-list-item"
                          }`}
                        >
                          <div className="flex  gap-2 w-full justify-content-betwee items-center">
                            <div className="text-clamp" style={{ flex: 1 }}>
                              <label style={{ fontSize: "14px" }}>
                                {item?.SenderName}
                              </label>
                              <p
                                className="text-clamp"
                                style={{ fontSize: "12px", marginBottom: 0 }}
                              >
                                {item?.Subject}
                              </p>
                            </div>

                            {item.MessageId === selectedMessage.MessageId && (
                              <div
                                onClick={e => {
                                  e.stopPropagation();
                                  handleDelete(item);
                                }}
                              >
                                <img
                                  src={DeleteIcon}
                                  alt=""
                                  style={{ height: "20px" }}
                                />
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </Space>
                  ) : (
                    <EmptyInfo>{labels.EMBARK_NO_ITEM_TO_DISPLAY}</EmptyInfo>
                  )}
                </div>
                {windowWidth < 480 && (
                  <div style={{ margin: "auto" }}>
                    <div className="tabs-header-col mt-3">
                      <button
                        className="tab-btn-coll"
                        style={{ background: "#6C63FF", color: "#fff" }}
                        onClick={() => {
                          navigate("/create-message");
                        }}
                      >
                        + New Message
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </Col>
          <Col md={16} xs={0} offset={0.5} className={"h-full"}>
            <div
              className=" resume-list h-full"
              style={{
                marginLeft: "16px",
                background: "#fff",
                borderRadius: "12px"
              }}
            >
              <div className="new-collaboration-detail-component message-detail-new">
                {isEmpty(selectedMessage) ? (
                  <EmptyInfo>{labels.Message_Sent}</EmptyInfo>
                ) : (
                  <MessageDetail
                    selectedMessage={selectedMessage}
                    labels={labels}
                    deleteMessage={handleDelete}
                  />
                )}
              </div>
            </div>
          </Col>
        </Row>
      </div>
    </PageWrapper>
  );
};

export default MessageSent;
