import { useRef } from "react";
import "./pagewrapper.scss";

const PageWrapper = ({ children, className, disabled }) => {
  const pageContainer = useRef(null);
  // const [scrollFromTop, setScrollFromTop] = useState(0);
  // const navigate = useNavigate();
  // const dispatch = useDispatch();
  // const notification = useSelector(state => state.notification);

  // useEffect(() => {
  //   if (pageContainer.current.scrollTo && scrollFromTop < 1) {
  //     pageContainer.current.scrollTo({
  //       top: 150,
  //       behavior: "smooth"
  //     });
  //     setScrollFromTop(150);
  //     return;
  //   }
  //   pageContainer.current.scrollTop = 150;
  // }, [scrollFromTop]);

  return (
    <div className={`page-wrapper ${className} `}>
      <div className={`${disabled} page-content dashHeadingBdr`}>
        {/* {notification.message && (
          <Notification
            status={notification.status}
            className={`${notification.className}`}
          >
            {notification.message}
          </Notification>
        )} */}
        <div className="views-cnt" ref={pageContainer}>
          {children}
        </div>
      </div>
    </div>
  );
};

PageWrapper.defaultProps = {
  className: ""
};

export default PageWrapper;
