import { But<PERSON>, <PERSON> } from "antd";
import ReasonIcon from "../../../../assets-alpha/images/svg/rating-reason-icon.svg?react";
import ArrowDown from "../../../../assets-alpha/images/svg/arrow-down-1.svg?react";
import ArrowRight from "../../../../assets-alpha/images/svg/arrow-right.svg?react";
import BackIcon from "../../../../assets-alpha/images/svg/arrow-square-left.svg?react";
import { useEffect, useState } from "react";
const RatingModal = ({
  selectedCollaboration,
  labels,
  onFormSelectChange,
  options,
  // isAlreadyRated,
  CollborationCloseReasonId,
  reviewQuestions,
  onSubmit,
  questions,
  setQuestions
}) => {
  useEffect(() => {
    if (reviewQuestions?.length) {
      setQuestions(reviewQuestions);
    }
  }, [reviewQuestions]);
  const [questionsTab, setQuestionsTab] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedReason, setSelectedReason] = useState([]);

  const currentQuestion = questions.find((q, index) => index === currentStep);

  const onRatingChange = (question, value) => {
    setQuestions(prevQuestions =>
      prevQuestions.map(q =>
        q.ReviewQuestionId === question.ReviewQuestionId
          ? { ...q, Answer: value }
          : q
      )
    );
  };
  const handleNext = () => {
    if (currentStep < questions.length - 1) {
      setCurrentStep(prev => prev + 1);
    } else {
      onSubmit(questions);
    }
  };

  const handleSkip = () => {
    if (currentStep < questions.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const renderAnswerUI = () => {
    if (!currentQuestion) return null;

    if (currentQuestion.Question !== "Rating_Question5") {
      return (
        <div className="!font-[Satoshi] flex w-full justify-center gap-2 mt-[25px]">
          {[1, 2, 3, 4, 5].map(num => {
            const isSelected = currentQuestion.Answer === num;
            return (
              <div
                key={num}
                className="flex flex-col gap-2 items-center cursor-pointer"
                onClick={() => onRatingChange(currentQuestion, num)}
              >
                <span
                  className={`flex justify-center items-center font-bold text-[16px] w-[50px] h-[50px] rounded-lg transition-all duration-200 ${
                    isSelected
                      ? "bg-[#8E81F5] text-white"
                      : "bg-[#F3F1FD] text-[#343333]"
                  }`}
                >
                  {num}
                </span>
                {num === 1 && (
                  <label
                    className={`font-medium text-xs -ml-2 ${
                      isSelected ? "text-[#8E81F5]" : "text-[#343333]"
                    }`}
                  >
                    {labels?.Dissatisfied_label}
                  </label>
                )}
                {num === 5 && (
                  <label
                    className={`font-medium text-xs ${
                      isSelected ? "text-[#8E81F5]" : "text-[#343333]"
                    }`}
                  >
                    {labels?.Satisfied_Label}
                  </label>
                )}
              </div>
            );
          })}
        </div>
      );
    }

    if (currentQuestion.Question === "Rating_Question5") {
      return (
        <div className="flex w-full justify-center gap-[14px] mt-[25px]">
          <Button
            type="default"
            className="!font-[Satoshi] !w-[114px] !h-[50px] bg-[#F3F1FD] !text-[#343333] !font-bold !text-[16px] rounded-lg"
          >
            {labels?.ShortlistResumeListDeletionCurtainNOBtnText}
          </Button>
          <Button
            type="default"
            className="!font-[Satoshi] !w-[114px] !h-[50px] bg-[#F3F1FD] !text-[#343333] !font-bold !text-[16px] rounded-lg"
          >
            {labels?.companyDeleteCurtainYESBtnText}
          </Button>
        </div>
      );
    }
  };

  return (
    <>
      {!questionsTab && (
        <div className="flex flex-col w-full h-[309px] p-6 !font-[Satoshi]">
          <div className="flex w-full justify-center">
            <ReasonIcon />
          </div>

          <div className="flex flex-col gap-3 mt-[19px]">
            <p className="text-[#343333] font-bold text-[16px] leading-[100%] !m-0">
              {labels?.Reason_Label}
            </p>

            <Select
              name="CollborationCloseReasonId"
              style={{ width: "100%" }}
              showSearch
              showArrow
              bordered={false}
              optionFilterProp="children"
              filterOption={(input, option) =>
                option?.label?.toLowerCase()?.includes(input.toLowerCase())
              }
              className="!h-11 bg-[#F3F1FD] border border-[#F3F1FD] rounded-[12px]
                [&_.ant-select-selector]:!text-[12px] md:[&_.ant-select-selector]:!text-[14px]
                [&_.ant-select-selection-placeholder]:!text-[#878787]"
              value={CollborationCloseReasonId}
              placeholder="Select a reason"
              onChange={(value, option) => {
                setSelectedReason(option);
                onFormSelectChange(value, option);
              }}
              suffixIcon={<ArrowDown className="w-[20px]" />}
              options={options || []}
            />
          </div>

          <div className="flex w-full justify-end mt-6">
            <Button
              disabled={!CollborationCloseReasonId}
              type="primary"
              className="w-[109px] h-9 text-sm"
              onClick={() => setQuestionsTab(true)}
            >
              {labels?.LANDING_LOGIN_CONTINUE_WITH_LBL}
              {CollborationCloseReasonId && (
                <ArrowRight className="w-4 h-4 text-white -ml-1" />
              )}
            </Button>
          </div>
        </div>
      )}

      {questionsTab && currentQuestion && (
        <div className="flex flex-col w-full h-[294px] p-6 !font-[Satoshi]">
          <div className="flex w-full justify-between">
            <BackIcon
              className="cursor-pointer"
              onClick={() => {
                if (currentStep === 0) {
                  setQuestionsTab(false);
                } else {
                  setCurrentStep(prev => prev - 1);
                }
              }}
            />
            <div className="flex items-center border-l-[3px] border-[#F05800]">
              <p className="pl-2 text-[#878787] text-xs font-medium">
                {selectedReason?.label}
              </p>
            </div>
          </div>

          <div className="flex w-full justify-center mt-6 gap-2">
            {questions.map((_, i) => (
              <div
                key={i}
                className={`h-[6px] w-6 rounded-[99px] ${
                  i === currentStep ? "bg-[#343333]" : "bg-[#878787]"
                }`}
              />
            ))}
          </div>

          <div className="flex w-full justify-center mt-4 text-center h-[22px]">
            <p className="flex flex-wrap w-full !m-0 text-[16px] font-bold leading-[100%] max-w-[100%] justify-center text-center">
              {currentQuestion.ReviewQuestionValue || "Question not found"}
            </p>
          </div>

          {renderAnswerUI()}

          {/* {!isAlreadyRated && ( */}
          <div className="flex w-full justify-between items-center mt-6">
            <span
              className="font-medium text-[#343333] text-sm cursor-pointer"
              onClick={handleSkip}
            >
              {labels?.searcherOnboardingSkip}
            </span>

            <Button
              type="primary"
              className="w-[109px] h-9 text-sm"
              onClick={handleNext}
            >
              {currentStep === questions.length - 1 ? "Submit" : "Continue"}
              <ArrowRight className="w-4 h-4 text-white -ml-1" />
            </Button>
          </div>
          {/* )} */}
        </div>
      )}
    </>
  );
};

export default RatingModal;
