<!DOCTYPE html>
<html>
<head>
    <title>ProDoo Messaging App Test</title>
    <script src="https://unpkg.com/@microsoft/signalr@latest/dist/browser/signalr.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        /* Left Sidebar - Conversations */
        .conversations-sidebar {
            width: 350px;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #f8f9fa;
        }

        .user-setup {
            margin-bottom: 15px;
        }

        .user-setup input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            margin-bottom: 10px;
        }

        .user-setup button {
            width: 100%;
            padding: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }

        .user-setup button:hover {
            background: #0056b3;
        }

        .connection-status {
            padding: 8px 12px;
            border-radius: 4px;
            text-align: center;
            font-size: 12px;
            font-weight: bold;
        }

        .connection-status.connected {
            background: #d4edda;
            color: #155724;
        }

        .connection-status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }

        .conversations-list {
            flex: 1;
            overflow-y: auto;
        }

        .conversation-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .conversation-item:hover {
            background-color: #f8f9fa;
        }

        .conversation-item.active {
            background-color: #e3f2fd;
            border-right: 3px solid #007bff;
        }

        .conversation-avatar {
            position: relative;
            width: 48px;
            height: 48px;
            flex-shrink: 0;
        }

        .profile-picture {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #e0e0e0;
        }

        .profile-initials {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
            border: 2px solid #e0e0e0;
        }

        .conversation-content {
            flex: 1;
            min-width: 0;
        }

        .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .conversation-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }

        .conversation-preview {
            font-size: 14px;
            color: #666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-status {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            margin-top: 5px;
            color: #666;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-online {
            background-color: #4caf50;
            box-shadow: 0 0 3px rgba(76, 175, 80, 0.5);
        }

        .status-offline {
            background-color: #9e9e9e;
        }

        .chat-header {
            padding: 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .chat-avatar {
            width: 40px;
            height: 40px;
            flex-shrink: 0;
        }

        .chat-avatar .profile-picture {
            width: 40px;
            height: 40px;
        }

        .chat-avatar .profile-initials {
            width: 40px;
            height: 40px;
            font-size: 14px;
        }

        .chat-user-status {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            color: #666;
        }

        .conversation-time {
            font-size: 12px;
            color: #999;
            white-space: nowrap;
        }

        /* Right Side - Chat Area */
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #f8f9fa;
        }

        .chat-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .chat-subtitle {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-end;
        }

        .message.own {
            justify-content: flex-end;
        }

        .message-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message.own .message-bubble {
            background: #007bff;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.other .message-bubble {
            background: #e9ecef;
            color: #333;
            border-bottom-left-radius: 4px;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin: 5px 10px 0;
        }

        .chat-input-area {
            padding: 20px;
            border-top: 1px solid #e0e0e0;
            background: white;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 20px;
            resize: none;
            outline: none;
            font-family: inherit;
            max-height: 100px;
        }

        .chat-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .send-button {
            padding: 12px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 600;
        }

        .send-button:hover:not(:disabled) {
            background: #0056b3;
        }

        .send-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            color: #666;
            text-align: center;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .error-state {
            text-align: center;
            padding: 40px 20px;
            color: #d32f2f;
            background-color: #ffebee;
            border-radius: 8px;
            margin: 20px;
        }

        .error-state h3 {
            margin-bottom: 15px;
            color: #d32f2f;
        }

        .retry-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 15px;
            font-size: 14px;
        }

        .retry-button:hover {
            background-color: #0056b3;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Left Sidebar - Conversations -->
        <div class="conversations-sidebar">
            <div class="sidebar-header">
                <div class="user-setup">
                    <input type="number" id="userIdInput" placeholder="Enter Your User ID (e.g., 101 , 103)">
                    <button id="loadConversationsBtn">Load My Conversations</button>
                </div>
                <div id="connectionStatus" class="connection-status disconnected">
                    Disconnected
                </div>
            </div>
            
            <div class="conversations-list" id="conversationsList">
                <div class="empty-state">
                    <h3>Welcome to ProDoo Messaging</h3>
                    <p>Enter your User ID above and click "Load My Conversations" to start</p>
                </div>
            </div>
        </div>

        <!-- Right Side - Chat Area -->
        <div class="chat-area">
            <div class="chat-header" id="chatHeader" style="display: none;">
                <div class="chat-user-info">
                    <div class="chat-avatar" id="chatAvatar" style="display: none;">
                        <img id="chatProfilePicture" class="profile-picture" style="display: none;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div id="chatProfileInitials" class="profile-initials"></div>
                    </div>
                    <div>
                        <div class="chat-title" id="chatTitle">Select a conversation</div>
                        <div class="chat-subtitle" id="chatSubtitle">Choose a conversation from the left to start messaging</div>
                    </div>
                </div>
                <div class="chat-user-status" id="chatUserStatus" style="display: none;">
                    <span class="status-indicator" id="chatStatusIndicator"></span>
                    <span id="chatStatusText">Offline</span>
                </div>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <div class="empty-state">
                    <h3>No conversation selected</h3>
                    <p>Select a conversation from the left sidebar to start chatting</p>
                </div>
            </div>
            
            <div class="chat-input-area" id="chatInputArea" style="display: none;">
                <div class="chat-input-container">
                    <textarea id="messageInput" class="chat-input" placeholder="Type your message..." rows="1"></textarea>
                    <button id="sendButton" class="send-button">Send</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // ========================================
        // ENVIRONMENT CONFIGURATION
        // ========================================
        // Uncomment the appropriate line for your environment:

        // LOCAL DEVELOPMENT:
       // const API_BASE_URL = 'http://localhost:5270';

        // AZURE STAGING:
         const API_BASE_URL = 'https://prodoov2-gqg9esa9g9b3d9bd.canadacentral-01.azurewebsites.net';

        // ========================================

        // Global variables
        let connection = null;
        let currentUserId = null;
        let currentConversationId = null;
        let conversations = [];

        // Store user statuses: { userId: { isOnline: boolean, lastSeen: Date } }
        window.userStatuses = {};

        // Initialize SignalR connection
        async function initializeSignalR() {
            connection = new signalR.HubConnectionBuilder()
                .withUrl(`${API_BASE_URL}/hubs/messages`)
                .build();

            // Connection events
            connection.onclose(() => {
                updateConnectionStatus(false);
            });

            connection.onreconnecting(() => {
                updateConnectionStatus(false, "Reconnecting...");
            });

            connection.onreconnected(() => {
                updateConnectionStatus(true);
                if (currentUserId) {
                    joinUserGroup(currentUserId);
                }
            });

            // SignalR event handlers
            connection.on("ConversationCreated", (data) => {
                console.log("New conversation created:", data);
                handleNewConversation(data);
            });

            connection.on("NewMessage", (data) => {
                console.log("New message received:", data);
                handleNewMessage(data);
            });

            // Handle user status change notifications
            connection.on("UserStatusChanged", (data) => {
                console.log("👤 Received UserStatusChanged notification:", data);
                updateUserStatus(parseInt(data.UserId), data.IsOnline);
            });

            // Don't automatically start connection - this will be done manually after user validation
        }

        // Update connection status UI
        function updateConnectionStatus(connected, customText = null) {
            const statusElement = document.getElementById('connectionStatus');
            if (connected) {
                statusElement.textContent = customText || 'Connected';
                statusElement.className = 'connection-status connected';
            } else {
                statusElement.textContent = customText || 'Disconnected';
                statusElement.className = 'connection-status disconnected';
            }
        }

        // Join user group for notifications
        async function joinUserGroup(userId) {
            if (connection && connection.state === signalR.HubConnectionState.Connected) {
                try {
                    await connection.invoke("JoinUserGroup", userId.toString());
                    console.log(`Joined user group for User ${userId}`);

                    // Load current user's status after joining group
                    await loadCurrentUserStatus(userId);
                } catch (err) {
                    console.error("Failed to join user group:", err);
                }
            }
        }

        // Load current user's status
        async function loadCurrentUserStatus(userId) {
            try {
                const response = await fetch(`${API_BASE_URL}/users/${userId}/status`);
                if (response.ok) {
                    const status = await response.json();
                    window.userStatuses[userId] = {
                        isOnline: status.isOnline,
                        lastSeen: new Date(status.lastSeenUtc)
                    };
                    console.log(`📊 Current user status loaded - User ${userId}: ${status.isOnline ? 'Online' : 'Offline'}`);
                } else {
                    console.error('Failed to load current user status:', response.status);
                }
            } catch (error) {
                console.error('Error loading current user status:', error);
            }
        }

        // Load conversations for a user
        async function loadConversations(userId) {
            const conversationsList = document.getElementById('conversationsList');
            conversationsList.innerHTML = '<div class="loading">Loading conversations...</div>';

            try {
                const response = await fetch(`${API_BASE_URL}/conversations/initiator/${userId}`);
                if (response.ok) {
                    conversations = await response.json();

                    // Load user statuses for all conversation participants
                    await loadUserStatuses();

                    renderConversations();
                } else {
                    conversationsList.innerHTML = `
                        <div class="empty-state">
                            <h3>Error loading conversations</h3>
                            <p>Failed to load conversations. Please try again.</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error("Error loading conversations:", error);
                conversationsList.innerHTML = `
                    <div class="empty-state">
                        <h3>Connection Error</h3>
                        <p>Unable to connect to server. Please check your connection.</p>
                    </div>
                `;
            }
        }

        // Load user statuses for all conversation participants
        async function loadUserStatuses() {
            if (conversations.length === 0) return;

            // Get all unique user IDs from conversations (excluding current user)
            const userIds = [...new Set(
                conversations.flatMap(conv => [conv.initiatorId, conv.recipientId])
                    .filter(id => id !== currentUserId)
            )];

            if (userIds.length === 0) return;

            try {
                const response = await fetch(`${API_BASE_URL}/users/status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ userIds })
                });

                if (response.ok) {
                    const statuses = await response.json();
                    statuses.forEach(status => {
                        window.userStatuses[status.userId] = {
                            isOnline: status.isOnline,
                            lastSeen: new Date(status.lastSeenUtc)
                        };
                    });
                    console.log('📊 User statuses loaded:', window.userStatuses);
                } else {
                    console.error('Failed to load user statuses:', response.status);
                }
            } catch (error) {
                console.error('Error loading user statuses:', error);
            }
        }

        // Update user status and refresh UI
        function updateUserStatus(userId, isOnline) {
            window.userStatuses[userId] = {
                isOnline: isOnline,
                lastSeen: new Date()
            };

            console.log(`👤 User ${userId} is now ${isOnline ? 'online' : 'offline'}`);

            // Update conversations list
            renderConversations();

            // Update chat header if this is the current conversation user
            if (currentConversationId) {
                const conversation = conversations.find(c => c.conversationId === currentConversationId);
                if (conversation) {
                    const otherUserId = getOtherUserId(conversation);
                    if (otherUserId === userId) {
                        const chatStatusIndicator = document.getElementById('chatStatusIndicator');
                        const chatStatusText = document.getElementById('chatStatusText');

                        if (chatStatusIndicator && chatStatusText) {
                            chatStatusIndicator.className = `status-indicator ${isOnline ? 'status-online' : 'status-offline'}`;
                            chatStatusText.textContent = isOnline ? 'Online' : 'Offline';
                        }
                    }
                }
            }
        }

        // Get user initials from display name
        function getUserInitials(displayName) {
            if (!displayName) return '?';
            return displayName.split(' ')
                .map(name => name.charAt(0).toUpperCase())
                .slice(0, 2)
                .join('');
        }

        // Get other user info from conversation
        function getOtherUserInfo(conversation) {
            const isInitiator = conversation.initiatorId === currentUserId;
            return isInitiator ? conversation.recipientInfo : conversation.initiatorInfo;
        }

        // Render conversations list
        function renderConversations() {
            const conversationsList = document.getElementById('conversationsList');

            if (conversations.length === 0) {
                conversationsList.innerHTML = `
                    <div class="empty-state">
                        <h3>No conversations yet</h3>
                        <p>You don't have any conversations yet. Start by creating one!</p>
                    </div>
                `;
                return;
            }

            conversationsList.innerHTML = conversations.map(conv => {
                const otherUserInfo = getOtherUserInfo(conv);
                const otherUserId = otherUserInfo?.userId || getOtherUserId(conv);
                const userStatus = window.userStatuses?.[otherUserId] || { isOnline: false };
                const displayName = otherUserInfo?.displayName || `User ${otherUserId}`;
                const profilePicture = otherUserInfo?.profilePicture;
                const initials = getUserInitials(displayName);

                // Create profile picture or initials
                const profileElement = profilePicture
                    ? `<img src="${profilePicture}" alt="${displayName}" class="profile-picture" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">`
                    : '';

                const initialsElement = `<div class="profile-initials" ${profilePicture ? 'style="display:none"' : ''}>${initials}</div>`;

                return `
                    <div class="conversation-item" data-conversation-id="${conv.conversationId}" onclick="selectConversation('${conv.conversationId}')">
                        <div class="conversation-avatar">
                            ${profileElement}
                            ${initialsElement}
                        </div>
                        <div class="conversation-content">
                            <div class="conversation-header">
                                <div class="conversation-title">${conv.title}</div>
                                <div class="conversation-time">${formatTime(conv.createdDate)}</div>
                            </div>
                            <div class="conversation-preview">${conv.lastMessage || 'No messages yet'}</div>
                            <div class="user-status">
                                <span class="status-indicator ${userStatus.isOnline ? 'status-online' : 'status-offline'}"></span>
                                <span>${displayName} • ${userStatus.isOnline ? 'Online' : 'Offline'}</span>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Select and load a conversation
        async function selectConversation(conversationId) {
            // Update UI to show selected conversation
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-conversation-id="${conversationId}"]`).classList.add('active');

            currentConversationId = conversationId;
            const conversation = conversations.find(c => c.conversationId === conversationId);

            // Update chat header
            document.getElementById('chatHeader').style.display = 'block';
            document.getElementById('chatTitle').textContent = conversation.title;

            const otherUserInfo = getOtherUserInfo(conversation);
            const otherUserId = otherUserInfo?.userId || getOtherUserId(conversation);
            const displayName = otherUserInfo?.displayName || `User ${otherUserId}`;
            const profilePicture = otherUserInfo?.profilePicture;
            const initials = getUserInitials(displayName);

            document.getElementById('chatSubtitle').textContent = `Conversation with ${displayName}`;

            // Update chat avatar
            const chatAvatar = document.getElementById('chatAvatar');
            const chatProfilePicture = document.getElementById('chatProfilePicture');
            const chatProfileInitials = document.getElementById('chatProfileInitials');

            chatAvatar.style.display = 'block';
            chatProfileInitials.textContent = initials;

            if (profilePicture) {
                chatProfilePicture.src = profilePicture;
                chatProfilePicture.alt = displayName;
                chatProfilePicture.style.display = 'block';
                chatProfileInitials.style.display = 'none';
            } else {
                chatProfilePicture.style.display = 'none';
                chatProfileInitials.style.display = 'flex';
            }

            // Update user status in chat header
            const userStatus = window.userStatuses?.[otherUserId] || { isOnline: false };
            const chatUserStatus = document.getElementById('chatUserStatus');
            const chatStatusIndicator = document.getElementById('chatStatusIndicator');
            const chatStatusText = document.getElementById('chatStatusText');

            chatUserStatus.style.display = 'flex';
            chatStatusIndicator.className = `status-indicator ${userStatus.isOnline ? 'status-online' : 'status-offline'}`;
            chatStatusText.textContent = userStatus.isOnline ? 'Online' : 'Offline';

            // Show chat input
            document.getElementById('chatInputArea').style.display = 'block';

            // Load messages for this conversation
            await loadMessages(conversationId);
        }

        // Get the other user ID in conversation
        function getOtherUserId(conversation) {
            return conversation.initiatorId === currentUserId ? conversation.recipientId : conversation.initiatorId;
        }

        // Load messages for a conversation
        async function loadMessages(conversationId) {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = '<div class="loading">Loading messages...</div>';

            try {
                const response = await fetch(`${API_BASE_URL}/messages/conversation/${conversationId}`);
                if (response.ok) {
                    const messages = await response.json();
                    window.currentMessages = messages; // Store messages globally
                    renderMessages(messages);
                } else {
                    chatMessages.innerHTML = `
                        <div class="empty-state">
                            <h3>Error loading messages</h3>
                            <p>Failed to load messages. Please try again.</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error("Error loading messages:", error);
                chatMessages.innerHTML = `
                    <div class="empty-state">
                        <h3>Connection Error</h3>
                        <p>Unable to load messages. Please check your connection.</p>
                    </div>
                `;
            }
        }

        // Render messages in chat
        function renderMessages(messages) {
            const chatMessages = document.getElementById('chatMessages');

            if (messages.length === 0) {
                chatMessages.innerHTML = `
                    <div class="empty-state">
                        <h3>No messages yet</h3>
                        <p>Start the conversation by sending a message!</p>
                    </div>
                `;
                return;
            }

            chatMessages.innerHTML = messages.map(msg => `
                <div class="message ${msg.senderId === currentUserId ? 'own' : 'other'}">
                    <div class="message-bubble">
                        ${msg.content}
                    </div>
                    <div class="message-time">${formatTime(msg.sentDate)}</div>
                </div>
            `).join('');

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Send a message
        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const content = messageInput.value.trim();

            if (!content || !currentConversationId) {
                return;
            }

            const sendButton = document.getElementById('sendButton');
            sendButton.disabled = true;
            sendButton.textContent = 'Sending...';

            try {
                const response = await fetch(`${API_BASE_URL}/messages`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        conversationId: currentConversationId,
                        senderId: currentUserId,
                        content: content,
                        type: 'Text'
                    })
                });

                if (response.ok) {
                    // Add message to UI immediately for the sender
                    const newMessage = {
                        senderId: currentUserId,
                        content: content,
                        sentDate: new Date().toISOString(),
                        messageType: 'Text'
                    };

                    // Add to messages array in chronological order
                    if (!window.currentMessages) {
                        window.currentMessages = [];
                    }

                    // Add the message and sort by sentDate to maintain order
                    window.currentMessages.push(newMessage);
                    window.currentMessages.sort((a, b) => new Date(a.sentDate) - new Date(b.sentDate));

                    renderMessages(window.currentMessages);

                    messageInput.value = '';
                } else {
                    alert('Failed to send message. Please try again.');
                }
            } catch (error) {
                console.error("Error sending message:", error);
                alert('Failed to send message. Please try again.');
            } finally {
                sendButton.disabled = false;
                sendButton.textContent = 'Send';
            }
        }

        // Handle new conversation from SignalR
        function handleNewConversation(data) {
            // Check if this conversation involves current user
            if (data.initiatorId === currentUserId || data.recipientId === currentUserId) {
                // Add to conversations list if not already there
                const existingConv = conversations.find(c => c.id === data.conversationId);
                if (!existingConv) {
                    const newConversation = {
                        id: data.conversationId,
                        title: data.title,
                        initiatorId: data.initiatorId,
                        recipientId: data.recipientId,
                        createdAt: data.timestamp,
                        lastMessage: 'New conversation started'
                    };
                    conversations.unshift(newConversation); // Add to top
                    renderConversations();
                }
            }
        }

        // Handle new message from SignalR
        function handleNewMessage(data) {
            // Update conversation list with latest message
            const conversation = conversations.find(c => c.conversationId === data.conversationId);
            if (conversation) {
                conversation.lastMessage = data.content;
                conversation.lastMessageTime = data.timestamp;

                // Move conversation to top
                conversations = conversations.filter(c => c.conversationId !== data.conversationId);
                conversations.unshift(conversation);
                renderConversations();

                // Re-select current conversation if it was moved
                if (currentConversationId === data.conversationId) {
                    document.querySelector(`[data-conversation-id="${currentConversationId}"]`).classList.add('active');
                }
            }

            // If this message is for the currently open conversation, add it to chat
            // But only if it's not from the current user (to avoid duplicates)
            if (currentConversationId === data.conversationId && data.senderId !== currentUserId) {
                addMessageToChat(data);
            }
        }

        // Add message to current chat
        function addMessageToChat(messageData) {
            // Add to messages array first
            if (!window.currentMessages) {
                window.currentMessages = [];
            }

            // Convert SignalR message format to our message format
            const newMessage = {
                senderId: messageData.senderId,
                content: messageData.content,
                sentDate: messageData.timestamp,
                messageType: messageData.messageType || 'Text'
            };

            // Add the message and sort by sentDate to maintain order
            window.currentMessages.push(newMessage);
            window.currentMessages.sort((a, b) => new Date(a.sentDate) - new Date(b.sentDate));

            // Re-render all messages to maintain proper order
            renderMessages(window.currentMessages);
        }

        // Format timestamp
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            const now = new Date();
            const diffInHours = (now - date) / (1000 * 60 * 60);

            if (diffInHours < 24) {
                return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            } else if (diffInHours < 24 * 7) {
                return date.toLocaleDateString([], { weekday: 'short' });
            } else {
                return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
            }
        }

        // Validate user before connecting
        async function validateUser(userId) {
            try {
                const response = await fetch(`${API_BASE_URL}/users/${userId}/validate`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                return result;
            } catch (error) {
                console.error('❌ User validation failed:', error);
                return { userId: userId, isValid: false };
            }
        }

        // Show error message
        function showError(message) {
            const conversationsList = document.getElementById('conversationsList');
            conversationsList.innerHTML = `
                <div class="error-state">
                    <h3>❌ Connection Error</h3>
                    <p>${message}</p>
                    <button onclick="location.reload()" class="retry-button">Retry</button>
                </div>
            `;
        }

        // Auto-resize textarea
        function autoResizeTextarea(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', async () => {
            // Don't initialize SignalR automatically - wait for user validation
            updateConnectionStatus(false, "Enter your User ID to connect");

            // Load conversations button
            document.getElementById('loadConversationsBtn').addEventListener('click', async () => {
                const userId = parseInt(document.getElementById('userIdInput').value);
                if (!userId) {
                    alert('Please enter a valid User ID');
                    return;
                }

                try {
                    // Step 1: Validate user first
                    updateConnectionStatus(false, "Validating user...");
                    const validation = await validateUser(userId);

                    if (!validation.isValid) {
                        updateConnectionStatus(false, "❌ User validation failed");
                        showError(`User ${userId} is not valid or not found in the system. Please check the user ID and make sure the user exists in the Users module.`);
                        return;
                    }

                    console.log('✅ User validated:', validation);

                    // Step 2: Initialize SignalR connection (only after validation)
                    updateConnectionStatus(false, `Initializing connection for ${validation.displayName || `User ${userId}`}...`);

                    if (!connection) {
                        await initializeSignalR();
                    }

                    // Step 3: Connect to hub
                    updateConnectionStatus(false, `Connecting to messaging hub...`);
                    await connection.start();

                    // Step 4: Join user group and load conversations
                    currentUserId = userId;
                    await joinUserGroup(userId);
                    await loadConversations(userId);

                    // Step 5: Update status to connected
                    updateConnectionStatus(true, `Connected as ${validation.displayName || `User ${userId}`}`);

                } catch (error) {
                    console.error('❌ Connection failed:', error);
                    updateConnectionStatus(false, "❌ Connection failed");
                    showError(`Failed to connect: ${error.message}`);
                }
            });

            // Send message button
            document.getElementById('sendButton').addEventListener('click', sendMessage);

            // Message input - Enter to send, Shift+Enter for new line
            const messageInput = document.getElementById('messageInput');
            messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                } else if (e.key === 'Enter' && e.shiftKey) {
                    // Allow new line
                }
            });

            // Auto-resize message input
            messageInput.addEventListener('input', () => {
                autoResizeTextarea(messageInput);
            });
        });
    </script>
</body>
</html>
