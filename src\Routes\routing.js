import { lazy } from "react";
import { roles } from "../utilities/helpers";
import { components } from "react-select";
const About = lazy(() => import("../views-alpha/About/About"));
const Feedback = lazy(() => import("../views/Feedback/Feedback"));
// const MessageInbox = lazy(() =>
//   import("../views/Message/MessageInbox/MessageInbox")
// );
// const MessageNew = lazy(() => import("../views/Message/MessageNew/MessageNew"));
// const MessageSent = lazy(() =>
//   import("../views/Message/MessageSent/MessageSent")
// );
const Messages = lazy(() => import("../views-alpha/Messages/Messages"));
const NewSearch = lazy(() => import("../views/NewResumeSearch/Search"));
const FindProfessional = lazy(() =>
  import("../views-alpha/FindProfessional/FindProfessional")
);
const NoMatch = lazy(() => import("../views/NoMatch/NoMatch"));
//import CompanyNew from "../views/Company/companyNew";
const AlphaCompany = lazy(() => import("../views-alpha/Workplace/Workplace"));
const NewResume = lazy(() => import("../views/NewResume/newResume"));
const AlphaNewPresent = lazy(() =>
  import("../views-alpha/Portfolio/Portfolio")
);
const Resume = lazy(() => import("../views/Resume/Resume"));
const ResumeBuilder = lazy(() => import("../views-alpha/Resume/Resume"));
const Statistics = lazy(() => import("../views/Statistics/Statistics"));
const NewCreateOpportunity = lazy(() =>
  import(
    "../views-alpha/Opportunity/Searcher/NewCreateOpportunity/NewCreateOpportunity"
  )
);
const NewSearcherOpportunityDraft = lazy(() =>
  import("../views-alpha/Opportunity/Searcher/NewSearcherOpportunityDraft")
);
const NewSearcherOpportunitySent = lazy(() =>
  import("../views-alpha/Opportunity/Searcher/NewSearcherOpportunitySent")
);
const NewShortlist = lazy(() => import("../views/newShortlist/Shortlist"));
const Assort = lazy(() => import("../views/Assort/Assort"));
const Cms = lazy(() => import("../views/Cms/Cms"));
const Embark = lazy(() => import("../views/Embark/Embark"));
const Network = lazy(() => import("../views-alpha/Network/Network"));
const NewSavedSearch = lazy(() => import("../views/NewSaveSearch/Search"));
const NextStep = lazy(() => import("../views/NextStep/NextStep"));
const Sense = lazy(() => import("../views/Sense/Sense"));
const Snapshot = lazy(() => import("../views/Snapshot/Snapshot"));
const SnapshotAlpha = lazy(() => import("../views-alpha/Snapshot/Snapshot"));
const Tictell = lazy(() => import("../views/Tictell/Tictell"));
const NewIproActiveCollaborations = lazy(() =>
  import("../views-alpha/Collaboration/NewIpro/IproActiveCollaboration")
);
const NewSearcherCollaborationAccepted = lazy(() =>
  import("../views-alpha/Collaboration/NewSearcher/activeCollobration")
);
const NewCreateCollaboration = lazy(() =>
  import("../views-alpha/Collaboration/NewSearcher/createCollobration")
);
const OpportunityNewIpro = lazy(() =>
  import("../views-alpha/Opportunity/Ipro/NewOpportunityIpro")
);
const NewDashboard = lazy(() => import("../views/Dashboard/NewDashboard"));
const Headsup = lazy(() => import("../views/Headsup/Headsup"));

const ConnectedNetwork = lazy(() =>
  import("../views-alpha/Network/ConnectedNetwork")
);
const InvitationsNetwork = lazy(() =>
  import("../views-alpha/Network/InvitationsNetwork")
);
const InvitedNetwork = lazy(() =>
  import("../views-alpha/Network/InvitedNetwork")
);
const IproPhillip = lazy(() => import("../components/Phillip/IproPhillip"));
const IProPhilip = lazy(() => import("../views/Philip/IProPhilipModule"));
const SearcherPhillip = lazy(() =>
  import("../components/Phillip/SearcherPhillip")
);
const Philip = lazy(() => import("../views/Philip/SearcherPhilipModule"));
const Dashboard = lazy(() => import("../views-alpha/Dashboard/Dashboard"));
const LandingPage = lazy(() =>
  import("../views-alpha/LandingPage/LandingPage")
);
const Login = lazy(() => import("../views-alpha/LandingPage/Auth/Login"));
const Signup = lazy(() => import("../views-alpha/LandingPage/Auth/Signup"));
const ForgetPassword = lazy(() =>
  import("../views-alpha/LandingPage/Auth/ForgetPassword")
);

export const privateRoutes = {
  dashboard: {
    id: 1,
    name: "dashboard",
    path: "/dashboard",
    component: Dashboard,
    roles: [roles.ipro, roles.searcher]
  },
  Messages: {
    id: 2,
    name: "Messages",
    path: "/messages",
    component: Messages,
    roles: [roles.ipro, roles.searcher]
  },
  // createMessage: {
  //   id: 2,
  //   name: "createMessage",
  //   path: "/create-message",
  //   component: MessageNew,
  //   roles: [roles.ipro, roles.searcher]
  // },
  // inboxMessages: {
  //   id: 3,
  //   name: "inboxMessages",
  //   path: "/inbox-messages",
  //   component: MessageInbox,
  //   roles: [roles.ipro, roles.searcher]
  // },
  // sentMessages: {
  //   id: 4,
  //   name: "sentMessages",
  //   path: "/sent-messages",
  //   component: MessageSent,
  //   roles: [roles.ipro, roles.searcher]
  // },
  iproNewOpportunity: {
    id: 5,
    name: "iproNewOpportunity",
    path: "/new-opportunity",
    component: OpportunityNewIpro,
    roles: [roles.ipro]
  },
  iproNewOpportunityWithId: {
    id: 51,
    name: "iproNewOpportunityWithId",
    path: "/new-opportunity/:id/:idd",
    component: OpportunityNewIpro,
    roles: [roles.ipro]
  },

  searcherCreateCollaboration: {
    id: 8,
    name: "searcherCreateCollaboration",
    path: "/create-collaboration",
    component: NewCreateCollaboration,
    roles: [roles.searcher]
  },

  searcherAcceptedCollaborations: {
    id: 10,
    name: "searcherAcceptedCollaborations",
    path: "/active-collaborations",
    component: NewSearcherCollaborationAccepted,
    roles: [roles.searcher]
  },

  iproActiveCollaborations: {
    id: 12,
    name: "CollaborationActive",
    path: "/ipro-active-collaborations",
    component: NewIproActiveCollaborations,
    roles: [roles.ipro]
  },

  resume: {
    id: 13,
    name: "resume",
    path: "/resume-builder",
    component: Resume,
    roles: [roles.ipro]
  },
  resumeBuilder: {
    id: 13.5,
    name: "resume",
    path: "/resume",
    component: ResumeBuilder,
    roles: [roles.ipro]
  },
  resumeEdit: {
    id: 14,
    name: "resumeEdit",
    path: "/resume-edit",
    component: NewResume,
    roles: [roles.ipro]
  },

  presentations: {
    id: 15,
    name: "presentations",
    path: "/presentations",
    component: AlphaNewPresent,
    roles: [roles.ipro]
  },

  companies: {
    id: 16,
    name: "companies",
    path: "/companies",
    component: AlphaCompany,
    roles: [roles.searcher]
  },

  searcherCreateOpportunity: {
    id: 17,
    name: "searcherCreateOpportunity",
    path: "/create-opportunity",
    component: NewCreateOpportunity,
    roles: [roles.searcher]
  },

  searcherSentOpportunity: {
    id: 18,
    name: "searcherSentOpportunity",
    path: "/sent-opportunities",
    component: NewSearcherOpportunitySent,
    roles: [roles.searcher]
  },

  searcherDraftOpportunity: {
    id: 19,
    name: "searcherDraftOpportunity",
    path: "/draft-opportunities",
    component: NewSearcherOpportunityDraft,
    roles: [roles.searcher]
  },

  iproPhillip: {
    id: 20,
    name: "iproPhillip",
    path: "/ipro-phillip",
    component: IproPhillip,
    roles: [roles.ipro]
  },
  searcherPhillip: {
    id: 21,
    name: "searcherPhillip",
    path: "/searcher-phillip",
    component: SearcherPhillip,
    roles: [roles.searcher]
  },
  resumeSearch: {
    id: 22,
    name: "resumeSearch",
    path: "/find-professional",
    component: NewSearch,
    roles: [roles.searcher]
  },

  findProfessional: {
    id: 25,
    name: "FindProfessional",
    path: "/resume-search",
    component: FindProfessional,
    roles: [roles.searcher]
  },

  Network: {
    id: 33,
    name: "Network",
    path: "/network",
    component: Network,
    roles: [roles.ipro, roles.searcher]
  },
  connectedNetwork: {
    id: 33,
    name: "Connected",
    path: "/connected-networks",
    component: ConnectedNetwork,
    roles: [roles.ipro, roles.searcher]
  },

  invitedNetwork: {
    id: 33,
    name: "Invited",
    path: "/invited-networks",
    component: InvitedNetwork,
    roles: [roles.ipro, roles.searcher]
  },

  invitationNetwork: {
    id: 33,
    name: "Invitations",
    path: "/invitations-networks",
    component: InvitationsNetwork,
    roles: [roles.ipro, roles.searcher]
  },

  savedSearch: {
    id: 23,
    name: "savedSearch",
    path: "/saved-searches",
    component: NewSavedSearch,
    roles: [roles.searcher]
  },

  shortlists: {
    id: 24,
    name: "shortlists",
    path: "/shortlists",
    component: NewShortlist,
    roles: [roles.searcher]
  },

  statistics: {
    id: 26,
    name: "statistics",
    path: "/statistics",
    component: Statistics,
    roles: [roles.ipro, roles.searcher]
  },
  feedback: {
    id: 27,
    name: "feedback",
    path: "/feedback",
    component: Feedback,
    roles: [roles.ipro, roles.searcher]
  },

  about: {
    id: 29,
    name: "about",
    path: "/about",
    component: About,
    roles: [roles.ipro, roles.searcher]
  },

  faq: {
    id: 290,
    name: "faq",
    path: "/faq",
    component: About,
    roles: [roles.ipro, roles.searcher]
  },

  snapshot: {
    id: 24,
    name: "snapshot",
    path: "/snapshot",
    component: SnapshotAlpha,
    roles: [roles.searcher],
    children: [
      { path: "home" },
      { path: "reports" },
      { path: "contracts" },
      { path: "pools" }
    ]
  },
  snapshotAlpha: {
    id: 24,
    name: "snapshot-alpha",
    path: "/snapshot-alpha",
    component: Snapshot,
    roles: [roles.searcher],
    children: [
      { path: "home" },
      { path: "reports" },
      { path: "collaborations" },
      { path: "pools" }
    ]
  },
  tictell: {
    id: 28,
    name: "tictell",
    path: "/tictell",
    component: Tictell,
    roles: [roles.ipro],
    children: [
      { path: "home" },
      { path: "timesheets" },
      { path: "expensesheets" }
    ]
  },
  assort: {
    id: 29,
    name: "asort",
    path: "/assort",
    component: Assort,
    roles: [roles.searcher],
    children: [
      { path: "home" },
      { path: "collaborations" },
      { path: "opportunites" }
    ]
  },
  embark: {
    id: 30,
    name: "embark",
    path: "/embark",
    component: Embark,
    roles: [roles.searcher],
    children: [
      { path: "home" },
      { path: "mywork" },
      { path: "manageipros" },
      { path: "manageflows" }
    ]
  },
  headsup: {
    id: 30,
    name: "headsup",
    path: "/headsup",
    component: Headsup,
    roles: [roles.searcher],
    children: [
      { path: "home" },
      { path: "shortlist" },
      { path: "opportunity" },
      { path: "savedsearch" }
    ]
  },
  philip: {
    id: 30,
    name: "searcherPhilip",
    path: "/searcher-philip",
    component: Philip,
    roles: [roles.searcher],
    children: [
      { path: "automate" },
      { path: "statistics" },
      { path: "monitoring" },
      { path: "start-new-search" },
      { path: "approve-time-and-expenses" },
      { path: "phillip-opportunites" }
    ]
  },
  iproPhilip: {
    id: 30,
    name: "iproPhilip",
    path: "/ipro-philip",
    component: IProPhilip,
    roles: [roles.ipro]
  },
  sense: {
    id: 3,
    name: "Sense",
    path: "/sense",
    component: Sense,
    roles: [roles.searcher],
    children: [{ path: "home" }]
  },
  nextstep: {
    id: 3,
    name: "NextStep",
    path: "/next-step",
    component: NextStep,
    roles: [roles.ipro],
    children: [
      { path: "home" },
      { path: "resume-parser" },
      { path: "resume-suggestor" }
    ]
  },

  alpha: {
    companies: {
      id: 16,
      name: "companies",
      path: "/companies",
      component: AlphaCompany,
      roles: [roles.searcher]
    }
  }
};

export const publicRoutes = {
  //  default: {
  //    id: 1,
  //    name: "default",
  //    path: "/",
  //    component: LeadPage,
  //  },
  default: {
    id: 1,
    name: "default",
    path: "/",
    component: LandingPage
  },
  ipro: {
    id: 1,
    name: "ipro",
    path: "/ipro",
    component: LandingPage
  },
  login: {
    id: 2,
    name: "login",
    path: "/login",
    component: Login
  },
  signup: {
    id: 2,
    name: "signup",
    path: "/signup",
    component: Signup
  },
  forgetPassword: {
    id: 2,
    name: "signup",
    path: "/forgetpassword",
    component: ForgetPassword
  },
  bottomFeature: {
    id: 33,
    name: "cms",
    path: "/cms",
    component: Cms
  },
  // mobileLogin: {
  //   id: 2,
  //   name: "mobile-login",
  //   path: "/mobile-login",
  //   component: MobileLogin
  // },
  redirectedfrom: {
    id: 3,
    name: "redirectedfrom",
    path: "/redirected-from",
    component: LandingPage
  },
  redirectedLogin: {
    id: 3,
    name: "redirectedLogin",
    path: "/redirected-login",
    component: LandingPage
  },
  emailverification: {
    id: 3,
    name: "emailverification",
    path: "/email-verification",
    component: LandingPage
  },

  noMatch: {
    id: 5,
    name: "noMatch",
    path: "",
    component: NoMatch
  }
};

export const appsRoutes = {
  default: {
    id: 1,
    name: "default",
    path: "/Apps/",
    component: LandingPage
  },
  tictell: {
    id: 2,
    name: "tictell",
    path: "/Apps/Tictell",
    component: Tictell
  },
  snapshot: {
    id: 3,
    name: "tictell",
    path: "/Apps/Snapshot",
    component: Snapshot
  },
  assort: {
    id: 3,
    name: "assort",
    path: "/Apps/assort",
    component: Assort
  },
  headsup: {
    id: 3,
    name: "headsup",
    path: "/Apps/headsup",
    component: Headsup
  },
  embark: {
    id: 30,
    name: "embark",
    path: "/Apps/embark",
    component: Embark
  },
  philip: {
    id: 30,
    name: "searcherPhilip",
    path: "/Apps/searcher-philip",
    component: Philip
  },
  iproPhilip: {
    id: 30,
    name: "iproPhilip",
    path: "/Apps/ipro-philip",
    component: IProPhilip
  },
  sense: {
    id: 3,
    name: "sense",
    path: "/Apps/Sense",
    component: Sense
  },
  nextstep: {
    id: 3,
    name: "next-step",
    path: "/Apps/next-step",
    component: NextStep
  }
};

export const privateRoutesArray = () => {
  return Object.keys(privateRoutes).map(key => {
    if (window.location.hostname === "localhost") {
      privateRoutes[key].path = `/react${privateRoutes[key].path}`;
    }
    return privateRoutes[key];
  });
};

export const publicRoutesArray = () => {
  return Object.keys(publicRoutes).map(key => publicRoutes[key]);
};
