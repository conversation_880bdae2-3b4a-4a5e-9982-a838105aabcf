import { Button, Input } from "antd";
import Slider from "./components/Slider";
import { useState } from "react";
import { isValidEmail } from "../../../utilities/helpers";
import {
  getForgotPasswordApi
} from "../landingPageApi";
import { notificationAction } from "../../../actions-alpha/notification";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { publicRoutes } from "../../../Routes/routing";
import ArrowLeft from "../../../assets-alpha/images/svg/arrow-left.svg?react";
import SMS from "../../../assets-alpha/images/svg/sms-signup.svg?react";

const ForgetPassword = () => {
  const labels = useSelector(state => state.systemLabel.labels);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [touched, setTouched] = useState(false);
  const [showConfirmScreen, setShowConfirmScreen] = useState(false);
  const [loading, setLoading] = useState(false);
  const isEmailValid = isValidEmail(email);
  const showError = touched && email !== "" && !isEmailValid;
  const handleSubmit = async () => {
    setLoading(true);
    const res = await getForgotPasswordApi(email);
    setLoading(false);
    const info = {
      message: res.message,
      status: res?.success === true ? "success" : "error"
    };
    dispatch(notificationAction(info));
    if (res?.success === true) {
      setShowConfirmScreen(true);
    }
  };

  return (
    <div className="flex md:flex-row flex-col p-3  w-full min-h-[100vh] gap-3 bg-[url('/assets/images/background.webp')] bg-cover bg-no-repeat bg-center">
      {/* Left Side - Slider */}
      <div className="flex md:flex-col h-[56px] md:h-auto flex-row justify-between md:justify-start items-center md:items-start w-full md:w-[56%] md:bg-[#FFFFFF80] md:rounded-2xl md:border-[1.5px] md:border-[#FFFFFF] relative">
        <span
          className="md:ml-8 md:mt-8 cursor-pointer"
          onClick={() => {
            navigate("/");
          }}
        >
          <img src="/assets/images/logo-prodoo.png" alt="prodoo-logo" />
        </span>
        <div className="hidden md:block absolute w-full max-w-[509px] top-[50%] left-[50%] transform -translate-x-1/2 -translate-y-1/2">
          <Slider />
        </div>
        <div className="block md:hidden">
          <p
            onClick={() => {
              navigate("/");
            }}
            className="text-[#8E81F5] font-medium text-sm"
          >
            {labels?.Goback_Label}
          </p>
        </div>
      </div>

      {showConfirmScreen ? (
        <div className="relative flex flex-col px-3  justify-center items-start lg:pt-0 pt-[26px] pb-3 lg:items-center w-full lg:w-[44%] bg-[#FFFFFF80] rounded-2xl border-[1.5px] border-[#FFFFFF]">
          <SMS />
          <h1 className="!m-0 !font-semibold !text-[20px] lg:!text-2xl text-[#0C1421]">
            {labels?.Confirm_Email_Message}
          </h1>

          <label className="!font-inter !m-0 text-[#878787] text-[13px] lg:!text-[16px] !font-normal !mt-3">
            {labels?.Correct_Email_Verify_Message}
          </label>
          <Button
            type="primary"
            className="lg:!h-12 !h-11 mt-6"
            onClick={() => {
              const domain = email?.split("@")[1];
              let redirectUrl = "";
              if (domain === "gmail.com")
                redirectUrl = "https://mail.google.com";
              else if (
                domain === "outlook.com" ||
                domain === "hotmail.com" ||
                domain === "live.com"
              )
                redirectUrl = "https://outlook.live.com";
              else if (domain === "yahoo.com")
                redirectUrl = "https://mail.yahoo.com";
              else redirectUrl = `https://${domain}`;
              window.open(redirectUrl, "_blank");
            }}
          >
            {labels?.Confirm_Email_Message}
          </Button>
          <span
            className="cursor-pointer text-[#8E81F5] !text-[16px] font-semibold !mt-5"
            onClick={handleSubmit}
          >
            {labels?.Resend_Email_Message}
          </span>
        </div>
      ) : (
        <div className="relative flex flex-col px-3  justify-center items-start lg:pt-0 pt-[26px] pb-3 lg:items-center w-full lg:w-[44%] bg-[#FFFFFF80] rounded-2xl border-[1.5px] border-[#FFFFFF]">
          <div
            className="hidden md:block absolute left-[24px] top-[24px] cursor-pointer"
            onClick={() => {
              navigate("/");
            }}
          >
            <ArrowLeft />
          </div>
          <div className="flex flex-col w-full md:w-[80%] justify-center">
            <div className="text-center mt-6 lg:mt-0">
              <h1 className="!m-0 !font-semibold text-2xl lg:!text-4xl text-[#0C1421]">
                {labels?.LANDING_FORGOT_PASSWORD_LBL}
              </h1>
              <p className="!font-inter !m-0 text-[#878787] text-[13px] lg:!text-[16px] !font-normal mt-3 lg:!mt-7">
                {labels?.Forget_Password_Descripiton}
              </p>
            </div>
            <div className="flex w-full flex-col items-start gap-2  mt-6">
              <label className="text-sm lg:text-[16px] font-normal">
                {labels?.loginEmail}
              </label>
              <Input
                name="email"
                type="email"
                value={email}
                onChange={e => setEmail(e.target.value)}
                onBlur={() => setTouched(true)}
                placeholder={labels?.LANDING_EMAIL_LBL_PLACEHOLDER}
                className="!bg-white !rounded-[12px] !h-11 lg:!h-12"
              />
              {showError && (
                <div className="text-red-500 text-sm text-left mt-1">
                  {labels?.Email_Error_Message}
                </div>
              )}
            </div>
            <Button
              type="primary"
              className="rounded-xl !h-11 lg:!h-12  w-full mt-6"
              onClick={handleSubmit}
              loading={loading}
              disabled={!email}
            >
              {labels?.LANDING_SUBMIT_BTN_LBL}
            </Button>
            <div className="flex justify-center w-full mt-6">
              <p className="!m-0 text-sm lg:!text-[16px] font-normal text-[#343333]">
                {labels?.forgotPasswordAlreadylabel}{" "}
                <span
                  className="text-[#8E81F5] cursor-pointer"
                  onClick={() => {
                    navigate(publicRoutes.signup.path);
                  }}
                >
                  {labels?.SIGNUP_BUTTON}
                </span>
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ForgetPassword;
