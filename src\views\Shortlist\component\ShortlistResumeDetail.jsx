import { useState } from "react";
import "./shortlistresumedetail.scss";
import { connect } from "react-redux";
import { htmlParser } from "../../../utilities/helpers";
import moment from "moment";
import {
  AvailablityGreenCriteria,
  AvailablityYellowCriteria
} from "../../../utilities/constants";

const AccordionDetail = ({ children }) => children;

const AccordionHeading = ({ heading, onToggle, active }) => (
  <div className="ResumeHeadingCnt">
    <div className="ResumeHeading">{heading}</div>
    <div
      className={`DownArrow ${active ? "" : "UpArrow"}`}
      onClick={onToggle}
    />
  </div>
);

const Accordion = ({ heading, children }) => {
  const [active, setActive] = useState(true);

  const handleToggle = () => {
    setActive(!active);
  };

  return (
    <div className="AvailableList">
      <AccordionHeading
        heading={heading}
        active={active}
        onToggle={handleToggle}
      />
      {active && <AccordionDetail>{children}</AccordionDetail>}
    </div>
  );
};

const ChildAccordionHeading = ({ heading, onToggle, active }) => (
  <div className="ResumeHeadingCnt">
    {heading}
    <div
      className={`DownArrow ${active ? "" : "UpArrow"}`}
      onClick={onToggle}
    />
  </div>
);

const ChildAccordion = ({ heading, children, testId }) => {
  const [active, setActive] = useState(true);

  const handleToggle = () => {
    setActive(!active);
  };

  return (
    <li className="AvailableList ResumeItem" data-testid={testId}>
      <ChildAccordionHeading
        heading={heading}
        active={active}
        onToggle={handleToggle}
      />
      {active && <AccordionDetail>{children}</AccordionDetail>}
    </li>
  );
};

const ShortlistResumeDetail = props => {
  const availability = record => {
    const availableDate = record.AvailabilityDate;
    if (availableDate) {
      const ResumeBusyUntilDate = new Date(availableDate);
      const SearchAvailDate =
        (props.searchedDate && new Date(props.searchedDate.date)) || new Date();
      const green = addDays(SearchAvailDate, AvailablityGreenCriteria);
      const yallow = addDays(SearchAvailDate, AvailablityYellowCriteria);

      if (ResumeBusyUntilDate <= green) {
        return "markGreen";
      } else if (ResumeBusyUntilDate > green && ResumeBusyUntilDate < yallow) {
        return "markYellow";
      } else if (ResumeBusyUntilDate >= yallow) {
        return "markRed";
      }
    } else {
      return "markYellow";
    }
  };

  const addDays = (date, days) => {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  };

  const getAvailable = value => {
    const availableDate = value.AvailabilityDate;
    const ResumeBusyUntilDate = new Date(availableDate);
    const SearchAvailDate = new Date();

    const green = addDays(SearchAvailDate, 14);
    const yallow = addDays(SearchAvailDate, 60);
    const { labels } = props;
    if (ResumeBusyUntilDate <= green) {
      return labels.ResumeAvailabilityGreenText;
    } else if (ResumeBusyUntilDate > green && ResumeBusyUntilDate < yallow) {
      return labels.ResumeAvailabilityYallowText;
    } else if (ResumeBusyUntilDate >= yallow) {
      return labels.ResumeAvailabilityRedText;
    } else {
      return labels.ResumeHeadingTypeMonthNotice;
    }
  };

  const GetExperianceLevel = value => {
    switch (value) {
      case 1:
        return "Beginner";
      case 2:
        return "Proficient";
      case 3:
        return "Expert";
    }
  };

  const allItemsWithCommaSeprated = data => {
    var item = "";
    if (data.length > 0) {
      for (var i = 0; i < data.length; i++) {
        if (i != data.length - 1) item += data[i].KeywordValue + ", ";
        else item += data[i].KeywordValue + ".";
      }

      item = item.substr(0, item.length - 1);
    }
    return item;
  };

  const GetLanguageLevel = value => value;
  const GetExperienceYears = value => value;

  const {
    values = {},
    resume,
    labels,
    showCorrelation,
    showAvailableTitle = true
  } = props;
  const {
    Region,
    CorrelationScore,
    Profiles,
    Skills,
    Certifications,
    Educations,
    Industries,
    Languages,
    ResumeExperience,
    ResumeOtherAchivenments,
    Keywords
  } = resume;

  return (
    <div className="shortlist-resume-detail-component">
      <div className="ResumeListWrap">
        {showAvailableTitle && (
          <div className="AvailableTitle">
            {showCorrelation && (
              <span className="TotalSkill">{CorrelationScore}</span>
            )}
            <span className={`AvailableHeading ${availability(resume)}`}>
              {getAvailable(resume)}
            </span>
          </div>
        )}
        <Accordion heading={labels.oppResumeDetailProfile}>
          <ul className="ResumeList roles-list">
            {Profiles &&
              Profiles.map((item, index) => (
                <li key={index} className="ResumeItem">
                  {showCorrelation ? (
                    <div className="SkillNumber">{item.Score}</div>
                  ) : (
                    ""
                  )}
                  <div className="SkillName">{item.ProfileValue}</div>
                  <div className="YearsCnt">
                    <span className="SkillYears">
                      {GetExperianceLevel(item.ExperienceLevel)}
                    </span>
                  </div>
                </li>
              ))}
          </ul>
        </Accordion>

        <Accordion heading={labels.oppResumeDetailSkill}>
          <ul className="ResumeList skills-list">
            {Skills &&
              Skills.map((item, index) => (
                <li key={index} className="ResumeItem">
                  {showCorrelation && (
                    <div className="SkillNumber">{item.Score}</div>
                  )}
                  <div className="SkillName">{item.SkillValue}</div>
                  <div className="YearsCnt">
                    <span className="SkillYears">
                      {GetExperianceLevel(item.ExperienceLevel)}
                    </span>
                  </div>
                </li>
              ))}
          </ul>
        </Accordion>

        <Accordion heading={labels.oppResumeDetailCertification}>
          <ul className="ResumeList certifications-list">
            {Certifications &&
              Certifications.map((item, index) => (
                <li key={index} className="ResumeItem">
                  {showCorrelation && (
                    <div className="SkillNumber">{item.Score}</div>
                  )}
                  <div className="SkillName">{item.CertificationValue}</div>
                  <div className="YearsCnt">
                    <span className="SkillYears">
                      {moment(item.CertificationDate).year()}
                    </span>
                  </div>
                </li>
              ))}
          </ul>
        </Accordion>

        <Accordion heading={labels.oppResumeDetailEducation}>
          <ul className="ResumeList educations-list">
            {Educations &&
              Educations.map((item, index) => (
                <li key={index} className="ResumeItem">
                  {showCorrelation && (
                    <div className="SkillNumber">{item.Score}</div>
                  )}
                  <div className="SkillName">{item.EducationValue}</div>
                  <div className="YearsCnt">
                    <span className="SkillYears">{item.EducationYear}</span>
                  </div>
                </li>
              ))}
          </ul>
        </Accordion>
        <Accordion heading={labels.oppResumeDetailIndustry}>
          <ul className="ResumeList industries-list">
            {Industries &&
              Industries.map((item, index) => (
                <li key={index} className="ResumeItem">
                  {showCorrelation && (
                    <div className="SkillNumber">{item.Score}</div>
                  )}
                  <div className="SkillName">{item.IndustryValue}</div>
                  <div className="YearsCnt">
                    <span className="SkillYears">
                      {GetExperianceLevel(item.ExperienceLevel)}
                    </span>
                  </div>
                </li>
              ))}
          </ul>
        </Accordion>

        <Accordion heading={labels.oppResumeDetailKeywords}>
          <ul className="ResumeList keyword-list">
            {Keywords &&
              Keywords.map((item, index) => (
                <li className="ResumeItem" key={index}>
                  {showCorrelation && (
                    <div className="SkillNumber">{item.Score}</div>
                  )}
                  <div className="SkillName">{item.KeywordValue}</div>
                </li>
              ))}
          </ul>
        </Accordion>
        <Accordion heading={labels.oppResumeDetailLocation}>
          <ul className="ResumeList locations-list">
            {Region && (
              <li className="ResumeItem">
                <div className="LocName">{Region}</div>
              </li>
            )}
          </ul>
        </Accordion>
        <Accordion heading={labels.oppResumeDetailLanguage}>
          <ul className="ResumeList languages-list">
            {Languages &&
              Languages.map((item, index) => (
                <li key={index} className="ResumeItem">
                  <div className="SkillName">{item.LanguageValue}</div>
                  <div className="YearsCnt">
                    <span className="SkillYears">
                      {GetExperianceLevel(item.ExperienceLevel)}
                    </span>
                  </div>
                </li>
              ))}
          </ul>
        </Accordion>

        <Accordion heading={labels.oppResumeDetailExperience}>
          <ul className="ResumeList experiences-list">
            {ResumeExperience.map((item, index) => (
              <ChildAccordion
                key={index}
                heading={
                  <>
                    <div className="CompanyName">{item.CompanyWorked}</div>
                    <div className="CompanyDate">
                      {item.StartDateYear}-
                      {item.EndDateYear === 0
                        ? htmlParser(labels.SHORTLIST_RESUME_CURRENT_WORKING)
                        : item.EndDateYear}
                    </div>
                  </>
                }
              >
                <div className="ResumeList otherAcheivements-list">
                  {item.IndustryValue && (
                    <div className="resumeProfile">
                      {labels.SHORTLIST_RESUME_INDUSTRY}
                      <div className="ExpName">{item.IndustryValue}</div>
                    </div>
                  )}
                  {item.ProfileValue && (
                    <div className="resumeProfile">
                      {labels.ROLE_LABEL}
                      <div className="ExpName">{item.ProfileValue}</div>
                    </div>
                  )}
                  {item.Description && (
                    <div className="resumeProfile">
                      {htmlParser(item.Description)}
                    </div>
                  )}
                </div>
              </ChildAccordion>
            ))}
          </ul>
        </Accordion>

        <Accordion heading={labels.oppResumeDetailWorkshops}>
          <ul className="ResumeList">
            {ResumeOtherAchivenments.map((item, index) => (
              <ChildAccordion
                key={index}
                heading={<div className="CompanyName">{item.Title}</div>}
              >
                <div className="ResumeList">
                  {item.AchivenmentName && (
                    <div className="resumeProfile">
                      {item.AchivenmentName}
                      <div className="ExpName">{item.Year}</div>
                    </div>
                  )}

                  {item.Description && (
                    <div className="resumeProfile">
                      {htmlParser(item.Description)}
                    </div>
                  )}
                </div>
              </ChildAccordion>
            ))}
          </ul>
        </Accordion>
      </div>
    </div>
  );
};

const mapStateToProps = ({ systemLabel }) => {
  const { labels } = systemLabel;
  return { labels };
};

export default connect(mapStateToProps)(ShortlistResumeDetail);
