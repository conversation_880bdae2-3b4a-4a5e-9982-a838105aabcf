import OurMission from "../../../../assets-alpha/images/view/our-mission.svg?react";
import OurStory from "../../../../assets-alpha/images/view/our-story.svg?react";
import User from "../../../../assets-alpha/images/svg/user-profile.svg?react";
import Tick from "../../../../assets-alpha/images/svg/tick-circle.svg?react";
import Radar from "../../../../assets-alpha/images/svg/radar.svg?react";
import { useSelector } from "react-redux";
const About = () => {
  const labels = useSelector(state => state.systemLabel.labels);
  return (
    <section className=" bg-[#F4F2FE] relative overflow-hidden lg:pb-[60px]">
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/6 w-64 h-64 bg-gradient-radial from-company/15 via-company/5 to-transparent rounded-full blur-3xl animate-pulse"></div>
        <div
          className="absolute bottom-1/4 right-1/6 w-48 h-48 bg-gradient-radial from-primary/15 via-primary/5 to-transparent rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: "2s" }}
        ></div>
        <div className="absolute inset-0 opacity-[0.03]">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `
              linear-gradient(to right, var(--company) 1px, transparent 1px),
              linear-gradient(to bottom, var(--company) 1px, transparent 1px),
              linear-gradient(to right, var(--primary) 1px, transparent 1px),
              linear-gradient(to bottom, var(--primary) 1px, transparent 1px)
            `,
              backgroundSize: "40px 40px, 40px 40px, 80px 80px, 80px 80px",
              backgroundPosition: "0 0, 0 0, 20px 20px, 20px 20px"
            }}
          ></div>
        </div>
      </div>
      <div className="!mt-8 lg:!mt-[64px] flex w-full justify-center ">
        <h1 className="!m-0 !text-2xl lg:!text-[40px] text-[#343333] font-medium">
          {labels?.About_Lbl}{" "}
          <span className="text-[#8E81F5]">{labels?.US_Lbl}</span>
        </h1>
      </div>
      <div className="flex flex-col gap-8 lg:gap-[69px] w-full items-center lg:mt-[72px] mt-8">
        <div className="flex w-full justify-between lg:px-0 px-4 lg:max-w-[82%]">
          <div className="w-full lg:w-[50%]">
            <div className="w-full flex flex-col">
              <div className="flex items-center gap-2">
                <div className="w-10 h-10 rounded-[8px] lg:w-[58px] lg:h-[58px] flex justify-center items-center bg-[#8E81F5] lf:rounded-lg">
                  <User className="w-5 h-5 lg:w-8 lg:h-8" />
                </div>
                <h1 className="!m-0 text-[#343333] !text-[20px] lg:!text-[40px] font-medium">
                  {labels?.Story_Label}
                </h1>
              </div>
            </div>
            <div className="mt-4 lg:mt-6">
              <p className="!m-0 text-xs lg:text-[18px] text-[#878787] font-normal leading-4 lg:leading-[27px]">
                {labels?.About_Desc_1} <br />
                <br />
                {labels?.About_Desc_2} <br />
                <br />
                {labels?.About_Desc_3}
              </p>
            </div>
          </div>
          <div className="w-[50%] lg:flex hidden justify-end">
            <OurStory />
          </div>
        </div>
        <div className="flex w-full justify-between lg:max-w-[82%] lg:px-0 px-4">
          <div className="w-[50%] lg:flex hidden ">
            <OurMission />
          </div>
          <div className="w-full lg:w-[50%]">
            <div className="w-full flex flex-col">
              <div className="flex items-center gap-2">
                <div className="w-10 h-10 rounded-[8px] lg:w-[58px] lg:h-[58px] flex justify-center items-center bg-[#02CAA8] lf:rounded-lg">
                  <Radar className="w-5 h-5 lg:w-8 lg:h-8" />
                </div>
                <h1 className="!m-0 text-[#343333] !text-[20px] lg:!text-[40px] font-medium">
                  {labels?.Our_Mission_lbl}
                </h1>
              </div>
            </div>
            <div className="mt-4 lg:mt-6 mb-8 lg:mb-0">
              <p className="!m-0 text-xs lg:text-[18px] text-[#878787] font-normal leading-4 lg:leading-[27px]">
                {labels?.Our_Mission_Desc}
              </p>
              <div className="flex flex-col gap-4 mt-4 lg:mt-6">
                <div className="flex gap-1">
                  <span>
                    <Tick className="w-5 h-5 lg:w-6 lg:h-6" />
                  </span>
                  <span className="text-[#878787] text-[13px] lg:text-[18px] font-normal">
                    {labels?.Our_Mission_L1}
                  </span>
                </div>
                <div className="flex gap-1">
                  <span>
                    <Tick className="w-5 h-5 lg:w-6 lg:h-6" />
                  </span>
                  <span className="text-[#878787] text-[13px] lg:text-[18px] font-normal">
                    {labels?.Our_Mission_L2}
                  </span>
                </div>
                <div className="flex gap-1">
                  <span>
                    <Tick className="w-5 h-5 lg:w-6 lg:h-6" />
                  </span>
                  <span className="text-[#878787] text-[13px] lg:text-[18px] font-normal">
                    {labels?.Our_Mission_L3}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
