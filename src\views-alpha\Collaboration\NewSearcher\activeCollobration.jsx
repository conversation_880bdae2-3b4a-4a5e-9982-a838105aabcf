import { useSelector, useDispatch } from "react-redux";
import NewCollaboration from "../common/newCollaboration";
import { useLocation } from "react-router-dom";

const NewSearcherCollaborationAccepted = props => {
  const {
    userId,
    labels,
    IsSearcher,
    notificationAction,
    isHelpActive
  } = useSelector(state => ({
    labels: state.systemLabel.labels,
    userId: state.userInfo.user.UserId,
    IsSearcher: !state.userInfo.user.IsFreelancer,
    isHelpActive: state.navigation.isHelpActive
  }));
  const dispatch = useDispatch();
  const location = useLocation();
  const createContract = location.state;
  return (
    <NewCollaboration
      createContract={createContract}
      IsSearcher={IsSearcher}
      isHelpActive={isHelpActive}
      notificationAction={notificationAction}
      isAcceptedView
      url={`GetAllCollaborationByUserId`}
      searchInputPlaceholder={
        labels.SearcherAcceptedCollaborationSearchPlaceholder
      }
      toolTipExpandList={labels.TooltipSearcherAcceptedCollaborationListExpand}
      helpTooltipExpandList={
        labels.HlpTooltipSearcherAcceptedCollaborationListExpand
      }
      toolTipCollapseList={
        labels.TooltipSearcherAcceptedCollaborationListCollapse
      }
      helpTooltipCollapseList={
        labels.HlpTooltipSearcherAcceptedCollaborationListCollapse
      }
      listColumnText={labels.SearcherAcceptedCollaborationListColumnText}
      // detail section system label
      detailHeadingText={labels.SearcherAcceptedCollaborationDetailHeadingText}
      toolTipExpandDetail={
        labels.TooltipSearcherAcceptedCollaborationDetailExpand
      }
      helpTooltipExpandDetail={
        labels.HlpTooltipSearcherAcceptedCollaborationDetailExpand
      }
      toolTipCollapseDetail={
        labels.TooltipSearcherAcceptedCollaborationDetailCollapse
      }
      helpTooltipCollapseDetail={
        labels.HlpTooltipSearcherAcceptedCollaborationDetailCollapse
      }
      detailsColumnText={labels.SearcherAcceptedCollaborationDetailColumnText}
      // selected user section system label
      userDetailHeadingText={
        labels.SearcherAcceptedCollaborationUserDetailHeaderText
      }
      toolTipExpandUserDetail={
        labels.TooltipSearcherAcceptedCollaborationUserDetailExpand
      }
      helpTooltipExpandUserDetail={
        labels.HlpTooltipSearcherAcceptedCollaborationUserDetailExpand
      }
      toolTipCollapseUserDetail={
        labels.TooltipSearcherAcceptedCollaborationUserDetailCollapse
      }
      helpTooltipCollapseUserDetail={
        labels.HlpTooltipSearcherAcceptedCollaborationUserDetailCollapse
      }
      userDetailsColumnText={
        labels.SearcherAcceptedCollaborationUserDetailColumnText
      }
      emptyCollaorationInfo={labels?.emptyCollaborationInfo}
      startDateLabel={labels.SearcherSentCollaborationStartDateLabel}
      durationLabel={labels.SearcherSentCollaborationDurationLabel}
      compensationLabel={labels.SearcherSentCollaborationCompensationLabel}
      companyLabel={labels.SearcherSentCollaborationCompanyLabel}
      descriptionLabel={labels.SearcherSentCollaborationDescriptionLabel}
      acceptedCollaboration={labels.SearcherSentCollaborationAcceptedLabel}
      selectInterest={labels.SearcherSentCollaborationInterestOrNotLabel}
      UserNameLabel={labels.SearcherAcceptedCollaborationUserNameLabel}
      UserPositionLabel={labels.SearcherAcceptedCollaborationPositionLabel}
      UserEmailLabel={labels.SearcherAcceptedCollaborationEmailLabel}
      UserPhoneLabel={labels.SearcherAcceptedCollaborationPhoneLabel}
      UserWebUrlLabel={labels.SearcherAcceptedCollaborationWebUrlLabel}
      collaborationDeletedMsg={labels.InfoSearcherCollaborationDeleted}
      collaborationClosedMsg={labels.InfoSearcherCollaborationClosed}
      likeToRateCollaborationMessage={labels.likeToRateCollaborationMessage}
      listCollapsedTestId={"listCollapsedTestId1"}
      listCollapsedHelpTestId={"listCollapsedTestId2"}
      listExpandTestId={"listCollapsedTestId3"}
      listExpandHelpTestId={"listCollapsedTestId4"}
      detailCollapsedTestId={"listCollapsedTestId5"}
      detailCollapsedHelpTestId={"listCollapsedTestId6"}
      detailExpandTestId={"listCollapsedTestId7"}
      detailExpandHelpTestId={"listCollapsedTestId8"}
      userDetailCollapsedTestId={"listCollapsedTestId9"}
      userDetailCollapsedHelpTestId={"listCollapsedTestId10"}
      userDetailExpandTestId={"listCollapsedTestId11"}
      userDetailExpandHelpTestId={"listCollapsedTestId12"}
      searchInputTestId="search-input"
      collaborationDeleteConfirmation={
        labels.InfoSearcherSentCollaborationDeleteConfirmationMsg
      }
      collaborationCloseConfirmation={
        labels.InfoSearcherSentCollaborationCloseConfirmationMsg
      }
      popupYesText={"Yes"}
      popupNoText={"No"}
      locationProp={location}
    />
  );
};

export default NewSearcherCollaborationAccepted;
