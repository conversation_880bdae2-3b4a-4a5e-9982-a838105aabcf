import "./emptyinfo.scss";
import { useNavigate } from "react-router-dom";
import { privateRoutes } from "../../Routes/routing";
import { connect } from "react-redux";
import { htmlParser } from "../../utilities/helpers";

const EmptyInfo = ({ children, testId, child, showBtn = false, labels }) => {
  const navigate = useNavigate();

  return (
    <div className="emptySkeletonResult" data-testid={testId}>
      {htmlParser(children)}
      {child}
      {showBtn && (
        <div className="button-main">
          <button
            onClick={() => {
              navigate(privateRoutes.resumeBuilder.path);
            }}
          >
            {labels?.update_resume}
          </button>
          <button
            onClick={() => {
              navigate(privateRoutes.resumeBuilder.path);
            }}
          >
            {labels?.upload_resume}
          </button>
        </div>
      )}
    </div>
  );
};

const mapStateToProps = ({ systemLabel }) => {
  const { labels } = systemLabel;

  return { labels };
};

export default connect(mapStateToProps, {})(EmptyInfo);
