import { useState, useEffect } from "react";
import { connect } from "react-redux";
import { notificationAction } from "../../../../actions/notification";
import { map } from "lodash";
import "./start-new-search-phillip.scss";
import { useNavigate } from "react-router-dom";
import { getSearcherPhillipInfoForNewUserApi } from "./startNewSearchApi";
// import "react-circular-progressbar/dist/styles.css";
import { publicRoutes } from "../../../../Routes/routing";
import { landingCandidateSearchAction } from "../../../../actions/landingActions";
import { Col, Row, Steps } from "antd";
import "../../components/phillip.scss";

const LOOKUPTYPES = {
  PROFILE: 1,
  SKILL: 2,
  KEYWORD: 3,
  CERTIFICATION: 4,
  COUNTRY: 5,
  LANGUAGE: 6,
  INDUSTRY: 7,
};

const StartNewSearch = (props) => {
  const [isSearched, setIsSearched] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isCollapsed1, setIsCollapsed1] = useState(false);
  const [isCollapsed2, setIsCollapsed2] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [keywords, setKeywords] = useState("");
  const [validation, setValidation] = useState({ textareaError: false });
  const [extractedSearchSettings, setExtractedSearchSettings] = useState({});
  const navigate = useNavigate();

  useEffect(() => {
    // componentDidMount logic here
  }, []);

  const handleSearchClick = () => {
    if (!keywords) {
      const info = {
        message: props?.labels?.searcher_phillips_start_new_search_input_error,
        status: "error",
      };
      props.notificationAction(info);
      return;
    }
    setIsLoading(true);
    getSearcherPhillipInfoForNewUserApi({ keywords })
      .then((response) => {
        if (response.success) {
          let {
            Certifications = [],
            Countries = [],
            Industries = [],
            Keywords = [],
            Languages = [],
            Profiles = [],
            Skills = [],
          } = response.items;

          Certifications = map(Certifications, (certification) => ({
            CertificationId: certification.CertificationId,
            ExpLevel: 0,
            LookupValue: certification.CertificationValue,
            CertificationValue: certification.CertificationValue,
          }));
          Countries = map(Countries, (country) => ({
            CountryId: country.CountryId,
            ExpLevel: 0,
            LookupValue: country.CountryName,
            CountryName: country.CountryName,
          }));
          Industries = map(Industries, (industry) => ({
            IndustryId: industry.IndustryId,
            ExpLevel: 0,
            LookupValue: industry.IndustryValue,
            IndustryValue: industry.IndustryValue,
          }));
          Keywords = map(Keywords, (keyword) => ({
            KeywordId: keyword.KeywordId,
            ExpLevel: 0,
            LookupValue: keyword.KeywordValue,
            KeywordValue: keyword.KeywordValue,
          }));
          Languages = map(Languages, (language) => ({
            LanguageId: language.LanguageId,
            ExpLevel: 0,
            LookupValue: language.LanguageValue,
            LanguageValue: language.LanguageValue,
          }));
          Profiles = map(Profiles, (profile) => ({
            ProfileId: profile.ProfileId,
            ExpLevel: 0,
            LookupValue: profile.ProfileValue,
            ProfileValue: profile.ProfileValue,
          }));
          Skills = map(Skills, (skill) => ({
            SkillId: skill.SkillId,
            ExpLevel: 0,
            LookupValue: skill.SkillValue,
            SkillValue: skill.SkillValue,
          }));
          const extractedSearchSettings = {
            Profiles,
            Skills,
            Certifications,
            Keywords,
            Languages,
            Countries,
            Industries,
          };
          setExtractedSearchSettings(extractedSearchSettings);
          setIsSearched(true);
          setCurrentStep(1);
          props.landingCandidateSearchAction({
            searchCriteria: extractedSearchSettings,
          });
          setIsLoading(false);
        }
      })
      .catch(() => {
        setIsLoading(false);
      });
  };

  const handleFindProfessionalsClick = () => {
    navigate(publicRoutes.visitorsearch.path);
  };

  const { labels } = props;
  const { searchCriteria } = props;
  const { Profiles, Skills, Educations } = searchCriteria;

  return (
    <div className="reports-view-new-search" data-testid="ipro-phillip-popup">
      {isLoading && (
        <div id="loader-wrapper">
          <div className="loader-container">
            <div className="loader">
              <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
                <defs>
                  <filter id="goo">
                    <feGaussianBlur
                      in="SourceGraphic"
                      stdDeviation="6"
                      result="blur"
                    />
                    <feColorMatrix
                      in="blur"
                      mode="matrix"
                      values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                      result="goo"
                    />
                    <feBlend in="SourceGraphic" in2="goo" />
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </div>
      )}
      <div className="phillips-search-steps-">
        <div
          className="phillip-component searcher-phillip marketing-page page-2"
          style={{
            padding: "20px 12px",
            background: "#fff!important",
            height: "-webkit-fill-available",
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              height: "100%",
            }}
          >
            <Steps
              current={currentStep}
              items={[
                {
                  title: labels?.searcher_phillip_automate_step1_title,
                  description:
                    labels?.searcher_phillip_automate_step1_description,
                },
                {
                  title: labels?.searcher_phillip_automate_step2_title,
                  description:
                    labels?.searcher_phillip_automate_step2_description,
                },
              ]}
            />
            <div
              style={{
                flex: 1,
                marginTop: "25px",
              }}
            >
              <Row
                gutter={[24, 24]}
                style={{
                  height: "100%",
                }}
              >
                <Col
                  xs={24}
                  sm={12}
                  md={12}
                  className="steps"
                  style={{
                    margin: 0,
                  }}
                >
                  <div
                    className="step"
                    style={{
                      background: "#F1EFFB",
                      borderRadius: "12px",
                      padding: "12px",
                      height: "100%",
                      border: validation.textareaError
                        ? "1px solid #f54949"
                        : null,
                    }}
                  >
                    <div
                      className="js_textarea_wrap body"
                      style={{
                        height: "100%",
                        width: "100%",
                      }}
                    >
                      <div className="text-area-wrapper">
                        <textarea
                          autoComplete="off"
                          className="js_textarea"
                          style={{
                            flex: 1,
                          }}
                          data-testid="searcher-phillip-keyword-input"
                          placeholder={labels.landingWidgetPhillipSubTitle}
                          onChange={(e) => {
                            setKeywords(e.target.value);
                            setValidation({ textareaError: false });
                          }}
                          value={keywords}
                        >
                          {keywords}
                        </textarea>
                        <button
                          onClick={handleSearchClick}
                          className="action-btn"
                        >
                          {labels.searchForMe}
                        </button>
                      </div>
                    </div>
                  </div>
                </Col>
                <Col
                  xs={24}
                  sm={12}
                  md={12}
                  className="steps"
                  style={{ margin: 0 }}
                >
                  <div
                    id="step2"
                    className={`step ${!isSearched ? "disabled" : ""}`}
                    style={{
                      background: "#F1EFFB",
                      borderRadius: "12px",
                      padding: "12px",
                      height: "100%",
                      display: "flex",
                      flexDirection: "column",
                    }}
                  >
                    <div className="left-div">
                      {isSearched && searchCriteria && (
                        <div className="first-div">
                          <div className="heading-div">
                            <span className="skillIcon"></span>
                            <label>{labels.Profiles}</label>
                          </div>
                          <div className="settings-container">
                            {Profiles && Profiles.length > 0 ? (
                              Profiles.map((item) => (
                                <label
                                  data-testid="resume-edit-suggested-role-item"
                                  className="draggable"
                                  key={item.Id}
                                >
                                  {item.LookupValue}
                                </label>
                              ))
                            ) : (
                              <div>{labels.noDataFoundMessage}</div>
                            )}
                          </div>
                          <div className="heading-div">
                            <span className="profileIcon"></span>
                            <label>Skills</label>
                          </div>
                          <div className="settings-container">
                            {Skills && Skills.length > 0 ? (
                              Skills.map((item) => (
                                <label
                                  data-testid="resume-edit-suggested-role-item"
                                  className="draggable"
                                  key={item.Id}
                                >
                                  {item.LookupValue}
                                </label>
                              ))
                            ) : (
                              <div>{labels.noDataFoundMessage}</div>
                            )}
                          </div>
                          <div className="heading-div">
                            <span className="educationIcon"></span>
                            <label>Educations</label>
                          </div>
                          <div className="settings-container">
                            {Educations && Educations.length > 0 ? (
                              Educations.map((item) => (
                                <label
                                  data-testid="resume-edit-suggested-role-item"
                                  className="draggable"
                                  // onClick={() =>
                                  //   extractedSettingUpdate(
                                  //     LOOKUPTYPES.SKILL,
                                  //     item
                                  //   )
                                  // }
                                  key={item.Id}
                                >
                                  {item.LookupValue}
                                </label>
                              ))
                            ) : (
                              <div>{labels.noDataFoundMessage}</div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    <button
                      onClick={handleFindProfessionalsClick}
                      className="action-btn"
                    >
                      {labels.findProfessionals}
                    </button>
                  </div>
                </Col>
              </Row>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const mapStateToProps = ({ systemLabel, userInfo, navigation, landing }) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  const { searchCriteria } = landing;
  const { isHelpActive } = navigation;
  let { FirstName } = user;
  FirstName =
    FirstName !== undefined ? FirstName : sessionStorage.getItem("userName");
  return { labels, FirstName, searchCriteria, isHelpActive };
};

export default connect(mapStateToProps, {
  notificationAction,
  landingCandidateSearchAction,
})(StartNewSearch);
