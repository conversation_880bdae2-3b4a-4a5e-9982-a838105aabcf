import { Button, Form, Grid } from "antd";
import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import FilterIcon from "../../assets-alpha/images/svg/filter.svg?react";
import FindProfessionalIcon from "../../assets-alpha/images/svg/search-normal.svg?react";
import RotateIcon from "../../assets-alpha/images/svg/rotate-left.svg?react";
import CloseIcon from "../../assets-alpha/images/svg/close.svg?react";
import CustomInput from "../../common-alpha/CustomInput/CustomInput";
import PageWrapper from "../../components-alpha/PageWrapper/PageWrapper";
import FilterDrawer from "./components/FilterDrawer";
import FilterSearch from "./components/FilterSearch";
import SearchResults from "./components/SearchResults";
import ShortlistsResults from "./components/ShortlistsResults";
import {
  useFilterWithAiApi,
  useResumeSearchApi,
  // useRolesLookup<PERSON>pi,
  useSaveSearch<PERSON><PERSON>,
  useSaveSearchDetailApi
} from "./findProfessionalApi";
import { useDeleteSaveSearches, useOutsideClick } from "./hooks";
import { DEFAULT_FILTERS } from "./constant";
import { useSelector } from "react-redux";

const FindProfessional = () => {
  const findProfessional = useSelector(
    state => state.systemLabel.labels?.findProfessional
  );
  // State persistence keys
  const SESSION_KEY = "findProfessionalState";
  const getInitialState = () => {
    const saved = sessionStorage.getItem(SESSION_KEY);
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        return {
          selectedTab: parsed.selectedTab || "Find",
          searchKey: parsed.searchKey || "",
          savedText: parsed.savedText,
          filters: parsed.filters || DEFAULT_FILTERS,
          openFilterDrawer: parsed.openFilterDrawer || false
        };
      } catch {
        // fallback to defaults
      }
    }
    return {
      selectedTab: "Find",
      searchKey: "",
      savedText: undefined,
      filters: DEFAULT_FILTERS,
      openFilterDrawer: false
    };
  };

  const [selectedTab, setSelectedTab] = useState(getInitialState().selectedTab);
  const [searchKey, setSearchKey] = useState(getInitialState().searchKey);
  const { loadingDelete, onDeleteSearch } = useDeleteSaveSearches();
  const screens = Grid.useBreakpoint();
  const [savedText, setSavedText] = useState(getInitialState().savedText);
  const [filters, setFilters] = useState(getInitialState().filters);
  const { ref, hasFocusInput, setHasFocusInput } = useOutsideClick(() => {
    setHasFocusInput(false);
  });
  const [openFilterDrawer, setOpenFilterDrawer] = useState(
    getInitialState().openFilterDrawer
  );

  // Persist state to sessionStorage on unmount
  useEffect(() => {
    return () => {
      sessionStorage.setItem(
        SESSION_KEY,
        JSON.stringify({
          selectedTab,
          searchKey,
          savedText,
          filters,
          openFilterDrawer
        })
      );
    };
  }, [selectedTab, searchKey, savedText, filters, openFilterDrawer]);

  // On mount, if searchKey exists in session storage, trigger handleApplyAi
  useEffect(() => {
    const saved = sessionStorage.getItem(SESSION_KEY);
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        if (parsed.searchKey && parsed.searchKey.trim() !== "") {
          handleApplyAi(parsed.searchKey);
        }
      } catch (e) {
        console.log(e);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  // const { data: rolesData, isLoading: loadingRoles } = useRolesLookupApi(
  //   searchKey
  // );
  const {
    mutate: mutateAi,
    isPending: loadingFilterResults
  } = useFilterWithAiApi();
  const {
    data: savedSearchData,
    isLoading: loadingSaveSearch
  } = useSaveSearchApi();
  const {
    mutate: saveDetailApi,
    isPending: loadingDetail
  } = useSaveSearchDetailApi();

  const {
    data: searchData,
    mutate: searchResumeApi,
    isPending: loadingResult
  } = useResumeSearchApi();

  const handlesSearch = item => {
    setHasFocusInput(false);
    saveDetailApi(
      { suffixUrl: `?savedSearchId=${item.SavedSearchId}` },
      {
        onSuccess: data => {
          onSaveSearchSelection(data);
        }
      }
    );
  };

  const handleFilterSearch = useCallback(
    filters => {
      setFilters(filters);
      setOpenFilterDrawer(false);
      setSavedText(null);
      searchResumeApi({
        CertificationIds: filters.certification.map(item => item.value),
        Countries: filters.location.map(item => item.value),
        IndusteryIds: filters.industry.map(item => item.value),
        KeywordIds: filters.keywords.map(item => item.value),
        Languages: filters.language.map(item => item.value),
        ProfileIds: filters.role.map(item => item.value),
        SkillIds: filters.skills.map(item => item.value),
        checked: [],
        Type: "Only20",
        limit: 20,
        page: 1
      });
    },
    [searchResumeApi]
  );

  const onSaveSearchSelection = useCallback(
    data => {
      const settings = data?.items?.SavedSearchSettings;
      const filters = {
        role: settings
          .filter(n => n.LookupTypeId === 1)
          .map(item => ({ label: item.SearchedValue, value: item.SearchedPK })),
        skills: settings
          .filter(n => n.LookupTypeId === 2)
          .map(item => ({ label: item.SearchedValue, value: item.SearchedPK })),
        keywords: settings
          .filter(n => n.LookupTypeId === 3)
          .map(item => ({ label: item.SearchedValue, value: item.SearchedPK })),
        industry: settings
          .filter(n => n.LookupTypeId === 4)
          .map(item => ({ label: item.SearchedValue, value: item.SearchedPK })),
        certification: settings
          .filter(n => n.LookupTypeId === 5)
          .map(item => ({ label: item.SearchedValue, value: item.SearchedPK })),
        language: settings
          .filter(n => n.LookupTypeId === 6)
          .map(item => ({ label: item.SearchedValue, value: item.SearchedPK })),
        location: settings
          .filter(n => n.LookupTypeId === 7)
          .map(item => ({ label: item.SearchedValue, value: item.SearchedPK }))
      };

      handleFilterSearch(filters);
      setSavedText(data?.items?.SearchName);
    },
    [handleFilterSearch]
  );

  const handleApplyAi = aiCommand => {
    if (selectedTab !== "Find") return;
    setHasFocusInput(false);
    setOpenFilterDrawer(false);
    setSavedText(null);
    setSearchKey(aiCommand);
    mutateAi(
      {
        keywords: aiCommand
      },
      {
        onSuccess: data => {
          const {
            Profiles,
            Skills,
            Keywords,
            Industries,
            Certifications,
            Countries,
            Languages
          } = data.items;
          const filters = {
            role: Profiles.map(item => ({
              label: item.ProfileValue,
              value: item.ProfileId
            })),
            skills: Skills.map(item => ({
              label: item.SkillValue,
              value: item.SkillId
            })),
            keywords: Keywords.map(item => ({
              label: item.KeywordValue,
              value: item.KeywordId
            })),
            industry: Industries.map(item => ({
              label: item.IndustryValue,
              value: item.IndustryId
            })),
            certification: Certifications.map(item => ({
              label: item.CertificationValue,
              value: item.CertificationId
            })),
            location: Countries.map(item => ({
              label: item.CountryName,
              value: item.CountryId
            })),
            language: Languages.map(item => ({
              label: item.LanguageValue,
              value: item.LanguageId
            }))
          };
          handleFilterSearch(filters);
        }
      }
    );
  };
  return (
    <PageWrapper className="pb-0" title={findProfessional?.mainTitle}>
      <Form>
        <div className="flex items-center gap-2">
          <div className="relative flex-1" ref={ref}>
            <CustomInput
              placeholder={
                selectedTab === "Find"
                  ? findProfessional?.searchByRole
                  : findProfessional?.searchShortlists
              }
              PrefixIcon={screens.md && FindProfessionalIcon}
              className="[&_.ant-input-affix-wrapper]:!p-[3px_3px_3px_16px] max-md:[&_.ant-input-affix-wrapper]:!p-[1px_1px_1px_16px] [&_.ant-input-prefix]:!border-0 [&_.ant-input-prefix]:!mr-0"
              suffixInput={
                <Button
                  className="!rounded-full max-md:!border-none max-md:!bg-white max-md:!rounded-l-none max-md:!h-9.5 max-md:!shadow-none"
                  onClick={e => {
                    e.stopPropagation();
                    handleApplyAi(searchKey);
                  }}
                  type="primary"
                >
                  {screens.md ? (
                    findProfessional?.search
                  ) : (
                    <FindProfessionalIcon />
                  )}
                </Button>
              }
              onKeyDown={e => {
                if (e.key === "Enter") {
                  e.target.blur();
                  setHasFocusInput(false);
                  handleApplyAi(searchKey);
                }
              }}
              onChange={e => setSearchKey(e.target.value)}
              onFocus={() => setHasFocusInput(true)}
              value={searchKey}
              allowClear={{
                clearIcon: <CloseIcon />
              }}
            />
            {hasFocusInput &&
              !!savedSearchData?.length &&
              searchKey &&
              selectedTab === "Find" && (
                <FilterSearch
                  roles={savedSearchData?.filter((n, i) => i < 5)}
                  onSelect={handlesSearch}
                  loading={loadingSaveSearch}
                  name={findProfessional?.savedSearches}
                  icon={<RotateIcon className="min-w-5" />}
                  setFilters={setFilters}
                  singleLine
                  onRemove={onDeleteSearch}
                  loadingDelete={loadingDelete || loadingSaveSearch}
                />
              )}
          </div>

          {selectedTab === "Find" && (
            <Button
              className="!rounded-full !border-1 !border-[var(--purple)] !h-[44px] max-md:!h-10 max-md:!min-w-10 max-md:!border-[#EAE5FC]"
              icon={<FilterIcon className="rotate-90 w-[14px]" />}
              onClick={() => setOpenFilterDrawer(true)}
            >
              {screens.md && findProfessional?.filters}
            </Button>
          )}
        </div>

        {/* Tabs */}
        <div className="mt-4 max-md:mt-2">
          <div className="flex items-center">
            <Button
              className="max-md:!px-[16px] max-md:!text-[13px] !rounded-l-full !rounded-r-none !w-[140px] max-md:!w-auto max-md:!rounded-[8px] max-md:mr-2"
              type={selectedTab === "Find" ? "primary" : "default"}
              onClick={() => {
                setSelectedTab("Find");
                setSearchKey("");
              }}
            >
              {findProfessional?.find}
            </Button>
            <Button
              className="max-md:!px-[16px] max-md:!text-[13px] !rounded-r-full !rounded-l-none max-md:!rounded-[8px]"
              type={selectedTab === "Shortlisted" ? "primary" : "default"}
              onClick={() => {
                setSelectedTab("Shortlisted");
                setSearchKey("");
              }}
            >
              {findProfessional?.shortlistedProfiles}
            </Button>

            {/* {selectedTab === "Find" && (
              <Button
                type="link"
                className="!text-[var(--purple)] ml-auto !pr-15 max-md:!pr-0 max-md:!pl-2 max-md:!text-[12px] !cursor-auto"
              >
                {findProfessional?.advancedSearch}
                <ArrowIcon className="ml-1 max-md:-ml-[10px] max-md:-rotate-75 max-md:w-[25px] max-md:mb-[30px]" />
              </Button>
            )} */}
          </div>

          {selectedTab === "Find" && (
            <SearchResults
              results={searchData?.items}
              filters={filters}
              savedText={savedText}
              loading={loadingResult || loadingFilterResults || loadingDetail}
            />
          )}
          {selectedTab === "Shortlisted" && (
            <ShortlistsResults searchKey={searchKey} />
          )}
        </div>

        {openFilterDrawer && (
          <FilterDrawer
            open={openFilterDrawer}
            onClose={() => setOpenFilterDrawer(false)}
            onApplyFilter={handleFilterSearch}
            appliedFilters={filters}
            onSaveSearchSelection={onSaveSearchSelection}
            onApplyAi={handleApplyAi}
            setFilters={setFilters}
          />
        )}
      </Form>
    </PageWrapper>
  );
};

export default FindProfessional;
