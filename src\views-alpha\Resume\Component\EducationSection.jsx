import { useSelector } from "react-redux";
import { findEducationDegree, Profiles } from "../constant";
import { ShowMoreSection } from "./LanguagesSection";
// Education Section
const EducationSection = ({ userData, openDrawer }) => {
  const resume = useSelector(state => state.systemLabel.labels?.resume);
  return (
    <ShowMoreSection
      title={resume?.education}
      items={userData?.items?.Educations || []}
      openDrawer={openDrawer}
      addEditAction={Profiles.education}
      itemKeyId="EducationId"
      viewMore
      itemValueKey="EducationValue"
      emptyMessage={resume?.educationEmptyMessage}
      addButtonAction={Profiles.education}
      renderItem={edu => (
        <div
          key={edu.EducationId}
          className="flex-1/3 gap-2 min-w-full justify-between items-center flex 
        bg-[#F3F1FD] border border-[#EAE5FC] rounded-lg px-4 py-2 text-[13px]"
        >
          <div className="whitespace-nowrap">
            <span className="text-[#343333]">
              {findEducationDegree(edu)?.label || ""}
            </span>
          </div>
          <Space />

          <span className="truncate">
            <span className="text-[#343333]">{edu.EducationValue}</span>
          </span>

          <Space />
          <span className="whitespace-nowrap">
            <span className="text-[#343333]">{edu.EducationYear || ""}</span>
          </span>
        </div>
      )}
    />
  );
};

export default EducationSection;

export const Space = () => <span className="border-l border-l-[#EAE5FC] h-5" />;
