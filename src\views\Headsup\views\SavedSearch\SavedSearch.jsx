import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import PageWrapper from "../../../../components/PageWrapper/PageWrapper";
import ShortlistResumes from "./component/ShortlistResumes";
import { notificationAction } from "../../../../actions/notification";
import toLower from "lodash/toLower";
import ShortlistResumeDetail from "./component/ShortlistResumeDetail";
import filter from "lodash/filter";
import find from "lodash/find";
import first from "lodash/first";
import moment from "moment";
import includes from "lodash/includes";
import {
  getSavedSearchesApi,
  getShortlistResumeApi,
  postShortlistApi,
  deleteSavedSearchApi,
  getSaveSearchDetailApi,
  searchResumesApi,
  updateSavedSearchName
} from "./savedSearchApi";
import { privateRoutes } from "../../../../Routes/routing";

import "./saved-search.scss";
import { isEmpty } from "lodash";
import SettingDetail from "../../components/SettingDetail";
import ConfirmDialog from "../../../../common/ConfirmDialog/ConfirmDialog";
import Column from "../../../../common/Column/Column";
import SearchInput from "../../../../common/Input/SearchInput";
import Button from "../../../../common/Button/Button";
import Input from "../../../../common/Input/Input";
import EmptyInfo from "../../../../common/EmptyInfo/EmptyInfo";
import List from "../../../../common/List/List";
import LoadingMaskRow from "../../../../common/LoadingMask/LoadingMaskRow";

const SavedSearch = props => {
  const [state, setState] = useState({
    searchCollapsed: false,
    resumeCollapsed: false,
    resumeDetailCollapsed: false,
    createCollapse: false,
    savedSearches: [],
    selectedResume: {},
    selectedSavedSearch: {},
    shortlistResumes: [],
    filteredSavedSearches: [],
    isLoading: false,
    fetchingSavedSearches: true,
    fetchingResumes: false,
    opportunityList: [],
    fetchingOpportunities: false,
    dialogMessage: "",
    newShortlistValue: "",
    createNewShortlist: false,
    dialogOpportunityMessage: "",
    showOpportunities: false,
    opportunitiesCollapse: false,
    searchKey: "",
    deletedId: null
  });

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { labels, isHelpActive } = useSelector(state => ({
    labels: state.systemLabel.labels,
    isHelpActive: state.navigation.isHelpActive
  }));

  useEffect(() => {
    getSavedSearches().then(() => {
      const { history } = props;
      if (history) {
        const { state } = history.location;
        if (state) {
          const { savedSearches } = state;
          handleSelectShortlist(savedSearches);
        }
        const { onBoardShortlistId } = state;
        if (onBoardShortlistId) {
          getSavedSearches();
        }
      }
    });
  }, []);

  const getSavedSearches = selectedId => {
    setState(prevState => ({ ...prevState, fetchingSavedSearches: true }));
    return getSavedSearchesApi()
      .then(data => {
        setState(prevState => ({
          ...prevState,
          savedSearches: data.items,
          filteredSavedSearches: data.items,
          fetchingSavedSearches: false
        }));
        const { onBoardShortlistId } = props;
        if (selectedId) {
          setState(prevState => ({
            ...prevState,
            savedSearches: data.items,
            filteredSavedSearches: data.items
          }));
          handleSelectShortlist(
            data.items.find(a => a.SavedSearchId == selectedId)
          );
        }
      })
      .catch(() => {
        setState(prevState => ({ ...prevState, fetchingSavedSearches: false }));
      });
  };

  const handleSelectShortlist = item => {
    setState(prevState => ({
      ...prevState,
      selectedSavedSearch: item,
      createNewShortlist: false,
      fetchingResumes: true,
      showOpportunities: false,
      clonedShortlist: {}
    }));
    getSaveSearchDetailApi(item.SavedSearchId)
      .then(response => {
        let newSetting = {
          roles: [],
          skills: [],
          keywords: [],
          certifications: [],
          industries: [],
          countries: [],
          languages: []
        };
        if (response.success) {
          const savedSearchSettings = response.items.SavedSearchSettings.reduce(
            function(filtered, option) {
              switch (option.LookupTypeId) {
                case 1: {
                  const someNewValue = {
                    ExperienceLevel: option.ExperienceLevel,
                    ProfileId: option.SearchedPK,
                    ProfileValue: option.SearchedValue
                  };
                  newSetting.roles.push(someNewValue);
                  break;
                }
                case 2: {
                  const someNewValue = {
                    ExperienceLevel: option.ExperienceLevel,
                    SkillId: option.SearchedPK,
                    SkillValue: option.SearchedValue
                  };
                  newSetting.skills.push(someNewValue);
                  break;
                }
                case 3: {
                  const someNewValue = {
                    ExperienceLevel: option.ExperienceLevel,
                    KeywordId: option.SearchedPK,
                    KeywordValue: option.SearchedValue
                  };
                  newSetting.keywords.push(someNewValue);
                  break;
                }
                case 4: {
                  const someNewValue = {
                    ExperienceLevel: option.ExperienceLevel,
                    IndustryId: option.SearchedPK,
                    IndustryValue: option.SearchedValue
                  };
                  newSetting.industries.push(someNewValue);
                  break;
                }
                case 5: {
                  const someNewValue = {
                    ExperienceLevel: option.ExperienceLevel,
                    CertificationId: option.SearchedPK,
                    CertificationValue: option.SearchedValue
                  };
                  newSetting.certifications.push(someNewValue);
                  break;
                }
                case 6: {
                  const someNewValue = {
                    ExperienceLevel: option.ExperienceLevel,
                    LanguageId: option.SearchedPK,
                    LanguageValue: option.SearchedValue
                  };
                  newSetting.languages.push(someNewValue);
                  break;
                }
                case 7: {
                  const someNewValue = {
                    ExperienceLevel: option.ExperienceLevel,
                    CountryId: option.SearchedPK,
                    CountryName: option.SearchedValue
                  };
                  newSetting.countries.push(someNewValue);
                  break;
                }
              }
              filtered = newSetting;
              return filtered;
            },
            []
          );
          const newSavedSearchSettings = savedSearchSettings.length
            ? savedSearchSettings
            : newSetting;
          setState(prevState => ({
            ...prevState,
            selectedSavedSearch: {
              ...prevState.selectedSavedSearch,
              savedSearchSettings: newSavedSearchSettings
            }
          }));
          const info = {
            CertificationIds: savedSearchSettings.certifications.map(
              item => item.CertificationId
            ),
            Countries: savedSearchSettings.countries.map(
              item => item.CountryId
            ),
            IndusteryIds: savedSearchSettings.industries.map(
              item => item.IndustryId
            ),
            KeywordIds: savedSearchSettings.keywords.map(
              item => item.KeywordId
            ),
            Languages: savedSearchSettings.languages.map(
              item => item.LanguageId
            ),
            ProfileIds: savedSearchSettings.roles.map(item => item.ProfileId),
            SkillIds: savedSearchSettings.skills.map(item => item.SkillId),
            Type: "Only20",
            limit: 20,
            page: 1
          };
          searchResumesApi(info)
            .then(data => {
              setState(prevState => ({
                ...prevState,
                shortlistResumes: data.items.map((item, index) => ({
                  ...item,
                  active: index == 0 ? true : false
                })),
                fetchingResumes: false,
                selectedResume: first(data.items) || {}
              }));
            })
            .catch(() => {
              setState(prevState => ({ ...prevState, fetchingResumes: false }));
            });
        }
      })
      .catch(res => console.log("Error ", res));
  };

  const handleResumeSelect = item => {
    const { shortlistResumes } = state;
    const selectedResume = find(shortlistResumes, { ResumeId: item.ResumeId });
    setState(prevState => ({ ...prevState, selectedResume }));
  };

  const handleSearchChange = e => {
    const { value } = e.target;
    const { savedSearches } = state;

    const filteredSavedSearches = filter(savedSearches, opp =>
      includes(toLower(opp.ShortlistName), toLower(value))
    );
    setState(prevState => ({
      ...prevState,
      filteredSavedSearches,
      searchKey: value
    }));
  };

  const handleShortlistActive = activeItem => {
    const shortlistResumes = state.shortlistResumes.map(item => ({
      ...item,
      active: activeItem.ResumeId === item.ResumeId ? !item.active : item.active
    }));
    setState(prevState => ({ ...prevState, shortlistResumes }));
  };

  const handleCreateNewShorlistClick = () => {
    const { selectedSavedSearch } = state;
    setState(prevState => ({ ...prevState, isLoading: true }));
    updateSavedSearchName(
      selectedSavedSearch.SavedSearchId,
      selectedSavedSearch.SearchName
    )
      .then(data => {
        if (data.success) {
          getSavedSearches(data.items);
          const info = {
            message: "Saved Search Name successfully updated",
            status: "success"
          };
          dispatch(notificationAction(info));
        } else {
          const info = {
            message: "Error while updating saved search name",
            status: "info"
          };
          dispatch(notificationAction(info));
          setState(prevState => ({ ...prevState, isLoading: false }));
        }
      })
      .catch(() => {
        setState(prevState => ({ ...prevState, isLoading: false }));
      });
  };

  const handleNewShortList = () => {
    setState(prevState => ({
      ...prevState,
      createNewShortlist: true,
      newShortlistValue: "",
      selectedResume: {},
      selectedSavedSearch: {}
    }));
  };

  const handleDeleteSavedSearch = id => {
    setState(prevState => ({
      ...prevState,
      deletedId: id,
      dialogMessage: "Are you sure you want to delete this saved search"
    }));
  };

  const handleYesClick = () => {
    const { deletedId } = state;
    setState(prevState => ({ ...prevState, dialogMessage: "" }));
    setState(prevState => ({ ...prevState, isLoading: true }));
    deleteSavedSearchApi({ id: deletedId })
      .then(data => {
        if (data.success) {
          getSavedSearches();
          const info = {
            message: "Saved search successfully deleted",
            status: "success"
          };
          dispatch(notificationAction(info));
          setState(prevState => ({
            ...prevState,
            selectedSavedSearch: {},
            selectedResume: {},
            deletedId: null
          }));
        } else {
          const info = {
            message: data.message,
            status: "error"
          };
          dispatch(notificationAction(info));
        }
        setState(prevState => ({ ...prevState, isLoading: false }));
      })
      .catch(() => {
        setState(prevState => ({ ...prevState, isLoading: false }));
      });
  };

  const handleNoClick = () => {
    setState(prevState => ({
      ...prevState,
      dialogMessage: "",
      deletedId: null
    }));
  };

  const handleCreateNewClick = item => {
    setState(prevState => ({
      ...prevState,
      clonedShortlist: item,
      selectedSavedSearch: {}
    }));
    getShortlistResumeApi(item.SavedSearchId)
      .then(data => {
        const resumes = data.items.map((item, index) => ({
          ...item,
          active: index == 0 ? true : false
        }));
        setState(prevState => ({
          ...prevState,
          clonedShortlist: {
            ...item,
            ShortlistResumes: resumes
          },
          shortlistResumes: resumes
        }));
      })
      .catch(() => {
        setState(prevState => ({ ...prevState, fetchingResumes: false }));
      });
  };

  const createNewShortlist = () => {
    const { shortlistResumes } = state;
    const selectedResumesIds = shortlistResumes
      .filter(item => item.active)
      .map(item => item.ResumeId);
    setState(prevState => ({ ...prevState, isLoading: true }));
    const info = {
      ShortlistName: `New savedSearches for ${
        selectedResumesIds.length
      } resume(s) at ${moment(new Date()).format("D MMMM YYYY")} at ${moment(
        new Date()
      ).format("LTS")}`,
      ResumeIdsList: selectedResumesIds
    };
    return postShortlistApi(info)
      .then(data => {
        return data;
      })
      .catch(() => {
        setState(prevState => ({ ...prevState, isLoading: false }));
      });
  };

  const handleCancelClick = () => {
    setState(prevState => ({ ...prevState, dialogOpportunityMessage: "" }));
  };

  const handleSelectOpportunity = item => {
    createNewShortlist().then(() => {
      sessionStorage.setItem("opportunity", JSON.stringify(item));

      navigate(privateRoutes.searcherCreateOpportunity.path);
    });
  };

  const onResumeDelete = item => {
    setState(prevState => ({
      ...prevState,
      clonedShortlist: {
        ...prevState.clonedShortlist,
        ShortlistResumes: prevState.clonedShortlist.ShortlistResumes.filter(
          x => x.ResumeId != item.ResumeId
        )
      }
    }));
  };

  const {
    searchCollapsed,
    resumeCollapsed,
    resumeDetailCollapsed,
    selectedSavedSearch,
    selectedResume,
    shortlistResumes,
    dialogMessage,
    filteredSavedSearches,
    createCollapse,
    fetchingResumes,
    dialogOpportunityMessage,
    opportunityList,
    showOpportunities,
    fetchingOpportunities,
    opportunitiesCollapse,
    searchKey,
    clonedShortlist = {}
  } = state;

  return (
    <PageWrapper className="saved-search-page">
      {dialogMessage && (
        <ConfirmDialog testId="confirm-diloag">
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              onClick={handleYesClick}
              testId="delete-yes"
            >
              {labels.shortlistDeletionCurtainYESBtnText}
            </ConfirmDialog.Button>
            <ConfirmDialog.Button
              className="dialog-btn"
              onClick={handleNoClick}
            >
              {labels.shortlistDeletionCurtainNOBtnText}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      {dialogOpportunityMessage && (
        <ConfirmDialog>
          <ConfirmDialog.Message>
            {dialogOpportunityMessage}
          </ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              // onClick={handleExistingClick}
            >
              {labels.ShortlistResumeListCurtainExistingBtnText}
            </ConfirmDialog.Button>
            <ConfirmDialog.Button
              className="dialog-btn"
              onClick={handleCreateNewClick}
            >
              {labels.ShortlistResumeListCurtainCreateNewBtnText}
            </ConfirmDialog.Button>
            <ConfirmDialog.Button
              className="dialog-btn"
              onClick={handleCancelClick}
            >
              {labels.ShortlistResumeListCurtainCancelBtnText}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      {!props.onBoardShortlistId && (
        <Column collapse={searchCollapsed} className="col-1">
          <Column.Collapsed
            onClick={() =>
              setState(prevState => ({ ...prevState, searchCollapsed: false }))
            }
            testId={"expand-btn-1"}
            tooltipPlace="left"
            text={labels.ViewTitleOpportunityShortlistSection}
            tooltipButton={labels.ToolTipShortlistExpandList}
            isHelpActive={isHelpActive}
            tooltipHelp={labels.HelpToolTipShortlistExpand}
          />
          <Column.Head>
            <SearchInput
              testId="search-input"
              value={searchKey}
              placeholder={"Search for your saved searches"}
              onChange={handleSearchChange}
            />
            <Button
              testId={"collapse-btn-1"}
              className="collapseBtn"
              tooltipPlace="left"
              tooltipButton={labels.ToolTipShortlistCollapse}
              onClick={() =>
                setState(prevState => ({ ...prevState, searchCollapsed: true }))
              }
              isHelpActive={isHelpActive}
              tooltipHelp={labels.HelpTooltipShortlistCollapse}
            />
          </Column.Head>
          <Column.Body>
            {filteredSavedSearches.map(item => (
              <div
                key={item.SavedSearchId}
                data-testid={`savedsearches-list-item${
                  item.isActive ? "-activeItem" : ""
                }`}
                className={`tictell-list-item-container  ${
                  item.SavedSearchId === selectedSavedSearch.SavedSearchId
                    ? "selected-item"
                    : ""
                } ${
                  item.SavedSearchId === selectedSavedSearch.SavedSearchId
                    ? "activeItem"
                    : ""
                }`}
              >
                <div
                  onClick={() => handleSelectShortlist(item)}
                  className="name-container flow-container"
                >
                  <label
                    className="pool-label"
                    data-testid={`collabs-list-owner-username`}
                  >
                    {item.SearchName}
                  </label>
                </div>
                {
                  <Button
                    onClick={() => handleDeleteSavedSearch(item.SavedSearchId)}
                    className="closeBtn"
                    tooltipButton={"Delete saved search"}
                    tooltipHelp={"Delete saved search"}
                    tooltipPlace="left"
                    isHelpActive={isHelpActive}
                    testId="delete-flow-btn"
                  />
                }
              </div>
            ))}
          </Column.Body>
        </Column>
      )}
      {selectedSavedSearch &&
        !isEmpty(selectedSavedSearch.savedSearchSettings) && (
          <Column collapse={createCollapse} className="col-2">
            <Column.Collapsed
              onClick={() =>
                setState(prevState => ({ ...prevState, createCollapse: false }))
              }
              testId={"expand-btn-2"}
              tooltipButton={labels.TooltipShortlistCreateShortlistExpand}
              tooltipPlace="left"
              text={labels.ViewTitleShortlistCreateShortlist}
              isHelpActive={isHelpActive}
              tooltipHelp={labels.HelpTooltipShortlistCreateShortlistExpand}
            />
            <Column.Head>
              <Column.HeaderTitle isActive={true}>
                {"Search Settings"}
              </Column.HeaderTitle>
              <Button
                className="SaveCloudBtn"
                onClick={handleCreateNewShorlistClick}
                tooltipPlace="left"
                testId="button-savedSearches-save"
                disabled={!selectedSavedSearch.SearchName}
                isHelpActive={isHelpActive}
                tooltipButton={"Update saved search name"}
                tooltipHelp={"Update saved search name"}
              />
              <Button
                className="collapseBtn"
                testId={"collapse-btn-2"}
                tooltipButton={labels.TooltipShortlistCreateShortlistCollapse}
                isHelpActive={isHelpActive}
                tooltipHelp={labels.HelpTooltipShortlistCreateShortlistCollapse}
                tooltipPlace="left"
                onClick={() =>
                  setState(prevState => ({
                    ...prevState,
                    createCollapse: true
                  }))
                }
              />
            </Column.Head>
            <Column.Body className="form-editor">
              <label className="form-label">{"Saved Search Name"}</label>
              <Input
                autoFocus
                type="text"
                testId="input-text-savedSearches-name"
                className="input-text"
                value={selectedSavedSearch.SearchName || ""}
                onChange={e =>
                  setState(prevState => ({
                    ...prevState,
                    selectedSavedSearch: {
                      ...selectedSavedSearch,
                      SearchName: e.target.value
                    }
                  }))
                }
              />
              <SettingDetail
                labels={labels}
                settings={selectedSavedSearch.savedSearchSettings}
              />
            </Column.Body>
          </Column>
        )}
      {isEmpty(clonedShortlist) && (
        <Column collapse={resumeCollapsed} className="col-2">
          <Column.Collapsed
            onClick={() =>
              setState(prevState => ({ ...prevState, resumeCollapsed: false }))
            }
            testId={"expand-btn-3"}
            tooltipButton={labels.TooltipShortlistResumeListExpand}
            tooltipPlace="left"
            text={labels.ViewTitleShortlistResult}
            isHelpActive={isHelpActive}
            tooltipHelp={labels.HelpTooltipShortlistResumeListExpand}
          />
          <Column.Head>
            <Column.HeaderTitle isActive={selectedSavedSearch}>
              {fetchingResumes || !selectedSavedSearch.SavedSearchId
                ? ""
                : shortlistResumes.length > 0
                ? shortlistResumes.length +
                  " " +
                  labels.HeadingShorlistResumeListResult
                : labels.HeadingShorlistResumeListNoResume}
            </Column.HeaderTitle>
            <Button
              className="collapseBtn"
              testId={"collapse-btn-3"}
              tooltipButton={labels.TooltipShortlistResumeListCollapse}
              onClick={() =>
                setState(prevState => ({ ...prevState, resumeCollapsed: true }))
              }
              tooltipPlace="left"
              isHelpActive={isHelpActive}
              tooltipHelp={labels.HelpTooltipShortlistResumeListCollapse}
            />
          </Column.Head>
          <Column.Body className="flex">
            {selectedSavedSearch.SavedSearchId ? (
              <ShortlistResumes
                fetchingResumes={fetchingResumes}
                handleSelectShortlist={handleSelectShortlist}
                selectedShortlist={selectedSavedSearch}
                onResumeSelect={handleResumeSelect}
                selectedResume={selectedResume}
                shortlistResumes={shortlistResumes}
                hideRemoveButton={true}
              />
            ) : (
              <EmptyInfo>{labels.emptySavedSearchHeadsUp}</EmptyInfo>
            )}
          </Column.Body>
        </Column>
      )}
      {showOpportunities && (
        <Column collapse={opportunitiesCollapse} className="col-3">
          <Column.Collapsed
            onClick={() =>
              setState(prevState => ({
                ...prevState,
                opportunitiesCollapse: false
              }))
            }
            testId={"expand-btn-4"}
            tooltipPlace="left"
            text={labels.ViewTitleShortlistOpportunityList}
            isHelpActive={isHelpActive}
            tooltipButton={labels.TooltipShortlistOpportunityListExpand}
            tooltipHelp={labels.HelpTooltipShortlistOpportunityListExpand}
          />
          <Column.Head>
            <Column.HeaderTitle isActive={true}>
              {labels.HeadingShortlistOpportunityList}
            </Column.HeaderTitle>

            <Button
              className="collapseBtn"
              testId={"collapse-btn-4"}
              isHelpActive={isHelpActive}
              tooltipPlace="left"
              onClick={() =>
                setState(prevState => ({
                  ...prevState,
                  opportunitiesCollapse: true
                }))
              }
              tooltipButton={labels.TooltipShortlistOpportunityListCollapse}
              tooltipHelp={labels.HelpTooltipShortlistOpportunityListCollapse}
            />
          </Column.Head>
          <Column.Body>
            <List isFetching={fetchingOpportunities}>
              {opportunityList.map(item => (
                <List.ListItem
                  onClick={() => handleSelectOpportunity(item)}
                  key={item.RequestId}
                >
                  {item.RequestName}
                </List.ListItem>
              ))}
            </List>
          </Column.Body>
        </Column>
      )}
      {!showOpportunities && selectedResume.ResumeId && (
        <Column collapse={resumeDetailCollapsed} className="col-3">
          <Column.Collapsed
            tooltipButton={labels.TooltipShortlistResumeDetailExpand}
            text={labels.ViewTitleShortlistDetail}
            onClick={() =>
              setState(prevState => ({
                ...prevState,
                resumeDetailCollapsed: false
              }))
            }
            testId={"expand-btn-5"}
            isHelpActive={isHelpActive}
            tooltipHelp={labels.HelpTooltipShortlistResumeDetailExpand}
          />
          <Column.Head>
            <div className="heading">Resume Detail</div>
            <Button
              className="collapseBtn"
              onClick={() =>
                setState(prevState => ({
                  ...prevState,
                  resumeDetailCollapsed: true
                }))
              }
              testId={"collapse-btn-5"}
              tooltipButton={labels.ToolTipShortlistCollaspeResumeDetail}
              tooltipPlace="left"
              isHelpActive={isHelpActive}
              tooltipHelp={labels.HelpTooltipShortlistResumeDetailCollapse}
            />
          </Column.Head>
          <Column.Body>
            {fetchingResumes ? (
              <LoadingMaskRow />
            ) : (
              <ShortlistResumeDetail resume={selectedResume} />
            )}
          </Column.Body>
        </Column>
      )}
    </PageWrapper>
  );
};

export default SavedSearch;
