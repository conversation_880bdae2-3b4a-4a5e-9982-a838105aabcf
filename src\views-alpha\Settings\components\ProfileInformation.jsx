import { PlusOutlined } from "@ant-design/icons";
import { <PERSON>ton, Form, Grid, Typography } from "antd";
import clsx from "clsx";
import { useEffect, useState } from "react";
import PhoneIcon from "../../../assets-alpha/images/svg/call.svg?react";
import DeleteIcon from "../../../assets-alpha/images/svg/delete.svg?react";
import InformationIcon from "../../../assets-alpha/images/svg/information.svg?react";
import LocationIcon from "../../../assets-alpha/images/svg/location.svg?react";
import ProfileIcon from "../../../assets-alpha/images/svg/profile.svg?react";
import ShareIcon from "../../../assets-alpha/images/svg/share.svg?react";
import MessageIcon from "../../../assets-alpha/images/svg/sms.svg?react";
import CustomInput from "../../../common-alpha/CustomInput/CustomInput";
import Icon from "../../../common-alpha/Icon/Icon";
import InputPhone from "../../../common-alpha/InputPhone/InputPhone";
import CustomSelect from "../../../common-alpha/Select/Select";
import { HelpTooltip } from "../../../common-alpha/Tooltip/Tooltip";
import { useCountriesApi, useGetUserProfileApi } from "../settingsApi";
import TooltipModal from "./TooltipModal";
import { useSelector } from "react-redux";

const { Title, Text } = Typography;

const ProfileInformation = ({ loading }) => {
  const labels = useSelector(state => state.systemLabel.labels?.setting);

  const {
    data: userProfileData,
    isLoading: loadingProfile
  } = useGetUserProfileApi();
  const {
    data: countriesData,
    isLoading: loadingCountries
  } = useCountriesApi();

  const form = Form.useFormInstance();
  const { setFieldsValue } = form;
  useEffect(() => {
    if (!userProfileData?.items) return;
    setFieldsValue({
      ...userProfileData?.items,
      CountryName: {
        label: userProfileData?.items?.CountryName,
        value: userProfileData?.items?.CountryId
      }
    });
  }, [userProfileData?.items, setFieldsValue]);
  // if (selectedTab < 0) return null;
  return (
    <div>
      <div className={clsx("grid grid-cols-2 gap-4 mb-[20px] mt-5")}>
        <CustomInput
          PrefixIcon={ProfileIcon}
          label={labels?.firstName}
          name={"UserFirstname"}
          disabled={loading}
          rules={[
            {
              required: true,
              message: labels?.firstNameRequired
            }
          ]}
        />
        <CustomInput
          label={labels?.lastName}
          name={"UserLastname"}
          disabled={loading}
          rules={[
            {
              required: true,
              message: labels?.lastNameRequired
            }
          ]}
        />
      </div>

      <div className="mb-[20px]">
        <CustomInput
          PrefixIcon={MessageIcon}
          name="UserEmail"
          label={labels?.registeredEmail}
          suffixText={labels?.registeredEmailCannotBeChanged}
          disabled
          rules={[
            { required: true, message: labels?.emailRequired },
            { type: "email", message: labels?.emailInvalid }
          ]}
        />
      </div>

      <div className="mb-[20px]">
        <CustomSelect
          label={labels?.location}
          name="CountryName"
          options={countriesData}
          showSearch
          optionFilterProp={"CountryName"}
          PrefixIcon={LocationIcon}
          disabled={loading}
        />
      </div>
      <UserDetails
        label={labels?.preferredEmail}
        name="EmailList"
        addBtnText={labels?.addNew}
        PrefixIcon={MessageIcon}
        disabled={loading}
        rules={[
          { required: true, message: labels?.emailRequired },
          { type: "email", message: labels?.emailInvalid }
        ]}
        subName={"UserEmailValue"}
        list={userProfileData?.items}
      />

      <UserDetails
        label={labels?.preferredPhone}
        name="PhoneList"
        addBtnText={labels?.addNew}
        subName={"UserPhonenumberValue"}
        PrefixIcon={PhoneIcon}
        disabled={loading}
        rules={[{ required: true, message: labels?.phoneRequired }]}
        list={userProfileData?.items}
      />
      <UserDetails
        label={labels?.socialMediaAndPortfolios}
        rules={[{ required: true, message: labels?.socialLinkRequired }]}
        name="SocialLinks"
        subName={"URL"}
        PrefixIcon={ShareIcon}
        disabled={loading}
        addBtnText={labels?.addNew}
        maxLength={8}
        list={userProfileData?.items}
      />
    </div>
  );
};

export default ProfileInformation;

const UserDetails = ({
  label,
  name,
  addBtnText,
  rules,
  subName,
  PrefixIcon,
  maxLength,
  list,
  disabled
}) => {
  const [openTooltipFor, setOpenTooltipFor] = useState(null);
  const { useBreakpoint } = Grid;
  const labels = useSelector(state => state.systemLabel.labels?.setting);

  const screens = useBreakpoint();
  const getTooltipContent = name => {
    switch (name) {
      case "PhoneList":
        return labels?.preferredPhoneTooltip;
      case "EmailList":
        return labels?.preferredEmailTooltip;
      case "SocialLinks":
        return labels?.socialMediaTooltip;
      default:
        return "";
    }
  };

  const getTooltipTitle = name => {
    switch (name) {
      case "PhoneList":
        return labels?.preferredPhoneTitleTooltip;
      case "EmailList":
        return labels?.preferredEmailTitleTooltip;
      case "SocialLinks":
        return labels?.socialMediaTitleTooltip;
      default:
        return "";
    }
  };
  return (
    <div className="mb-[20px]">
      <Form.List name={name}>
        {(fields, { add, remove }) => (
          <div>
            <div className="flex justify-between items-center mb-[6px]">
              <Text className="text-sm !text-[var(--gray-3)]">{label}</Text>
              <Button
                type="text"
                disabled={maxLength && fields?.length >= maxLength}
                className="flex items-center gap-1 !text-sm font-medium !p-0 !bg-transparent"
              >
                <Button
                  type="text"
                  className="!text-[var(--purple)] !p-0 !bg-transparent"
                  disabled={disabled}
                  onClick={() => add()}
                >
                  <PlusOutlined className="text-[var(--purple)]" />
                  {addBtnText}
                </Button>
                {screens.md ? (
                  <HelpTooltip
                    title={getTooltipTitle(name)}
                    content={getTooltipContent(name)}
                    placement="top"
                  >
                    <Icon src={InformationIcon} renderSvg />
                  </HelpTooltip>
                ) : (
                  <>
                    <Icon
                      src={InformationIcon}
                      renderSvg
                      onClick={() => setOpenTooltipFor(name)}
                    />
                    <TooltipModal
                      open={openTooltipFor === name}
                      setOpen={isOpen => setOpenTooltipFor(isOpen)}
                      title={getTooltipTitle(name)}
                      description={getTooltipContent(name)}
                    />
                  </>
                )}
              </Button>
            </div>
            {fields.map(({ key, name: fieldName, ...restField }) => {
              const item = list?.[name]?.[fieldName];
              return (
                <div key={key} className="gap-2 mb-2">
                  {name === "phoneNumbers-1" ? (
                    <InputPhone
                      disabled={disabled}
                      listField={restField}
                      prefixIconClass="text-[#878787] w-[20px]"
                      PrefixIcon={PrefixIcon}
                      name={[fieldName, subName]}
                      rules={rules}
                      suffixInput={
                        !item?.IsUsedInPresentation && (
                          <Button
                            className={"!p-0"}
                            type="text"
                            disabled={disabled}
                            onClick={() => remove(fieldName)}
                          >
                            <DeleteIcon
                              className="cursor-pointer"
                              disabled={disabled}
                              onClick={() => remove(fieldName)}
                            />
                          </Button>
                        )
                      }
                    />
                  ) : (
                    <CustomInput
                      disabled={disabled}
                      listField={restField}
                      PrefixIcon={PrefixIcon}
                      name={[fieldName, subName]}
                      rules={rules}
                      suffixInput={
                        item?.IsUsedInPresentation ? (
                          <HelpTooltip content={labels?.presentationUsed}>
                            <Icon
                              className="z-10"
                              src={InformationIcon}
                              renderSvg
                            />
                          </HelpTooltip>
                        ) : (
                          <Button
                            className={"!p-0"}
                            type="text"
                            disabled={disabled}
                            onClick={() => remove(fieldName)}
                          >
                            <DeleteIcon className="cursor-pointer" />
                          </Button>
                        )
                      }
                    />
                  )}
                </div>
              );
            })}
          </div>
        )}
      </Form.List>
    </div>
  );
};
