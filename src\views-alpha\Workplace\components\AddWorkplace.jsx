import { useEffect, useState } from "react";
import {
  Avatar,
  Checkbox,
  Divider,
  Input,
  Select,
  Space,
  Typography,
  Upload,
  Button
} from "antd";
import ArrowDown from "../../../assets-alpha/images/svg/arrow-down-1.svg?react";
import { CloseOutlined, PlusOutlined } from "@ant-design/icons";
import AddNewPhoneNumber from "./addNewNumber";
import LoadingMask from "../../../common/LoadingMask/LoadingMask";
import AvatarIcon from "../../../assets-alpha/images/svg/avatar-workplace.svg";
import CloseIcon from "../../../assets-alpha/images/svg/close-modal.svg";
const { TextArea } = Input;
const { Title } = Typography;

const CustomInput = ({
  value,
  label,
  placeholder,
  name = "",
  handleChange,
  maxLength,
  className = ""
}) => {
  return (
    <div>
      <p className="!mb-1 text-[13px] md:text-sm font-medium text-[#878787]">
        {label}
      </p>
      <Input
        placeholder={placeholder}
        value={value}
        name={name}
        maxLength={maxLength}
        onChange={e => handleChange(e)}
        className={`placeholder:!text-[#878787] placeholder:!text-[12px] md:placeholder:!text-[13px] md:!text-[14px] !text-[12px] font-normal !h-11 ${className}`}
      />
    </div>
  );
};

const AddWorkplace = props => {
  const [addNewNumber, setAddNewNumber] = useState(false);
  const hideAddNumberModel = () => {
    setAddNewNumber(false);
  };

  const {
    companyForm,
    labels,
    onFormFieldChange,
    onFormSelectChange,
    industries,
    countries,
    userPhoneNumbers,
    onCompanySave,
    onAvatarChange,
    isLoading,
    getNumber,
    setIsEditDrawerOpen,
    onStateChangeAction,
    avatar,
    setAvatar,
    setRemoveLogo
  } = props;
  useEffect(() => {
    if (companyForm?.Logo) {
      setAvatar(companyForm.Logo);
    }
  }, [companyForm?.Logo]);
  if (companyForm && companyForm.UserCompanyId) {
    const {
      CompanyName,
      invalidCompanyName,
      CompanyAddress,
      invalidUserPhonenumberId,
      IndustryId,
      UserPhonenumberId,
      CountryId,
      WebUrl,
      Detail,
      Vat,
      IsDefaultCompany
    } = companyForm;
    const uploadProps = {
      beforeUpload: file => {
        onAvatarChange(file);
        return false;
      },
      fileList: [],
      maxCount: 1
    };
    return (
      <div className="!w-full">
        {isLoading && <LoadingMask />}

        <AddNewPhoneNumber
          open={addNewNumber}
          setOpen={setAddNewNumber}
          hideAddNumberModel={hideAddNumberModel}
          title="Add Phone Number"
          heading="Add your new phone number here"
          getNumber={getNumber}
          labels={labels}
        />

        <div className="!w-full">
          <div className="relative w-[100%] md:w-[497px] h-[64px] ">
            <div className="fixed md:w-[497px] w-[100%] h-[64px] border-b border-[#EAE5FC] z-[999] bg-white rounded-tl-2xl rounded-tr-2xl">
              <div className="flex w-full h-[64px] justify-between items-center p-6">
                <div className="flex md:justify-start justify-between w-full items-center gap-4 ">
                  <img
                    src={CloseIcon}
                    alt=""
                    onClick={() => {
                      setIsEditDrawerOpen(false);
                    }}
                    className="pointer order-2 md:order-1"
                  />
                  <Title
                    level={3}
                    className="!m-0 flex-1 order-1 md:order-2 !text-[18px] !font-semibold md:!text-2xl md:!font-medium"
                  >
                    {companyForm.UserCompanyId === -1
                      ? labels?.Add_Workplace_Label
                      : "Edit"}
                  </Title>
                </div>
                <div className="hidden md:flex order-3">
                  <Button
                    type="primary"
                    className=" w-[57px] !h-9 pl-4 pr-4"
                    disabled={!CompanyName}
                    onClick={onCompanySave}
                  >
                    {labels?.SearcherOpportunityDraftDetailSave}
                  </Button>
                </div>
              </div>
            </div>
          </div>
          <div className="pl-4 pr-4 pt-5 md:pl-6 md:pr-6 md:pt-4">
            <div className="flex flex-col lg:flex-row items-center justify-between">
              <div className="flex flex-row items-center w-full gap-4">
                <div>
                  <Avatar
                    src={avatar ? avatar : AvatarIcon}
                    className="!m-auto !block "
                    size={72}
                    shape="square"
                  />
                </div>

                <div className="flex flex-col gap-[8px] w-full md:w-[210px]">
                  <Upload
                    {...uploadProps}
                    accept="image/*"
                    type="button"
                    className="flex-1 [&_.ant-upload]:w-full [&_.ant-upload-list]:hidden"
                    beforeUpload={file => {
                      const reader = new FileReader();
                      reader.onload = () => {
                        const base64 = reader.result;
                        setAvatar(base64);
                        onStateChangeAction({
                          companyForm: {
                            ...companyForm,
                            Logo: base64
                          }
                        });
                      };
                      reader.readAsDataURL(file);
                    }}
                  >
                    <div className="flex flex-col w-full md:w-[210px] justify-center gap-2">
                      <Button
                        mainClassName="block"
                        className="bg-[#F3F1FD] !w-full sm:!w-[210px] !h-8 text-sm !text-[var(--purple)] rounded-2xl "
                      >
                        {avatar
                          ? labels?.Image_Change_Label
                          : labels?.Image_Upload}
                      </Button>
                    </div>
                  </Upload>

                  {avatar ? (
                    <Button
                      mainClassName="block"
                      className="!bg-white md:max-w-[210px] !border !border-[#EAE5FC] w-full !h-8 text-sm !text-[#343333] rounded-2xl"
                      onClick={() => {
                        setAvatar(null);
                        setRemoveLogo(true);
                        onStateChangeAction({
                          companyForm: {
                            ...companyForm,
                            Logo: ""
                          }
                        });
                      }}
                    >
                      {labels?.Remove_Image_Label}
                    </Button>
                  ) : (
                    <p className="flex w-full justify-center !text-[#878787] font-normal text-xs whitespace-pre">
                      JPG, PNG, 1.5 MB Max
                    </p>
                  )}
                </div>
              </div>
            </div>
            <div className="!flex !flex-col gap-3 md:!gap-4 md:mt-6 mt-5">
              <div>
                <CustomInput
                  label={labels.NAME_LABEL}
                  placeholder={labels.companyNamePlaceholder}
                  name="CompanyName"
                  maxLength={75}
                  handleChange={onFormFieldChange}
                  value={CompanyName ? CompanyName : ""}
                  className={`${
                    invalidCompanyName ? "!border !border-red-500" : ""
                  }`}
                />
              </div>
              <div className="grid grid-cols-2 sm:!gap-3 !gap-[9px] md:!gap-4">
                <div>
                  <p className="!mb-1 text-[13px] md:text-sm font-medium text-[#878787]">
                    {labels.PHONE_LABEL}
                  </p>
                  <Select
                    style={{ width: "100%" }}
                    showSearch
                    name="UserPhonenumberId"
                    value={UserPhonenumberId !== "" ? UserPhonenumberId : null}
                    placeholder={labels?.companyPhoneNumberPlaceholder}
                    promptTextCreator={value => `Create New Number ${value}`}
                    onChange={(e, selectedOption) =>
                      onFormSelectChange("UserPhonenumberId", selectedOption)
                    }
                    autoBlur={true}
                    options={userPhoneNumbers}
                    bordered={false}
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      option?.props?.label
                        ?.toLowerCase()
                        ?.indexOf(input?.toLowerCase()) >= 0
                    }
                    suffixIcon={
                      !UserPhonenumberId ? (
                        <ArrowDown className="w-[20px]" />
                      ) : (
                        <CloseOutlined
                          onClick={e => {
                            e.stopPropagation();
                            onFormSelectChange("UserPhonenumberId", null);
                          }}
                          className="!text-[#878787]"
                        />
                      )
                    }
                    className="!h-11 bg-[#F3F1FD] border border-[#F3F1FD] rounded-[99px]"
                    rootClassName="
                       [&_.ant-select-selection-item]:!pl-[6px]
                       [&_.ant-select-selection-placeholder]:!pl-[6px]
                       [&_.ant-select-selection-search]:!pl-[6px]
                       [&_.ant-select-selection-item]:!max-w-[180px]
                       md:[&_.ant-select-selection-placeholder]:!text-[13px]
                       [&_.ant-select-selection-placeholder]:!text-[12px]
                       [&_.ant-select-selection-placeholder]:!text-[#878787]
                       [&_.ant-select-selector]:!text-[12px]
                       md:[&_.ant-select-selector]:!text-[14px]
                       [&_.ant-select-selector]:!text-[#343333]"
                    dropdownRender={menu => (
                      <>
                        {menu}
                        <>
                          <Divider style={{ margin: "8px 0" }} />
                          <Space className="flex w-full justify-center">
                            <Button
                              type="text"
                              className="flex items-center"
                              icon={<PlusOutlined />}
                              onClick={() => {
                                setAddNewNumber(true);
                              }}
                            >
                              {labels?.Add_Phonenumber_Label}
                            </Button>
                          </Space>
                        </>
                      </>
                    )}
                  ></Select>
                </div>
                <div className="new-select">
                  <p className="!mb-1 text-[13px] md:text-sm font-medium text-[#878787]">
                    {labels.COUNTRY_LABEL}
                  </p>
                  <Select
                    style={{ width: "100%" }}
                    showSearch
                    showArrow
                    bordered={false}
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      option?.props?.label
                        ?.toLowerCase()
                        ?.indexOf(input?.toLowerCase()) >= 0
                    }
                    name="CountryId"
                    className="!h-11 bg-[#F3F1FD] border border-[#F3F1FD] rounded-[99px]"
                    value={CountryId && CountryId}
                    placeholder={labels.companyCountryPlaceholder}
                    onChange={(e, selectedOption) =>
                      onFormSelectChange("CountryId", selectedOption)
                    }
                    suffixIcon={<ArrowDown className="w-[20px]" />}
                    options={countries}
                    rootClassName=" [&_.ant-select-selection-item]:!pl-[6px]
                       [&_.ant-select-selection-placeholder]:!pl-[6px]
                       [&_.ant-select-selection-search]:!pl-[6px]
                       [&_.ant-select-selection-item]:!max-w-[180px]
                       md:[&_.ant-select-selection-placeholder]:!text-[13px]
                       [&_.ant-select-selection-placeholder]:!text-[12px]
                       [&_.ant-select-selection-placeholder]:!text-[#878787]
                       [&_.ant-select-selector]:!text-[12px]
                       md:[&_.ant-select-selector]:!text-[14px]
                       [&_.ant-select-selector]:!text-[#343333]"
                  ></Select>
                </div>
              </div>
              <div className="grid grid-cols-2 sm:!gap-3 !gap-[9px] md:!gap-4">
                <div className="new-select">
                  <p className="!mb-1 text-[13px] md:text-sm font-medium text-[#878787]">
                    {labels.INDUSTRY_LABEL}
                  </p>
                  <Select
                    showSearch
                    value={IndustryId && IndustryId}
                    placeholder={labels.companyIndustryPlaceholder}
                    onChange={(e, selectedOption) =>
                      onFormSelectChange("IndustryId", selectedOption)
                    }
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      option?.props?.label
                        ?.toLowerCase()
                        ?.indexOf(input?.toLowerCase()) >= 0
                    }
                    options={industries}
                    suffixIcon={<ArrowDown className="w-[20px]" />}
                    className="!h-11 w-full bg-[#F3F1FD] !border !border-[#F3F1FD] rounded-[99px]"
                    rootClassName="
                     [&_.ant-select-selection-item]:!pl-[6px]
                       [&_.ant-select-selection-placeholder]:!pl-[6px]
                       [&_.ant-select-selection-search]:!pl-[6px]
                       [&_.ant-select-selection-item]:!max-w-[180px]
                       md:[&_.ant-select-selection-placeholder]:!text-[13px]
                       [&_.ant-select-selection-placeholder]:!text-[12px]
                       [&_.ant-select-selection-placeholder]:!text-[#878787]
                       [&_.ant-select-selector]:!text-[12px]
                       md:[&_.ant-select-selector]:!text-[14px]
                       [&_.ant-select-selector]:!text-[#343333]
                       [&_.ant-select-selector]:!border-[#F3F1FD] 
                       [&_.ant-select-selector:hover]:!border-[#F3F1FD] 
                       [&_.ant-select-selector:focus]:!border-[#F3F1FD] 
                       [&_.ant-select-selector:active]:!border-[#F3F1FD]
                       [&_.ant-select-selector]:!shadow-none
                       [&_.ant-select-focused_.ant-select-selector]:!border-[#F3F1FD]"
                  ></Select>
                </div>
                <div>
                  <CustomInput
                    label={labels.ADDRESS_LABEL}
                    placeholder={labels.companyAddressPlaceholder}
                    name="CompanyAddress"
                    maxLength={100}
                    handleChange={onFormFieldChange}
                    value={CompanyAddress ? CompanyAddress : ""}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 sm:!gap-3 !gap-[9px] md:!gap-4">
                <CustomInput
                  label={labels.WEB_URL_LABEL}
                  placeholder={labels.companyURLPlaceholder}
                  name="WebUrl"
                  maxLength={100}
                  handleChange={onFormFieldChange}
                  value={WebUrl ? WebUrl : ""}
                  className="!h-11"
                />

                <CustomInput
                  label={labels.VAT_LABEL}
                  placeholder={labels.companyVATPlaceholder}
                  name="Vat"
                  maxLength={50}
                  handleChange={onFormFieldChange}
                  value={Vat ? Vat : ""}
                />
              </div>
              <div>
                <p className="!mb-1 text-[13px] md:text-sm font-medium text-[#878787]">
                  Description
                </p>
                <TextArea
                  placeholder={labels.companyDescriptionPlaceholder}
                  rows={6}
                  name="Detail"
                  maxLength={400}
                  value={Detail ? Detail : ""}
                  onChange={onFormFieldChange}
                  className="!bg-[#F3F1FD] md:!text-[14px] !text-[12px] placeholder:!text-[12px] md:placeholder:!text-[13px] placeholder:!text-[#878787] !rounded-2xl h-[132px] md:!h-[167px] !resize-none !pt-[10px] overflow-y-auto [scrollbar-width:'none'] [-ms-overflow-style:'none'] [&::-webkit-scrollbar]:hidden"
                />
              </div>
              <div className="flex w-full mb-[78px]">
                <Checkbox
                  className={IsDefaultCompany ? "checked" : "input-checkbox"}
                  disabled={IsDefaultCompany}
                  value={IsDefaultCompany}
                  name="IsDefaultCompany"
                  checked={IsDefaultCompany ? IsDefaultCompany : false}
                  onChange={onFormFieldChange}
                >
                  Set as default
                </Checkbox>
              </div>
            </div>
          </div>
          <div className="flex w-full md:hidden justify-end !mt-6 gap-4">
            <div className="md:hidden flex w-full max-w-[100%] md:max-w-[497px] h-[64px] bg-white fixed bottom-0 justify-center items-center  border-t-[0.5px] border-[#EAE5FC]">
              <Button
                type="primary"
                className="w-full ml-4 mr-4 !h-10 pl-4 pr-4"
                disabled={!CompanyName}
                onClick={onCompanySave}
              >
                {labels?.presentSaveBtnTxt}
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export default AddWorkplace;
