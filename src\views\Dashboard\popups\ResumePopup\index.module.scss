$lightPurpleBackground: rgba(241, 237, 255, 0.5);

.actionBtns {
  margin: 10px 0 0 5px !important;
}

.roleTagsContainer {
  gap: 5px;
  align-items: center;
  margin-top: 5px;
  line-height: 2.8;
}

.roleTag {
  color: #8f82f5 !important;
  background-color: $lightPurpleBackground;
  border-radius: 5px;
  font-size: 10px;
  padding: 4px;
  margin-right: 10px;
}

.cvRoot {
  max-width: 1200px;
  flex: 1;
  overflow: auto;
  .userInfo {
    .resumeTitle {
      text-transform: capitalize !important;
    }
  }
  .resumeTitle {
    font-size: 24px !important;
    font-family: Inter !important;
  }
  .EducationValue,
  .EducationYear,
  .text {
    font-size: 14px !important;
    font-family: Inter !important;
  }
  .userDetailItem,
  .heading {
    font-size: 16px !important;
    font-family: Inter !important;
  }
  .marginTop4 {
    margin-top: 16px !important;
  }
  .margint {
    margin-top: 0.5rem !important ;
  }
  .propertiesBox {
    margin-top: 10px;
  }
  .imgBorder {
    background: linear-gradient(134.07deg, #8f82f5 1.09%, #bf90d4 103.62%);
    border-radius: 155px;
    width: 200px;
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      width: 190px;
      height: 190px;
    }
  }

  .personalDetails {
    display: flex;
  }

  .logo {
    width: 303px;
    height: 75px;
  }

  .title {
    font-size: 18px;
    color: #000000;
    line-height: 20px;
    font-weight: 400;
    font-family: "Inter", sans-serif;
    text-transform: capitalize;
  }

  .designation {
    color: #8f82f5;
    font-size: 16px;
    line-height: 20px;
    display: block;
    margin-top: 8px;
  }

  .basicDetails {
    span {
      color: #413c3b;
      font-size: 14px;
      margin-left: 18px;
    }
  }

  .badge {
    text-align: center;
    border-radius: 12px;
    padding: 1rem 1rem;
    font-size: 14px;
    min-width: 120px;
    margin-right: 10px;
    margin-bottom: 7px;
    min-width: 0 !important;
    margin-right: 7px !important;
    .badgeText {
      color: #000;
      font-size: 16px;
    }
  }

  .otherDetails {
    color: #413c3b;
    font-size: 16px;
    font-weight: 600;
    margin-right: 7px;
    margin-bottom: 7px;
    font-size: 14px !important;
    font-family: Inter !important;
    padding: 0.5rem 1rem;
    border: 1px solid #8e81f5;
    border-radius: 12px;
    p {
      margin: 0;
      font-weight: 400;
    }

    .heading {
      font-weight: 400;
      font-size: 18px;
      line-height: 1.5;
      color: #8f82f5;
    }
  }

  .resumeButtons {
    display: none;
  }

  @media (max-width: 839px) {
    .resumeHeader .logoLight {
      display: none;
    }
  }

  @media (max-width: 462px) {
    .userAvatar {
      width: 75.29px !important;
      height: 75.29px !important;
    }

    .margint {
      margin-top: 0rem !important ;
    }

    .userInfo {
      margin-left: 5px !important;
      .userDetailItem {
        span {
          font-size: 10px;
          margin-left: 1px !important;
        }
      }
      .heading {
        font-size: 14px !important;
      }
    }
    .resumeTitle {
      font-size: 22px !important;
    }
    .ResumePopup_title__1dt0t {
      font-size: 10px !important;
    }
  }

  @media (max-width: 330px) {
    .userAvatar {
      width: 67.29px !important;
      height: 55.29px !important;
    }
    .resumeTitle {
      font-size: medium !important;
    }
    .userDetailItem {
      font-size: small !important;
    }
    .heading {
      font-size: 14px !important;
    }
  }

  @media (max-width: 686px) {
    .resumeButtons {
      display: flex;
      gap: 10px;
    }
    .imgBorder {
      width: 120px;
      height: 120px;

      img {
        width: 110px;
        height: 110px;
      }
    }

    .title {
      font-size: 18px;
    }
    .heading {
      font-size: 14px !important;
    }

    .designation {
      font-size: 13px;
    }
  }

  @media (max-width: 560px) {
    .userDetailItem {
      font-size: 14px !important;
    }
    .userInfo {
      margin-top: 1rem;
    }

    .personalDetails {
      flex-direction: column;
    }

    .imgBorder {
      margin-bottom: 2rem;
    }

    .badge {
      min-width: 100%;
      line-height: 1;
      margin-bottom: 0.3rem;
      margin-right: 0.3rem !important;
      .badgeText {
        font-size: 11px;
      }
    }

    .subText {
      font-size: 10px;
      margin-top: 4px;
    }

    .badge1Text {
      font-size: 10px;
      margin-top: 4px;
    }

    .badge {
      font-size: 10px;
    }

    .otherDetails {
      margin-right: 10px;
    }
  }
}

.actionBtnsWarper {
  display: flex;
  flex-wrap: wrap;
  margin-top: -1rem;
}
@media (max-width: 363px) {
  .actionBtnsWarper {
    .actionBtns {
      width: 80px;
      height: 28px;
      font-size: xx-small;
      padding: 0px;
    }
  }
}
