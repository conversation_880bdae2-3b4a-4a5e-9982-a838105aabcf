import { useEffect } from "react";
import { connect } from "react-redux";
import { notificationAction } from "../../actions/notification";
import "./notification.scss";
import { htmlParser } from "../../utilities/helpers";

const Message = ({ children }) => (
  <div className="notification-message">{children}</div>
);

const Notification = ({ children, status, className, notificationAction }) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      hideNotification();
    }, 4000);
    return () => clearTimeout(timer);
  }, []);

  const hideNotification = () => {
    notificationAction({
      message: "",
      status: "success"
    });
  };

  return (
    <div
      className={`notification-dialog notification-${status} ${className} animated ${
        children ? "fadeInDown" : "fadeOutUp"
      }`}
      onClick={hideNotification}
      data-testid={`notification-dialog-${status}`}
    >
      {htmlParser(children)}
    </div>
  );
};

Notification.Message = Message;

export default connect(null, { notificationAction })(Notification);
