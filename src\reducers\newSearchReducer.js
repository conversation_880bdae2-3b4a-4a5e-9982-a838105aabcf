import { ActionTypes } from "../actions/ActionsTypes";

const INITIAL_STATE = {
  resumeList: [],
  savedSearch: "",
  selected: {
    CertificationIds: [],
    Countries: [],
    IndusteryIds: [],
    KeywordIds: [],
    Languages: [],
    ProfileIds: [],
    SkillIds: [],
    checked: [],
    Type: "Only20",
    limit: 20,
    page: 1
  },
  defaultList: {
    CertificationIds: [],
    Countries: [],
    IndusteryIds: [],
    KeywordIds: [],
    Languages: [],
    ProfileIds: [],
    SkillIds: []
  },
  collapse: {
    role: true,
    location: true,
    language: false,
    skill: false,
    keyword: false,
    industry: false,
    certification: false,
    date: false
  }
};

export default (state = INITIAL_STATE, action) => {
  switch (action.type) {
    case ActionTypes.NEWSEARCH.RESUME_LIST:
      return {
        ...state,
        resumeList: action.payload
      };
    case ActionTypes.NEWSEARCH.SELECTED_RESUME:
      return { ...state, selected: action.payload };
    case ActionTypes.NEWSEARCH.SEARCH_COLLAPSED:
      return { ...state, collapse: action.payload };
    case ActionTypes.NEWSEARCH.DEFAULT_LIST:
      return { ...state, defaultList: action.payload };
    case ActionTypes.NEWSEARCH.SAVED_SEARCH:
      return { ...state, savedSearch: action.payload };
    default:
      return state;
  }
};
