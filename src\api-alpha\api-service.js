import {
  useMutation,
  useQueries,
  useQuery
} from "@tanstack/react-query";
import { isEmpty } from "lodash";
import { useNavigate } from "react-router-dom";
import { StorageService } from "../api/storage";
const BASE_URL = `${import.meta.env.VITE_API_URL ?? ""}/webapi/api/`;
const STALE_TIME = 5 * 60 * 1000;
const REFETCH_ON_RECONNECT = false;
const REFETCH_ON_WINDOW_FOCUS = false;
// utils/fetchWithInterceptor.js

export const useFetchWithInterceptor = () => {
  const navigate = useNavigate();

  return async (url, options = {}) => {
    const response = await fetch(url, {
      credentials: "include",
      ...options
    });

    if (response.status === 401) {
      StorageService.clearAll();
      navigate("/");

      throw new Error("Unauthorized");
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const error = new Error("Request failed");
      error.status = response.status;
      error.data = errorData;
      throw error;
    }

    return response.json();
  };
};

export const useClientQuery = ({
  queryKeys = [],
  url = "",
  enabled = true,
  staleTime = STALE_TIME,
  refetchOnReconnect = REFETCH_ON_RECONNECT,
  refetchOnWindowFocus = REFETCH_ON_WINDOW_FOCUS
}) => {
  const fetchWithInterceptor = useFetchWithInterceptor();
  return useQuery({
    queryKey: queryKeys?.length ? queryKeys : [url],
    queryFn: () => fetchWithInterceptor(`${BASE_URL}${url}`),
    enabled: enabled && !!url,
    staleTime,
    refetchOnReconnect,
    refetchOnWindowFocus
  });
};

export const useClientQueies = ({
  queryKeys = [],
  urls = [],
  enabled = true,
  staleTime = STALE_TIME,
  refetchOnReconnect = REFETCH_ON_RECONNECT,
  refetchOnWindowFocus = REFETCH_ON_WINDOW_FOCUS
}) => {
  const fetchWithInterceptor = useFetchWithInterceptor();
  return useQueries({
    queries: urls.map(url => ({
      queryKey: [url],
      queryFn: () => fetchWithInterceptor(`${BASE_URL}${url}`),
      enabled: enabled && !!url,

      staleTime,
      refetchOnReconnect,
      refetchOnWindowFocus
    }))
  });
};

export const useGetMutation = ({ queryKeys = [], url = "" }) => {
  const fetchWithInterceptor = useFetchWithInterceptor();
  return useMutation({
    mutationKey: queryKeys?.length ? queryKeys : [url],
    mutationFn: () => fetchWithInterceptor(`${BASE_URL}${url}`),
    onError: e => {
      console.log(e);
    }
  });
};

export const useClientMutation = ({
  queryKeys = [],
  url = "",
  method = "POST"
}) => {
  const fetchWithInterceptor = useFetchWithInterceptor();
  return useMutation({
    mutationKey: queryKeys?.length ? queryKeys : [url],
    mutationFn: ({ suffixUrl, ...data }) => {
      const body = isEmpty(data) ? undefined : JSON.stringify(data);
      return fetchWithInterceptor(`${BASE_URL}${url}${suffixUrl ?? ""}`, {
        method,
        body,
        headers: {
          "Content-Type": "application/json"
        }
      });
    }
  });
};
