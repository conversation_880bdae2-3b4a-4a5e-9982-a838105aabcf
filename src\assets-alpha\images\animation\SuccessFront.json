{"nm": "success 2", "ddd": 0, "h": 850, "w": 800, "meta": {"g": "LottieFiles AE 0.1.20"}, "layers": [{"ty": 5, "nm": "Successful", "sr": 1, "st": -5, "op": 235, "ip": 55, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-0.815, -33.278, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.499, "y": 0}, "i": {"x": 0.577, "y": 0.896}, "s": [400, 418, 0], "t": 55, "ti": [0, 43.167, 0], "to": [0, -43.167, 0]}, {"s": [400, 159, 0], "t": 60}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.06;\nfreq = 2;\ndecay = 4;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 1, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [0], "t": 55}, {"s": [100], "t": 60}], "ix": 11}}, "ef": [], "t": {"m": {"a": {"a": 0, "k": [0, 0], "ix": 2}, "g": 1}, "p": {}, "a": [], "d": {"k": [{"s": {"f": "HelveticaRounded-Black", "s": 88, "t": "Successful", "fc": [0.2039, 0.7804, 0.349], "lh": 105.6, "tr": 0, "j": 2, "ca": 0}, "t": 0}]}}, "ind": 1}, {"ty": 4, "nm": "Shape Layer 3", "sr": 1, "st": 31, "op": 261, "ip": 31, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [77, 2, 0], "ix": 1}, "s": {"a": 0, "k": [30.158, 30.158, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-44, 120, 0], "ix": 2}, "r": {"a": 1, "k": [{"o": {"x": 0.865, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [-225], "t": 43}, {"s": [-180], "t": 60}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.06;\nfreq = 2;\ndecay = 4;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Shape 1", "ix": 1, "cix": 2, "np": 3, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-56, 6], [30, 92], [210, -88]]}, "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 70, "ix": 5}, "c": {"a": 0, "k": [0.2039, 0.7804, 0.349], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "tm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Trim", "nm": "Trim Paths 1", "ix": 2, "e": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [0], "t": 43}, {"s": [100], "t": 55}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "s": {"a": 0, "k": 0, "ix": 1}, "m": 1}], "ind": 2, "parent": 4}, {"ty": 4, "nm": "Shape Layer 1", "sr": 1, "st": 0, "op": 61, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-44, 120, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.243, "y": 0}, "i": {"x": 0.322, "y": 1}, "s": [50, 50, 100], "t": 24}, {"o": {"x": 1, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [115, 115, 100], "t": 48}, {"s": [100, 100, 100], "t": 60}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.06;\nfreq = 6;\ndecay = 10;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [400, 418, 0], "ix": 2}, "r": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [0], "t": 15}, {"s": [270], "t": 48}], "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 48}, {"s": [0], "t": 60}], "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Ellipse 1", "ix": 1, "cix": 2, "np": 3, "it": [{"ty": "el", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Ellipse", "nm": "Ellipse Path 1", "d": 1, "p": {"a": 0, "k": [0, 0], "ix": 3}, "s": {"a": 0, "k": [246.094, 246.094], "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"o": {"x": 0.863, "y": 0}, "i": {"x": 0.206, "y": 1}, "s": [60], "t": 24}, {"s": [4], "t": 43}], "ix": 5}, "c": {"a": 0, "k": [0.5569, 0.5059, 0.9608], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [-44, 120], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "tm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Trim", "nm": "Trim Paths 1", "ix": 2, "e": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "s": {"a": 1, "k": [{"o": {"x": 0.84, "y": 0}, "i": {"x": 0.273, "y": 1}, "s": [0], "t": 24}, {"s": [100], "t": 48}], "ix": 1}, "m": 1}], "ind": 3}, {"ty": 4, "nm": "Shape Layer 2", "sr": 1, "st": 0, "op": 240, "ip": 48, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-44, 120, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 1, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [115, 115, 100], "t": 48}, {"s": [100, 100, 100], "t": 60}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\namp = 0.06;\nfreq = 6;\ndecay = 6;\n$bm_rt = n = 0;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [400, 418, 0], "ix": 2}, "r": {"a": 0, "k": 540, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [0], "t": 48}, {"s": [100], "t": 60}], "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Ellipse 1", "ix": 1, "cix": 2, "np": 3, "it": [{"ty": "el", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Ellipse", "nm": "Ellipse Path 1", "d": 1, "p": {"a": 0, "k": [0, 0], "ix": 3}, "s": {"a": 0, "k": [246.094, 246.094], "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 6, "ix": 5}, "c": {"a": 0, "k": [0.2039, 0.7804, 0.349], "ix": 3}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [1, 1, 1], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [-44, 120], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "tm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Trim", "nm": "Trim Paths 1", "ix": 2, "e": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "s": {"a": 0, "k": 100, "ix": 1}, "m": 1}], "ind": 4}, {"ty": 4, "nm": "Shape Layer 4", "sr": 1, "st": -3, "op": 237, "ip": 45, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [-44, 120, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [127, 127, 100], "t": 45}, {"s": [170, 170, 100], "t": 60}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [400, 418, 0], "ix": 2}, "r": {"a": 0, "k": 270, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [60], "t": 45}, {"s": [0], "t": 60}], "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Ellipse 1", "ix": 1, "cix": 2, "np": 3, "it": [{"ty": "el", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Ellipse", "nm": "Ellipse Path 1", "d": 1, "p": {"a": 0, "k": [0, 0], "ix": 3}, "s": {"a": 0, "k": [246.094, 246.094], "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 30, "ix": 5}, "c": {"a": 0, "k": [1, 1, 1], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [-44, 120], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "tm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Trim", "nm": "Trim Paths 1", "ix": 2, "e": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "s": {"a": 0, "k": 100, "ix": 1}, "m": 1}], "ind": 5}, {"ty": 0, "nm": "spark", "sr": 1, "st": -2, "op": 238, "ip": 60, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [400, 700, 0], "ix": 1}, "s": {"a": 0, "k": [90, 90, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [400, 418, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "w": 800, "h": 1400, "refId": "comp_0", "ind": 6}], "v": "5.5.7", "fr": 24, "op": 120, "ip": 0, "fonts": {"list": [{"ascent": 74.6078491210938, "fClass": "", "fFamily": "Helvetica Rounded Black", "fStyle": "Regular", "fName": "HelveticaRounded-Black", "fPath": "", "fWeight": "", "origin": 0}]}, "assets": [{"nm": "", "id": "comp_0", "layers": [{"ty": 4, "nm": "Group 1", "sr": 1, "st": 16, "op": 256, "ip": 59, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [775.158, 375.931, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 59}, {"o": {"x": 0.76, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 69}, {"s": [0, 0, 100], "t": 77}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [400, 700, 0], "t": 59, "ti": [26.317, 175.061, 0], "to": [-34.61, -37.71, 0]}, {"s": [192.34, 473.739, 0], "t": 77}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[4.977, 0], [0, 4.974], [6.751, 0], [0, 4.977], [-4.976, 0], [0, 6.742], [-4.977, 0], [0, -4.977], [-6.751, 0], [0, -4.976], [4.974, 0], [0, -6.745]], "o": [[-4.977, 0], [0, -6.745], [-4.976, 0], [0, -4.976], [6.751, 0], [0, -4.977], [4.977, 0], [0, 6.742], [4.974, 0], [0, 4.977], [-6.751, 0], [0, 4.974]], "v": [[-0.001, 30.248], [-9.01, 21.239], [-21.251, 9.004], [-30.26, -0.002], [-21.251, -9.01], [-9.01, -21.239], [-0.001, -30.248], [9.005, -21.239], [21.25, -9.01], [30.26, -0.002], [21.25, 9.004], [9.005, 21.239]]}, "ix": 2}}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.1412, 0.7922, 1], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [775.158, 375.931], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 1}, {"ty": 4, "nm": "Group 2", "sr": 1, "st": 16, "op": 256, "ip": 59, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [962.158, 269.931, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 59}, {"o": {"x": 0.76, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 69}, {"s": [0, 0, 100], "t": 77}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [400, 700, 0], "t": 59, "ti": [-38.037, 194.374, 0], "to": [49.639, -26.675, 0]}, {"s": [697.832, 539.953, 0], "t": 77}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 2", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[10.818, -6.162], [-6.161, -10.815], [-10.814, 6.161], [6.162, 10.818]], "o": [[10.818, 6.161], [6.162, -10.815], [-10.814, -6.162], [-6.161, 10.818]], "v": [[-25.982, -0.002], [-0.003, 25.977], [25.977, -0.002], [-0.003, -25.982]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 2", "ix": 2, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[4.977, 0], [0, 4.974], [28.149, 0], [0, 4.977], [-4.977, 0], [0, 28.149], [-4.976, 0], [0, -4.977], [-28.153, 0], [0, -4.977], [4.974, 0], [0, -28.153]], "o": [[-4.976, 0], [0, -28.153], [-4.977, 0], [0, -4.977], [28.149, 0], [0, -4.977], [4.977, 0], [0, 28.149], [4.974, 0], [0, 4.977], [-28.153, 0], [0, 4.974]], "v": [[-0.003, 69.07], [-9.011, 60.06], [-60.061, 9.005], [-69.07, -0.002], [-60.061, -9.01], [-9.011, -60.06], [-0.003, -69.07], [9.003, -60.06], [60.06, -9.01], [69.069, -0.002], [60.06, 9.005], [9.003, 60.06]]}, "ix": 2}}, {"ty": "mm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Merge", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.9922, 0.7765, 0.0039], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [962.158, 269.931], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 2}, {"ty": 4, "nm": "Group 3", "sr": 1, "st": 16, "op": 256, "ip": 59, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [684.158, 369.93, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 59}, {"o": {"x": 0.76, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 69}, {"s": [0, 0, 100], "t": 77}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [400, 700, 0], "t": 59, "ti": [98.774, 95.751, 0], "to": [-53.224, -13.134, 0]}, {"s": [80.653, 621.195, 0], "t": 77}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 3", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.138, -1.003], [-1.003, -1.138], [-1.138, 1.003], [1.003, 1.138]], "o": [[1.138, 1.003], [1.003, -1.138], [-1.138, -1.003], [-1.003, 1.138]], "v": [[-3.218, -0.001], [-0.001, 3.216], [3.216, -0.001], [-0.001, -3.218]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 2", "ix": 2, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[4.977, 0], [0, 4.974], [9.936, 0], [0, 4.977], [-4.976, 0], [0, 9.936], [-4.976, 0], [0, -4.976], [-9.936, 0], [0, -4.977], [4.973, 0], [0, -9.935]], "o": [[-4.976, 0], [0, -9.935], [-4.976, 0], [0, -4.977], [9.936, 0], [0, -4.976], [4.977, 0], [0, 9.936], [4.973, 0], [0, 4.977], [-9.936, 0], [0, 4.974]], "v": [[-0.001, 36.037], [-9.01, 27.028], [-27.029, 9.005], [-36.036, -0.001], [-27.029, -9.01], [-9.01, -27.028], [-0.001, -36.037], [9.005, -27.028], [27.029, -9.01], [36.036, -0.001], [27.029, 9.005], [9.005, 27.028]]}, "ix": 2}}, {"ty": "mm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Merge", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.9922, 0.7765, 0.0039], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [684.158, 369.93], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 3}, {"ty": 4, "nm": "Group 4", "sr": 1, "st": 16, "op": 256, "ip": 59, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [594.158, 457.93, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 59}, {"o": {"x": 0.76, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 69}, {"s": [0, 0, 100], "t": 77}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [400, 700, 0], "t": 59, "ti": [41.383, 101.468, 0], "to": [5.026, -43.345, 0]}, {"s": [430.158, 439.93, 0], "t": 77}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 4", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.138, -1.003], [-1.003, -1.138], [-1.138, 1.003], [1.003, 1.138]], "o": [[1.138, 1.003], [1.003, -1.138], [-1.138, -1.003], [-1.003, 1.138]], "v": [[-3.218, -0.001], [-0.001, 3.216], [3.216, -0.001], [-0.001, -3.218]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 2", "ix": 2, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[4.977, 0], [0, 4.974], [9.936, 0], [0, 4.977], [-4.976, 0], [0, 9.936], [-4.976, 0], [0, -4.976], [-9.936, 0], [0, -4.977], [4.973, 0], [0, -9.935]], "o": [[-4.976, 0], [0, -9.935], [-4.976, 0], [0, -4.977], [9.936, 0], [0, -4.976], [4.977, 0], [0, 9.936], [4.973, 0], [0, 4.977], [-9.936, 0], [0, 4.974]], "v": [[-0.001, 36.037], [-9.01, 27.028], [-27.029, 9.005], [-36.036, -0.001], [-27.029, -9.01], [-9.01, -27.028], [-0.001, -36.037], [9.005, -27.028], [27.029, -9.01], [36.036, -0.001], [27.029, 9.005], [9.005, 27.028]]}, "ix": 2}}, {"ty": "mm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Merge", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.9922, 0.7765, 0.0039], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [594.158, 457.93], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 4}, {"ty": 4, "nm": "Group 5", "sr": 1, "st": 16, "op": 256, "ip": 59, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [841.158, 531.931, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 59}, {"o": {"x": 0.76, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 69}, {"s": [0, 0, 100], "t": 77}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [400, 700, 0], "t": 59, "ti": [36.63, 115.231, 0], "to": [-44.795, -30.465, 0]}, {"s": [131.232, 517.209, 0], "t": 77}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 5", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[1.138, -1.003], [-1.003, -1.138], [-1.138, 1.003], [1.003, 1.138]], "o": [[1.138, 1.003], [1.003, -1.138], [-1.138, -1.003], [-1.003, 1.138]], "v": [[-3.218, -0.002], [-0.001, 3.216], [3.216, -0.002], [-0.001, -3.219]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 2", "ix": 2, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[4.977, 0], [0, 4.973], [9.936, 0], [0, 4.977], [-4.976, 0], [0, 9.936], [-4.976, 0], [0, -4.976], [-9.936, 0], [0, -4.977], [4.973, 0], [0, -9.936]], "o": [[-4.976, 0], [0, -9.936], [-4.976, 0], [0, -4.977], [9.936, 0], [0, -4.976], [4.977, 0], [0, 9.936], [4.973, 0], [0, 4.977], [-9.936, 0], [0, 4.973]], "v": [[-0.001, 36.037], [-9.01, 27.029], [-27.029, 9.005], [-36.036, -0.002], [-27.029, -9.01], [-9.01, -27.029], [-0.001, -36.037], [9.005, -27.029], [27.029, -9.01], [36.036, -0.002], [27.029, 9.005], [9.005, 27.029]]}, "ix": 2}}, {"ty": "mm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Merge", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.2039, 0.7804, 0.349], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [841.158, 531.931], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 5}, {"ty": 4, "nm": "Group 6", "sr": 1, "st": 16, "op": 256, "ip": 59, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [581.172, 695.884, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 59}, {"o": {"x": 0.76, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 69}, {"s": [0, 0, 100], "t": 77}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [400, 700, 0], "t": 59, "ti": [-109.69, -13.854, 0], "to": [22.029, 28.147, 0]}, {"s": [532.172, 868.884, 0], "t": 77}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 6", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.457, 2.452], [0, 0], [2.434, 2.434], [0, 0], [2.434, -2.44], [0, 0], [0, -1.77], [-1.176, -1.176]], "o": [[2.63, 2.499], [0, 0], [2.434, -2.431], [0, 0], [-2.434, -2.44], [0, 0], [-1.176, 1.173], [0, 1.769], [0, 0]], "v": [[-5.865, 30.797], [3.355, 30.866], [28.431, 5.808], [28.431, -3.327], [3.355, -28.385], [-5.795, -28.385], [-30.871, -3.323], [-32.695, 1.241], [-30.871, 5.812]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 2", "ix": 2, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[6.275, 0], [4.806, 4.64], [0, 0], [0, 0], [0, 6.581], [-4.581, 4.578], [0, 0], [-9.548, -9.543], [0, 0], [9.542, -9.549], [0, 0]], "o": [[-6.224, 0], [0, 0], [0, 0], [-4.584, -4.587], [0, -6.583], [0, 0], [9.549, -9.537], [0, 0], [9.542, 9.543], [0, 0], [-4.706, 4.705]], "v": [[-1.14, 50.673], [-18.419, 43.718], [-18.531, 43.611], [-43.607, 18.557], [-50.713, 1.241], [-43.61, -16.068], [-18.534, -41.13], [16.094, -41.13], [41.171, -16.068], [41.171, 18.551], [16.091, 43.611]]}, "ix": 2}}, {"ty": "mm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Merge", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.9922, 0.7765, 0.0039], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [582.365, 694.69], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 6}, {"ty": 4, "nm": "Group 7", "sr": 1, "st": 16, "op": 256, "ip": 59, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [715.172, 231.884, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 59}, {"o": {"x": 0.76, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 69}, {"s": [0, 0, 100], "t": 77}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [400, 700, 0], "t": 59, "ti": [70.673, -79.155, 0], "to": [-10.084, 49.187, 0]}, {"s": [339.498, 995.123, 0], "t": 77}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 7", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.457, 2.452], [0, 0], [2.434, 2.434], [0, 0], [2.434, -2.44], [0, 0], [0, -1.772], [-1.176, -1.176]], "o": [[2.63, 2.499], [0, 0], [2.434, -2.431], [0, 0], [-2.434, -2.44], [0, 0], [-1.176, 1.173], [0, 1.768], [0, 0]], "v": [[-5.865, 30.796], [3.355, 30.866], [28.431, 5.809], [28.431, -3.327], [3.355, -28.385], [-5.795, -28.385], [-30.871, -3.324], [-32.695, 1.243], [-30.871, 5.811]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 2", "ix": 2, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[6.275, 0], [4.806, 4.64], [0, 0], [0, 0], [0, 6.58], [-4.581, 4.577], [0, 0], [-9.548, -9.543], [0, 0], [9.542, -9.549], [0, 0]], "o": [[-6.224, 0], [0, 0], [0, 0], [-4.584, -4.587], [0, -6.584], [0, 0], [9.549, -9.537], [0, 0], [9.542, 9.542], [0, 0], [-4.706, 4.705]], "v": [[-1.14, 50.673], [-18.419, 43.717], [-18.531, 43.611], [-43.607, 18.556], [-50.713, 1.243], [-43.61, -16.069], [-18.534, -41.131], [16.094, -41.131], [41.171, -16.069], [41.171, 18.551], [16.091, 43.611]]}, "ix": 2}}, {"ty": "mm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Merge", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.9922, 0.7765, 0.0039], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [716.365, 230.69], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 7}, {"ty": 4, "nm": "Group 8", "sr": 1, "st": 16, "op": 256, "ip": 59, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [936.178, 627.884, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 59}, {"o": {"x": 0.76, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 69}, {"s": [0, 0, 100], "t": 77}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [400, 700, 0], "t": 59, "ti": [188.364, 9.547, 0], "to": [-44.84, 1.308, 0]}, {"s": [130.961, 707.849, 0], "t": 77}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 8", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.46, 2.452], [0, 0], [2.435, 2.434], [0, 0], [2.434, -2.44], [0, 0], [0, -1.77], [-1.176, -1.176]], "o": [[2.639, 2.499], [0, 0], [2.435, -2.431], [0, 0], [-2.434, -2.44], [0, 0], [-1.176, 1.173], [0, 1.769], [0, 0]], "v": [[-5.865, 30.797], [3.354, 30.866], [28.43, 5.808], [28.43, -3.327], [3.354, -28.385], [-5.796, -28.385], [-30.872, -3.323], [-32.695, 1.241], [-30.872, 5.812]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 2", "ix": 2, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[6.275, 0], [4.806, 4.64], [0, 0], [0, 0], [0, 6.581], [-4.581, 4.578], [0, 0], [-9.548, -9.543], [0, 0], [9.543, -9.549], [0, 0]], "o": [[-6.224, 0], [0, 0], [0, 0], [-4.584, -4.587], [0, -6.583], [0, 0], [9.548, -9.537], [0, 0], [9.543, 9.543], [0, 0], [-4.709, 4.705]], "v": [[-1.141, 50.673], [-18.419, 43.718], [-18.531, 43.611], [-43.607, 18.557], [-50.714, 1.241], [-43.611, -16.068], [-18.534, -41.13], [16.091, -41.13], [41.17, -16.068], [41.17, 18.551], [16.091, 43.611]]}, "ix": 2}}, {"ty": "mm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Merge", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.9922, 0.7765, 0.0039], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [937.371, 626.69], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 8}, {"ty": 4, "nm": "Group 9", "sr": 1, "st": 16, "op": 256, "ip": 59, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [1210.183, 446.882, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 59}, {"o": {"x": 0.76, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 69}, {"s": [0, 0, 100], "t": 77}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [400, 700, 0], "t": 59, "ti": [-161.709, 26.625, 0], "to": [51.948, 8.358, 0]}, {"s": [711.688, 750.148, 0], "t": 77}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 9", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.461, 2.464], [0, 0], [2.434, 2.434], [0, 0], [2.431, -2.44], [0, 0], [0, -1.772], [-1.176, -1.176]], "o": [[2.627, 2.499], [0, 0], [2.434, -2.431], [0, 0], [-2.441, -2.44], [0, 0], [-1.176, 1.173], [0, 1.768], [0, 0]], "v": [[-5.865, 30.798], [3.352, 30.868], [28.431, 5.81], [28.431, -3.325], [3.355, -28.384], [-5.795, -28.384], [-30.872, -3.322], [-32.695, 1.244], [-30.872, 5.813]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 2", "ix": 2, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[6.276, 0], [4.807, 4.64], [0, 0], [0, 0], [0, 6.58], [-4.581, 4.577], [0, 0], [-9.549, -9.546], [0, 0], [9.543, -9.549], [0, 0]], "o": [[-6.223, 0], [0, 0], [0, 0], [-4.584, -4.587], [0, -6.584], [0, 0], [9.551, -9.546], [0, 0], [9.543, 9.542], [0, 0], [-4.71, 4.705]], "v": [[-1.141, 50.675], [-18.42, 43.719], [-18.531, 43.613], [-43.607, 18.558], [-50.714, 1.244], [-43.61, -16.067], [-18.534, -41.129], [16.091, -41.126], [41.17, -16.067], [41.17, 18.552], [16.091, 43.613]]}, "ix": 2}}, {"ty": "mm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Merge", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.9922, 0.7765, 0.0039], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [1211.376, 445.688], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 9}, {"ty": 4, "nm": "Group 10", "sr": 1, "st": 16, "op": 256, "ip": 59, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [793.158, 703.896, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 59}, {"o": {"x": 0.76, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 69}, {"s": [0, 0, 100], "t": 77}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [400, 700, 0], "t": 59, "ti": [97.42, 11.358, 0], "to": [-62.614, 110.25, 0]}, {"s": [86.197, 828.475, 0], "t": 77}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 10", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.915, 0.915], [0, 0], [0, 0.4], [0.581, 0.58], [0, 0], [0.898, -0.897], [0, 0], [0, -0.401], [-0.584, -0.58]], "o": [[1.029, 0.956], [0, 0], [0.587, -0.583], [0, -0.401], [0, 0], [-0.898, -0.891], [0, 0], [-0.584, 0.58], [0, 0.4], [0, 0]], "v": [[-1.825, 21.292], [1.753, 21.362], [20.348, 2.772], [21.023, 1.033], [20.354, -0.705], [1.75, -19.298], [-1.749, -19.298], [-20.35, -0.708], [-21.022, 1.033], [-20.35, 2.772]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 2", "ix": 2, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[5.255, 0], [4.011, 3.871], [0, 0], [0, 0], [0, 5.505], [-3.836, 3.836], [0, 0], [-7.988, -7.976], [0, 0], [0, -5.507], [3.842, -3.836], [0, 0]], "o": [[-5.2, 0], [0, 0], [0, 0], [-3.836, -3.83], [0, -5.507], [0, 0], [7.988, -7.982], [0, 0], [3.836, 3.833], [0, 5.505], [0, 0], [-3.947, 3.942]], "v": [[0.055, 40.025], [-14.373, 34.219], [-14.488, 34.107], [-33.089, 15.515], [-39.039, 1.033], [-33.089, -13.453], [-14.488, -32.043], [14.483, -32.046], [33.087, -13.453], [39.04, 1.033], [33.081, 15.521], [14.489, 34.107]]}, "ix": 2}}, {"ty": "mm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Merge", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.2039, 0.7804, 0.349], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [793.158, 702.898], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 10}, {"ty": 4, "nm": "Group 11", "sr": 1, "st": 16, "op": 256, "ip": 59, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [581.156, 567.93, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 59}, {"o": {"x": 0.76, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 69}, {"s": [0, 0, 100], "t": 77}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [400, 700, 0], "t": 59, "ti": [-5.655, 46.971, 0], "to": [134.568, -54.706, 0]}, {"s": [573.156, 503.93, 0], "t": 77}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 11", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[12.021, 0], [0, -12.012], [-12.024, 0], [0, 12.015]], "o": [[-12.024, 0], [0, 12.015], [12.021, 0], [0, -12.012]], "v": [[0.001, -21.787], [-21.803, -0.001], [0.001, 21.785], [21.805, -0.001]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 2", "ix": 2, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[21.956, 0], [0, 21.951], [-21.957, 0], [0, -21.947]], "o": [[-21.957, 0], [0, -21.947], [21.956, 0], [0, 21.951]], "v": [[0.001, 39.804], [-39.821, -0.001], [0.001, -39.804], [39.822, -0.001]]}, "ix": 2}}, {"ty": "mm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Merge", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.1412, 0.7922, 1], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [581.156, 567.93], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 11}, {"ty": 4, "nm": "Group 12", "sr": 1, "st": 16, "op": 256, "ip": 59, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [965.446, 443.546, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 59}, {"o": {"x": 0.76, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 69}, {"s": [0, 0, 100], "t": 77}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [400, 700, 0], "t": 59, "ti": [-9.867, 115.403, 0], "to": [-10.759, -34.076, 0]}, {"s": [335.446, 495.546, 0], "t": 77}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 12", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[2.059, 0], [0, -2.058], [-2.064, 0], [0, 2.059]], "o": [[-2.064, 0], [0, 2.059], [2.059, 0], [0, -2.058]], "v": [[0.003, -3.735], [-3.739, -0.001], [0.003, 3.735], [3.739, -0.001]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 2", "ix": 2, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[11.994, 0], [0, 11.995], [-12, 0], [0, -11.994]], "o": [[-12, 0], [0, -11.994], [11.994, 0], [0, 11.995]], "v": [[0.003, 21.752], [-21.757, -0.001], [0.003, -21.752], [21.757, -0.001]]}, "ix": 2}}, {"ty": "mm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Merge", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.1412, 0.7922, 1], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [965.446, 443.546], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 12}, {"ty": 4, "nm": "Group 13", "sr": 1, "st": 16, "op": 256, "ip": 59, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [1109.157, 578.929, 0], "ix": 1}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 59}, {"o": {"x": 0.76, "y": 0}, "i": {"x": 0.833, "y": 0.833}, "s": [30, 30, 100], "t": 69}, {"s": [0, 0, 100], "t": 77}], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0, "y": 1}, "s": [400, 700, 0], "t": 59, "ti": [-132.766, 49.693, 0], "to": [21.9, 68.907, 0]}, {"s": [735.084, 845.977, 0], "t": 77}], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 13", "ix": 1, "cix": 2, "np": 4, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-2.229, 2.229], [0, 0], [0, 1.531], [1.073, 1.071], [0, 0], [2.223, -2.231], [0, 0], [0, -1.528], [-1.073, -1.07]], "o": [[2.229, 2.229], [0, 0], [1.073, -1.07], [0, -1.528], [0, 0], [-2.232, -2.231], [0, 0], [-1.073, 1.071], [0, 1.531], [0, 0]], "v": [[-4.048, 28.978], [4.046, 28.978], [27.844, 5.192], [29.51, 1.157], [27.844, -2.876], [4.043, -26.662], [-4.045, -26.662], [-27.846, -2.876], [-29.508, 1.157], [-27.846, 5.192]]}, "ix": 2}}, {"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 2", "ix": 2, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[6.073, 0], [4.628, 4.616], [0, 0], [0, 0], [0, 6.349], [-4.475, 4.476], [0, 0], [-9.249, -9.244], [0, 0], [0, -6.343], [4.475, -4.475], [0, 0]], "o": [[-6.079, 0], [0, 0], [0, 0], [-4.475, -4.475], [0, -6.343], [0, 0], [9.261, -9.25], [0, 0], [4.475, 4.476], [0, 6.349], [0, 0], [-4.627, 4.622]], "v": [[-0.001, 48.656], [-16.781, 41.729], [-16.784, 41.723], [-40.585, 17.934], [-47.527, 1.157], [-40.585, -15.621], [-16.784, -39.406], [16.782, -39.406], [40.583, -15.621], [47.527, 1.157], [40.583, 17.934], [16.782, 41.723]]}, "ix": 2}}, {"ty": "mm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Merge", "nm": "Merge Paths 1", "mm": 1}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [0.1412, 0.7922, 1], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [1109.157, 577.772], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 13}]}]}