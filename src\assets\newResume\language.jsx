
const Language = props => {
  return (
    <svg
      width="35"
      height="35"
      viewBox="0 0 35 35"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M29.9282 32.5002C25.8958 32.5002 21.8557 32.5002 17.8232 32.5002C17.5113 32.4013 17.1765 32.3405 16.8874 32.1959C15.7994 31.6557 15.2668 30.7427 15.2668 29.5329C15.2592 26.4059 15.2668 23.2788 15.2668 20.1518C15.2668 20.0376 15.2668 19.9235 15.2668 19.7866C15.0994 19.7866 14.9777 19.7866 14.8484 19.7866C11.7593 19.7866 8.67031 19.7866 5.58129 19.7866C4.56937 19.7866 3.70962 19.4518 3.13899 18.6072C2.8727 18.2192 2.75096 17.7323 2.56836 17.2834C2.56836 13.2129 2.56836 9.13476 2.56836 5.06425C2.58358 5.01099 2.61401 4.95773 2.62162 4.90447C2.76618 4.2121 3.09334 3.64147 3.64115 3.19257C4.052 2.8502 4.53894 2.70564 5.02588 2.55347C9.14204 2.55347 13.2506 2.55347 17.3667 2.55347C17.633 2.64477 17.9069 2.70564 18.158 2.82737C19.2612 3.36757 19.8167 4.27297 19.8167 5.49793C19.8243 8.62499 19.8167 11.7521 19.8167 14.8867C19.8167 15.0009 19.8167 15.115 19.8167 15.2519C19.9612 15.2519 20.0753 15.2519 20.1971 15.2519C23.3089 15.2519 26.4132 15.2519 29.525 15.2519C30.5217 15.2519 31.3662 15.6095 31.9369 16.4388C32.2032 16.8269 32.3249 17.3138 32.5151 17.7551C32.5151 21.8256 32.5151 25.9037 32.5151 29.9742C32.4999 30.0275 32.4695 30.0808 32.4618 30.134C32.3173 30.8112 31.9977 31.3742 31.4651 31.8231C31.001 32.2111 30.476 32.3633 29.9282 32.5002ZM15.8374 16.3552C15.8755 16.3323 15.9211 16.3095 15.9516 16.2791C16.3244 15.8302 16.7961 15.5258 17.3591 15.3889C17.7243 15.2976 18.1048 15.2748 18.4852 15.2215C18.4928 15.153 18.5004 15.0769 18.5004 15.0009C18.5004 11.851 18.5004 8.70868 18.5004 5.5588C18.5004 5.37619 18.4852 5.18598 18.4395 5.01099C18.2417 4.27297 17.6406 3.85451 16.7809 3.85451C13.0756 3.85451 9.3779 3.85451 5.6726 3.85451C4.48568 3.85451 3.8694 4.46318 3.8694 5.6501C3.8694 9.34018 3.8694 13.0379 3.8694 16.728C3.8694 17.8768 4.47046 18.4855 5.61173 18.4855C8.72357 18.4931 11.8278 18.5007 14.9397 18.5083C15.0462 18.5083 15.1451 18.5083 15.2896 18.5083C15.1983 17.6866 15.4646 16.9943 15.8603 16.3475C15.8451 16.3475 15.8374 16.3552 15.8374 16.3552ZM16.5831 23.8647C16.5831 25.7287 16.5831 27.5852 16.5831 29.4493C16.5831 29.6395 16.5831 29.8449 16.6363 30.0275C16.8265 30.7731 17.4124 31.1916 18.2797 31.1916C22.0231 31.1916 25.7664 31.1916 29.5022 31.1916C29.6924 31.1916 29.8978 31.1764 30.0804 31.1231C30.7956 30.9329 31.2141 30.3318 31.2141 29.5101C31.2141 25.7972 31.2141 22.0843 31.2141 18.3714C31.2141 17.1693 30.5978 16.553 29.4033 16.5454C25.7208 16.5454 22.0383 16.553 18.3558 16.5378C17.2374 16.5301 16.5602 17.2606 16.5755 18.3105C16.6059 20.167 16.5831 22.0158 16.5831 23.8647Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.5"
      />
      <path
        d="M23.2631 2.56104C23.9783 2.69799 24.7087 2.77407 25.4087 2.97189C27.4021 3.5273 29.0379 4.64574 30.3313 6.25873C31.7541 8.0391 32.4464 10.0858 32.4769 12.3683C32.4845 12.9846 32.2562 13.228 31.6475 13.2356C30.8182 13.2433 29.9889 13.2356 29.1596 13.2356C28.8553 13.2356 28.6042 13.1367 28.4748 12.84C28.3683 12.5889 28.3911 12.3302 28.5966 12.1705C28.764 12.0411 29.015 11.9574 29.2281 11.9422C29.7835 11.9118 30.3389 11.9346 30.8943 11.9346C30.9856 11.9346 31.0845 11.927 31.191 11.9194C31.1226 10.489 30.7269 9.17275 29.9889 7.97823C28.7107 5.90113 26.8695 4.58487 24.488 4.05989C23.9707 3.94577 23.4305 3.92294 22.8979 3.86968C22.4338 3.82403 22.2055 3.69469 22.1218 3.38274C22.0305 3.06319 22.1675 2.81972 22.5707 2.56864C22.7914 2.56104 23.0272 2.56104 23.2631 2.56104Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.5"
      />
      <path
        d="M11.7976 32.5003C11.4552 32.447 11.1052 32.4166 10.7629 32.3481C7.28581 31.6862 4.8359 29.7308 3.38269 26.52C2.82727 25.2799 2.59902 23.956 2.60663 22.5941C2.61424 22.0996 2.8501 21.8485 3.33704 21.8485C4.19679 21.8409 5.04893 21.8409 5.90868 21.8485C6.34997 21.8485 6.63149 22.0996 6.63909 22.48C6.6467 22.8604 6.35758 23.1343 5.90868 23.1419C5.24675 23.1495 4.58482 23.1419 3.83919 23.1419C3.94571 23.7506 3.99897 24.344 4.15875 24.8995C4.90437 27.5472 6.54779 29.4265 9.06618 30.5297C10.0857 30.9786 11.1661 31.1612 12.2769 31.1992C12.7791 31.2221 13.091 31.5796 12.9465 31.9981C12.878 32.1883 12.6497 32.3329 12.4976 32.4927C12.2693 32.5003 12.0335 32.5003 11.7976 32.5003Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.5"
      />
      <path
        d="M20.0903 28.1101C20.1284 27.9884 20.1588 27.8666 20.2045 27.7449C21.2088 25.0135 22.2055 22.2821 23.2098 19.5582C23.3467 19.193 23.5674 19.0028 23.8793 18.9952C24.1761 18.9876 24.4271 19.1778 24.5565 19.5354C25.5912 22.2821 26.626 25.0363 27.6607 27.7829C27.7748 28.0797 27.7901 28.3536 27.5618 28.597C27.3792 28.7873 27.1281 28.8709 26.8999 28.7492C26.7249 28.6579 26.5575 28.4829 26.4814 28.3003C26.2379 27.7373 26.0325 27.1591 25.8347 26.5808C25.7662 26.3906 25.6825 26.3069 25.4619 26.3145C24.4424 26.3297 23.4152 26.3297 22.3957 26.3145C22.175 26.3145 22.0913 26.3906 22.0229 26.5808C21.8174 27.1591 21.6044 27.7297 21.3838 28.3003C21.2772 28.5894 21.0794 28.7796 20.7523 28.7796C20.4784 28.7796 20.2653 28.6503 20.1588 28.3916C20.1208 28.3079 20.1055 28.209 20.0903 28.1101ZM25.1499 25.0135C24.7239 23.8342 24.3054 22.6853 23.8565 21.468C23.4076 22.7005 22.9968 23.8418 22.5707 25.0135C23.438 25.0135 24.2674 25.0135 25.1499 25.0135Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.5"
      />
      <path
        d="M15.8449 16.3476C15.6395 16.5454 15.3884 16.6139 15.0993 16.6062C13.783 16.5606 12.5733 16.1954 11.4624 15.4878C11.3711 15.4269 11.2798 15.3737 11.1809 15.3128C10.2907 15.9215 9.31686 16.3323 8.25168 16.4997C7.88648 16.553 7.51366 16.5834 7.14846 16.5986C6.71478 16.6139 6.42566 16.3552 6.41805 15.9747C6.41044 15.6095 6.66913 15.3356 7.08759 15.3128C7.84843 15.2672 8.59406 15.1454 9.30164 14.8411C9.57555 14.7194 9.84184 14.5748 10.1157 14.4378C9.81902 14.0346 9.51468 13.6694 9.25599 13.2814C8.67014 12.3912 8.31255 11.4021 8.1832 10.3445C8.15277 10.1239 8.07669 10.063 7.87126 10.0782C7.6354 10.0934 7.40714 10.0858 7.17128 10.0782C6.68434 10.063 6.40283 9.82715 6.40283 9.4239C6.40283 9.02826 6.68434 8.77718 7.17128 8.76958C8.1756 8.76197 9.17991 8.76958 10.1842 8.76958C10.2907 8.76958 10.3896 8.76958 10.5266 8.76958C10.5266 8.06199 10.5266 7.39245 10.5266 6.72291C10.5266 6.57835 10.5342 6.43379 10.557 6.28923C10.6103 5.96968 10.8462 5.77186 11.1581 5.76425C11.4853 5.75664 11.7516 5.95446 11.7896 6.28923C11.8276 6.59357 11.82 6.91312 11.8276 7.21746C11.8353 7.71961 11.8276 8.22938 11.8276 8.76958C11.957 8.76958 12.0635 8.76958 12.1624 8.76958C13.1059 8.76958 14.0569 8.76958 15.0004 8.76958C15.0688 8.76958 15.1373 8.76958 15.2058 8.76958C15.6395 8.7924 15.9286 9.04348 15.9362 9.40868C15.9438 9.7815 15.6547 10.0554 15.1982 10.0706C14.8786 10.0782 14.5591 10.0706 14.2015 10.0706C14.0189 11.7293 13.3645 13.1672 12.2081 14.3922C12.7178 14.7346 13.258 14.9704 13.8363 15.0998C14.2623 15.1987 14.696 15.2519 15.1373 15.29C15.8297 15.3509 16.0731 15.7085 15.8221 16.3476C15.8373 16.3552 15.8449 16.3476 15.8449 16.3476ZM9.49185 10.0934C9.65924 11.4858 10.2147 12.6423 11.1885 13.6314C12.1548 12.627 12.733 11.4782 12.87 10.0934C11.7287 10.0934 10.6331 10.0934 9.49185 10.0934Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.5"
      />
    </svg>
  );
};

export default Language;
