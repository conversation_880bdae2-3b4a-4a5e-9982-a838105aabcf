import { useState } from "react";
import CloseIcon from "../../../assets-alpha/images/svg/close-modal.svg";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";

const PreviewImages = ({ presentForm, setShowPreviwImages }) => {
  const Images = presentForm?.PortfolioImages || [];
  const [selectedIndex, setSelectedIndex] = useState(0);

  const handleImageClick = index => {
    setSelectedIndex(index);
  };

  const showPrevious = () => {
    if (selectedIndex > 0) {
      setSelectedIndex(selectedIndex - 1);
    }
  };

  const showNext = () => {
    if (selectedIndex < Images.length - 1) {
      setSelectedIndex(selectedIndex + 1);
    }
  };

  return (
    <div className="absolute w-full h-full bg-[#343333] z-[999] top-0">
      <p className="hidden lg:flex w-full justify-center text-white !mt-[37px]">
        {Images.length ? `${selectedIndex + 1}/${Images.length}` : "0/0"}
      </p>
      <div className="lg:hidden absolute top-[26px] right-[26px]">
        <img
          src={CloseIcon}
          alt="close"
          onClick={() => {
            setShowPreviwImages(false);
          }}
          className="pointer"
        />
      </div>
      <div className="flex lg:flex-row flex-col pl-0 lg:pl-[64px]">
        <div className="order-3 lg:order-1 flex lg:flex-col w-[calc(100vw - 16px)] lg:w-[14%] lg:max-w-[175px] lg:min-w-[175px]  min-h-[94px] gap-2 h-auto lg:h-[calc(100vh-81px)] overflow-auto lg:ml-0 ml-4 mt-[66px] lg:mt-5">
          <style>
            {`
              div::-webkit-scrollbar {
                display: none;
              }
            `}
          </style>
          {Images?.map((item, index) => (
            <div
              key={index}
              className={`border-[2px] ${
                index === selectedIndex
                  ? "border-[#8E81F5]"
                  : "border-[#EAE5FC] "
              } flex justify-center items-center w-[94px] h-[94px] lg:h-[175px] lg:w-[175px] rounded-[10px] cursor-pointer`}
              onClick={() => handleImageClick(index)}
              style={{ flexShrink: 0 }}
            >
              <img
                className={`rounded-[10px] object-cover transition-all duration-300 ${
                  index === selectedIndex
                    ? "w-[84px] h-[84px] lg:w-[150px] lg:h-[150px]"
                    : "w-[92px] h-[91px] lg:w-[173px] lg:h-[171px]"
                }`}
                src={item?.Image}
                alt=""
              />
            </div>
          ))}
        </div>
        <div className="order-1 lg:order-2 flex lg:justify-start justify-center !w-full lg:w-[61.8%] max-h-[calc(100vh-163px)] h-auto mr-4 lg:ml-[64px] mt-[103px] lg:mt-5 rounded-[10px] overflow-hidden">
          <img
            src={Images[selectedIndex]?.Image}
            alt=""
            className="w-[50%] lg:w-full min-w-[343px] min-h-[343px] lg:h-auto lg:max-h-full h-[343px]  object-cover rounded-[10px]"
          />
        </div>
        <div className="order-2 lg:order-3 flex flex-col ml-0 lg:ml-[64px] lg:mt-0 mt-[40px]">
          <div className="lg:flex hidden justify-end !mr-[55px]">
            <img
              src={CloseIcon}
              alt="close"
              onClick={() => {
                setShowPreviwImages(false);
              }}
              className="pointer"
            />
          </div>
          <div className="flex w-full justify-center lg:justify-start gap-[195px] lg:gap-10 h-full items-center mr-0 lg:mr-[55px]">
            <div
              onClick={showPrevious}
              className={`flex justify-center items-center w-[44px] h-[44px] rounded-[50%] ${
                selectedIndex > 0
                  ? "bg-white cursor-pointer"
                  : "bg-gray-400 cursor-not-allowed"
              }`}
            >
              <LeftOutlined />
            </div>
            <div
              onClick={showNext}
              className={`flex justify-center items-center w-[44px] h-[44px] rounded-[50%] ${
                selectedIndex < Images.length - 1
                  ? "bg-white cursor-pointer"
                  : "bg-gray-400 cursor-not-allowed"
              }`}
            >
              <RightOutlined />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreviewImages;
