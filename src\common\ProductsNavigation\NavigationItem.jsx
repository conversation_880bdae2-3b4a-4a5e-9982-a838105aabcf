import { Fragment } from "react";
import { htmlParser } from "../../utilities/helpers";
import { NavLink } from "react-router-dom";
import "./navigation-item.scss";
import { HelpTooltip, Tooltip } from "../Tooltip/Tooltip";
import NotificationCircle from "../NotificationCircle/NotificationCircle";

const HelpGuideIcon = ({ testId, tooltipHelp }) => (
  <span className={`nav-help-icon-container`}>
    <HelpTooltip testId={testId} content={tooltipHelp} />
  </span>
);

const NavigationToolTip = ({ children, label, isHelpActive }) => (
  <>
    {isHelpActive || window.innerWidth <= 1240 ? (
      <>{children}</>
    ) : (
      <Tooltip followCursor={true} content={label}>
        <div>{children}</div>
      </Tooltip>
    )}
  </>
);

const NavLinkButton = ({
  Id,
  children,
  classIcon,
  className,
  to,
  testId,
  disabled,
  IsActive
}) => (
  <NavLink
    // activeClassName={`${!disabled && IsActive ? "activeBtn" : ""}`}
    to={!disabled ? to : to}
    className={({ isActive }) =>
      `nav-button ${className} ${isActive && !disabled ? "activeBtn" : ""}`
    }
    data-testid={testId}
    disabled={disabled}
  >
    <i className={classIcon} />
    <span>{children}</span>
  </NavLink>
);

const NavigationItem = ({
  Id,
  tooltipLabel,
  isHelpActive,
  Disabled = false,
  CssClass,
  CssClassIcon,
  Url,
  testId,
  menuText,
  isNavShrink,
  tooltipHelp,
  NotificationBadge,
  NotificationCount,
  IsActive
}) => (
  <Fragment key={Id}>
    <NavigationToolTip label={tooltipLabel} isHelpActive={isHelpActive}>
      <NavLinkButton
        disabled={Disabled}
        className={CssClass}
        classIcon={CssClassIcon}
        to={Url}
        testId={testId}
        Id={Id}
        IsActive={IsActive}
      >
        {htmlParser(menuText)}
        {isHelpActive && (
          <HelpGuideIcon
            testId={`${testId}-help`}
            isNavShrink={isNavShrink}
            tooltipHelp={tooltipHelp}
          />
        )}
        {NotificationBadge && NotificationCount > 0 && (
          <NotificationCircle testId={`${testId}-notification`}>
            {NotificationCount > 9 ? "9+" : NotificationCount}
          </NotificationCircle>
        )}
      </NavLinkButton>
    </NavigationToolTip>
  </Fragment>
);

export default NavigationItem;
