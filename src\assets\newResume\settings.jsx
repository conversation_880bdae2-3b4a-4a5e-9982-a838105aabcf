
const Settings = props => {
  return (
    <svg
      width="35"
      height="35"
      viewBox="0 0 35 35"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_1926_3845)">
        <path
          d="M33.6717 18.6236C33.6717 19.6244 33.2896 20.2067 32.5072 20.3704C32.0978 20.4523 31.6793 20.5342 31.2698 20.5797C30.8695 20.6343 30.6785 20.8435 30.5329 21.2165C30.1599 22.1991 29.7505 23.1726 29.3229 24.137C29.1591 24.4918 29.15 24.7739 29.3866 25.0832C29.6413 25.4107 29.8688 25.7474 30.0962 26.084C30.5238 26.739 30.4601 27.4487 29.9051 28.0128C29.2865 28.6405 28.6587 29.2683 28.0219 29.896C27.4396 30.4783 26.739 30.5329 26.0476 30.0689C25.7019 29.8324 25.3562 29.5958 25.0195 29.3411C24.7648 29.15 24.51 29.1227 24.228 29.2865C23.2636 29.8505 22.2355 30.2691 21.1529 30.5693C20.789 30.6694 20.6434 30.9059 20.5888 31.2516C20.5251 31.652 20.4523 32.0523 20.3796 32.4617C20.2431 33.2077 19.6881 33.6808 18.9239 33.6899C18.005 33.6899 17.0861 33.6899 16.1581 33.6899C15.3939 33.6899 14.8389 33.2168 14.7024 32.4708C14.6296 32.0705 14.5477 31.6701 14.4932 31.2607C14.4386 30.8786 14.2293 30.6876 13.8654 30.5511C12.8828 30.1781 11.9093 29.7778 10.9449 29.3411C10.5901 29.1864 10.3172 29.15 9.99874 29.3956C9.66212 29.6504 9.3164 29.8869 8.96157 30.1235C8.31562 30.542 7.61507 30.4783 7.06919 29.9324C6.42324 29.2956 5.77728 28.6496 5.14042 28.0037C4.60364 27.4578 4.53995 26.7481 4.96756 26.1113C5.2223 25.7292 5.48614 25.3652 5.74998 24.9922C5.92285 24.7557 5.93194 24.51 5.78638 24.2644C5.2223 23.2818 4.7947 22.2537 4.49446 21.162C4.39438 20.798 4.13964 20.6616 3.80301 20.607C3.4027 20.5433 2.99329 20.4705 2.59298 20.3977C1.86514 20.2613 1.39205 19.6972 1.38295 18.9602C1.37385 18.0323 1.38295 17.0952 1.38295 16.1672C1.38295 15.4302 1.86514 14.8661 2.58388 14.7297C2.9842 14.6569 3.38451 14.575 3.79392 14.5204C4.19423 14.4658 4.38529 14.2475 4.53085 13.8745C4.90387 12.8919 5.31328 11.9184 5.74998 10.954C5.90465 10.6174 5.93194 10.3444 5.70449 10.0442C5.44975 9.70759 5.2132 9.36186 4.97666 9.01614C4.53995 8.36108 4.61274 7.66054 5.16771 7.09646C5.78638 6.4687 6.41414 5.84094 7.051 5.21318C7.64237 4.6309 8.32472 4.56722 9.02526 5.04031C9.38008 5.27686 9.7349 5.53161 10.0897 5.77725C10.3354 5.95921 10.581 5.96831 10.854 5.81364C11.8183 5.25867 12.8282 4.84016 13.9018 4.54902C14.2748 4.44894 14.4477 4.2124 14.5022 3.84848C14.5659 3.44816 14.6387 3.04785 14.7115 2.63844C14.848 1.89241 15.403 1.41931 16.1672 1.41931C17.0861 1.41931 18.005 1.41931 18.933 1.41931C19.6881 1.41931 20.2431 1.89241 20.3796 2.64754C20.4523 3.04785 20.5342 3.44816 20.5888 3.85757C20.6434 4.22149 20.8344 4.41255 21.1893 4.54902C22.1991 4.94024 23.209 5.36784 24.2007 5.80454C24.5009 5.94102 24.7557 5.95921 25.0286 5.75906C25.3743 5.50431 25.7292 5.24957 26.084 5.01302C26.739 4.57632 27.4487 4.64 28.0037 5.18588C28.6405 5.81364 29.2774 6.4505 29.9051 7.08736C30.4601 7.65144 30.5238 8.35198 30.0871 9.00704C29.8415 9.38006 29.5867 9.73488 29.3229 10.0988C29.15 10.3353 29.1227 10.5719 29.2683 10.8266C29.8415 11.8274 30.2782 12.8828 30.5875 13.9927C30.6603 14.2566 30.824 14.4203 31.1061 14.4658C31.561 14.5386 32.0341 14.5932 32.4708 14.7206C32.7619 14.8025 33.044 14.9753 33.2805 15.1846C33.3715 15.2665 33.3806 15.5758 33.2987 15.6941C33.2168 15.8123 32.9075 15.9124 32.8074 15.8487C32.2251 15.4666 31.5428 15.503 30.915 15.3574C30.2873 15.2119 29.9233 14.8298 29.705 14.2384C29.332 13.2649 28.9499 12.2914 28.5132 11.3543C28.2038 10.6993 28.1947 10.117 28.6223 9.53472C28.8589 9.21629 29.0863 8.88877 29.3047 8.56124C29.5321 8.22461 29.5139 7.96987 29.2228 7.67873C28.6223 7.06917 28.0219 6.4687 27.4123 5.86823C27.103 5.568 26.8573 5.5498 26.5025 5.79545C26.1568 6.032 25.8201 6.28674 25.4653 6.52329C24.9285 6.88721 24.3645 6.9327 23.8004 6.60517C22.8633 6.05929 21.8625 5.66808 20.8163 5.36784C20.0975 5.15859 19.77 4.62181 19.6699 3.91216C19.6153 3.53005 19.5425 3.14793 19.4698 2.77491C19.4152 2.47468 19.215 2.32001 18.9148 2.32001C17.9959 2.31092 17.077 2.31092 16.149 2.32001C15.8306 2.32001 15.6395 2.48378 15.5758 2.80221C15.4848 3.25711 15.403 3.71201 15.3211 4.176C15.2119 4.80377 14.8389 5.20408 14.2475 5.37694C13.1921 5.67717 12.1914 6.08658 11.2361 6.62337C10.6629 6.95089 10.1079 6.8963 9.57114 6.52329C9.22542 6.27764 8.88879 6.03199 8.53397 5.80454C8.18825 5.5771 7.9517 5.59529 7.65146 5.88643C7.0328 6.49599 6.42324 7.10556 5.81367 7.71513C5.54073 7.98806 5.52253 8.25191 5.74089 8.57034C5.98653 8.92516 6.24128 9.27998 6.48692 9.6348C6.85084 10.1625 6.90543 10.7084 6.587 11.2724C6.04112 12.2368 5.63171 13.2558 5.32238 14.3203C5.15862 14.9025 4.7583 15.2665 4.14874 15.3756C3.70294 15.4575 3.25714 15.5394 2.81133 15.6304C2.42012 15.7123 2.28365 15.876 2.28365 16.2581C2.27455 17.1406 2.28365 18.0323 2.28365 18.9148C2.28365 19.2605 2.44741 19.4697 2.79314 19.5334C3.23894 19.6153 3.68474 19.7063 4.13054 19.7791C4.7583 19.8882 5.15862 20.2613 5.33148 20.8526C5.64081 21.908 6.03202 22.9179 6.5779 23.8641C6.89633 24.419 6.85084 24.974 6.49602 25.5017C6.25947 25.8565 5.99563 26.2023 5.74998 26.5662C5.51344 26.9119 5.53163 27.1575 5.82277 27.4487C6.43233 28.0673 7.0419 28.6769 7.66056 29.2774C7.9426 29.5503 8.18825 29.5776 8.51577 29.3593C8.8524 29.1318 9.18902 28.8953 9.52565 28.6587C10.0897 28.2493 10.672 28.1947 11.2816 28.5495C12.2096 29.0863 13.2012 29.4775 14.2293 29.7687C14.9481 29.9779 15.2847 30.5056 15.3757 31.2243C15.4302 31.5974 15.503 31.9613 15.5758 32.3252C15.6304 32.6527 15.8306 32.8256 16.1672 32.8256C17.077 32.8256 17.9868 32.8256 18.8966 32.8256C19.2059 32.8256 19.4061 32.6618 19.4698 32.3525C19.5607 31.8976 19.6517 31.4427 19.7245 30.9787C19.8337 30.3327 20.2249 29.9415 20.8254 29.7687C21.8534 29.4684 22.8451 29.0863 23.7731 28.5495C24.3645 28.2038 24.9467 28.2493 25.5017 28.6496C25.8292 28.8771 26.1477 29.1136 26.4752 29.3411C26.8573 29.6049 27.0939 29.5867 27.4123 29.2683C28.0128 28.6769 28.6041 28.0855 29.1955 27.4851C29.5048 27.1666 29.523 26.921 29.2683 26.548C29.0408 26.2204 28.8134 25.8929 28.5859 25.5745C28.1947 25.0286 28.1401 24.4645 28.4768 23.8823C29.0226 22.927 29.423 21.9262 29.7232 20.8708C29.896 20.2795 30.2964 19.9064 30.9241 19.7973C31.379 19.7154 31.8339 19.6335 32.2979 19.5425C32.6072 19.4788 32.7619 19.2787 32.771 18.9693C32.7801 18.7237 32.771 18.469 32.771 18.2233C32.7801 17.9413 32.9166 17.7684 33.2077 17.7684C33.4989 17.7684 33.6444 17.9322 33.6535 18.2142C33.6808 18.3052 33.6717 18.4599 33.6717 18.6236Z"
          fill={props.color || "white"}
          stroke={props.color || "white"}
          strokeWidth="0.8"
        />
        <path
          d="M26.1021 17.5591C26.1294 22.2628 22.2172 26.1112 17.5136 26.1021C12.8099 26.093 8.95234 22.2446 8.96144 17.5227C8.97054 12.8463 12.819 8.94329 17.5681 8.96149C22.29 8.98878 26.1476 12.8827 26.1021 17.5591ZM23.9913 21.5895C25.8018 18.9875 25.7745 14.575 22.6357 11.8274C19.2877 8.88871 14.5112 9.26172 11.6363 12.628C9.12521 15.5757 9.5892 19.6516 11.0813 21.5622C12.819 19.6971 14.957 18.6599 17.5227 18.6599C20.1065 18.6599 22.2354 19.7062 23.9913 21.5895ZM17.5227 25.2014C17.6682 25.2014 17.8229 25.2105 17.9685 25.2014C18.114 25.1923 18.2596 25.1559 18.4052 25.1468C20.3339 24.9194 21.9352 24.0642 23.2544 22.6358C23.4182 22.4629 23.4364 22.3355 23.2635 22.1536C21.4803 20.2521 19.315 19.3514 16.6947 19.6243C14.7933 19.8245 13.2193 20.6706 11.9092 22.0444C11.5817 22.3901 11.5817 22.3992 11.9183 22.754C13.4377 24.3553 15.3118 25.165 17.5227 25.2014Z"
          fill={props.color || "white"}
          stroke={props.color || "white"}
          strokeWidth="0.8"
        />
        <path
          d="M17.5317 11.3544C19.2057 11.3544 20.5067 12.6554 20.5067 14.3294C20.5067 15.9762 19.1966 17.2954 17.5499 17.3045C15.8758 17.3045 14.5566 16.0034 14.5566 14.3385C14.5566 12.6554 15.8577 11.3544 17.5317 11.3544ZM19.6151 14.3294C19.6151 13.1649 18.7144 12.246 17.5499 12.246C16.358 12.246 15.4573 13.1467 15.4573 14.3294C15.4573 15.4849 16.3762 16.4038 17.5317 16.4038C18.6871 16.4038 19.6151 15.4849 19.6151 14.3294Z"
          fill={props.color || "white"}
          stroke={props.color || "white"}
          strokeWidth="0.8"
        />
      </g>
      <defs>
        <clipPath id="clip0_1926_3845">
          <rect width="34.9909" height="35" fill={props.color || "white"} />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Settings;
