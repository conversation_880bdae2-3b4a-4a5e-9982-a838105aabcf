import { ArrowUpOutlined, LoadingOutlined } from "@ant-design/icons";
import { But<PERSON> } from "antd";
import Title from "antd/es/typography/Title";
import React, { useMemo, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import RightArrow from "../../../assets-alpha/images/svg/arrow-right.svg?react";
import ProfessionalIcon from "../../../assets-alpha/images/view/professional.svg?react";
import EmptyView from "../../../common-alpha/EmptyView/EmptyView";
import JobCard from "./JobCard";
import ResumeDrawer from "./ResumeDetail";
import ShortlistDropdown from "./ShortlistDropdown";
import clsx from "clsx";
import Mask from "../../../common-alpha/Mask/Mask";
import { twMerge } from "tailwind-merge";
import { flatten, valuesIn } from "lodash";
import { useSelector } from "react-redux";
import { useCreateUpdateShortlistApi } from "../findProfessionalApi";
import moment from "moment";

const SearchResults = ({ results = [], loading, filters, savedText }) => {
  const location = useLocation();
  const [openResume, setOpenResume] = useState(false);
  const findProfessional = useSelector(
    state => state.systemLabel.labels?.findProfessional
  );
  const [isShortlistOpen, setIsShortlistOpen] = useState(false);
  const navigate = useNavigate();
  const [selectedResumes, setSelectedResumes] = useState([]);
  const moveToTop = React.useRef(null);
  const handleResumeSelection = resume => {
    setSelectedResumes(resumes =>
      resumes?.find(item => item.ResumeId === resume.ResumeId)
        ? resumes.filter(item => item.ResumeId !== resume.ResumeId)
        : [...resumes, resume]
    );
  };
  const {
    mutate: createShortlist,
    isPending: loadingCreatingShortlist,
    error: errorCreatingShortlist
  } = useCreateUpdateShortlistApi();

  const filteredResults = useMemo(() => {
    return flatten(valuesIn(filters))
      .filter(n => n && n.label)
      .map(n => n.label);
  }, [filters]);
  const handleContinue = () => {
    createShortlist(
      {
        ShortlistName: `Auto shortlist ${moment().format(
          "D MMMM YYYY, H:mm:ss"
        )}`,
        ResumeIdsList: selectedResumes?.map(n => n.ResumeId)
      },
      {
        onSuccess: data => {
          navigate("/active-collaborations", {
            state: {
              newInvitation: true,
              from: location.pathname
            }
          });
        }
      }
    );
  };
  return (
    <div
      className={clsx("mt-[20px] max-md:mt-[8px] max-md:mb-[50px] gap-[8px]", {
        "h-[calc(100vh-270px)] flex justify-center items-center mb-0": !results?.length
      })}
      ref={moveToTop}
    >
      {results?.length > 0 && (
        <>
          <div className="flex items-start justify-between gap-2 sticky top-[-24px] max-md:static z-10 bg-white pt-[12px]">
            <Title className="mb-[12px]" level={4}>
              {/* Search Results {savedText ? ` for saved filters '${savedText}'` : ""}({results?.length}) */}
              {/* findProfessional.filterText="for saved filters @savedText";  */}
              {/* findProfessional.searchResultsHeading="Search Results @filterText (@results)"; */}
              {findProfessional?.searchResultsHeading
                ?.replace(
                  "@filterText",
                  `${
                    savedText
                      ? findProfessional?.filterText?.replace(
                          "@savedText",
                          savedText
                        )
                      : ""
                  }`
                )
                .replace("@results", results?.length)}
              <div className="text-gray-500 text-sm">
                {filteredResults?.join(", ")}
              </div>
            </Title>

            {selectedResumes?.length > 0 && (
              <div className="flex items-center gap-3 max-md:z-10 max-md:p-[12px_16px] max-md:bg-white max-md:absolute max-md:bottom-0 max-md:left-0 max-md:right-0">
                <div className="relative max-md:flex-1">
                  <Button
                    block
                    type="default"
                    className="max-md:!px-0"
                    onClick={() => {
                      setIsShortlistOpen(true);
                    }}
                  >
                    {findProfessional?.addToShortlist} (
                    {selectedResumes?.length})
                  </Button>
                  {isShortlistOpen && (
                    <>
                      <Mask
                        onClick={() => {
                          setIsShortlistOpen(false);
                        }}
                      />
                      <div
                        className={twMerge(`absolute max-md:fixed -left-20 max-md:left-0 -right-20 max-md:right-0 top-full max-md:top-auto 
                        z-100 flex items-center justify-center
                        max-md:bottom-0 max-md:w-full
                        `)}
                      >
                        <ShortlistDropdown
                          selectedResumes={selectedResumes}
                          setIsShortlistOpen={setIsShortlistOpen}
                        />
                      </div>
                    </>
                  )}
                </div>
                <div className="max-md:flex-1">
                  <Button
                    block
                    type="primary"
                    className="max-md:!px-0"
                    icon={<RightArrow className="!text-white" />}
                    iconPosition="end"
                    onClick={handleContinue}
                    loading={loadingCreatingShortlist}
                  >
                    {findProfessional?.continue}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </>
      )}

      {loading ? (
        <LoadingOutlined className="text-3xl !table m-auto mt-[40px]" />
      ) : (
        <div className="relative">
          <div className="fixed z-[999] bottom-13 right-13">
            {results?.length > 0 && (
              <Button
                onClick={() =>
                  moveToTop.current?.scrollIntoView({
                    behavior: "smooth"
                  })
                }
                type="primary"
                className="!rounded-[50%] !w-12 !h-12 !p-0"
              >
                <ArrowUpOutlined className="[&_svg]:!w-6 [&_svg]:!h-6" />
              </Button>
            )}
          </div>
          {results?.length > 0 ? (
            results?.map(resume => (
              <JobCard
                key={resume.ResumeId}
                {...resume}
                onResumeOpen={setOpenResume}
                onSelectItem={handleResumeSelection}
                selectedResume={selectedResumes.find(
                  n => n.ResumeId === resume.ResumeId
                )}
              />
            ))
          ) : (
            <EmptyView
              className="mt-0"
              title={findProfessional?.noSearchResults}
              detail={findProfessional?.noSearchResultsDetail}
              noBorder
              icon={<ProfessionalIcon />}
            />
          )}
        </div>
      )}
      <ResumeDrawer
        open={openResume}
        onClose={() => setOpenResume(false)}
        selectedResume={openResume}
      />
    </div>
  );
};

export default SearchResults;
