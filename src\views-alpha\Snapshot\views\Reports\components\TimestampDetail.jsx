import { Grid, Image } from "antd";
import ArrowDownIcon from "../../../../../assets-alpha/images/svg/arrow-down-icon.svg?react";
import CommonDrawer from "../../../../../common-alpha/Drawer/Drawer";
import { InfoItemCard } from "../../../../Resume/Component/ExperienceSection";
import { SubHeading } from "./TimelogList";
import moment from "moment";

const TimestampDetail = ({
  snapshotDate,
  currentTimelog,
  ReviewedBylabel,
  ReviewedDatelabel,
  StartTimeLabel,
  EndTimeLabel,
  DurationLabel,
  MemoLabel,
  CommentLabel,
  AmountLabel,
  Dateformatelabel,
  labels,
  Status,
  Screenshots,
  onClose
}) => {
  const {
    TimeLogId,
    Date,
    Memo = "",
    Amount,
    StartTime = "",
    EndTime = "",
    Duration = "",
    Currency,
    ReviewedBy,
    ReviewedDate
  } = currentTimelog;
  const { md } = Grid.useBreakpoint();
  const screenshotsToShow = Screenshots || currentTimelog.Screenshots || [];
  return (
    <CommonDrawer
      drawerKey="suggestions"
      zIndex={2000}
      title={"Timestamp Detail"}
      mask={md ? false : true}
      onClose={onClose}
      open={true}
      closeIcon={
        <ArrowDownIcon className="rotate-270 max-md:rotate-0 !text-[#343333] w-[28px] h-[28px]" />
      }
      width={400}
      rootClassName="
          [&_.ant-drawer-content-wrapper]:!inset-y-[90px] 
          [&_.ant-drawer-content-wrapper]:!right-[450px]
          [&_.ant-drawer-content-wrapper]:max-md:!inset-y-[8px]
          "
      className="[&_.ant-drawer-header]:md:!border-b-0 [&_.ant-drawer-header]:md:!pb-4.5
          [&_.ant-drawer-body]:!pt-0
          "
    >
      <Details currentTimelog={currentTimelog} />
    </CommonDrawer>
  );
};

export default TimestampDetail;

const Details = ({ currentTimelog }) => {
  const data = [
    {
      label: "Date",
      value: moment(currentTimelog.Date).format("ddd MMM DD YYYY")
    },
    {
      label: "Start time",
      value: moment(currentTimelog.StartTime, "HH:mm:ss").format("h:mm A")
    },
    {
      label: "End time",
      value: moment(currentTimelog.EndTime, "HH:mm:ss").format("h:mm A")
    },
    {
      label: "Duration",
      value: currentTimelog.Duration
    },
    {
      label: "Amount",
      value: `${currentTimelog.Amount} ${currentTimelog.HourlyRateType}`
    },
    {
      label: "Description",
      value: currentTimelog.Memo || "None"
    }
  ];
  return (
    <div className="">
      <InfoItemCard fields={data} labelClassName="min-w-[90px]" />

      {/* Attached Images */}
      <div className="mt-6">
        {currentTimelog?.Screenshots?.length > 0 && (
          <SubHeading className="text-[13px] text-[var(--gray-3)] font-normal">
            Attached Images
          </SubHeading>
        )}

        <div className="grid grid-cols-2 gap-3 text-[0px]">
          {currentTimelog?.Screenshots?.map(shot => (
            <div
              key={shot.ScreenshotId}
              className="rounded-lg overflow-hidden border border-gray-200"
            >
              <Image
                src={shot.BlobUrl}
                alt={shot.BlobUrl?.split("/")?.pop()}
                className="w-full h-32"
                preview={false}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
