import { useState, useEffect } from "react";
import { connect } from "react-redux";
import { useNavigate } from "react-router-dom";
import PageWrapper from "../../components/PageWrapper/PageWrapper";
import { notificationAction } from "../../actions/notification";
import { tictellLogin, tictellAppLoginApi } from "../Tictell/tictellApi";
import "./searcher-phillip.scss";

import { privateRoutes } from "../../Routes/routing";
import { StorageService } from "../../api/storage";
import { updatePhillipPopupStatusApi } from "../../components/Phillip/phillipApi";
import SearcherPhillip from "./components/SearcherPhillip";
import { getChildFeatureByName } from "../Snapshot/snapshotApi";
import PhilipNavigation from "./Navigation/Navigation";
import Dashboard from "./views/Dashboard/Dashboard";
import Monitoring from "./views/Monitoring/Monitoring";

import StartNewSearch from "./views/StartNewSearch/StartNewSearch";
import Reports from "./views/ApproveTimeExpenses/ApproveTimeExpenses";
import Opportunity from "./views/Opportunity/Opportunity";

const SearcherPhilipModule = ({
  labels,
  isHelpActive,
  User,
  notificationAction,
}) => {
  const [state, setState] = useState({
    UserFeatures: [],
    token: "",
    ExpenseCategories: [],
    Currencies: [],
    isLoading: true,
    UserId: -1,
    dialogMessage: "",
    showPhillip: true,
    facebookClientId: "",
    linkedInClientId: "",
  });

  const navigate = useNavigate();

  useEffect(() => {
    getChildFeatureByName({ featureName: "PHILIPSearcher" })
      .then((res) => {
        let usersFeaturesList = res.items.filter((a) => a.Url);
        usersFeaturesList.push(usersFeaturesList.shift());
        const list = usersFeaturesList?.map((single, index) => ({
          ...single,
          mobileName:
            single?.Url?.charAt(0).toUpperCase() + single?.Url.slice(1),
          mobileIcon: `icon-phillips-mobile-${index}`,
        }));
        setState((prevState) => ({
          ...prevState,
          UserFeatures: list,
          isLoading: false,
        }));
        const firstUrl = usersFeaturesList.find((a) => a.Url);
        var queryString = window.location.href
          .toLowerCase()
          .split("searcher-philip")[1];
        if (queryString === "" && firstUrl) {
          const isApp = window.location.hash.toLowerCase().indexOf("apps") > -1;

          navigate((isApp ? "/apps" : "") + "/searcher-philip/" + firstUrl.Url);
        }
      })
      .catch((err) => console.log("Err ", err));

    let { UserEmail } = User;
    let Email = UserEmail;
    if (!Email) {
      const user = StorageService.getUser();
      if (user) {
        Email = user.Email || user.UserEmail || user.userEmail;
      }
    }

    if (Email && Email !== "undefined") {
      tictellAppLoginApi(Email)
        .then((res) => {
          if (res.success) {
            tictellLogin(Email)
              .then((res) => {
                if (res.success) {
                  const { access_token } = res.items;
                  setState((prevState) => ({
                    ...prevState,
                    token: access_token,
                  }));
                }
              })
              .catch((err) => console.log(err));
          }
        })
        .catch((Err) => console.log("Error ", Err));
    } else {
      console.warn("No valid email found for Philip Tictell login");
    }

    setState((prevState) => ({
      ...prevState,
      UserId: User.UserId,
    }));
  }, [User, navigate]);

  const handleOkClick = () => {
    navigate(privateRoutes.dashboard.path);
  };

  const handleClosePhillip = () => {
    const { IsFreelancer } = User;
    updatePhillipPopupStatusApi({ isFreelancer: IsFreelancer }).then(() => {});
    setState((prevState) => ({ ...prevState, showPhillip: false }));
  };

  const handleShowPhillip = () => {
    setState((prevState) => ({ ...prevState, showPhillip: true }));
  };

  const handleSearcherPhillipSkip = () => {
    const { IsFreelancer } = User;
    updatePhillipPopupStatusApi({ isFreelancer: IsFreelancer }).then(
      (response) => {
        if (response.success) {
          const info = {
            message: "Searcher Phillip skipped",
            status: "success",
          };
          notificationAction(info);
        }
      }
    );
    setState((prevState) => ({ ...prevState, showPhillip: false }));
  };

  const handleUpdateCompanyPresentationsWidget = () => {
    setState((prevState) => ({
      ...prevState,
      isReloadCompanyPresentationsWidget: !prevState.isReloadCompanyPresentationsWidget,
    }));
  };

  const handleUpdateResumeShortlistWidgetWidget = () => {
    setState((prevState) => ({
      ...prevState,
      isReloadResumeShortlistWidget: !prevState.isReloadResumeShortlistWidget,
    }));
  };

  const {
    showPhillip,
    facebookClientId,
    linkedInClientId,
    UserFeatures,
    token,
    Currencies,
    isLoading,
  } = state;
  const currentViewHash =
    window.location.hash.toLowerCase().split("/")[1] === "apps"
      ? window.location.hash.toLocaleLowerCase().split("/")[3]
      : window.location.hash.toLocaleLowerCase().split("/")[2];

  return (
    <PageWrapper className="philip-page">
      {isLoading && (
        <div id="loader-wrapper">
          <div className="loader-container">
            <div className="loader">
              <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
                <defs>
                  <filter id="goo">
                    <feGaussianBlur
                      in="SourceGraphic"
                      stdDeviation="6"
                      result="blur"
                    />
                    <feColorMatrix
                      in="blur"
                      mode="matrix"
                      values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                      result="goo"
                    />
                    <feBlend in="SourceGraphic" in2="goo" />
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </div>
      )}
      <PhilipNavigation
        labels={labels}
        disabled={`${isLoading ? "disabled" : ""}`}
        isHelpActive={isHelpActive}
        UserFeatures={UserFeatures}
      />
      {currentViewHash === "automate" &&
        UserFeatures.findIndex(
          (a) => a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
        ) > -1 && (
          <SearcherPhillip
            open={showPhillip}
            onCloseModal={handleClosePhillip}
            onSkip={handleSearcherPhillipSkip}
            facebookClientId={facebookClientId}
            linkedInClientId={linkedInClientId}
            onCompanyPresentationsUpdate={
              handleUpdateCompanyPresentationsWidget
            }
            onResumeShortlistUpdate={handleUpdateResumeShortlistWidgetWidget}
          />
        )}
      {UserFeatures.findIndex(
        (a) => a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
      ) > -1 &&
        currentViewHash === "statistics" && (
          <Dashboard token={token} UserFeatures={UserFeatures} />
        )}
      {UserFeatures.findIndex(
        (a) => a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
      ) > -1 &&
        currentViewHash === "monitoring" && <Monitoring token={token} />}
      {currentViewHash === "start-new-search" &&
        UserFeatures.findIndex(
          (a) => a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
        ) > -1 && <StartNewSearch />}
      {currentViewHash === "approve-time-and-expenses" &&
        UserFeatures.findIndex(
          (a) => a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
        ) > -1 && <Reports />}
      {currentViewHash === "phillip-opportunites" &&
        UserFeatures.findIndex(
          (a) => a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
        ) > -1 && <Opportunity token={token} Currencies={Currencies} />}
    </PageWrapper>
  );
};

const mapStateToProps = ({ systemLabel, navigation, userInfo }) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  const User = user ? user : StorageService.getUser();
  const { isHelpActive } = navigation;
  return { labels, isHelpActive, User };
};

export default connect(mapStateToProps, { notificationAction })(
  SearcherPhilipModule
);
