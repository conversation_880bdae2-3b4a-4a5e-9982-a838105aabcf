import { useSelector } from "react-redux";
import { RolesLevels } from "../../../utilities-alpha/constant";
import { Profiles } from "../constant";
import { ShowMoreSection } from "./LanguagesSection";
// Roles Section
const RolesSection = ({ openDrawer, userData }) => {
  const resume = useSelector(state => state.systemLabel.labels?.resume);

  return (
    <ShowMoreSection
      title={resume?.roles}
      items={userData?.items?.Profiles || []}
      openDrawer={openDrawer}
      addEditAction={Profiles.role}
      viewMore
      itemKeyId="ProfileId"
      itemValueKey="ProfileValue"
      emptyMessage={resume?.rolesEmptyMessage}
      addButtonAction={Profiles.role}
      renderItem={role => (
        <div
          key={role.ProfileId}
          className="bg-[#F3F1FD] border border-[#EAE5FC] rounded-lg px-4 py-3 text-sm text-[#343333]"
        >
          <div className="text-[13px] text-[#343333]">{role.ProfileValue}</div>
          <div className="text-[#878787] text-xs">
            {RolesLevels[role.ExperienceLevel]}
          </div>
        </div>
      )}
    />
  );
};
export default RolesSection;
