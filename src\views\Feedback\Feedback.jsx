import { useState, useEffect } from "react";
import {
  feedbackApi,
  feedbackSubmitApi,
  feedbackDelete<PERSON>pi,
} from "./feedbackApi";
import { connect } from "react-redux";
import { find } from "lodash";
import Messages from "../../utilities/Messages";
import FeedbackList from "./components/FeedbackList";
import FeedbackDetail from "./components/FeedbackDetail";
import CreateFeedback from "./components/CreateFeedback";
import PageWrapper from "../../components/PageWrapper/PageWrapper";
import { notificationAction } from "../../actions/notification";
import "./feedback.scss";
import { onStateChangeAction } from "./feedbackAction";
import toLower from "lodash/toLower";
import filter from "lodash/filter";
import includes from "lodash/includes";
import { useLocation, useNavigate } from "react-router-dom";
import Column from "../../common/Column/Column";
import ConfirmDialog from "../../common/ConfirmDialog/ConfirmDialog";
import Button from "../../common/Button/Button";
const Feedback = ({
  isFetching,
  labels,
  feedbackList,
  selectedFeedback,
  isCreateNew,
  feedbackForm,
  dialogMessage,
  filteredList,
  listCollapsed,
  detailCollapsed,
  formCollapsed,
  searchKey,
  isHelpActive,
  dispatch,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  useEffect(() => {
    if (isFetching) {
      getFeedbacks();
    }
  }, [isFetching]);

  const getFeedbacks = () => {
    feedbackApi().then((data) => {
      if (data.success) {
        const feedbackList = data.items.map((item) => {
          const [user] = item.FeedbackDetails;
          let admin =
            user.Status !== 1 &&
            item.FeedbackDetails.filter((item) => item.Status === 1);

          return {
            ...item,
            admin,
            user: {
              ...user,
              Title: item.Title,
            },
          };
        });
        dispatch(
          onStateChangeAction({
            feedbackList,
            filteredList: feedbackList,
            isFetching: false,
          })
        );
        const spliturl = location.pathname.match(/\d+/g);
        if (spliturl != null) {
          let feedback = data.items.filter((a) => a.FeedbackId == spliturl[0]);
          handleFeedbackClick(feedback[0].FeedbackId);
        }
      }
    });
  };

  const handleFeedbackClick = (id) => {
    const selectedFeedback = find(feedbackList, { FeedbackId: id });
    dispatch(onStateChangeAction({ selectedFeedback, isCreateNew: false }));
  };

  const handleCreateNewClick = () => {
    dispatch(
      onStateChangeAction({
        isCreateNew: true,
        selectedFeedback: {},
        feedbackForm: {},
      })
    );
  };

  const handleContentChange = (content, delta, source, editor) => {
    const data = {
      ...feedbackForm,
      content,
      isInvalidContent: !editor.getText().trim(),
    };
    dispatch(onStateChangeAction({ feedbackForm: data }));
  };

  const handleTitleChange = (value) => {
    const data = {
      ...feedbackForm,
      title: value,
      isInvalidTitle: !value,
    };
    dispatch(onStateChangeAction({ feedbackForm: data }));
  };

  const handleSubmitFeedback = () => {
    const {
      title,
      content,
      imgSrc,
      isInvalidContent,
      isInvalidTitle,
    } = feedbackForm;
    if (!title || !content || isInvalidContent || isInvalidTitle) {
      const info = {
        message: labels.feedbackAddTitle,
        status: "error",
      };
      dispatch(notificationAction(info));
      dispatch(
        onStateChangeAction({
          feedbackForm: {
            ...feedbackForm,
            isInvalidTitle: !title,
            isInvalidContent: isInvalidContent || !content,
          },
        })
      );
      return;
    }
    dispatch(
      onStateChangeAction({
        feedbackForm: {
          ...feedbackForm,
          isSubmitting: true,
        },
      })
    );
    setIsLoading(true);
    feedbackSubmitApi({ title, content, imgSrc }).then((data) => {
      if (data.success) {
        getFeedbacks();
        dispatch(
          onStateChangeAction({
            feedbackForm: {},
            isCreateNew: false,
          })
        );
        const info = {
          message: labels.feedbackSuccessfullySent,
          status: "success",
        };
        setIsLoading(false);
        dispatch(notificationAction(info));
      }
    });
  };

  const handleDeleteFeedback = (id) => {
    dispatch(
      onStateChangeAction({
        deletedId: id,
        dialogMessage: labels.AlrtDelMsg,
      })
    );
  };

  const handleYesClick = () => {
    const { deletedId } = feedbackForm;
    setIsLoading(true);
    dispatch(onStateChangeAction({ dialogMessage: "" }));
    feedbackDeleteApi({ id: deletedId }).then((data) => {
      if (data.success) {
        const info = {
          message: labels.feedbackSuccessfullyDeleted,
          status: "success",
        };
        dispatch(notificationAction(info));
        getFeedbacks();
        dispatch(
          onStateChangeAction({
            feedbackForm: {},
            selectedFeedback: {},
            deletedId: null,
          })
        );
        setIsLoading(false);
      }
    });
  };

  const handleNoClick = () => {
    dispatch(onStateChangeAction({ dialogMessage: "", deletedId: null }));
  };

  const getImgSrc = (src) => {
    dispatch(
      onStateChangeAction({
        feedbackForm: {
          ...feedbackForm,
          imgSrc: src,
        },
      })
    );
  };

  const handleSearchChange = (e) => {
    const { value } = e.target;
    const filteredList = filter(feedbackList, (sl) =>
      includes(toLower(sl.Title), toLower(value))
    );
    dispatch(onStateChangeAction({ filteredList, searchKey: value }));
  };

  return (
    <PageWrapper className="feedback-page">
      {isLoading && (
        <div id="loader-wrapper">
          <div className="loader-container">
            <div className="loader">
              <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
                <defs>
                  <filter id="goo">
                    <feGaussianBlur
                      in="SourceGraphic"
                      stdDeviation="6"
                      result="blur"
                    />
                    <feColorMatrix
                      in="blur"
                      mode="matrix"
                      values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                      result="goo"
                    />
                    <feBlend in="SourceGraphic" in2="goo" />
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </div>
      )}
      {dialogMessage && (
        <ConfirmDialog>
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <button className="dialog-btn" onClick={handleYesClick}>
              {Messages.feedback.confirm.Yes}
            </button>
            <button className="dialog-btn" onClick={handleNoClick}>
              {Messages.feedback.confirm.No}
            </button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      <Column collapse={listCollapsed} className="col-1">
        <Column.Collapsed
          text={labels.ViewTitleFeedBackList}
          onClick={() =>
            dispatch(onStateChangeAction({ listCollapsed: false }))
          }
          tooltipButton={labels.ToolTipFeedBackExpandList}
          isHelpActive={isHelpActive}
          tooltipHelp={labels.HlpTooltipFeedbackExpandListButton}
          tooltipPlace="left"
        />
        <Column.Head>
          <input
            className="round-search"
            type="text"
            onChange={handleSearchChange}
            placeholder={labels.searcherFeedbackSearch}
            value={searchKey}
          />
          <Button
            tooltipButton={labels.TooltipFeedbackCreateButton}
            tooltipHelp={labels.HlpTooltipFeedbackCreateButton}
            tooltipPlace="left"
            className="AddBtn"
            onClick={handleCreateNewClick}
            isHelpActive={isHelpActive}
          />
          <Button
            onClick={() =>
              dispatch(onStateChangeAction({ listCollapsed: true }))
            }
            className="collapseBtn"
            tooltipButton={labels.ToolTipFeedBackCollaspeList}
            tooltipHelp={labels.HlpTooltipFeedbackCollapseListButton}
            tooltipPlace="left"
            isHelpActive={isHelpActive}
          />
        </Column.Head>
        <Column.Body>
          <FeedbackList
            feedbackList={filteredList}
            handleFeedbackClick={handleFeedbackClick}
            selectedFeedback={selectedFeedback}
            handleDeleteFeedback={handleDeleteFeedback}
            labels={labels}
          />
        </Column.Body>
      </Column>
      {!isCreateNew && (
        <Column collapse={detailCollapsed} className="col-2">
          <Column.Collapsed
            text={labels.ViewTitleFeedBackDetail}
            onClick={() =>
              dispatch(onStateChangeAction({ detailCollapsed: false }))
            }
            isHelpActive={isHelpActive}
            tooltipButton={labels.ToolTipFeedBackExpandDetail}
            tooltipHelp={labels.HlpTooltipFeedbackExpandDetailButton}
            tooltipPlace="left"
          />
          <Column.Head>
            <div className="heading">
              {selectedFeedback.FeedbackId ? labels.searcherFeedbackDetail : ""}
            </div>
            <Button
              className="collapseBtn"
              onClick={() =>
                dispatch(onStateChangeAction({ detailCollapsed: true }))
              }
              tooltipButton={labels.ToolTipFeedBackCollaspeDetail}
              tooltipHelp={labels.HlpTooltipFeedbackCollapseDetailButton}
              tooltipPlace="left"
              isHelpActive={isHelpActive}
            />
          </Column.Head>
          <Column.Body className="flex">
            <FeedbackDetail selectedFeedback={selectedFeedback} />
          </Column.Body>
        </Column>
      )}
      {isCreateNew && (
        <Column collapse={formCollapsed} className="col-2">
          <Column.Collapsed
            text={labels.ViewTitleFeedBackCreate}
            onClick={() =>
              dispatch(onStateChangeAction({ formCollapsed: false }))
            }
            isHelpActive={isHelpActive}
            tooltipButton={labels.ToolTipFeedBackExpandCreate}
            tooltipHelp={labels.HlpTooltipFeedbackExpandCreateButton}
            tooltipPlace="left"
          />
          <Column.Head>
            <div className="heading">{labels.searcherFeedbackCreate}</div>
            <Button
              onClick={handleSubmitFeedback}
              className="SendBtn"
              disabled={feedbackForm.isSubmitting}
              tooltipButton={labels.TooltipFeedbackSendButton}
              tooltipHelp={labels.HlpTooltipFeedbackSendButton}
              tooltipPlace="left"
              isHelpActive={isHelpActive}
            />
            <Button
              className="collapseBtn"
              onClick={() =>
                dispatch(onStateChangeAction({ formCollapsed: true }))
              }
              tooltipButton={labels.ToolTipFeedBackCollaspeCreate}
              tooltipHelp={labels.HlpTooltipFeedbackCollapseCreateButton}
              tooltipPlace="left"
              isHelpActive={isHelpActive}
            />
          </Column.Head>
          <Column.Body>
            <CreateFeedback
              handleContentChange={handleContentChange}
              handleTitleChange={handleTitleChange}
              feedbackForm={feedbackForm}
              getImgSrc={getImgSrc}
            />
          </Column.Body>
        </Column>
      )}
    </PageWrapper>
  );
};

const mapStateToProps = ({ systemLabel, navigation, feedback }) => {
  const { labels } = systemLabel;
  const { isHelpActive } = navigation;
  return { labels, isHelpActive, ...feedback };
};

export default connect(mapStateToProps)(Feedback);
