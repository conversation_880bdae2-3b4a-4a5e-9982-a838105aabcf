import { Avatar, Button, Tag } from "antd";
import AvatarIcon from "../../../../assets-alpha/images/svg/avatar.svg";
import ProfileIcon from "../../../../assets-alpha/images/svg/profile.svg?react";
import LocationIcon from "../../../../assets-alpha/images/svg/location.svg?react";
import RankingIcon from "../../../../assets-alpha/images/svg/ranking.svg?react";
import CloseIcon from "../../../../assets-alpha/images/svg/close-modal.svg?react";
import { formatDate } from "../../../../utilities/helpers";
import { UserOutlined } from "@ant-design/icons";
import { round } from "lodash";
import { RolesLevels } from "../../../../utilities-alpha/constant";
import { useNavigate } from "react-router-dom";
import { publicRoutes } from "../../../../Routes/routing";
import { useSelector } from "react-redux";
const SingleResumeDetail = ({
  resumeDetail,
  selectedResumeId,
  setShowResumeDetail
}) => {
  const navigate = useNavigate();
  const labels = useSelector(state => state.systemLabel.labels);
  const selectedResume = resumeDetail?.Top5Resumes?.find(
    item => item?.ResumeId === selectedResumeId
  );

  return (
    <>
      <div
        className={`relative flex flex-col bg-white overflow-y-auto h-full mb-6 `}
      >
        <div className="flex items-center px-4 top-0 border-b border-[#EAE5FC] !min-h-[70px] gap-4">
          <span
            onClick={() => {
              setShowResumeDetail(false);
            }}
          >
            <CloseIcon />
          </span>
          <span className="font-medium !text-lg md:!text-2xl text-[#343333]">
            {labels?.searcher_seach_resume_detail_title}
          </span>
        </div>
        <div className="px-4">
          <div className="flex items-center gap-2 mb-6 mt-3 !h-[72px]">
            <Avatar size={72} icon={<UserOutlined />} src={AvatarIcon} />
            <div className="flex flex-col gap-1">
              <p className="font-medium text-[14px] text-sm flex items-center text-[#878787] gap-[3px]">
                <span className="text-[#878787] flex items-center gap-[4px]">
                  <ProfileIcon />
                  {selectedResume?.UserFirstName} {selectedResume?.UserLastName}
                </span>
              </p>
              <p className="text-sm text-[14px] !mt-0.5 text-[#7C7C7C]">
                <span className="text-[var(--purple)] flex items-center gap-[4px]">
                  <LocationIcon />
                  {selectedResume?.Region}
                </span>
              </p>
              <p className="text-sm text-[14px] text-[var(--green-2)] !mt-1 flex gap-[4px]">
                <RankingIcon className="!w-5 !h-5" />
                {labels?.Match_Score_Label} (
                {round(
                  (selectedResume?.CorrelationScore /
                    (selectedResume?.TotalScore || 1)) *
                    100,
                  2
                )}
                %)
              </p>
            </div>
          </div>
          <p className="text-[#878787] text-sm font-normal !mb-2">
            {labels?.More_Info_Label}
          </p>

          {/* More Info Section */}
          <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mb-[6px] max-md:!mb-[6px]">
            <p className="text-gray-500 text-sm !mb-2">
              {labels?.Availability_Label}
            </p>
            <p className="font-medium text-sm mt-1">
              <span className="text-gray-500 text-sm !mb-2">
                {labels?.IProSentMessageDetailDateLabel}:
              </span>
              {formatDate(selectedResume?.AvailabilityDate)}
            </p>
          </div>

          {/* Roles Section */}
          {selectedResume?.Profiles?.length > 0 && (
            <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mb-2 max-md:!mb-[6px]">
              <p className="text-gray-500 text-sm !mb-2">
                {labels?.ADMIN_ROLES_LABEL}
              </p>
              {selectedResume?.Profiles.slice(0, 5)?.map(role => (
                <div
                  key={role.ProfileId}
                  className="flex justify-between items-center text-sm mb-2"
                >
                  <span>
                    {renderScore(role.Score)}
                    {role.ProfileValue}
                  </span>
                  {renderTags(RolesLevels[role.ExperienceLevel])}
                </div>
              ))}
            </div>
          )}

          {/* Skills Section */}
          {selectedResume?.Skills?.length > 0 && (
            <div className="w-full bg-[#F6F3FC] rounded-xl p-4 relative">
              <p className="text-gray-500 text-sm !mb-2">
                {labels?.Skills_Label}
              </p>
              {selectedResume?.Skills?.slice(0, 5)?.map(skill => (
                <div
                  key={skill.SkillId}
                  className="flex justify-between items-center text-sm mb-2"
                >
                  <span>
                    {renderScore(skill.Score)}
                    {skill.SkillValue}
                  </span>
                  {renderTags(RolesLevels[skill.ExperienceLevel])}
                </div>
              ))}
              <div className="absolute bottom-0 left-0 right-0 h-[51%] bg-gradient-to-t from-white/100 via-white/70 to-[#F6F3FC]/30 z-10"></div>
            </div>
          )}
        </div>
        <div className="flex w-full justify-center mt-[50px]">
          <Button
            type="primary"
            className="md:!h-11 !h-10 w-full mx-4 md:w-[171px]"
            onClick={() => {
              document.body.style.overflow = "auto";
              navigate(publicRoutes.signup.path);
            }}
          >
            {labels?.Sign_up_for_proceeding_Lbl}
          </Button>
        </div>
      </div>
    </>
  );
};
const renderScore = score => {
  return <span className="text-green-500 font-medium mr-2">{score}</span>;
};
const renderTags = name => {
  return (
    name && (
      <Tag className="!bg-white rounded-lg border-none text-xs !px-3 !py-0.5 !text-[#8E81F5]">
        {name}
      </Tag>
    )
  );
};
export default SingleResumeDetail;
