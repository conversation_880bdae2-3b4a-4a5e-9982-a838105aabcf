import { connect } from "react-redux";
import Button from "../../../common/Button/Button";
import EmptyInfo from "../../../common/EmptyInfo/EmptyInfo";
import Input from "../../../common/Input/Input";
import ListGroup from "../../../common/ListGroup/ListGroup";
import LoadingMask from "../../../common/LoadingMask/LoadingMask";
const UserPhoneNumbers = ({
  phoneNumbers,
  onPhoneNumberDelete,
  onSaveUserSetting,
  onPhoneNumberChange,
  isFetching,
  labels
}) => {
  return (
    <div
      className={`userSettings ${
        phoneNumbers.length === 0 ? "center-content" : ""
      }`}
    >
      {isFetching && <LoadingMask />}

      <div className="form-row">
        <ListGroup>
          {phoneNumbers.map(item => (
            <ListGroup.Item key={item.uniqueId}>
              <ListGroup.Column>
                <Input
                  type="text"
                  name="phonenumber"
                  className={`input-text ${
                    item.isInvalidPhone ? "inValid" : ""
                  }`}
                  value={item.UserPhonenumberValue || ""}
                  placeholder={"Enter phone number"}
                  testId="txtsettingsphonnumber"
                  onChange={e => onPhoneNumberChange(item.uniqueId, e)}
                />
              </ListGroup.Column>

              <ListGroup.Column size={0}>
                <ListGroup.Close onClick={() => onPhoneNumberDelete(item)} />
              </ListGroup.Column>
            </ListGroup.Item>
          ))}
        </ListGroup>
      </div>

      {phoneNumbers.length > 0 && (
        <Button
          className="SendButton"
          onClick={onSaveUserSetting}
          testId="btnsettingupdate"
          tooltipHelp="Update user settings."
          tooltipButton="Update user settings."
        >
          {"Update"}
        </Button>
      )}
      {phoneNumbers.length == 0 && (
        <EmptyInfo>
          {labels.USER_SETTING_LIST_PROFILE_PHONE_EMPTY_INFO}
        </EmptyInfo>
      )}
    </div>
  );
};

const mapStateToProps = ({ systemLabel }) => {
  const { labels } = systemLabel;
  return { labels };
};

export default connect(mapStateToProps)(UserPhoneNumbers);
