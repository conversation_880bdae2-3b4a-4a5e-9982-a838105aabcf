<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>
      Global Freelance Hub: Connect with Top Remote Opportunities Worldwide.
    </title>
    <meta
      name="description"
      content="Hire top freelance professionals worldwide. Find expert freelancers for remote jobs, projects, and independent work opportunities."
    />
    <link rel="stylesheet" type="text/css" href="assets/css/preloading.css" />

    <!-- Canonical -->
    <link rel="canonical" href="https://prodoo.com" />

    <!-- Open Graph / Facebook -->
    <meta
      property="og:title"
      content="Global Freelance Hub: Connect with Top Remote Opportunities 
Worldwide. 
"
    />
    <meta
      property="og:description"
      content="Hire top freelance professionals worldwide. Find expert freelancers for remote jobs, projects, and independent work opportunities."
    />
    <meta
      property="og:image"
      content="https://prodoo.com/assets/images/logo-prodoo.png"
    />
    <meta property="og:url" content="https://prodoo.com" />
    <meta property="og:type" content="website" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="Global Freelance Hub: Connect with Top Remote Opportunities 
Worldwide. 
"
    />
    <meta
      name="twitter:description"
      content="Hire top freelance professionals worldwide. Find expert freelancers for remote jobs, projects, and independent work opportunities."
    />
    <meta
      name="twitter:image"
      content="https://prodoo.com/assets/images/logo-prodoo.png"
    />

    <!-- Theme color -->
    <meta name="theme-color" content="#000000" />

    <!-- Google Site Verification -->
    <meta
      name="google-site-verification"
      content="rIueZ43V_mecsgWoodyvpOrrQ61ovOgKAX6eDE3FvCQ"
    />

    <!-- Favicon -->
    <link rel="icon" href="favicon.ico" />
    <link rel="apple-touch-icon" href="/icons/192x192.png" />
    <link rel="manifest" href="manifest.json" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Caveat:wght@400..700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap"
      rel="stylesheet"
    />

    <!-- Google Analytics -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-XZEH7NVPVF"
    ></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());
      gtag("config", "G-XZEH7NVPVF");
    </script>

    <!-- Google Tag Manager -->
    <script>
      (function(w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-TMQG7DG");
    </script>

    <!-- Facebook Pixel -->
    <script>
      !(function(f, b, e, v, n, t, s) {
        if (f.fbq) return;
        n = f.fbq = function() {
          n.callMethod
            ? n.callMethod.apply(n, arguments)
            : n.queue.push(arguments);
        };
        if (!f._fbq) f._fbq = n;
        n.push = n;
        n.loaded = !0;
        n.version = "2.0";
        n.queue = [];
        t = b.createElement(e);
        t.async = !0;
        t.src = v;
        s = b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t, s);
      })(
        window,
        document,
        "script",
        "https://connect.facebook.net/en_US/fbevents.js"
      );
      fbq("init", "190497786392733");
      fbq("track", "PageView");
    </script>

    <!-- Structured Data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "name": "Prodoo.com",
        "image": "https://prodoo.com/assets/images/logo-prodoo.png",
        "url": "https://prodoo.com/",
        "telephone": "+45 30 43 03 07",
        "address": {
          "@type": "PostalAddress",
          "addressCountry": "DK"
        }
      }
    </script>
  </head>

  <body>
    <noscript> You need to enable JavaScript to run this app. </noscript>
    <noscript>
      <iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-TMQG7DG"
        height="0"
        width="0"
        style="display:none;visibility:hidden"
      ></iframe>
    </noscript>
    <div id="root">
      <div id="preLoader" class="preLoader">
        <div style="color: red;">
          <img class="loaderImage" src="assets/images/logo.png" alt="logo" />
        </div>
        <div class="loader-container">
          <div class="loader">
            <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
              <defs>
                <filter id="goo">
                  <feGaussianBlur
                    in="SourceGraphic"
                    stdDeviation="6"
                    result="blur"
                  ></feGaussianBlur>
                  <feColorMatrix
                    in="blur"
                    mode="matrix"
                    values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                    result="goo"
                  ></feColorMatrix>
                  <feBlend in="SourceGraphic" in2="goo"></feBlend>
                </filter>
              </defs>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </body>
  <script>
    document.body.addEventListener("wheel", e => {
      if (e.ctrlKey) e.preventDefault(); //prevent zoom
    });
  </script>
  <script type="module" src="/src/main.jsx"></script>
</html>
