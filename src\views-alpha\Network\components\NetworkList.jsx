import AvatarIcon from "../../../assets-alpha/images/svg/workplace-avatar.svg";
import { Loader } from "lucide-react";
const NetworkList = ({
  UsersList,
  handleNetworkClick,
  handleDeleteNetwork,
  selectedUser,
  setSelectedNetwork,
  isInvitedView,
  labels,
  setShowAddNetworkDrawer,
  state,
  dataLoading
}) => {
  return (
    <>
      {dataLoading ? (
        <div className="flex justify-center items-center w-full h-full mt-9">
          <Loader className="w-6 h-6 animate-spin text-[#343333]" />
        </div>
      ) : UsersList?.length > 0 ? (
        <div className="flex flex-col gap-[6px] w-full h-full mt-4 md:mb-0 mb-[46px]">
          {UsersList.map(item => (
            <div
              onClick={() => {
                handleNetworkClick(item.UserId);
                setShowAddNetworkDrawer(true);
              }}
              key={item.UserId}
              isSelected={selectedUser?.UserId === item.UserId}
              className="flex gap-3 w-full border-[0.5px] border-[#EAE5FC] rounded-xl min-h-16 p-3 cursor-pointer"
            >
              <div className="md:!w-11 md:!h-11 !h-10 !w-10 !rounded-[50%]">
                <img
                  className="md:!w-11 md:!h-11 !h-10 !w-10 !rounded-[50%]"
                  src={item.ProfilePicture ? item.ProfilePicture : AvatarIcon}
                  alt={"Profile-image"}
                />
              </div>

              <div className="flex flex-col gap-1">
                <h3 className="!m-0 font-medium text-sm text-[#343333]">
                  {item.UserFirstname +
                    " " +
                    (item.UserLastname ? item.UserLastname : "")}
                </h3>
                <p className="!m-0 font-normal text-xs text-[#878787]">
                  {item.UserEmail}
                </p>
              </div>

              {/* {isInvitedView ? (
                <BulletList.Close
                  testId="iProOpportunity-delete-item"
                  onClick={e => {
                    handleDeleteNetwork(item);
                    e.stopPropagation();
                  }}
                />
              ) : null} */}
            </div>
          ))}
        </div>
      ) : (
        ""
      )}
    </>
  );
};

export default NetworkList;
