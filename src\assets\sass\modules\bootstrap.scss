// .col-xs-1,
// .col-xs-2,
// .col-xs-3,
// .col-xs-4,
// .col-xs-5,
// .col-xs-6,
// .col-xs-7,
// .col-xs-8,
// .col-xs-9,
// .col-xs-10,
// .col-xs-11,
// .col-xs-12 {
//   float: left;
// }

// .col-xs-12 {
//   width: 100%;
// }

// .col-xs-11 {
//   width: 91.66666667%;
// }

// .col-xs-10 {
//   width: 83.33333333%;
// }

// .col-xs-9 {
//   width: 75%;
// }

// .col-xs-8 {
//   width: 66.66666667%;
// }

// .col-xs-7 {
//   width: 58.33333333%;
// }

// .col-xs-6 {
//   width: 50%;
// }

// .col-xs-5 {
//   width: 41.66666667%;
// }

// .col-xs-4 {
//   width: 33.33333333%;
// }

// .col-xs-3 {
//   width: 25%;
// }

// .col-xs-2 {
//   width: 16.66666667%;
// }

// .col-xs-1 {
//   width: 8.33333333%;
// }

// .col-xs-pull-12 {
//   right: 100%;
// }

// .col-xs-pull-11 {
//   right: 91.66666667%;
// }

// .col-xs-pull-10 {
//   right: 83.33333333%;
// }

// .col-xs-pull-9 {
//   right: 75%;
// }

// .col-xs-pull-8 {
//   right: 66.66666667%;
// }

// .col-xs-pull-7 {
//   right: 58.33333333%;
// }

// .col-xs-pull-6 {
//   right: 50%;
// }

// .col-xs-pull-5 {
//   right: 41.66666667%;
// }

// .col-xs-pull-4 {
//   right: 33.33333333%;
// }

// .col-xs-pull-3 {
//   right: 25%;
// }

// .col-xs-pull-2 {
//   right: 16.66666667%;
// }

// .col-xs-pull-1 {
//   right: 8.33333333%;
// }

// .col-xs-pull-0 {
//   right: auto;
// }

// .col-xs-push-12 {
//   left: 100%;
// }

// .col-xs-push-11 {
//   left: 91.66666667%;
// }

// .col-xs-push-10 {
//   left: 83.33333333%;
// }

// .col-xs-push-9 {
//   left: 75%;
// }

// .col-xs-push-8 {
//   left: 66.66666667%;
// }

// .col-xs-push-7 {
//   left: 58.33333333%;
// }

// .col-xs-push-6 {
//   left: 50%;
// }

// .col-xs-push-5 {
//   left: 41.66666667%;
// }

// .col-xs-push-4 {
//   left: 33.33333333%;
// }

// .col-xs-push-3 {
//   left: 25%;
// }

// .col-xs-push-2 {
//   left: 16.66666667%;
// }

// .col-xs-push-1 {
//   left: 8.33333333%;
// }

// .col-xs-push-0 {
//   left: auto;
// }

// .col-xs-offset-12 {
//   margin-left: 100%;
// }

// .col-xs-offset-11 {
//   margin-left: 91.66666667%;
// }

// .col-xs-offset-10 {
//   margin-left: 83.33333333%;
// }

// .col-xs-offset-9 {
//   margin-left: 75%;
// }

// .col-xs-offset-8 {
//   margin-left: 66.66666667%;
// }

// .col-xs-offset-7 {
//   margin-left: 58.33333333%;
// }

// .col-xs-offset-6 {
//   margin-left: 50%;
// }

// .col-xs-offset-5 {
//   margin-left: 41.66666667%;
// }

// .col-xs-offset-4 {
//   margin-left: 33.33333333%;
// }

// .col-xs-offset-3 {
//   margin-left: 25%;
// }

// .col-xs-offset-2 {
//   margin-left: 16.66666667%;
// }

// .col-xs-offset-1 {
//   margin-left: 8.33333333%;
// }

// .col-xs-offset-0 {
//   margin-left: 0%;
// }

// @media (min-width: 768px) {
//   .col-sm-1,
//   .col-sm-2,
//   .col-sm-3,
//   .col-sm-4,
//   .col-sm-5,
//   .col-sm-6,
//   .col-sm-7,
//   .col-sm-8,
//   .col-sm-9,
//   .col-sm-10,
//   .col-sm-11,
//   .col-sm-12 {
//     float: left;
//   }
//   .col-sm-12 {
//     width: 100%;
//   }
//   .col-sm-11 {
//     width: 91.66666667%;
//   }
//   .col-sm-10 {
//     width: 83.33333333%;
//   }
//   .col-sm-9 {
//     width: 75%;
//   }
//   .col-sm-8 {
//     width: 66.66666667%;
//   }
//   .col-sm-7 {
//     width: 58.33333333%;
//   }
//   .col-sm-6 {
//     width: 50%;
//   }
//   .col-sm-5 {
//     width: 41.66666667%;
//   }
//   .col-sm-4 {
//     width: 33.33333333%;
//   }
//   .col-sm-3 {
//     width: 25%;
//   }
//   .col-sm-2 {
//     width: 16.66666667%;
//   }
//   .col-sm-1 {
//     width: 8.33333333%;
//   }
//   .col-sm-pull-12 {
//     right: 100%;
//   }
//   .col-sm-pull-11 {
//     right: 91.66666667%;
//   }
//   .col-sm-pull-10 {
//     right: 83.33333333%;
//   }
//   .col-sm-pull-9 {
//     right: 75%;
//   }
//   .col-sm-pull-8 {
//     right: 66.66666667%;
//   }
//   .col-sm-pull-7 {
//     right: 58.33333333%;
//   }
//   .col-sm-pull-6 {
//     right: 50%;
//   }
//   .col-sm-pull-5 {
//     right: 41.66666667%;
//   }
//   .col-sm-pull-4 {
//     right: 33.33333333%;
//   }
//   .col-sm-pull-3 {
//     right: 25%;
//   }
//   .col-sm-pull-2 {
//     right: 16.66666667%;
//   }
//   .col-sm-pull-1 {
//     right: 8.33333333%;
//   }
//   .col-sm-pull-0 {
//     right: auto;
//   }
//   .col-sm-push-12 {
//     left: 100%;
//   }
//   .col-sm-push-11 {
//     left: 91.66666667%;
//   }
//   .col-sm-push-10 {
//     left: 83.33333333%;
//   }
//   .col-sm-push-9 {
//     left: 75%;
//   }
//   .col-sm-push-8 {
//     left: 66.66666667%;
//   }
//   .col-sm-push-7 {
//     left: 58.33333333%;
//   }
//   .col-sm-push-6 {
//     left: 50%;
//   }
//   .col-sm-push-5 {
//     left: 41.66666667%;
//   }
//   .col-sm-push-4 {
//     left: 33.33333333%;
//   }
//   .col-sm-push-3 {
//     left: 25%;
//   }
//   .col-sm-push-2 {
//     left: 16.66666667%;
//   }
//   .col-sm-push-1 {
//     left: 8.33333333%;
//   }
//   .col-sm-push-0 {
//     left: auto;
//   }
//   .col-sm-offset-12 {
//     margin-left: 100%;
//   }
//   .col-sm-offset-11 {
//     margin-left: 91.66666667%;
//   }
//   .col-sm-offset-10 {
//     margin-left: 83.33333333%;
//   }
//   .col-sm-offset-9 {
//     margin-left: 75%;
//   }
//   .col-sm-offset-8 {
//     margin-left: 66.66666667%;
//   }
//   .col-sm-offset-7 {
//     margin-left: 58.33333333%;
//   }
//   .col-sm-offset-6 {
//     margin-left: 50%;
//   }
//   .col-sm-offset-5 {
//     margin-left: 41.66666667%;
//   }
//   .col-sm-offset-4 {
//     margin-left: 33.33333333%;
//   }
//   .col-sm-offset-3 {
//     margin-left: 25%;
//   }
//   .col-sm-offset-2 {
//     margin-left: 16.66666667%;
//   }
//   .col-sm-offset-1 {
//     margin-left: 8.33333333%;
//   }
//   .col-sm-offset-0 {
//     margin-left: 0%;
//   }
// }

// @media (min-width: 992px) {
//   .col-md-1,
//   .col-md-2,
//   .col-md-3,
//   .col-md-4,
//   .col-md-5,
//   .col-md-6,
//   .col-md-7,
//   .col-md-8,
//   .col-md-9,
//   .col-md-10,
//   .col-md-11,
//   .col-md-12 {
//     float: left;
//   }
//   .col-md-12 {
//     width: 100%;
//   }
//   .col-md-11 {
//     width: 91.66666667%;
//   }
//   .col-md-10 {
//     width: 83.33333333%;
//   }
//   .col-md-9 {
//     width: 75%;
//   }
//   .col-md-8 {
//     width: 66.66666667%;
//   }
//   .col-md-7 {
//     width: 58.33333333%;
//   }
//   .col-md-6 {
//     width: 50%;
//   }
//   .col-md-5 {
//     width: 41.66666667%;
//   }
//   .col-md-4 {
//     width: 33.33333333%;
//   }
//   .col-md-3 {
//     width: 25%;
//   }
//   .col-md-2 {
//     width: 16.66666667%;
//   }
//   .col-md-1 {
//     width: 8.33333333%;
//   }
//   .col-md-pull-12 {
//     right: 100%;
//   }
//   .col-md-pull-11 {
//     right: 91.66666667%;
//   }
//   .col-md-pull-10 {
//     right: 83.33333333%;
//   }
//   .col-md-pull-9 {
//     right: 75%;
//   }
//   .col-md-pull-8 {
//     right: 66.66666667%;
//   }
//   .col-md-pull-7 {
//     right: 58.33333333%;
//   }
//   .col-md-pull-6 {
//     right: 50%;
//   }
//   .col-md-pull-5 {
//     right: 41.66666667%;
//   }
//   .col-md-pull-4 {
//     right: 33.33333333%;
//   }
//   .col-md-pull-3 {
//     right: 25%;
//   }
//   .col-md-pull-2 {
//     right: 16.66666667%;
//   }
//   .col-md-pull-1 {
//     right: 8.33333333%;
//   }
//   .col-md-pull-0 {
//     right: auto;
//   }
//   .col-md-push-12 {
//     left: 100%;
//   }
//   .col-md-push-11 {
//     left: 91.66666667%;
//   }
//   .col-md-push-10 {
//     left: 83.33333333%;
//   }
//   .col-md-push-9 {
//     left: 75%;
//   }
//   .col-md-push-8 {
//     left: 66.66666667%;
//   }
//   .col-md-push-7 {
//     left: 58.33333333%;
//   }
//   .col-md-push-6 {
//     left: 50%;
//   }
//   .col-md-push-5 {
//     left: 41.66666667%;
//   }
//   .col-md-push-4 {
//     left: 33.33333333%;
//   }
//   .col-md-push-3 {
//     left: 25%;
//   }
//   .col-md-push-2 {
//     left: 16.66666667%;
//   }
//   .col-md-push-1 {
//     left: 8.33333333%;
//   }
//   .col-md-push-0 {
//     left: auto;
//   }
//   .col-md-offset-12 {
//     margin-left: 100%;
//   }
//   .col-md-offset-11 {
//     margin-left: 91.66666667%;
//   }
//   .col-md-offset-10 {
//     margin-left: 83.33333333%;
//   }
//   .col-md-offset-9 {
//     margin-left: 75%;
//   }
//   .col-md-offset-8 {
//     margin-left: 66.66666667%;
//   }
//   .col-md-offset-7 {
//     margin-left: 58.33333333%;
//   }
//   .col-md-offset-6 {
//     margin-left: 50%;
//   }
//   .col-md-offset-5 {
//     margin-left: 41.66666667%;
//   }
//   .col-md-offset-4 {
//     margin-left: 33.33333333%;
//   }
//   .col-md-offset-3 {
//     margin-left: 25%;
//   }
//   .col-md-offset-2 {
//     margin-left: 16.66666667%;
//   }
//   .col-md-offset-1 {
//     margin-left: 8.33333333%;
//   }
//   .col-md-offset-0 {
//     margin-left: 0%;
//   }
// }

// @media (min-width: 1200px) {
//   .col-lg-1,
//   .col-lg-2,
//   .col-lg-3,
//   .col-lg-4,
//   .col-lg-5,
//   .col-lg-6,
//   .col-lg-7,
//   .col-lg-8,
//   .col-lg-9,
//   .col-lg-10,
//   .col-lg-11,
//   .col-lg-12 {
//     float: left;
//   }
//   .col-lg-12 {
//     width: 100%;
//   }
//   .col-lg-11 {
//     width: 91.66666667%;
//   }
//   .col-lg-10 {
//     width: 83.33333333%;
//   }
//   .col-lg-9 {
//     width: 75%;
//   }
//   .col-lg-8 {
//     width: 66.66666667%;
//   }
//   .col-lg-7 {
//     width: 58.33333333%;
//   }
//   .col-lg-6 {
//     width: 50%;
//   }
//   .col-lg-5 {
//     width: 41.66666667%;
//   }
//   .col-lg-4 {
//     width: 33.33333333%;
//   }
//   .col-lg-3 {
//     width: 25%;
//   }
//   .col-lg-2 {
//     width: 16.66666667%;
//   }
//   .col-lg-1 {
//     width: 8.33333333%;
//   }
//   .col-lg-pull-12 {
//     right: 100%;
//   }
//   .col-lg-pull-11 {
//     right: 91.66666667%;
//   }
//   .col-lg-pull-10 {
//     right: 83.33333333%;
//   }
//   .col-lg-pull-9 {
//     right: 75%;
//   }
//   .col-lg-pull-8 {
//     right: 66.66666667%;
//   }
//   .col-lg-pull-7 {
//     right: 58.33333333%;
//   }
//   .col-lg-pull-6 {
//     right: 50%;
//   }
//   .col-lg-pull-5 {
//     right: 41.66666667%;
//   }
//   .col-lg-pull-4 {
//     right: 33.33333333%;
//   }
//   .col-lg-pull-3 {
//     right: 25%;
//   }
//   .col-lg-pull-2 {
//     right: 16.66666667%;
//   }
//   .col-lg-pull-1 {
//     right: 8.33333333%;
//   }
//   .col-lg-pull-0 {
//     right: auto;
//   }
//   .col-lg-push-12 {
//     left: 100%;
//   }
//   .col-lg-push-11 {
//     left: 91.66666667%;
//   }
//   .col-lg-push-10 {
//     left: 83.33333333%;
//   }
//   .col-lg-push-9 {
//     left: 75%;
//   }
//   .col-lg-push-8 {
//     left: 66.66666667%;
//   }
//   .col-lg-push-7 {
//     left: 58.33333333%;
//   }
//   .col-lg-push-6 {
//     left: 50%;
//   }
//   .col-lg-push-5 {
//     left: 41.66666667%;
//   }
//   .col-lg-push-4 {
//     left: 33.33333333%;
//   }
//   .col-lg-push-3 {
//     left: 25%;
//   }
//   .col-lg-push-2 {
//     left: 16.66666667%;
//   }
//   .col-lg-push-1 {
//     left: 8.33333333%;
//   }
//   .col-lg-push-0 {
//     left: auto;
//   }
//   .col-lg-offset-12 {
//     margin-left: 100%;
//   }
//   .col-lg-offset-11 {
//     margin-left: 91.66666667%;
//   }
//   .col-lg-offset-10 {
//     margin-left: 83.33333333%;
//   }
//   .col-lg-offset-9 {
//     margin-left: 75%;
//   }
//   .col-lg-offset-8 {
//     margin-left: 66.66666667%;
//   }
//   .col-lg-offset-7 {
//     margin-left: 58.33333333%;
//   }
//   .col-lg-offset-6 {
//     margin-left: 50%;
//   }
//   .col-lg-offset-5 {
//     margin-left: 41.66666667%;
//   }
//   .col-lg-offset-4 {
//     margin-left: 33.33333333%;
//   }
//   .col-lg-offset-3 {
//     margin-left: 25%;
//   }
//   .col-lg-offset-2 {
//     margin-left: 16.66666667%;
//   }
//   .col-lg-offset-1 {
//     margin-left: 8.33333333%;
//   }
//   .col-lg-offset-0 {
//     margin-left: 0%;
//   }
// }

// .w-25 {
//   width: 25% !important;
// }
// .w-50 {
//   width: 50% !important;
// }
// .w-75 {
//   width: 75% !important;
// }
// .w-100 {
//   width: 100% !important;
// }
// .w-auto {
//   width: auto !important;
// }
// .h-25 {
//   height: 25% !important;
// }
// .h-50 {
//   height: 50% !important;
// }
// .h-75 {
//   height: 75% !important;
// }
// .h-100 {
//   height: 100% !important;
// }
// .h-auto {
//   height: auto !important;
// }
// .mw-100 {
//   max-width: 100% !important;
// }
// .mh-100 {
//   max-height: 100% !important;
// }
// .min-vw-100 {
//   min-width: 100vw !important;
// }
// .min-vh-100 {
//   min-height: 100vh !important;
// }
// .vw-100 {
//   width: 100vw !important;
// }
// .vh-100 {
//   height: 100vh !important;
// }
// .stretched-link::after {
//   position: absolute;
//   top: 0;
//   right: 0;
//   bottom: 0;
//   left: 0;
//   z-index: 1;
//   pointer-events: auto;
//   content: "";
//   background-color: rgba(0, 0, 0, 0);
// }
// .m-0 {
//   margin: 0 !important;
// }
// .mt-0,
// .my-0 {
//   margin-top: 0 !important;
// }
// .mr-0,
// .mx-0 {
//   margin-right: 0 !important;
// }
// .mb-0,
// .my-0 {
//   margin-bottom: 0 !important;
// }
// .ml-0,
// .mx-0 {
//   margin-left: 0 !important;
// }
// .m-1 {
//   margin: 0.25rem !important;
// }
// .mt-1,
// .my-1 {
//   margin-top: 0.25rem !important;
// }
// .mr-1,
// .mx-1 {
//   margin-right: 0.25rem !important;
// }
// .mb-1,
// .my-1 {
//   margin-bottom: 0.25rem !important;
// }
// .ml-1,
// .mx-1 {
//   margin-left: 0.25rem !important;
// }
// .m-2 {
//   margin: 0.5rem !important;
// }
// .mt-2,
// .my-2 {
//   margin-top: 0.5rem !important;
// }
// .mr-2,
// .mx-2 {
//   margin-right: 0.5rem !important;
// }
// .mb-2,
// .my-2 {
//   margin-bottom: 0.5rem !important;
// }
// .ml-2,
// .mx-2 {
//   margin-left: 0.5rem !important;
// }
// .m-3 {
//   margin: 1rem !important;
// }
// .mt-3,
// .my-3 {
//   margin-top: 1rem !important;
// }
// .mr-3,
// .mx-3 {
//   margin-right: 1rem !important;
// }
// .mb-3,
// .my-3 {
//   margin-bottom: 1rem !important;
// }
// .ml-3,
// .mx-3 {
//   margin-left: 1rem !important;
// }
// .m-4 {
//   margin: 1.5rem !important;
// }
// .mt-4,
// .my-4 {
//   margin-top: 1.5rem !important;
// }
// .mr-4,
// .mx-4 {
//   margin-right: 1.5rem !important;
// }
// .mb-4,
// .my-4 {
//   margin-bottom: 1.5rem !important;
// }
// .ml-4,
// .mx-4 {
//   margin-left: 1.5rem !important;
// }
// .m-5 {
//   margin: 3rem !important;
// }
// .mt-5,
// .my-5 {
//   margin-top: 3rem !important;
// }
// .mr-5,
// .mx-5 {
//   margin-right: 3rem !important;
// }
// .mb-5,
// .my-5 {
//   margin-bottom: 3rem !important;
// }
// .ml-5,
// .mx-5 {
//   margin-left: 3rem !important;
// }
// .p-0 {
//   padding: 0 !important;
// }
// .pt-0,
// .py-0 {
//   padding-top: 0 !important;
// }
// .pr-0,
// .px-0 {
//   padding-right: 0 !important;
// }
// .pb-0,
// .py-0 {
//   padding-bottom: 0 !important;
// }
// .pl-0,
// .px-0 {
//   padding-left: 0 !important;
// }
// .p-1 {
//   padding: 0.25rem !important;
// }
// .pt-1,
// .py-1 {
//   padding-top: 0.25rem !important;
// }
// .pr-1,
// .px-1 {
//   padding-right: 0.25rem !important;
// }
// .pb-1,
// .py-1 {
//   padding-bottom: 0.25rem !important;
// }
// .pl-1,
// .px-1 {
//   padding-left: 0.25rem !important;
// }
// .p-2 {
//   padding: 0.5rem !important;
// }
// .pt-2,
// .py-2 {
//   padding-top: 0.5rem !important;
// }
// .pr-2,
// .px-2 {
//   padding-right: 0.5rem !important;
// }
// .pb-2,
// .py-2 {
//   padding-bottom: 0.5rem !important;
// }
// .pl-2,
// .px-2 {
//   padding-left: 0.5rem !important;
// }
// .p-3 {
//   padding: 1rem !important;
// }
// .pt-3,
// .py-3 {
//   padding-top: 1rem !important;
// }
// .pr-3,
// .px-3 {
//   padding-right: 1rem !important;
// }
// .pb-3,
// .py-3 {
//   padding-bottom: 1rem !important;
// }
// .pl-3,
// .px-3 {
//   padding-left: 1rem !important;
// }
// .p-4 {
//   padding: 1.5rem !important;
// }
// .pt-4,
// .py-4 {
//   padding-top: 1.5rem !important;
// }
// .pr-4
// // ,
// // .px-4 
//  {
//   padding-right: 1.5rem !important;
// }
// .pb-4,
// .py-4 {
//   padding-bottom: 1.5rem !important;
// }
// .pl-4
// // ,
// // .px-4 
//  {
//   padding-left: 1.5rem !important;
// }
// .p-5 {
//   padding: 3rem !important;
// }
// .pt-5,
// .py-5 {
//   padding-top: 3rem !important;
// }
// .pr-5,
// .px-5 {
//   padding-right: 3rem !important;
// }
// .pb-5,
// .py-5 {
//   padding-bottom: 3rem !important;
// }
// .pl-5,
// .px-5 {
//   padding-left: 3rem !important;
// }
// .d-flex {
//   display: -ms-flexbox !important;
//   display: flex !important;
// }
// .d-inline-flex {
//   display: -ms-inline-flexbox !important;
//   display: inline-flex !important;
// }
// @media (min-width: 576px) {
//   .d-sm-none {
//     display: none !important;
//   }
//   .d-sm-inline {
//     display: inline !important;
//   }
//   .d-sm-inline-block {
//     display: inline-block !important;
//   }
//   .d-sm-block {
//     display: block !important;
//   }
//   .d-sm-table {
//     display: table !important;
//   }
//   .d-sm-table-row {
//     display: table-row !important;
//   }
//   .d-sm-table-cell {
//     display: table-cell !important;
//   }
//   .d-sm-flex {
//     display: -ms-flexbox !important;
//     display: flex !important;
//   }
//   .d-sm-inline-flex {
//     display: -ms-inline-flexbox !important;
//     display: inline-flex !important;
//   }
// }
// @media (min-width: 768px) {
//   .d-md-none {
//     display: none !important;
//   }
//   .d-md-block {
//     display: block !important;
//   }
//   .d-md-inline {
//     display: inline !important;
//   }
//   .d-md-inline-block {
//     display: inline-block !important;
//   }
// }
// .flex-row {
//   -ms-flex-direction: row !important;
//   flex-direction: row !important;
// }
// .flex-column {
//   -ms-flex-direction: column !important;
//   flex-direction: column !important;
// }
// .flex-row-reverse {
//   -ms-flex-direction: row-reverse !important;
//   flex-direction: row-reverse !important;
// }
// .flex-column-reverse {
//   -ms-flex-direction: column-reverse !important;
//   flex-direction: column-reverse !important;
// }
// .-wrap flex {
//   -ms-flex-wrap: wrap !important;
//   flex-wrap: wrap !important;
// }
// .flex-wrap {
//   -ms-flex-wrap: wrap !important;
//   flex-wrap: wrap !important;
// }
// .flex-nowrap {
//   -ms-flex-wrap: nowrap !important;
//   flex-wrap: nowrap !important;
// }
// .flex-wrap-reverse {
//   -ms-flex-wrap: wrap-reverse !important;
//   flex-wrap: wrap-reverse !important;
// }
// .flex-fill {
//   -ms-flex: 1 1 auto !important;
//   flex: 1 1 auto !important;
// }
// .flex-grow-0 {
//   -ms-flex-positive: 0 !important;
//   flex-grow: 0 !important;
// }
// .flex-grow-1 {
//   -ms-flex-positive: 1 !important;
//   flex-grow: 1 !important;
// }
// .flex-shrink-0 {
//   -ms-flex-negative: 0 !important;
//   flex-shrink: 0 !important;
// }
// .flex-shrink-1 {
//   -ms-flex-negative: 1 !important;
//   flex-shrink: 1 !important;
// }
// .justify-content-start {
//   -ms-flex-pack: start !important;
//   justify-content: flex-start !important;
// }
// .justify-content-end {
//   -ms-flex-pack: end !important;
//   justify-content: flex-end !important;
// }
// .justify-content-center {
//   -ms-flex-pack: center !important;
//   justify-content: center !important;
// }
// .justify-content-between {
//   -ms-flex-pack: justify !important;
//   justify-content: space-between !important;
// }
// .justify-content-around {
//   -ms-flex-pack: distribute !important;
//   justify-content: space-around !important;
// }
// .align-items-start {
//   -ms-flex-align: start !important;
//   align-items: flex-start !important;
// }
// .align-items-end {
//   -ms-flex-align: end !important;
//   align-items: flex-end !important;
// }
// .align-items-center {
//   -ms-flex-align: center !important;
//   align-items: center !important;
// }
// .align-items-baseline {
//   -ms-flex-align: baseline !important;
//   align-items: baseline !important;
// }
// .align-items-stretch {
//   -ms-flex-align: stretch !important;
//   align-items: stretch !important;
// }
// .align-content-start {
//   -ms-flex-line-pack: start !important;
//   align-content: flex-start !important;
// }
// .align-content-end {
//   -ms-flex-line-pack: end !important;
//   align-content: flex-end !important;
// }
// .align-content-center {
//   -ms-flex-line-pack: center !important;
//   align-content: center !important;
// }
// .align-content-between {
//   -ms-flex-line-pack: justify !important;
//   align-content: space-between !important;
// }
// .align-content-around {
//   -ms-flex-line-pack: distribute !important;
//   align-content: space-around !important;
// }
// .align-content-stretch {
//   -ms-flex-line-pack: stretch !important;
//   align-content: stretch !important;
// }
// .align-self-auto {
//   -ms-flex-item-align: auto !important;
//   align-self: auto !important;
// }
// .align-self-start {
//   -ms-flex-item-align: start !important;
//   align-self: flex-start !important;
// }
// .align-self-end {
//   -ms-flex-item-align: end !important;
//   align-self: flex-end !important;
// }
// .align-self-center {
//   -ms-flex-item-align: center !important;
//   align-self: center !important;
// }
// .align-self-baseline {
//   -ms-flex-item-align: baseline !important;
//   align-self: baseline !important;
// }
// .align-self-stretch {
//   -ms-flex-item-align: stretch !important;
//   align-self: stretch !important;
// }
// .gap-8 {
//   gap: 8px;
// }
// .gap-25 {
//   gap: 25px;
// }
// .gap-16 {
//   gap: 16px;
//   @media (max-width: 767.98px) {
//     gap: 10px;
//   }
// }
// @media (min-width: 576px) {
//   .flex-sm-row {
//     -ms-flex-direction: row !important;
//     flex-direction: row !important;
//   }
// }

// .container,
// .container-fluid,
// .container-xxl,
// .container-xl,
// .container-lg,
// .container-md,
// .container-sm {
//   --bs-gutter-x: 1.5rem;
//   --bs-gutter-y: 0;
//   width: 100%;
//   padding-right: calc(var(--bs-gutter-x) * 0.5);
//   padding-left: calc(var(--bs-gutter-x) * 0.5);
//   margin-right: auto;
//   margin-left: auto;
// }

// @media (min-width: 576px) {
//   .container-sm,
//   .container {
//     max-width: 540px;
//   }
// }
// @media (min-width: 768px) {
//   .container-md,
//   .container-sm,
//   .container {
//     max-width: 720px;
//   }
// }
// @media (min-width: 992px) {
//   .container-lg,
//   .container-md,
//   .container-sm,
//   .container {
//     max-width: 960px;
//   }
// }
// @media (min-width: 1200px) {
//   .container-xl,
//   .container-lg,
//   .container-md,
//   .container-sm,
//   .container {
//     max-width: 1140px;
//   }
// }
// @media (min-width: 1400px) {
//   .container-xxl,
//   .container-xl,
//   .container-lg,
//   .container-md,
//   .container-sm,
//   .container {
//     max-width: 1320px;
//   }
// }
