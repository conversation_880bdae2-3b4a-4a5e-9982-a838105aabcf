import { useSelector } from "react-redux";
import { RolesLevels } from "../../../utilities-alpha/constant";
import { Profiles } from "../constant";
import { ShowMoreSection } from "./LanguagesSection";
// Skills Section
const SkillsSection = ({ userData, openDrawer }) => {
  const resume = useSelector(state => state.systemLabel.labels?.resume);

  return (
    <ShowMoreSection
      title={resume?.skills}
      items={userData?.items?.Skills || []}
      openDrawer={openDrawer}
      addEditAction={Profiles.skill}
      itemKeyId="SkillId"
      viewMore
      itemValueKey="SkillValue"
      emptyMessage={resume?.skillsEmptyMessage}
      addButtonAction={Profiles.skill}
      renderItem={skill => (
        <div
          key={skill.SkillId}
          className="bg-[#F3F1FD] border border-[#EAE5FC] rounded-lg px-4 py-3 text-sm text-[#343333]"
        >
          <div className="text-[13px]">{skill.SkillValue}</div>
          <div className="text-[#878787] text-xs">
            {RolesLevels[skill.ExperienceLevel]}
          </div>
        </div>
      )}
    />
  );
};
export default SkillsSection;
