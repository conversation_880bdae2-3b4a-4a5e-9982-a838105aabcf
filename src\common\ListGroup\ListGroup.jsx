import "./list-group.scss";
const Item = ({ children, className, testId }) => (
  <li data-testid={testId} className={`list-group-item ${className}`}>
    {children}
  </li>
);

const Column = ({ children, className, testId, size }) => (
  <div
    data-testid={testId}
    className={`list-group-column ${className} ${size === 0 ? "no-flex" : ""}`}
  >
    {children}
  </div>
);
const Close = ({ onClick, testId }) => (
  <button onClick={onClick} className="closeBtn" data-testid={testId} />
);

const ListGroup = ({ children, className, testId, isFetching }) => (
  <div className={`list-group-wrapper ${className}`}>
    <ul
      className={`list-group-ul ${isFetching ? "loading-list-mask" : ""}`}
      data-testid={testId}
    >
      {children}
    </ul>
  </div>
);

ListGroup.Item = Item;
ListGroup.Close = Close;
ListGroup.Column = Column;

ListGroup.defaultProps = {
  className: ""
};
ListGroup.Item.defaultProps = {
  className: ""
};
ListGroup.Column.defaultProps = {
  className: ""
};

export default ListGroup;
