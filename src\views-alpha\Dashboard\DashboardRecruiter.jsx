import { useQueryClient } from "@tanstack/react-query";
import { Avatar, Button, Card, Form, Grid, message, Select } from "antd";
import clsx from "clsx";
import moment from "moment";
import { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import { notificationAction } from "../../actions-alpha/notification";
import { ApiUrl } from "../../api-alpha/apiUrls";
import PlusOutlined from "../../assets-alpha/images/svg/add.svg?react";
import ArrowDownIcon from "../../assets-alpha/images/svg/arrow-down-icon.svg?react";
import ArrowRightIcon from "../../assets-alpha/images/svg/arrow-right.svg?react";
import CalendarIcon from "../../assets-alpha/images/svg/calendar.svg?react";
import CheckIcon from "../../assets-alpha/images/svg/check.svg?react";
import MoneysIcon from "../../assets-alpha/images/svg/moneys.svg?react";
import ProfileIcon from "../../assets-alpha/images/svg/profile.svg?react";
import SendIcon from "../../assets-alpha/images/svg/send.svg?react";
import AiIcon from "../../assets-alpha/images/svg/ai.svg?react";

import CustomInput from "../../common-alpha/CustomInput/CustomInput";
import CustomSelect from "../../common-alpha/Select/Select";
import Modal from "../../components-alpha/Modal/Modal";
import { privateRoutes } from "../../Routes/routing";
import {
  DURATION_TYPE_LIST,
  STATUS_CODES
} from "../../utilities-alpha/constant";
import JobCard from "../FindProfessional/components/JobCard";
import {
  useFilterWithAiApi,
  useGetShortlists,
  useResumeSearchApi
} from "../FindProfessional/findProfessionalApi";
import {
  useCreateRequestApi,
  useGetCurrencyApi,
  useGetRecruiterContractsApi,
  useGetWorkplaceApi
} from "./dashboardApi";

const { Option } = Select;
const DashboardRecruiter = () => {
  const dashboard = useSelector(state => state.systemLabel.labels?.dashboard);
  const [currentView, setCurrentView] = useState("dashboard");
  const handleJobCreation = form => {
    message.success(dashboard?.jobInvitationCreated);
    setCurrentView("dashboard");
  };
  return (
    <div>
      {currentView === "jobCreation" && (
        <RenderJobCreationStep
          handleJobCreation={handleJobCreation}
          onCloseModal={() => setCurrentView("dashboard")}
        />
      )}
      <RenderDashboard setCurrentView={setCurrentView} />
    </div>
  );
};
const RenderJobCreationStep = ({ onCloseModal }) => {
  const [showShortlist, setShowShortlist] = useState(false);

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [currentStep, setCurrentStep] = useState(0);
  const [command, setCommand] = useState("");

  const {
    data: searchData,
    mutate: searchResumeApi,
    isPending: loadingResult
  } = useResumeSearchApi();
  const [shortlistResumes, setShortlistResumes] = useState([]);
  useEffect(() => {
    setShortlistResumes(searchData?.items);
  }, [searchData?.items]);

  const {
    mutateAsync: createRequestApi,
    isPending: loadingRequest
  } = useCreateRequestApi();

  const {
    data: workplaceData,
    refetch: refetchWorkplace
  } = useGetWorkplaceApi();

  const handleFilterSearch = filters => {
    searchResumeApi({
      CertificationIds: filters.certification.map(item => item.value),
      Countries: filters.location.map(item => item.value),
      IndusteryIds: filters.industry.map(item => item.value),
      KeywordIds: filters.keywords.map(item => item.value),
      Languages: filters.language.map(item => item.value),
      ProfileIds: filters.role.map(item => item.value),
      SkillIds: filters.skills.map(item => item.value),
      checked: [],
      Type: "Only20",
      limit: 20,
      page: 1
    });
    setCurrentStep(1);
  };

  const handleJobCreation = async form => {
    setLoading(true);
    try {
      await createRequestApi(
        {
          RequestName: form.RequestName,
          Duration: form.Duration,
          HourlyFee: form.HourlyFee,
          StartDate: moment().format(),
          FeeCurrencyType: form.FeeCurrencyType || 2,
          DurationType: form.DurationType || "Months",
          LanguageIds: [],
          LocationsIds: [],
          // UserCompanyId,
          SavedSearchedId: null,
          EndDate: moment().format(),
          IsSent: true,
          RequestCountries: null,
          ShortListIds: [],
          UserCompanyId: form.Workplace?.value || null,
          UserCompanyName: form.UserCompanyName || "",
          Description: form.Description,
          ResumesIds: shortlistResumes?.map(s => s.ResumeId)
        },
        {
          onSuccess: data => {
            queryClient.invalidateQueries({
              queryKey: [ApiUrl.UserCompany.Get]
            });
            queryClient.invalidateQueries({
              queryKey: [ApiUrl.Shortlists.Get]
            });
            queryClient.invalidateQueries({
              queryKey: [
                ApiUrl.Opportunity.GetSentRequest({ isFreelancer: false })
              ]
            });
            const info = {
              message: data?.message,
              status: data?.success ? "success" : "info"
            };
            dispatch(notificationAction(info));
            onCloseModal();
          }
        }
      );
    } catch (err) {
      console.log(err?.message);
    } finally {
      setLoading(false);
    }
  };
  return (
    <Modal
      width={showShortlist ? 1120 : 560}
      onCloseModal={onCloseModal}
      closable
      open={true}
      className={"max-md:!w-full"}
    >
      <Form
        name="profile"
        form={form}
        requiredMark={false}
        preserve
        layout="vertical"
        onFinish={handleJobCreation}
      >
        {currentStep === 0 && (
          <Step1Modal
            setCurrentStep={setCurrentStep}
            onCloseModal={onCloseModal}
            form={form}
            handleFilterSearch={handleFilterSearch}
            showShortlist={showShortlist}
            setShowShortlist={setShowShortlist}
            command={command}
            setCommand={setCommand}
          />
        )}
        {currentStep === 1 && (
          <Step2Modal
            form={form}
            onCloseModal={onCloseModal}
            shortlistResumes={shortlistResumes}
            setShortlistResumes={setShortlistResumes}
            loading={loading}
            showShortlist={showShortlist}
            setShowShortlist={setShowShortlist}
            workplaceData={workplaceData?.items || []}
            setCurrentStep={setCurrentStep}
            command={command}
          />
        )}
      </Form>
    </Modal>
  );
};
const Step1Modal = ({
  setCurrentStep,
  onCloseModal,
  form,
  handleFilterSearch,
  command,
  setCommand
}) => {
  const dashboard = useSelector(state => state.systemLabel.labels?.dashboard);
  const {
    mutate: mutateAi,
    isPending: loadingFilterResults
  } = useFilterWithAiApi();
  const handleApplyAi = aiCommand => {
    mutateAi(
      {
        keywords: aiCommand
      },
      {
        onSuccess: data => {
          const {
            Profiles,
            Skills,
            Keywords,
            Industries,
            Certifications,
            Countries,
            Languages
          } = data.items;
          const filters = {
            role: Profiles.map(item => ({
              label: item.ProfileValue,
              value: item.ProfileId
            })),
            skills: Skills.map(item => ({
              label: item.SkillValue,
              value: item.SkillId
            })),
            keywords: Keywords.map(item => ({
              label: item.KeywordValue,
              value: item.KeywordId
            })),
            industry: Industries.map(item => ({
              label: item.IndustryValue,
              value: item.IndustryId
            })),
            certification: Certifications.map(item => ({
              label: item.CertificationValue,
              value: item.CertificationId
            })),
            location: Countries.map(item => ({
              label: item.CountryName,
              value: item.CountryId
            })),
            language: Languages.map(item => ({
              label: item.LanguageValue,
              value: item.LanguageId
            }))
          };
          handleFilterSearch(filters);
        }
      }
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="max-md:pb-10">
        <div className="p-6 pb-0">
          <h2 className="text-2xl font-semibold text-gray-800 flex items-center">
            {dashboard?.phillipAi} <AiIcon className="text-[#8E81F5] w-8 h-8" />
          </h2>
          <Steps current={1} />
        </div>

        <div className="p-6 pt-0">
          <div
            className="mb-6 p-[1px] rounded-[13px]
              bg-[linear-gradient(90deg,#3864FD_0%,#D22CFF_50.26%,#F72384_100%)]"
          >
            <div className="bg-[#F6F6F6] rounded-[12px] p-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {dashboard?.writeCommand}
              </label>
              <CustomInput
                type="textarea"
                rows={8}
                name="command"
                value={command}
                onChange={e => setCommand(e.target.value)}
                placeholder={dashboard?.writeCommandPlaceholder}
                autoSize={{ minRows: 8 }}
                className="w-full [&_textarea]:!p-0 [&_textarea]:!rounded-none [&_textarea]:!bg-transparent [&_textarea]:!border-0"
              />
            </div>
          </div>
          <div className="flex items-center flex-wrap">
            <div className="flex-1"></div>
            {/* <div className="border-l-3 flex-1 border-[#F05800] !px-2 mb-6 leading-none">
              <p className="text-sm text-[#878787]">
                {dashboard?.poweredByPhillipAi}
              </p>
            </div> */}

            <div
              className="flex justify-end
              max-md:z-20 bg-white
              max-md:fixed max-md:inset-0 max-md:top-auto max-md:px-4 max-md:py-3 max-md:!mb-0
              max-md:border-t-1 border-t-[#EAE5FC]
            "
            >
              <Button
                type="primary"
                size="large"
                disabled={!command}
                className="max-md:!w-full"
                onClick={() => {
                  handleApplyAi(command);
                }}
                loading={loadingFilterResults}
              >
                {dashboard?.okImDone} →
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const Steps = ({ total = 2, current = 1 }) => {
  return (
    <div className="flex items-center gap-1.5 justify-center mt-3 mb-8">
      {Array(total)
        .fill(0)
        .map((n, i) => (
          <span
            className={twMerge(
              clsx("w-8 h-1 bg-[#F3F1FD] rounded-full", {
                "bg-[#8E81F5]": i < current
              })
            )}
            key={i}
          ></span>
        ))}
    </div>
  );
};
const Step2Modal = ({
  onCloseModal,
  form,
  shortlistResumes,
  setShortlistResumes,
  loading,
  showShortlist,
  setShowShortlist,
  workplaceData,
  setCurrentStep,
  command
}) => {
  const dashboard = useSelector(state => state.systemLabel.labels?.dashboard);
  const { data: currencyData } = useGetCurrencyApi();
  const { md } = Grid.useBreakpoint();
  return (
    <div className="max-w-6xl mx-auto ">
      <div
        className={twMerge(
          clsx("grid grid-cols-1 md:grid-cols-2 gap-6", {
            "md:grid-cols-1": !showShortlist
          })
        )}
      >
        {/* Left Panel - Job Creation Form */}
        {(md || !showShortlist) && (
          <div className="md:max-h-[calc(100vh-90px)] overflow-auto p-6 pt-0 max-md:pb-14">
            <h2 className="text-2xl font-semibold flex items-center text-gray-800 sticky top-0 z-10 pt-6 bg-white">
              {dashboard?.phillipAi}{" "}
              <AiIcon className="text-[#8E81F5] w-8 h-8" />
            </h2>
            <div className="">
              <Steps current={2} />

              <div className="mb-6">
                <h5 className="text-[16px] font-semibold mb-2">
                  {dashboard?.aiCommand}
                </h5>
                <div
                  className="
                  p-[1px]
                  rounded-[14px]
                  bg-[linear-gradient(90deg,#3864FD_0%,#D22CFF_50.26%,#F72384_100%)]
                "
                >
                  <div className="flex items-center justify-between gap-2 p-3 bg-[#f6f6f6] rounded-xl">
                    <span className="text-[13px] font-medium">{command}</span>
                    <Button
                      type="link"
                      size="small"
                      onClick={() => setCurrentStep(0)}
                    >
                      {dashboard?.editCommand}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <h5 className="text-[16px] font-semibold mb-2">
                  {dashboard?.iPros}
                </h5>
                <div
                  className="
                  p-[1px]
                  rounded-[14px]
                  bg-[linear-gradient(90deg,#3864FD_0%,#D22CFF_50.26%,#F72384_100%)]
                  "
                >
                  <div
                    onClick={() => setShowShortlist(true)}
                    className="cursor-pointer flex items-center justify-between gap-2 p-3 bg-[#f6f6f6] rounded-xl 
                  "
                  >
                    <span className="text-[13px] font-medium text-[var(--purple)]">
                      {dashboard?.viewShortlistedProfiles}
                    </span>
                    <Button type="link" size="small">
                      <SendIcon />
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <div className="">
              <h3 className="text-lg font-semibold mb-4">
                {dashboard?.createJobInvitation}
              </h3>

              <CustomInput
                label={dashboard?.jobName}
                name="RequestName"
                layout="vertical"
                className={"!mb-6"}
                rules={[
                  { required: true, message: dashboard?.pleaseEnterJobName }
                ]}
                placeholder={dashboard?.writeName}
              />

              <div className={"grid grid-cols-2 gap-4"}>
                <CustomInput
                  label={dashboard?.hourlyFee}
                  name="HourlyFee"
                  rules={[
                    { required: true, message: dashboard?.pleaseEnterHourlyFee }
                  ]}
                  placeholder={dashboard?.hourlyFeePlaceholder}
                  layout="vertical"
                  className="w-full !mb-6 [&_.ant-input-group-addon]:!p-0 [&_.ant-input-group-addon]:!border-2 [&_.ant-input-group-addon]:!border-[#F3F1FD] [&_.ant-input-group-addon]:!bg-white
                  max-md:[&_input]:!h-10
                  "
                  addonAfter={
                    <Form.Item
                      name="FeeCurrencyType"
                      className="w-[100px] !mb-0"
                    >
                      <Select
                        defaultValue="USD"
                        popupClassName="[&_.ant-select-item]:!items-center [&_.ant-select-item]:!text-center"
                      >
                        {currencyData?.items?.map(c => (
                          <Option key={c.CurrencyId} value={c.CurrencyId}>
                            {c.Name}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  }
                />
                <CustomInput
                  label={dashboard?.duration}
                  name="Duration"
                  layout="vertical"
                  rules={[
                    { required: true, message: dashboard?.pleaseSelectDuration }
                  ]}
                  placeholder={dashboard?.durationPlaceholder}
                  className="w-full !mb-6 [&_.ant-input-group-addon]:!p-0 [&_.ant-input-group-addon]:!border-2 [&_.ant-input-group-addon]:!border-[#F3F1FD] [&_.ant-input-group-addon]:!bg-white
                  max-md:[&_input]:!h-10
                  "
                  addonAfter={
                    <Form.Item name="DurationType" className="w-[100px] !mb-0">
                      <Select
                        defaultValue="Months"
                        popupClassName="[&_.ant-select-item]:!items-center [&_.ant-select-item]:!text-center"
                      >
                        {DURATION_TYPE_LIST.map(item => (
                          <Option key={item.value} value={item.value}>
                            {item.label}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  }
                />
              </div>
              {workplaceData?.length > 0 ? (
                <CustomSelect
                  label={dashboard?.workplace}
                  name="Workplace"
                  layout="vertical"
                  rules={[
                    { required: true, message: dashboard?.pleaseEnterWorkplace }
                  ]}
                  placeholder={dashboard?.workplaceNamePlaceholder}
                  options={workplaceData?.map(item => ({
                    label: item.CompanyName,
                    value: item.UserCompanyId
                  }))}
                  mainClassName="!mb-6"
                />
              ) : (
                <CustomInput
                  label={dashboard?.workplace}
                  name="UserCompanyName"
                  layout="vertical"
                  className={"!mb-6"}
                  rules={[
                    { required: true, message: dashboard?.pleaseEnterWorkplace }
                  ]}
                  placeholder={dashboard?.workplaceNamePlaceholder}
                />
              )}
              <CustomInput
                label={dashboard?.description}
                name="Description"
                rules={[
                  {
                    required: true,
                    message: dashboard?.pleaseEnterJobDescription
                  }
                ]}
                className={"!mb-6"}
                layout="vertical"
                type="textarea"
                rows={6}
                placeholder={dashboard?.descriptionPlaceholder}
              />
              <div className="flex items-center justify-end mt-6">
                {/* <div className="border-l-3 border-[#F05800] px-4">
                  <p className="text-sm text-gray-600">
                    {dashboard?.onceYouCreateTheJobInvitation}
                  </p>
                </div> */}

                <div
                  className="flex justify-end
                  max-md:z-20 bg-white
                  max-md:fixed max-md:inset-0 max-md:top-auto max-md:px-4 max-md:py-3 max-md:!mb-0
                  max-md:border-t-1 border-t-[#EAE5FC]
                "
                >
                  <Button
                    loading={loading}
                    onClick={form.submit}
                    type="primary"
                    className="max-md:!w-full"
                  >
                    {dashboard?.soundsGoodLetGo} →
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Right Panel - Shortlisted Professionals */}
        {showShortlist && (
          <div className="">
            <div className="pt-6 max-md:pl-6">
              <div className="flex items-center">
                <ArrowDownIcon
                  onClick={() => setShowShortlist(false)}
                  className="mr-4.5 rotate-90 cursor-pointer"
                />
                <h3 className="text-xl font-semibold">
                  {dashboard?.shortlistediPros}
                </h3>
              </div>
            </div>

            <div className="md:max-h-[calc(100vh-90px)] overflow-auto p-6 pt-0 pl-0 max-md:pl-6">
              <div className="space-y-4">
                {shortlistResumes?.map(resume => (
                  <JobCard
                    key={resume.ResumeId}
                    {...resume}
                    onRemove={() =>
                      setShortlistResumes(resumes =>
                        resumes.filter(r => r.ResumeId !== resume.ResumeId)
                      )
                    }
                    onResumeOpen={() => {}}
                    onSelectItem={() => {}}
                  />
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
const RenderDashboard = ({ setCurrentView }) => {
  const dashboard = useSelector(state => state.systemLabel.labels?.dashboard);
  const navigate = useNavigate();
  const { data: shortListData } = useGetShortlists();
  const { data: contractsData } = useGetRecruiterContractsApi();
  const { data: currencyData } = useGetCurrencyApi();

  const { data: workplaceData } = useGetWorkplaceApi();
  const step1Completed = workplaceData?.items?.length > 0;
  const step2Completed = shortListData?.length > 0;
  const step3Completed = contractsData?.items?.length > 0;
  const step4Completed = contractsData?.items?.length > 0;
  const filterContracts = useMemo(
    () => contractsData?.items?.filter(c => c.Status === 1 || c.Status === 2),
    [contractsData?.items]
  );
  return (
    <div>
      {/* <div className="max-w-7xl mx-auto p-6">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">Dashboard</h1> */}

      {/* AI Recruiter Banner */}
      <div
        className="bg-[#370064] bg-[url('/assets/images/dashboard-recruiter.svg')] bg-repeat-x bg-[right_center] max-md:bg-[83%_center] bg-cover
          p-8 mb-8 text-white relative rounded-2xl"
      >
        <div className="relative z-10">
          <h2 className="text-2xl font-bold mb-4">
            {dashboard?.createYourNextJobWithAi}
          </h2>
          <p className="!mb-8 max-w-2xl text-[#AFAFAF] text-[16px]">
            {dashboard?.meetAiRecruiter}
          </p>
          <Button
            size="large"
            className="!text-[#300151] !bg-white hover:bg-gray-100 font-semibold"
            onClick={() => setCurrentView("jobCreation")}
          >
            {dashboard?.getStarted}
          </Button>
        </div>
      </div>

      {/* Active Contracts or Onboarding Steps */}
      <div className="">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-gray-800">
            {dashboard?.activeContracts}
          </h3>
          {filterContracts?.length > 0 && (
            <Button
              type="primary"
              onClick={() =>
                navigate(privateRoutes.searcherAcceptedCollaborations.path)
              }
            >
              <PlusOutlined />
              {dashboard?.newHire}
            </Button>
          )}
        </div>

        {filterContracts?.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filterContracts
              ?.filter((c, i) => i < 5)
              ?.map(contract => (
                <TimesheetCard
                  key={contract.CollaborationId}
                  contract={contract}
                  currencyType={
                    currencyData?.items?.find(
                      c =>
                        c.CurrencyId === parseInt(contract.HourlyRateType, 10)
                    )?.Name
                  }
                />
              ))}
            {filterContracts?.length >= 5 && (
              <EmptyCard
                link={privateRoutes.searcherAcceptedCollaborations.path}
              />
            )}
          </div>
        ) : (
          <div className="">
            <h4 className="text-sm font-semibold mb-3 text-[#878787] ">
              {dashboard?.completeYourFirstHireStepByStep}
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <StepCard
                completed={step1Completed}
                title={dashboard?.workplace}
                detail={
                  step1Completed
                    ? dashboard?.step1CompletedDetail
                    : dashboard?.step1Detail
                }
                btnText={dashboard?.createWorkspace}
                stepNo={1}
                link={privateRoutes.companies.path}
              />
              <StepCard
                completed={step2Completed}
                title={dashboard?.findProfessional}
                detail={
                  step2Completed
                    ? dashboard?.step2CompletedDetail
                    : dashboard?.step2Detail
                }
                btnText={dashboard?.findNow}
                stepNo={2}
                link={privateRoutes.findProfessional.path}
              />

              <StepCard
                completed={step3Completed}
                title={dashboard?.invitation}
                detail={
                  step3Completed
                    ? dashboard?.step3CompletedDetail
                    : dashboard?.step3Detail
                }
                btnText={dashboard?.sendInvite}
                stepNo={3}
                link={privateRoutes.searcherAcceptedCollaborations.path}
              />

              <StepCard
                completed={step4Completed}
                title={dashboard?.contract}
                detail={
                  step4Completed
                    ? dashboard?.step4CompletedDetail
                    : dashboard?.step4Detail
                }
                btnText={dashboard?.createContract}
                stepNo={3}
                link={privateRoutes.searcherAcceptedCollaborations.path}
              />
            </div>
          </div>
        )}
      </div>
      {/* </div> */}
    </div>
  );
};

export default DashboardRecruiter;

const StepCard = ({ completed, title, detail, stepNo, btnText, link }) => {
  const navigate = useNavigate();
  const dashboard = useSelector(state => state.systemLabel.labels?.dashboard);
  return (
    <Card
      className={`!rounded-2xl border-none ${
        completed
          ? "!bg-[#F3F1FD]"
          : "bg-white !border-2 !border-dashed !border-gray-300"
      }`}
    >
      {completed ? (
        <div className="flex flex-col space-y-3 items-start">
          <CheckIcon />
          <div className="flex items-start space-x-2">
            <div>
              <h3 className="text-base font-semibold text-[#343333] mb-1">
                {title}
              </h3>
              <p className="text-sm text-[#878787]">{detail}</p>
            </div>
          </div>
          <Button
            onClick={() => navigate(link)}
            className="mt-3 !text-[#34C759] !shadow-none !rounded-full !bg-white !text-xs !h-[27px]"
          >
            {dashboard?.completed}
          </Button>
        </div>
      ) : (
        <div className="flex flex-col space-y-6">
          <div>
            <div className="inline-flex items-center p-0.5 text-sm font-medium space-x-2 bg-[#F3F1FD] rounded-4xl text-[#878787] mb-3">
              <span className="px-2 py-0.5">{dashboard?.step}</span>
              <span className="text-sm w-7 h-7 bg-white rounded-full flex items-center justify-center">
                {stepNo}
              </span>
            </div>
            <h3 className="text-base font-semibold text-[#343333] mb-1 ">
              {title}
            </h3>
            <p className="text-sm text-[#878787]">{detail}</p>
          </div>
          <Button
            type="primary"
            onClick={() => navigate(link)}
            className="!bg-[#F3F1FD] !h-[27px] !text-[var(--purple)] !rounded-full w-fit !text-xs !font-medium"
          >
            {btnText}
          </Button>
        </div>
      )}
    </Card>
  );
};

const TimesheetCard = ({ contract, currencyType }) => {
  const navigate = useNavigate();
  return (
    <Card className="!bg-[#F3F1FD] rounded-2xl !border-none !p-4 [&_.ant-card-body]:!p-0">
      {/* Header */}
      <div className="flex items-start space-x-4 gap-4">
        <Avatar shape="square" size={50} src={contract?.userdetail?.Logo} />
        <div className="truncate">
          <h3 className="truncate text-lg font-semibold text-gray-800">
            {contract?.Title}
          </h3>
          <p className="text-sm text-gray-500">
            {contract?.company?.CompanyName}
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="mt-4 space-y-3 text-[13px] font-medium text-[#343333]">
        {/* User Row */}
        <div className="flex items-center space-x-3">
          <ProfileIcon className="text-[#878787] w-4" />
          <span>{contract?.IProName}</span>
        </div>

        {/* Dates Row */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <CalendarIcon className="text-[#878787] w-4" />
            <span>{moment(contract?.StartDate).format("DD MMMM YYYY")}</span>
          </div>
        </div>

        {/* Total */}
        <div className="flex items-center space-x-3">
          <MoneysIcon className="text-[#878787]" />
          <span>
            {contract?.HourlyRate} {currencyType}/h
          </span>
        </div>
      </div>

      {/* Pending Timesheets */}
      <div className="mt-4">
        <Button
          type="primary"
          className="!bg-[white] !shadow-none !rounded-full w-fit text-sm font-medium"
          style={{
            color: `${STATUS_CODES[contract?.Status]?.color}`
          }}
          onClick={() =>
            navigate(privateRoutes.searcherAcceptedCollaborations.path, {
              state: { selectedContract: contract }
            })
          }
        >
          {STATUS_CODES[contract?.Status]?.label}
        </Button>
      </div>
    </Card>
  );
};

export const EmptyCard = ({ link, className }) => {
  const dashboard = useSelector(state => state.systemLabel.labels?.dashboard);
  const navigate = useNavigate();
  return (
    <Card
      className={twMerge(
        "!border-[#EAE5FC] min-h-[230px] !border-1 rounded-2xl !p-4 [&_.ant-card-body]:!p-0 flex items-center justify-center",
        className
      )}
    >
      {/* Header */}

      <Button
        type="default"
        className="!bg-[#F4F2FE] !shadow-none w-fit text-sm font-medium"
        onClick={() => link && navigate(link)}
      >
        {dashboard?.viewAll}
        <ArrowRightIcon />
      </Button>
    </Card>
  );
};
