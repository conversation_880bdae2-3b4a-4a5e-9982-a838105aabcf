import { useEffect } from "react";
import PageWrapper from "../../components/PageWrapper/PageWrapper";
import { connect } from "react-redux";
import find from "lodash/find";
import { Accordion, AccordionItem } from "@szhsin/react-accordion";
import "./about.scss";
import { getFaqsApi, getAboutProdooListApi } from "./aboutApi";
import { onStateChangeAction } from "./aboutAction";
import { useNavigate } from "react-router-dom";
import Column from "../../common/Column/Column";
import Button from "../../common/Button/Button";
import List from "../../common/List/List";
import { htmlParser } from "../../utilities/helpers";

const About = props => {
  const navigate = useNavigate();

  useEffect(() => {
    if (props.isFetching) {
      getAboutList();
    }
  }, [props.isFetching]);

  const getAboutList = () => {
    getFaqsApi({ type: 1 }).then(data => {
      props.onStateChangeAction({
        faqsList: data.items,
        isFetching: false
      });
    });
    getAboutProdooListApi().then(data => {
      props.onStateChangeAction({ aboutProdooList: data.items });
      handleSelectAboutProdooList(1);
    });
  };

  const handleSelectFaqList = id => {
    const { faqsList } = props;
    let selectedItem = find(faqsList, { QuestionId: id });
    selectedItem = {
      ...selectedItem,
      title: selectedItem.Title,
      description: selectedItem.Answer,
      id: selectedItem.QuestionId
    };
    props.onStateChangeAction({ selectedItem });
  };

  const handleSelectAboutProdooList = id => {
    const { aboutProdooList } = props;
    let selectedItem = find(aboutProdooList, { ConfigurationId: id });
    selectedItem = {
      ...selectedItem,
      title: selectedItem.ConfigName,
      description: selectedItem.ConfigValue,
      id: selectedItem.ConfigurationId
    };
    props.onStateChangeAction({ selectedItem });
  };

  const handleExpanded = name => {
    const { expanded } = props;
    props.onStateChangeAction({
      expanded: {
        ...expanded,
        [name]: !expanded[name]
      }
    });
  };

  const { labels, isHelpActive } = props;
  const {
    faqsList,
    selectedItem,
    aboutProdooList,
    listCollapsed,
    detailCollapsed,
    expanded
  } = props;

  return (
    <PageWrapper className="about-page">
      <Column collapse={listCollapsed} className="col-1 w-full">
        <Column.Collapsed
          text={labels.ViewTitleAboutList}
          onClick={() => props.onStateChangeAction({ listCollapsed: false })}
          tooltipButton={labels.ToolTipAboutExpandList}
          isHelpActive={isHelpActive}
          tooltipHelp={labels.HlpTooltipAboutExpandListButton}
          tooltipPlace="left"
        />
        <Column.Head className>
          <div className="heading">{labels.ViewTitleAboutList}</div>
          <Button
            className="collapseBtn"
            tooltipButton={labels.ToolTipAboutCollaspeList}
            tooltipHelp={labels.HlpTooltipAboutCollapseListButton}
            tooltipPlace="left"
            isHelpActive={isHelpActive}
            onClick={() => props.onStateChangeAction({ listCollapsed: true })}
          />
        </Column.Head>
        <Column.Body>
          <Accordion>
            <AccordionItem
              header={labels.ABOUT_ABOUTPRODOO_HEADING}
              expanded={expanded.about}
            >
              <div>
                <List>
                  {aboutProdooList.map(item => (
                    <List.ListItem
                      onClick={() =>
                        handleSelectAboutProdooList(item.ConfigurationId)
                      }
                      isSelected={
                        item.ConfigurationId === selectedItem.ConfigurationId
                      }
                      key={item.ConfigurationId}
                    >
                      {item.ConfigName}
                    </List.ListItem>
                  ))}
                </List>
              </div>
            </AccordionItem>
          </Accordion>
          <Accordion>
            <AccordionItem
              expanded={expanded.faq}
              header={labels.ABOUT_FAQ_HEADING}
            >
              <div>
                <List>
                  {faqsList.map(item => (
                    <List.ListItem
                      onClick={() => handleSelectFaqList(item.QuestionId)}
                      isSelected={item.QuestionId === selectedItem.QuestionId}
                      key={item.QuestionId}
                    >
                      {item.Title}
                    </List.ListItem>
                  ))}
                </List>
              </div>
            </AccordionItem>
          </Accordion>
        </Column.Body>
      </Column>
      <Column collapse={detailCollapsed} className="col-2 w-full">
        <Column.Collapsed
          onClick={() => props.onStateChangeAction({ detailCollapsed: false })}
          text={labels.ViewTitleAboutDetail}
          tooltipButton={labels.ToolTipAboutExpandDetail}
          isHelpActive={isHelpActive}
          tooltipHelp={labels.HlpTooltipAboutExpandDetailButton}
          tooltipPlace="left"
        />
        <Column.Head>
          <div className="heading">{selectedItem.title}</div>
          <Button
            className="collapseBtn"
            tooltipButton={labels.ToolTipAboutCollaspeDetail}
            tooltipHelp={labels.HlpTooltipAboutCollapseDetailButton}
            tooltipPlace="left"
            isHelpActive={isHelpActive}
            onClick={() => props.onStateChangeAction({ detailCollapsed: true })}
          />
        </Column.Head>
        <Column.Body className="description">
          {htmlParser(selectedItem.description)}
        </Column.Body>
      </Column>
    </PageWrapper>
  );
};

const mapStateToProps = ({ systemLabel, navigation, about }) => {
  const { labels } = systemLabel;
  const { isHelpActive } = navigation;
  return { labels, isHelpActive, ...about };
};

const mapDispatchToProps = dispatch => ({
  onStateChangeAction: payload => dispatch(onStateChangeAction(payload))
});

export default connect(mapStateToProps, mapDispatchToProps)(About);
