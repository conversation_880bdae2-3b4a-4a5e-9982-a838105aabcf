import { LoadingOutlined, MoreOutlined, PlusOutlined } from "@ant-design/icons";
import { useQueryClient } from "@tanstack/react-query";
import { Button, Input, Dropdown } from "antd";
import { isEmpty, omitBy } from "lodash";
import { useState } from "react";
import { twMerge } from "tailwind-merge";
import { ApiUrl } from "../../../api-alpha/apiUrls";
import AiIcon from "../../../assets-alpha/images/svg/ai.svg?react";
import CloseModal from "../../../assets-alpha/images/svg/close.svg?react";
import SaveFilterIcon from "../../../assets-alpha/images/view/save-filter.svg?react";
import SegmentedUi from "../../../common-alpha/SegmentedUi/SegmentedUi";
import NameInputModal from "../../../components-alpha/Modal/NameInputModal";
import { formatDate } from "../../../utilities/helpers";

import {
  useCertificationsLookupApi,
  useDeleteAllSavedSearch,
  useFilterWithAiApi,
  useIndustriesLookupApi,
  useKeyworkLookupApi,
  useLanguageLookupApi,
  useLocationLookupApi,
  useNewSaveSearchApi,
  useRolesLookupApi,
  useSaveSearchApi,
  useSaveSearchDetailApi,
  useSaveSearchRenameApi,
  useSkillsLookupApi
} from "../findProfessionalApi";
import { useDeleteSaveSearches, useOutsideClick } from "../hooks";
import clsx from "clsx";
import { DEFAULT_FILTERS, DropdownMenu } from "../constant";
import { notificationAction } from "../../../actions-alpha/notification";
import { useDispatch, useSelector } from "react-redux";
import EmptyView from "../../../common-alpha/EmptyView/EmptyView";
import CommonDrawer from "../../../common-alpha/Drawer/Drawer";
import CustomSelect from "../../../common-alpha/Select/Select";

const { TextArea } = Input;
const TABS = [
  { key: "search", label: "Search filter" },
  { key: "saved", label: "Saved filter" }
];

const FilterDrawer = ({
  open,
  onClose,
  onApplyFilter,
  appliedFilters,
  onSaveSearchSelection,
  onApplyAi,
  setFilters
}) => {
  const findProfessional = useSelector(
    state => state.systemLabel.labels?.findProfessional
  );
  const [aiMode, setAiMode] = useState(false);
  const [selectedTab, setSelectedTab] = useState(TABS[0]);
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const onSelectedTab = label => {
    const selected = TABS.find(tab => tab.label === label);
    setSelectedTab(selected);
  };
  const {
    data: savedSearchData,
    isLoading: loadingSaveSearch
  } = useSaveSearchApi();

  const {
    mutate: deleteSearchHistory,
    isPending: isDeleting
  } = useDeleteAllSavedSearch();
  const deleteHistory = () => {
    deleteSearchHistory(
      {},
      {
        onSuccess: data => {
          const info = {
            message: data?.message,
            status: data?.success ? "success" : "info"
          };
          dispatch(notificationAction(info));
          queryClient.invalidateQueries({
            queryKey: [ApiUrl.ResumeSearches.SavedSearchesBase]
          });
        },
        onError: error => {
          const info = {
            message: error?.message,
            status: "error"
          };
          dispatch(notificationAction(info));
        }
      }
    );
  };

  return (
    <CommonDrawer
      title={findProfessional?.filterWith}
      onClose={onClose}
      open={open}
      width={500}
    >
      <div className="flex justify-between items-center mb-5">
        <SegmentedUi
          className={clsx(`
            [&_.ant-segmented-item-label]:!p-[12px_15px] !m-0
            `)}
          options={TABS.map(tab => tab.label)}
          value={selectedTab.label}
          onChange={onSelectedTab}
        />

        {selectedTab.key === TABS[0].key ? (
          !isEmpty(omitBy(appliedFilters, isEmpty)) ? (
            <Button
              type="link"
              onClick={() => setFilters(DEFAULT_FILTERS)}
              className="!text-[var(--red)] !p-0 h-auto"
            >
              {findProfessional?.clearFilter}
              <CloseModal />
            </Button>
          ) : (
            <Button
              type="link"
              className={twMerge(
                `![-webkit-text-fill-color:transparent] ![background-clip:text] 
            !bg-[linear-gradient(90deg,#3864FD_0%,#D22CFF_50%,#F72384_100%)]
            !pr-0
            `,
                "text-white"
              )}
              onClick={() => setAiMode(true)}
            >
              {findProfessional?.filterWithAi}{" "}
              <AiIcon className="text-[#3864FD]" />
            </Button>
          )
        ) : (
          !!savedSearchData?.length && (
            <Button
              type="link"
              onClick={deleteHistory}
              loading={isDeleting}
              className="!text-[var(--red)] !p-0 h-auto"
            >
              {findProfessional?.clearHistory}
            </Button>
          )
        )}
      </div>

      {selectedTab.key === TABS[0].key ? (
        <SearchFilter
          aiMode={aiMode}
          setAiMode={setAiMode}
          onApplyFilter={onApplyFilter}
          appliedFilters={appliedFilters}
          onApplyAi={onApplyAi}
          setFilters={setFilters}
        />
      ) : (
        <SavedFilter
          onSaveSearchSelection={onSaveSearchSelection}
          data={savedSearchData}
          loadingSaveSearch={loadingSaveSearch}
        />
      )}
    </CommonDrawer>
  );
};

export default FilterDrawer;

const SavedFilter = ({ onSaveSearchSelection, data, loadingSaveSearch }) => {
  const findProfessional = useSelector(
    state => state.systemLabel.labels?.findProfessional
  );
  return (
    <div className="space-y-3 mt-5">
      {/* <div className="flex justify-between items-center">
        <span className="text-lg font-semibold">{TABS[1].label}</span>
      </div> */}
      {loadingSaveSearch && <LoadingOutlined />}
      {data?.length === 0 && (
        <EmptyView
          noBorder
          title={findProfessional?.looksEmpty}
          detail={findProfessional?.saveFiltersToApplyNextTime}
          icon={<SaveFilterIcon />}
        />
      )}
      {data?.map(item => (
        <SaveFilterItem
          item={item}
          key={item.SavedSearchId}
          onSaveSearchSelection={onSaveSearchSelection}
        />
      ))}
    </div>
  );
};

const SaveFilterItem = ({ item, onSaveSearchSelection }) => {
  const { loadingDelete, onDeleteSearch } = useDeleteSaveSearches();
  const queryClient = useQueryClient();
  const [isModalOpen, setModalOpen] = useState(false);
  const findProfessional = useSelector(
    state => state.systemLabel.labels?.findProfessional
  );
  const {
    mutate: renameApi,
    isPending: loadingRenaming,
    error: errorRenaming
  } = useSaveSearchRenameApi();
  const {
    mutate: saveDetailApi,
    isPending: loadingDetail
  } = useSaveSearchDetailApi();

  const onSelectedSaveSearch = item => {
    saveDetailApi(
      { suffixUrl: `?savedSearchId=${item.SavedSearchId}` },
      {
        onSuccess: data => {
          onSaveSearchSelection(data);
        }
      }
    );
  };

  const onRenameSaveSearch = name => {
    renameApi(
      {
        suffixUrl: `?savedSearchId=${item.SavedSearchId}&savedSearchNewName=${name}`
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: [ApiUrl.ResumeSearches.SavedSearchesBase]
          });
          setModalOpen(false);
        }
      }
    );
  };

  return (
    <div className="flex justify-between items-center py-2">
      <div>
        <Button
          type="link"
          loading={loadingDetail || loadingDelete}
          onClick={() => onSelectedSaveSearch(item)}
          className="!p-0 !text-[var(--purple)] !h-auto font-medium !underline !whitespace-normal !text-left"
        >
          {item.SearchName}
        </Button>
        <p className="text-xs text-gray-500">{formatDate(item.UpdatedOn)}</p>
      </div>
      <Dropdown
        overlayClassName="[&_.ant-dropdown-menu]:!py-[6px] [&_.ant-dropdown-menu-item]:!py-[6px]"
        menu={{
          ...DropdownMenu(findProfessional),

          onClick: e => {
            if (e.key === "rename") {
              setModalOpen({ ...item, title: findProfessional?.renameSearch });
            } else if (e.key === "delete") {
              onDeleteSearch(item);
            }
          }
        }}
        trigger={["click"]}
      >
        <Button type="text" icon={<MoreOutlined className="!text-[20px]" />} />
      </Dropdown>

      <NameInputModal
        isOpen={isModalOpen}
        title={isModalOpen?.title}
        initialValue={isModalOpen?.SearchName}
        placeholder={findProfessional?.saveSearchNamePlaceholder}
        confirmText={findProfessional?.save}
        onConfirm={onRenameSaveSearch}
        onClose={() => setModalOpen(false)}
        isLoading={loadingRenaming}
        error={errorRenaming?.message}
      />
    </div>
  );
};

const SearchFilter = ({
  aiMode,
  setAiMode,
  onApplyFilter,
  appliedFilters: filters,
  onApplyAi,
  setFilters
}) => {
  const findProfessional = useSelector(
    state => state.systemLabel.labels?.findProfessional
  );
  const dispatch = useDispatch();
  const [StartDate, setStartDate] = useState(null);
  const [isMore, setIsMore] = useState(
    filters.keywords?.length > 0 ||
      filters.industry?.length > 0 ||
      filters.certification?.length > 0
  );
  const queryClient = useQueryClient();
  const [isModalOpen, setModalOpen] = useState(false);
  const {
    mutate: saveFilterApi,
    isPending: loadingSaveFilter,
    error: errorSaveFilter
  } = useNewSaveSearchApi();

  const [error, setError] = useState("");
  const [aiCommand, setAiCommand] = useState("");
  const [searchKey, setSearchKey] = useState({
    role: "",
    skills: "",
    location: "",
    language: "",
    keywords: "",
    industry: "",
    certification: ""
  });
  const { mutate: mutateAi, isPending } = useFilterWithAiApi();

  const { data: rolesData, isLoading: loadingRoles } = useRolesLookupApi(
    searchKey.role
  );
  const {
    data: locationData,
    isLoading: loadingLocation
  } = useLocationLookupApi(searchKey.location);
  const {
    data: languageData,
    isLoading: loadingLanguage
  } = useLanguageLookupApi(searchKey.language);
  const { data: skillsData, isLoading: loadingSkills } = useSkillsLookupApi(
    searchKey.skills
  );
  const {
    data: industryData,
    isLoading: loadingIndustry
  } = useIndustriesLookupApi(searchKey.industry);
  const {
    data: certificationData,
    isLoading: loadingCertification
  } = useCertificationsLookupApi(searchKey.certification);
  const { data: keywordData, isLoading: loadingKeyword } = useKeyworkLookupApi(
    searchKey.keywords
  );

  const onSearch = (value, name) => {
    setSearchKey(val => ({ ...val, [name]: value }));
  };

  const onSelectTags = (item, name) => {
    setFilters(val => {
      // const selectedTags = uniqBy(
      //   val[name].find(tag => tag.value === item.value)
      //     ? val[name].filter(tag => tag.value !== item.value)
      //     : [...val[name], item],
      //   "value"
      // );

      // return { ...val, [name]: selectedTags };
      return { ...val, [name]: item };
    });
  };

  const onDateChange = date => {
    setStartDate(date);
  };

  const onSaveNewFilter = name => {
    saveFilterApi(
      {
        ReplaceExisting: !!error,
        SearchName: name,
        SavedSearchId: 0,
        Certifications: filters.certification.map(n => ({
          Id: n.value,
          LookupValue: n.label,
          ExpLevel: 0
        })),
        Countries: filters.location.map(n => ({
          Id: n.value,
          ExpLevel: 0,
          LookupValue: n.label
        })),
        Industries: filters.industry.map(n => ({
          Id: n.value,
          LookupValue: n.label,
          ExpLevel: 0
        })),
        Keywords: filters.keywords.map(n => ({
          Id: n.value,
          LookupValue: n.label,
          ExpLevel: 0
        })),
        Languages: filters.language.map(n => ({
          Id: n.value,
          LookupValue: n.label,
          ExpLevel: 0
        })),
        Profiles: filters.role.map(n => ({
          Id: n.value,
          LookupValue: n.label,
          ExpLevel: 0
        })),
        Skills: filters.skills.map(n => ({
          Id: n.value,
          LookupValue: n.label,
          ExpLevel: 0
        }))
      },
      {
        onSuccess: res => {
          if (res.success) {
            queryClient.invalidateQueries({
              queryKey: [ApiUrl.ResumeSearches.SavedSearchesBase]
            });
            setModalOpen(false);
            const info = {
              message: res?.message,
              status: res?.success ? "success" : "info"
            };
            dispatch(notificationAction(info));
            setError("");
          } else {
            setError(
              res.message?.includes("already exists")
                ? findProfessional?.alreadySavedWantToReplace
                : res.message
            );
          }
        }
      }
    );
  };
  return (
    <div className="space-y-4 max-md:pb-[30px] flex-1 flex flex-col">
      {aiMode ? (
        <div>
          <div className="mt-5 border p-4 rounded-lg border-violet-300 bg-[#F6F6F6]">
            <div className="flex justify-between items-center">
              <p className="font-semibold">{findProfessional?.writeCommand}</p>
              <Button
                className="!bg-transparent !border-0 !shadow-none !text-[#878787]"
                icon={<CloseModal />}
                size="small"
                onClick={() => setAiMode(false)}
              />
            </div>
            <TextArea
              onChange={e => setAiCommand(e.target.value)}
              value={aiCommand}
              placeholder="Simply write or paste a job description"
              rows={5}
              className="!mt-2 !p-0 !rounded-none !bg-transparent !border-0 [&:placeholder-shown]:![opacity:1] [&::placeholder]:![color:#878787] "
            />
          </div>
          <Button
            onClick={() => onApplyAi(aiCommand)}
            block
            loading={isPending}
            type="primary"
            className="mt-4 text-white rounded-md"
          >
            {findProfessional?.apply}
          </Button>
        </div>
      ) : (
        <>
          {false && (
            <div className="flex justify-between items-center">
              <Input
                placeholder="Filter with Ai ✨"
                readOnly
                className="rounded-xl border border-violet-400"
                onClick={() => setAiMode(true)}
              />
              <Button type="link" onClick={() => setAiMode(true)}>
                {findProfessional?.editCommand}
              </Button>
            </div>
          )}
          <div>
            <p className="mb-1 font-medium">{findProfessional?.role}</p>
            <RenderTags
              items={filters.role}
              name="role"
              placeholder={findProfessional?.addRole}
              onSearch={onSearch}
              searchValue={searchKey.role}
              data={rolesData}
              loading={loadingRoles}
              onSelect={onSelectTags}
            />
          </div>
          <div>
            <p className="mb-1 font-medium">{findProfessional?.skills}</p>
            <RenderTags
              items={filters.skills}
              name="skills"
              placeholder={findProfessional?.addSkills}
              onSearch={onSearch}
              searchValue={searchKey.skills}
              data={skillsData}
              loading={loadingSkills}
              onSelect={onSelectTags}
            />
          </div>
          <div>
            <p className="mb-1 font-medium">{findProfessional?.targetRegion}</p>
            <RenderTags
              items={filters.location}
              name="location"
              placeholder={findProfessional?.addLocation}
              onSearch={onSearch}
              searchValue={searchKey.location}
              data={locationData}
              loading={loadingLocation}
              onSelect={onSelectTags}
            />
          </div>
          <div>
            <p className="mb-1 font-medium">{findProfessional?.language}</p>
            <RenderTags
              items={filters.language}
              name="language"
              placeholder={findProfessional?.addLanguage}
              onSearch={onSearch}
              searchValue={searchKey.language}
              data={languageData}
              loading={loadingLanguage}
              onSelect={onSelectTags}
            />
          </div>
          {isMore ? (
            <>
              <div>
                <p className="mb-1 font-medium">{findProfessional?.keywords}</p>
                <RenderTags
                  items={filters.keywords}
                  name="keywords"
                  placeholder={findProfessional?.addKeywords}
                  onSearch={onSearch}
                  searchValue={searchKey.keywords}
                  data={keywordData}
                  loading={loadingKeyword}
                  onSelect={onSelectTags}
                />
              </div>

              <div>
                <p className="mb-1 font-medium">{findProfessional?.industry}</p>
                <RenderTags
                  items={filters.industry}
                  name="industry"
                  placeholder={findProfessional?.addIndustry}
                  onSearch={onSearch}
                  searchValue={searchKey.industry}
                  data={industryData}
                  loading={loadingIndustry}
                  onSelect={onSelectTags}
                />
              </div>

              <div>
                <p className="mb-1 font-medium">
                  {findProfessional?.certification}
                </p>
                <RenderTags
                  items={filters.certification}
                  name="certification"
                  placeholder={findProfessional?.addCertification}
                  onSearch={onSearch}
                  searchValue={searchKey.certification}
                  data={certificationData}
                  loading={loadingCertification}
                  onSelect={onSelectTags}
                />
              </div>

              {/* <div>
            <p className="mb-1 font-medium">Availability</p>
            <DatePickerField
              name="DatePicker"
              dateFormat="DD-MM-YYYY"
              selected={StartDate && moment(StartDate)}
              onChange={onDateChange}
              todayButton="Today"
              // placeholder={labels.collStartDate}
              // className={``}
              minDate={moment()}
              placeholderText={"DD-MM-YYYY"}
            />
          </div> */}
            </>
          ) : (
            <Button
              onClick={() => setIsMore(true)}
              className="!p-0 !max-w-fit"
              type="link"
            >
              <PlusOutlined />
              {findProfessional?.moreFilters}
            </Button>
          )}
          {!isEmpty(omitBy(filters, isEmpty)) && (
            <div
              className="flex justify-between gap-2 pt-4 z-20 bg-white
              max-md:fixed max-md:inset-0 max-md:top-auto max-md:px-4 max-md:py-3 
              mt-auto
            "
            >
              <Button
                onClick={() => setModalOpen(true)}
                block
                className="bg-[#F3F1FD]"
              >
                {findProfessional?.saveFilter}
              </Button>
              <Button
                onClick={() => onApplyFilter(filters)}
                block
                type="primary"
                className="text-white"
              >
                {findProfessional?.applyFilter}
              </Button>
            </div>
          )}
        </>
      )}
      {isModalOpen && (
        <NameInputModal
          isOpen={isModalOpen}
          title={findProfessional?.saveFilter}
          confirmText={
            error ? findProfessional?.replace : findProfessional?.save
          }
          placeholder={findProfessional?.saveFilterPlaceholder}
          onConfirm={onSaveNewFilter}
          onClose={() => setModalOpen(false)}
          isLoading={loadingSaveFilter}
          error={errorSaveFilter?.message || error}
        />
      )}
    </div>
  );
};

const RenderTags = ({
  items,
  name,
  onSearch,
  searchValue,
  data = [],
  onSelect,
  loading,
  placeholder
}) => {
  const { ref, hasFocusInput, setHasFocusInput } = useOutsideClick(() => {
    setHasFocusInput(false);
  });
  const findProfessional = useSelector(
    state => state.systemLabel.labels?.findProfessional
  );

  return (
    <label
      ref={ref}
      // className="relative block border-[#EAE5FC] border-1 p-[8px] rounded-[12px] mt-2"
    >
      <CustomSelect
        placeholder={`${findProfessional?.select} ${name}`}
        mainClassName="!mb-2"
        layout={"vertical"}
        showSearch
        optionFilterProp="label"
        className=" [&_.ant-select-selector]:!p-[8px] [&_.ant-select-selector]:max-md:!p-[6px] [&_.ant-select-selector]:!rounded-[12px]
          [&_.ant-select-selector]:!bg-transparent !h-auto
          "
        // value={searchValue}
        value={items}
        onChange={(value, item) =>
          // setSelectedRole(role => ({ ...role, role: item }))
          onSelect(item, name)
        }
        mode="multiple"
        onSearch={
          // handleSearch(0)
          value => onSearch(value, name)
        }
        options={data}
        loading={loading}
        showSuffixIcon={false}
      />
      {/* <div className="flex flex-wrap gap-y-2" htmlFor={name}>
        <input
          onChange={e => onSearch(e.target.value, name)}
          onFocus={() => setHasFocusInput(true)}
          value={searchValue}
          id={name}
          placeholder={items?.length === 0 ? placeholder : ""}
          className="outline-0 flex-1 w-0 h-[30px] min-w-full"
        />
        {items.map(item => (
          <Tag className="!p-[5px_8px] !flex !items-center" key={item}>
            {item?.label}
            &nbsp;
            <CloseModal
              className="w-4 h-4"
              onClick={() => onSelect(item, name)}
            />
          </Tag>
        ))}
      </div>
      {hasFocusInput && searchValue && (
        <FilterSearch
          roles={data}
          name={name}
          onSelect={item => onSelect(item, name)}
          loading={loading}
          selectedRoles={items}
        />
      )} */}
    </label>
  );
};
