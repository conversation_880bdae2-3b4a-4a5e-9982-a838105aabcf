@use "../../../../assets/sass/importFiles" as *;

.btnContainer {
  justify-content: center;
  display: flex;
  align-items: center;
  .timerBtns {
    width: 100%;
    max-width: 300px;
    border: none;
    display: block;
    background: #04caa7;
    padding: 12px;
    margin: 20px 10px;
    border-radius: 3px;
    font-size: 16px;
    // font-family: "rubik", Arial, sans-sarif;
    color: #fff;
    line-height: 1;
    transition: all 0.3s;
  }
  .autoPadding {
    margin: 20px auto 0 !important;
  }

  .btn-red {
    background: #ff0000;
  }
  .btn-gre {
    background: #04caa7;
  }
}
.timerDisplayValues {
  margin: auto;
  width: 100%;
  background: #9783cf;
  text-align: center;
  span {
    font-size: 30px;
  }
}

.timerDisplayScreenshotInfo {
  margin: auto;
  width: 50%;
  background: #9783cf;
  text-align: center;
  span {
    font-size: 10px;
  }
}

.top-manual-buttons {
  margin: 0px 5px 16px 5px;
}

/* Screenshot Gallery styles */
.screenshot-section {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #ddd;
  background-color: #f0e6ff; /* Light purple background */
  border-radius: 5px;
  padding: 15px;
}

.screenshot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 0 5px;
}

.toggle-btn {
  background: #5755d9;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background: #4644c8;
  }
}

.screen-capture-status {
  color: #4caf50; /* Green text for "active" status */
  font-size: 14px;
}

.screenshot-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* Three columns layout */
  gap: 10px;
  overflow-y: auto;
  max-height: 300px;
  padding: 5px;
}

.screenshot-item {
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
  background-color: white;

  img {
    width: 100%;
    display: block;
    object-fit: cover;
    aspect-ratio: 16/9; /* Maintain consistent aspect ratio */
  }
}

.timestamp {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #9370db; /* Purple background */
  color: white;
  text-align: center;
  padding: 3px 0;
  font-size: 12px;
}
