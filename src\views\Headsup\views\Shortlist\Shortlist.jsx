import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import PageWrapper from "../../../../components/PageWrapper/PageWrapper";
import ShortlistResumes from "./component/ShortlistResumes";
import { notificationAction } from "../../../../actions/notification";
import toLower from "lodash/toLower";
import ShortlistResumeDetail from "./component/ShortlistResumeDetail";
import filter from "lodash/filter";
import find from "lodash/find";
import first from "lodash/first";
import moment from "moment";
import includes from "lodash/includes";
import {
  getShortlistApi,
  getShortlistResumeApi,
  postShortlistApi,
  deleteShortlistApi,
  getOppuntinitiesApi
} from "./shortlistApi";
import { privateRoutes } from "../../../../Routes/routing";

import "./shortlist.scss";
import { isEmpty } from "lodash";
import { deleteResumeApi } from "../../../Shortlist/shortlistApi";
import ConfirmDialog from "../../../../common/ConfirmDialog/ConfirmDialog";
import Column from "../../../../common/Column/Column";
import SearchInput from "../../../../common/Input/SearchInput";
import Button from "../../../../common/Button/Button";
import Input from "../../../../common/Input/Input";
import EmptyInfo from "../../../../common/EmptyInfo/EmptyInfo";
import LoadingMaskRow from "../../../../common/LoadingMask/LoadingMaskRow";

const Shortlist = props => {
  const [state, setState] = useState({
    searchCollapsed: false,
    resumeCollapsed: false,
    resumeDetailCollapsed: false,
    createCollapse: false,
    shortlist: [],
    selectedResume: {},
    selectedShortlist: {},
    shortlistResumes: [],
    filteredShortlist: [],
    isLoading: false,
    fetchingShortlist: true,
    fetchingResumes: false,
    opportunityList: [],
    fetchingOpportunities: false,
    dialogMessage: "",
    newShortlistValue: "",
    createNewShortlist: false,
    dialogOpportunityMessage: "",
    showOpportunities: false,
    opportunitiesCollapse: false,
    searchKey: "",
    deletedId: null
  });

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const { labels, isHelpActive } = useSelector(state => ({
    labels: state.systemLabel.labels,
    isHelpActive: state.navigation.isHelpActive
  }));

  useEffect(() => {
    getShortlist().then(() => {
      if (location) {
        const { state } = location;
        if (state) {
          const { shortlist } = state;
          handleSelectShortlist(shortlist);
        }
        const { onBoardShortlistId } = state;
        if (onBoardShortlistId) {
          getShortlist();
        }
      }
    });
  }, []);

  const getOpportunities = () => {
    setState(prevState => ({ ...prevState, fetchingOpportunities: true }));
    getOppuntinitiesApi()
      .then(data => {
        setState(prevState => ({
          ...prevState,
          opportunityList: data.items,
          fetchingOpportunities: false
        }));
        if (data.items.length > 0) {
          setState(prevState => ({
            ...prevState,
            showOpportunities: true
          }));
        } else {
          const info = {
            message: labels.InfoIShortlistNoQuickOpportunity,
            status: "info"
          };
          dispatch(notificationAction(info));
        }
      })
      .then(() => {
        setState(prevState => ({
          ...prevState,
          fetchingOpportunities: false
        }));
      });
  };

  const getShortlist = selectedId => {
    setState(prevState => ({ ...prevState, fetchingShortlist: true }));
    return getShortlistApi()
      .then(data => {
        setState(prevState => ({
          ...prevState,
          shortlist: data.items,
          filteredShortlist: data.items,
          fetchingShortlist: false
        }));
        const { onBoardShortlistId } = props;
        if (selectedId) {
          setState(prevState => ({
            ...prevState,
            shortlist: data.items,
            filteredShortlist: data.items
          }));
          handleSelectShortlist(
            data.items.find(a => a.ShortlistId == selectedId)
          );
        }
      })
      .catch(() => {
        setState(prevState => ({ ...prevState, fetchingShortlist: false }));
      });
  };

  const handleSelectShortlist = item => {
    setState(prevState => ({
      ...prevState,
      selectedShortlist: item,
      createNewShortlist: false,
      fetchingResumes: true,
      showOpportunities: false,
      clonedShortlist: {}
    }));
    getShortlistResumeApi(item.ShortlistId)
      .then(data => {
        setState(prevState => ({
          ...prevState,
          shortlistResumes: data.items.map((item, index) => ({
            ...item,
            active: index == 0 ? true : false
          })),
          fetchingResumes: false,
          selectedResume: first(data.items) || {}
        }));
      })
      .catch(() => {
        setState(prevState => ({ ...prevState, fetchingResumes: false }));
      });
  };

  const handleResumeSelect = item => {
    const { shortlistResumes } = state;
    const selectedResume = find(shortlistResumes, { ResumeId: item.ResumeId });
    setState(prevState => ({ ...prevState, selectedResume }));
  };

  const handleSearchChange = e => {
    const { value } = e.target;
    const { shortlist } = state;

    const filteredShortlist = filter(shortlist, opp =>
      includes(toLower(opp.ShortlistName), toLower(value))
    );
    setState(prevState => ({
      ...prevState,
      filteredShortlist,
      searchKey: value
    }));
  };

  const handleShortlistActive = activeItem => {
    const shortlistResumes = state.shortlistResumes.map(item => ({
      ...item,
      active: activeItem.ResumeId === item.ResumeId ? !item.active : item.active
    }));
    setState(prevState => ({ ...prevState, shortlistResumes }));
  };

  const handleCreateNewShorlistClick = () => {
    const { clonedShortlist } = state;
    setState(prevState => ({ ...prevState, isLoading: true }));
    postShortlistApi({ ...clonedShortlist, ShortlistId: 0 })
      .then(data => {
        if (data.success) {
          getShortlist(data.items);
          setState(prevState => ({
            ...prevState,
            createNewShortlist: false,
            isLoading: false,
            clonedShortlist: {},
            selectedResume: {}
          }));
          const info = {
            message: labels.shortlistSuccessAdded,
            status: "success"
          };
          dispatch(notificationAction(info));
        } else {
          const info = {
            message: labels.shortlistAlreadyExist,
            status: "info"
          };
          dispatch(notificationAction(info));
          setState(prevState => ({
            ...prevState,
            isLoading: false,
            newShortlistValue: null
          }));
        }
      })
      .catch(() => {
        setState(prevState => ({ ...prevState, isLoading: false }));
      });
  };

  const handleNewShortList = () => {
    setState(prevState => ({
      ...prevState,
      createNewShortlist: true,
      newShortlistValue: "",
      selectedResume: {},
      selectedShortlist: {}
    }));
  };

  const handleDeleteShortlist = id => {
    setState(prevState => ({
      ...prevState,
      deletedId: id,
      dialogMessage: labels.shortlistDeletionConfirmation
    }));
  };

  const handleYesClick = () => {
    const { deletedId } = state;
    setState(prevState => ({ ...prevState, dialogMessage: "" }));
    setState(prevState => ({ ...prevState, isLoading: true }));
    deleteShortlistApi(deletedId)
      .then(data => {
        if (data.success) {
          getShortlist();
          const info = {
            message: labels.shortlistSuccessfullDeletion,
            status: "success"
          };
          dispatch(notificationAction(info));
          setState(prevState => ({
            ...prevState,
            selectedShortlist: {},
            selectedResume: {},
            deletedId: null
          }));
        } else {
          const info = {
            message: data.message,
            status: "error"
          };
          dispatch(notificationAction(info));
        }
        setState(prevState => ({ ...prevState, isLoading: false }));
      })
      .catch(() => {
        setState(prevState => ({ ...prevState, isLoading: false }));
      });
  };

  const handleNoClick = () => {
    setState(prevState => ({
      ...prevState,
      dialogMessage: "",
      deletedId: null
    }));
  };

  const handleCreateNewClick = item => {
    const clonedShortlist = {
      ...item,
      ShortlistName: "Copy of " + item.ShortlistName
    };
    setState(prevState => ({
      ...prevState,
      clonedShortlist,
      selectedShortlist: {}
    }));
    getShortlistResumeApi(item.ShortlistId)
      .then(data => {
        const resumes = data.items.map((item, index) => ({
          ...item,
          active: index == 0 ? true : false
        }));
        setState(prevState => ({
          ...prevState,
          clonedShortlist: {
            ...clonedShortlist,
            ShortlistResumes: resumes
          },
          shortlistResumes: resumes
        }));
      })
      .catch(() => {
        setState(prevState => ({ ...prevState, fetchingResumes: false }));
      });
  };

  const createNewShortlist = () => {
    const { shortlistResumes } = state;
    const selectedResumesIds = shortlistResumes
      .filter(item => item.active)
      .map(item => item.ResumeId);
    setState(prevState => ({ ...prevState, isLoading: true }));
    const info = {
      ShortlistName: `New shortlist for ${
        selectedResumesIds.length
      } resume(s) at ${moment(new Date()).format("D MMMM YYYY")} at ${moment(
        new Date()
      ).format("LTS")}`,
      ResumeIdsList: selectedResumesIds
    };
    return postShortlistApi(info)
      .then(data => {
        return data;
      })
      .catch(() => {
        setState(prevState => ({ ...prevState, isLoading: false }));
      });
  };

  const handleSelectOpportunity = item => {
    createNewShortlist().then(() => {
      sessionStorage.setItem("opportunity", JSON.stringify(item));

      navigate(privateRoutes.searcherCreateOpportunity.path);
    });
  };

  const handleResumeDelete = item => {
    const { shortlistResumes, selectedShortlist, selectedResume } = state;
    deleteResumeApi(item.ResumeId, selectedShortlist.ShortlistId)
      .then(data => {
        if (data.success) {
          const filteredShortlistResumes = shortlistResumes.filter(
            x => x.ResumeId !== item.ResumeId
          );
          setState(prevState => ({
            ...prevState,
            shortlistResumes: filteredShortlistResumes,
            selectedResume:
              item.ResumeId == selectedResume.ResumeId
                ? filteredShortlistResumes.length > 0
                  ? filteredShortlistResumes[0]
                  : {}
                : selectedResume
          }));
        }
      })
      .catch(err => console.log("Error ", err));
  };

  const onResumeDelete = item => {
    setState(prevState => ({
      ...prevState,
      clonedShortlist: {
        ...prevState.clonedShortlist,
        ShortlistResumes: prevState.clonedShortlist.ShortlistResumes.filter(
          x => x.ResumeId != item.ResumeId
        )
      },
      selectedResume:
        prevState.selectedResume.ResumeId === item.ResumeId
          ? {}
          : prevState.selectedResume
    }));
  };

  const {
    searchCollapsed,
    resumeCollapsed,
    resumeDetailCollapsed,
    selectedShortlist,
    selectedResume,
    shortlistResumes,
    dialogMessage,
    filteredShortlist,
    isLoading,
    createCollapse,
    fetchingResumes,
    showOpportunities,
    searchKey,
    clonedShortlist = {}
  } = state;

  return (
    <PageWrapper className="short-list-page">
      {dialogMessage && (
        <ConfirmDialog testId="confirm-diloag">
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              onClick={handleYesClick}
              testId="delete-yes"
            >
              {labels.shortlistDeletionCurtainYESBtnText}
            </ConfirmDialog.Button>

            <ConfirmDialog.Button
              className="dialog-btn"
              onClick={handleNoClick}
            >
              {labels.shortlistDeletionCurtainNOBtnText}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      {!props.onBoardShortlistId && (
        <Column collapse={searchCollapsed} className="col-1">
          <Column.Collapsed
            onClick={() =>
              setState(prevState => ({ ...prevState, searchCollapsed: false }))
            }
            testId={"expand-btn1"}
            tooltipPlace="left"
            text={labels.ViewTitleOpportunityShortlistSection}
            tooltipButton={labels.ToolTipShortlistExpandList}
            isHelpActive={isHelpActive}
            tooltipHelp={labels.HelpToolTipShortlistExpand}
          />
          <Column.Head>
            <SearchInput
              testId="search-input"
              value={searchKey}
              placeholder={labels.ShortlistSearchPlacehoder}
              onChange={handleSearchChange}
            />
            <Button
              className="collapseBtn"
              testId={"collapse-btn1"}
              tooltipPlace="left"
              tooltipButton={labels.ToolTipShortlistCollapse}
              onClick={() =>
                setState(prevState => ({ ...prevState, searchCollapsed: true }))
              }
              isHelpActive={isHelpActive}
              tooltipHelp={labels.HelpTooltipShortlistCollapse}
            />
          </Column.Head>
          <Column.Body>
            {filteredShortlist.map(item => (
              <div
                key={item.Id || item.FlowId}
                data-testid={`shortlist-list-item${
                  item.isActive ? "-activeItem" : ""
                }`}
                className={`tictell-list-item-container  ${
                  item.ShortlistId === selectedShortlist.ShortlistId
                    ? "selected-item"
                    : ""
                } ${
                  item.ShortlistId === selectedShortlist.ShortlistId
                    ? "activeItem"
                    : ""
                }`}
              >
                <div
                  onClick={() => handleSelectShortlist(item)}
                  className="name-container flow-container"
                >
                  <label
                    className={"pool-label"}
                    data-testid={`collabs-list-owner-username`}
                  >
                    {item.ShortlistName}
                  </label>
                </div>
                {
                  <Button
                    onClick={() => handleCreateNewClick(item)}
                    className="clone-icon"
                    tooltipButton={"Clone Shortlist"}
                    tooltipHelp={"Clone Shortlist"}
                    tooltipPlace="left"
                    isHelpActive={isHelpActive}
                    testId="clone-shortlist-btn"
                  />
                }
                {
                  <Button
                    onClick={() => handleDeleteShortlist(item.ShortlistId)}
                    className="closeBtn"
                    tooltipButton={"Delete Shortlist"}
                    tooltipHelp={"Delete Shortlist"}
                    tooltipPlace="left"
                    isHelpActive={isHelpActive}
                    testId="delete-shortlist-btn"
                  />
                }
              </div>
            ))}
          </Column.Body>
        </Column>
      )}
      {!isEmpty(clonedShortlist) && (
        <Column collapse={createCollapse} className="col-2">
          <Column.Collapsed
            onClick={() =>
              setState(prevState => ({ ...prevState, createCollapse: false }))
            }
            testId={"expand-btn2"}
            tooltipButton={labels.TooltipShortlistCreateShortlistExpand}
            tooltipPlace="left"
            text={labels.ViewTitleShortlistCreateShortlist}
            isHelpActive={isHelpActive}
            tooltipHelp={labels.HelpTooltipShortlistCreateShortlistExpand}
          />
          <Column.Head>
            <Column.HeaderTitle isActive={true}>
              {"Clone Shortlist"}
            </Column.HeaderTitle>
            <Button
              className="SaveCloudBtn"
              onClick={handleCreateNewShorlistClick}
              tooltipPlace="left"
              testId="button-shortlist-save"
              disabled={isLoading || !clonedShortlist.ShortlistName}
              isHelpActive={isHelpActive}
              tooltipButton={labels.TooltipShortlistCreateShortlistSaveButton}
              tooltipHelp={labels.HelpTooltipShortlistCreateShortlistSaveButton}
            />
            <Button
              className="collapseBtn"
              testId={"collapse-btn2"}
              tooltipButton={labels.TooltipShortlistCreateShortlistCollapse}
              isHelpActive={isHelpActive}
              tooltipHelp={labels.HelpTooltipShortlistCreateShortlistCollapse}
              tooltipPlace="left"
              onClick={() =>
                setState(prevState => ({ ...prevState, createCollapse: true }))
              }
            />
          </Column.Head>
          <Column.Body className="form-editor">
            <label className="form-label">
              {labels.LabelShortlistCreateShortlistName}
            </label>
            <Input
              autoFocus
              type="text"
              testId="input-text-shortlist-name"
              className="input-text"
              value={clonedShortlist.ShortlistName || ""}
              onChange={e =>
                setState(prevState => ({
                  ...prevState,
                  clonedShortlist: {
                    ...clonedShortlist,
                    ShortlistName: e.target.value
                  }
                }))
              }
            />
            {clonedShortlist.ShortlistResumes &&
            clonedShortlist.ShortlistResumes.length > 0 ? (
              <ShortlistResumes
                fetchingResumes={fetchingResumes}
                handleSelectShortlist={handleSelectShortlist}
                selectedShortlist={clonedShortlist}
                onResumeSelect={handleResumeSelect}
                selectedResume={selectedResume}
                shortlistResumes={clonedShortlist.ShortlistResumes}
                onResumeDelete={onResumeDelete}
              />
            ) : (
              <EmptyInfo>{labels.emptyShortlistHeadsUp}</EmptyInfo>
            )}
          </Column.Body>
        </Column>
      )}

      {isEmpty(clonedShortlist) && (
        <Column collapse={resumeCollapsed} className="col-2">
          <Column.Collapsed
            onClick={() =>
              setState(prevState => ({ ...prevState, resumeCollapsed: false }))
            }
            testId={"expand-btn3"}
            tooltipButton={labels.TooltipShortlistResumeListExpand}
            tooltipPlace="left"
            text={labels.ViewTitleShortlistResult}
            isHelpActive={isHelpActive}
            tooltipHelp={labels.HelpTooltipShortlistResumeListExpand}
          />
          <Column.Head>
            <Column.HeaderTitle isActive={selectedShortlist}>
              {fetchingResumes || !selectedShortlist.ShortlistId
                ? ""
                : shortlistResumes.length > 0
                ? shortlistResumes.length +
                  " " +
                  labels.HeadingShorlistResumeListResult
                : labels.HeadingShorlistResumeListNoResume}
            </Column.HeaderTitle>
            <Button
              className="collapseBtn"
              testId={"collapse-btn3"}
              tooltipButton={labels.TooltipShortlistResumeListCollapse}
              onClick={() =>
                setState(prevState => ({ ...prevState, resumeCollapsed: true }))
              }
              tooltipPlace="left"
              isHelpActive={isHelpActive}
              tooltipHelp={labels.HelpTooltipShortlistResumeListCollapse}
            />
          </Column.Head>
          <Column.Body className="flex">
            {selectedShortlist.ShortlistId ? (
              <ShortlistResumes
                fetchingResumes={fetchingResumes}
                handleSelectShortlist={handleSelectShortlist}
                selectedShortlist={selectedShortlist}
                onResumeSelect={handleResumeSelect}
                selectedResume={selectedResume}
                shortlistResumes={shortlistResumes}
                onResumeDelete={handleResumeDelete}
              />
            ) : (
              <EmptyInfo>{labels.Company_Shortlist}</EmptyInfo>
            )}
          </Column.Body>
        </Column>
      )}
      {!showOpportunities && selectedResume.ResumeId && (
        <Column collapse={resumeDetailCollapsed} className="col-3">
          <Column.Collapsed
            tooltipButton={labels.TooltipShortlistResumeDetailExpand}
            text={labels.ViewTitleShortlistDetail}
            testId={"expand-btn5"}
            onClick={() =>
              setState(prevState => ({
                ...prevState,
                resumeDetailCollapsed: false
              }))
            }
            isHelpActive={isHelpActive}
            tooltipHelp={labels.HelpTooltipShortlistResumeDetailExpand}
          />
          <Column.Head>
            <div className="heading">Resume Detail</div>
            <Button
              className="collapseBtn"
              testId={"collapse-btn5"}
              onClick={() =>
                setState({
                  resumeDetailCollapsed: true
                })
              }
              tooltipButton={labels.ToolTipShortlistCollaspeResumeDetail}
              tooltipPlace="left"
              isHelpActive={isHelpActive}
              tooltipHelp={labels.HelpTooltipShortlistResumeDetailCollapse}
            />
          </Column.Head>
          <Column.Body>
            {fetchingResumes ? (
              <LoadingMaskRow />
            ) : (
              <ShortlistResumeDetail resume={selectedResume} />
            )}
          </Column.Body>
        </Column>
      )}
    </PageWrapper>
  );
};

export default Shortlist;
