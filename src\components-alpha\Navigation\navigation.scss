@use "../../assets/sass/importFiles" as *;

.navigation {
  overflow: auto;
  overflow-x: hidden;
  min-width: 240px;
  left: 0;
  transition: 0.5s min-width ease, 0.5s left ease-out;

  @include breakpoint(screen1240) {
    overflow: auto;
  }
  @include breakpoint(screen991) {
    min-width: 272px;
  }

  &.nav-shrink-container {
    width: 70px;
    min-width: 70px;
    flex: none;
    overflow: hidden;

    @include breakpoint(screen991) {
      min-width: 272px;
      left: -300px;
    }

    .child-text {
      display: none;
      @include breakpoint(screen991) {
        display: block;
      }
    }
  }

  .sub-nav-wrapper > div,
  .sub-nav-wrapper > .sub-nav-link {
    position: relative;
    &::before {
      position: absolute;
      width: 8px;
      height: 50px;
      left: -8px;
      top: -27px;
      border-left: 1px solid #878787;
      border-bottom: 1px solid #878787;
      border-bottom-left-radius: 7px;
      content: "";
    }
    &:first-child {
      &::before {
        height: 30px;
        top: -8px;
      }
    }
  }

  .nav-help-icon-container {
    position: relative;
    &.nav-help-icon-shrink-container {
      position: unset;
    }
    .help-icon {
      top: 2px;
      left: 5px;
      right: 0;
    }
  }
  .leftNavIcon {
    border-radius: 50%;
    min-width: 30px;
    text-align: center;
    height: 30px;
    display: flex;
    align-items: center;
    // margin-right: 5px;
    // background-color: $purple;
    &:before {
      /* use !important to prevent issues with browser extensions that change fonts */
      font-family: "icomoon" !important;
      speak: none;
      font-style: normal;
      font-weight: normal;
      font-variant: normal;
      text-transform: none;
      line-height: 1;

      /* Better Font Rendering =========== */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      content: "";
      display: inline-block;
      margin: 0 auto;
      // @extend %fi;
      font-size: 24px;
      color: $darkBlue; // content: $fi-profile;
    }
  }
  .sub-nav-wrapper {
    padding-left: 50px;
  }

  .arrowCls {
    &:after {
      //// @include spriteIcon(down, $imp);
      background: url("../../assets/images/Icons/down.png") no-repeat center
        center;
      width: 8px;
      height: 5px;
      content: "";
      position: absolute;
      right: 15px;
      top: 50%;
      margin-top: -2px;
    }
  }
}
.navigation::-webkit-scrollbar {
  width: 6px !important;
}
