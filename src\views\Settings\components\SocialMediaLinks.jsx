import { connect, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";

import Button from "../../../common/Button/Button";
import Input from "../../../common/Input/Input";
import LoadingMask from "../../../common/LoadingMask/LoadingMask";
import Messages from "../../../utilities/Messages";
import "./socialMedialLinks.scss";
const SocialMediaLinks = props => {
  const {
    SocialLinks,
    onSocialMediaChange,
    onSaveUserSetting,
    onSocialMediaActive,
    isFetching,
    labels
  } = props;

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const onStateChangeAction = () => {
    dispatch({ type: "STATE_CHANGE_ACTION" });
  };

  return (
    <div className={`userSettings`}>
      {isFetching && <LoadingMask />}
      <div className="social-media-wrap">
        <div className="socialCnt">
          {SocialLinks &&
            SocialLinks.map((item, index) => (
              <button
                key={index}
                onClick={() => onSocialMediaActive(item)}
                className={`socialBtn ${
                  item.Name ? item.Name : "!hidden "
                }Btn ${item.isActive ? "activeBtn" : ""}`}
                data-testid={"input-button-present-sm" + item.Name}
              />
            ))}
        </div>
        {SocialLinks &&
          SocialLinks.filter(item => item.isActive).map((item, index) => {
            return (
              <>
                <Input
                  key={index}
                  name={item.Name}
                  className="input-text"
                  testId={"input-text-present-sm" + item.Name}
                  value={item.URL ? item.URL : ""}
                  placeholder={Messages.present.placeholder[item.Name]}
                  onChange={e => onSocialMediaChange(item, e.target.value)}
                  // onBlur={(e) => onSaveUserSetting()}
                />
                <Button
                  className="SendButton"
                  onClick={onSaveUserSetting}
                  testId="btnsettingupdate"
                  tooltipHelp="Update user settings."
                  tooltipButton="Update user settings."
                >
                  {"Update"}
                </Button>
              </>
            );
          })}
      </div>
    </div>
  );
};

const mapStateToProps = ({ systemLabel }) => {
  const { labels } = systemLabel;
  return { labels };
};

export default connect(mapStateToProps)(SocialMediaLinks);
