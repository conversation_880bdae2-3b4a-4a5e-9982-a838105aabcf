import "./list.scss";
import LoadingMaskRow from "../LoadingMask/LoadingMaskRow";

let isTouch;
if (typeof document !== "undefined") {
  isTouch = "ontouchstart" in document.documentElement;
}

const ListItem = ({
  children,
  onClick,
  isSelected,
  className,
  testId,
  onMouseEnter,
  onMouseLeave,
  isEffect = true,
  onTap
}) => (
  <li
    data-testid={testId}
    onClick={onClick}
    onTouchStart={onTap}
    className={`list-item animated ${isEffect && "fadeIn"} ${
      isSelected ? "selected-item" : ""
    } ${className} `}
    onMouseEnter={onMouseEnter}
    onMouseLeave={onMouseLeave}
  >
    {children}
  </li>
);

const Close = ({ onClick, onFocus, testId }) => (
  <button
    onFocus={onFocus}
    onClick={onClick}
    className="closeBtn"
    data-testid={testId}
  />
);

const List = ({ children, className, testId, isFetching }) => (
  <div className={`list-wrapper ${className}`}>
    {isFetching ? (
      <LoadingMaskRow />
    ) : (
      <ul className={`list-ul`} data-testid={testId}>
        {children}
      </ul>
    )}
  </div>
);

List.ListItem = ListItem;
List.Close = Close;

List.defaultProps = {
  className: ""
};
List.ListItem.defaultProps = {
  className: ""
};

export default List;
