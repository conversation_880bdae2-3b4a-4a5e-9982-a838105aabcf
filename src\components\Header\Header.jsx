import { Dropdown } from "antd";
import axios from "axios";
import { useState } from "react";
import { connect } from "react-redux";
import { useNavigate } from "react-router-dom";
import { helpActiveAction } from "../../actions/navigationActions";
import { resumeChangeAction } from "../../actions/resumeActions";
import { StorageService } from "../../api/storage";
import LogoSrc from "../../assets/images/logo.png";
import MessageIcon from "../../assets/images/message1.png";
import { privateRoutes } from "../../Routes/routing";
import { logoutAction } from "../../store/initialConfig";
import { RESET_LANDINGPAGE_URL } from "../../utilities/enviroments";
import { htmlParser } from "../../utilities/helpers";
import { logoutApi } from "../Navigation/navigationApi";
import "./header.scss";

const Header = props => {
  const navigate = useNavigate();
  const { isResumeChange, resumeChangeAction, labels } = props;
  const [isSettingOpen, setSettingOpen] = useState(false);

  const handleLogout = () => {
    if (isResumeChange) {
      resumeChangeAction({
        isResumeChange,
        message: labels.RESUME_EDIT_UNSAVED_CHANGES_MESSAGE,
        isLogoutTrigger: true
      });
      return;
    }
    logout();
  };

  const logout = () => {
    const { history } = props;
    let windowLocation = window.location.href;
    logoutApi().then(response => {
      if (response.success) {
        delete axios.defaults.headers.common[".ASPXAUTH"];
        props.logoutAction();
        let windowLocation = window.location.href;
        StorageService.clearAll();
        if (
          windowLocation.indexOf("/apps/") >= 0 ||
          windowLocation.indexOf("app-login") >= 0 ||
          windowLocation.indexOf("/Apps/") >= 0
        ) {
          window.location.href = windowLocation;
        } else {
          window.location.href = RESET_LANDINGPAGE_URL;
        }
      }
    });
  };

  const handleHelpClick = () => {
    const { helpActiveAction, isHelpActive } = props;
    helpActiveAction({ isHelpActive: !isHelpActive });
  };

  const {
    user,
    onNavigationToggle,
    isHelpActive,
    history,
    hideUserSetting
  } = props;
  const { FirstName, IsFreelancer, UserFirstname } = user;
  const isVisitor = IsFreelancer === null;
  return (
    <header className="page-header" data-testid="application-header">
      {!hideUserSetting && (
        <button className="ToggleMenu" onClick={onNavigationToggle} />
      )}
      <span className="user-name">
        {IsFreelancer
          ? htmlParser(labels.IPRO_APP_HEADER_GREETING_LABEL)
          : htmlParser(labels.SEARCHER_APP_HEADER_GREETING_LABEL)}{" "}
        {isVisitor
          ? "Visitor"
          : FirstName?.split(" ")[0] || UserFirstname?.split(" ")[0]}
      </span>
      <span
        className={`helpBtn ${isHelpActive ? "helpActive" : ""}`}
        onClick={handleHelpClick}
        data-testid="header-help-btn"
      />

      <div className="header-logo-container">
        <img className="header-logo" src={LogoSrc} alt="logo" />
      </div>

      {!hideUserSetting ? (
        <>
          <Dropdown
            className="setting-button"
            overlayClassName="setting-dropdown"
            menu={{
              items: [
                {
                  label: (
                    <div className="clr-black">{labels?.setting_label}</div>
                  ),
                  key: "0"
                },
                {
                  label: (
                    <div className="clr-black">
                      {labels?.IPRO_NAVIGATION_FEEDBACK_LABEL}
                    </div>
                  ),
                  key: "2"
                },
                {
                  label: (
                    <div className="clr-black">
                      {labels?.IPRO_NAVIGATION_ABOUT_LABEL}
                    </div>
                  ),
                  key: "3"
                },
                {
                  label: (
                    <div className="clr-red-logout">
                      {labels?.HEADER_LOGOUT_ICON_HELP}
                    </div>
                  ),
                  key: "1"
                }
              ],
              onClick: e => {
                if (e.key == "0") {
                  setSettingOpen(true);
                  // navigate(privateRoutes.settings.path);
                }
                if (e.key == "1") {
                  handleLogout();
                }
                if (e.key == "2") {
                  navigate(privateRoutes.feedback.path);
                }
                if (e.key == "3") {
                  navigate(privateRoutes.about.path);
                }
              }
            }}
            trigger={["click"]}
          >
            <span />
          </Dropdown>
          {/* <Button
            followCursor={true}
            positionHelp="bottom"
            className="setting-button"
            mainClassName={"header-button"}
            alt="Settings"
            tooltipButton={labels.HEADER_SETTING_ICON_TOOLTIP}
            tooltipHelp={labels.HEADER_SETTING_ICON_HELP}
            tooltipPlace="left"
            isHelpActive={isHelpActive}
            testId="user-setting-settings-btn"
            testIdHelp="user-setting-settings-help-btn"
            disabled={isVisitor}
          /> */}
        </>
      ) : (
        ""
      )}
      <div
        className="blog-button"
        onClick={() => {
          navigate(privateRoutes.inboxMessages.path);
        }}
      >
        <img src={MessageIcon} alt="" />
      </div>
    </header>
  );
};

const mapStateToProps = state => {
  const {
    systemLabel,
    userInfo,
    navigation,
    resume: { isResumeChange, isLogout }
  } = state;
  const { labels } = systemLabel;
  const { user } = userInfo;
  const { isHelpActive } = navigation;
  return {
    labels,
    user,
    isHelpActive,
    isResumeChange,
    isLogout
  };
};

export default connect(mapStateToProps, {
  logoutAction,
  helpActiveAction,
  resumeChangeAction
})(Header);
