# Node.js with React
# Build a Node.js project that uses React.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
  - Staging
  - master
  - master-20250210
  - Production

pool:
  vmImage: "ubuntu-latest"

steps:
  - task: NodeTool@0
    inputs:
      versionSpec: "22.x"
    displayName: "Install Node.js"

  - script: |
      npm install
      if [ "$(Build.SourceBranchName)" == "Staging" ]; then
        npm run build -- --mode staging
      elif [ "$(Build.SourceBranchName)" == "Stagining-20250210" ]; then
        npm run build -- --mode staging
      elif [ "$(Build.SourceBranchName)" == "Production" ]; then
        npm run build -- --mode production
      else
        npm run build -- --mode master
      fi
    displayName: "npm install and build"
  - task: ArchiveFiles@2
    inputs:
      rootFolderOrFile: "$(System.DefaultWorkingDirectory)/build"
      includeRootFolder: false
      archiveType: "zip"
      archiveFile: "$(Build.ArtifactStagingDirectory)/artifact.zip"
      replaceExistingArchive: true
  - task: PublishBuildArtifacts@1
    inputs:
      PathtoPublish: "$(Build.ArtifactStagingDirectory)"
      ArtifactName: "PDWebReact"
