import { useState, useEffect } from "react";
import { connect } from "react-redux";
import { useNavigate, useLocation } from "react-router-dom";
import PageWrapper from "../../../components/PageWrapper/PageWrapper";
import EmptyInfo from "../../../common/EmptyInfo/EmptyInfo";
import { notificationAction } from "../../../actions/notification";
import "./messagenew.scss";
import { getAllUserApi, sendMessage } from "../messageApi";
import CreateMessage from "./components/CreateMessage";
import isEmpty from "lodash/isEmpty";
import filter from "lodash/filter";
import map from "lodash/map";
import includes from "lodash/includes";
import toLower from "lodash/toLower";
import { onStateChangeAction } from "./messageNewAction";
import { Col, Row, Input, Space, Checkbox, Typography } from "antd";
import ArrowLeft from "../../../assets/images/Icons/arrow-left.png";
import SearchIcon from "../../../assets/images/secrch-icon.png";
const { Title } = Typography;

const MessageNew = ({
  labels,
  IsFreelancer,
  notificationAction,
  onStateChangeAction,
  isHelpActive,
  ...props
}) => {
  const [state, setState] = useState({
    searchListHover: false,
    windowWidth: window?.innerWidth,
    messageDetail: false,
    assortAction: ""
  });

  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const handleResize = () => {
      setState(prevState => ({ ...prevState, windowWidth: window.innerWidth }));
    };

    const url = location.pathname;
    const lastSegment = url.split("/").pop();
    setState(prevState => ({ ...prevState, assortAction: lastSegment }));
    getAllUsers();
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [location.pathname]);

  const getAllUsers = () => {
    onStateChangeAction({ isFetching: true });
    getAllUserApi({ IsFreelancer }).then(data => {
      if (data.success) {
        if (isEmpty(data.items)) {
          notificationAction({
            status: "info",
            message: IsFreelancer
              ? labels.InfoIProCreateMsgRestriction
              : labels.searcherCreateMessageWithoutInvitationInfo
          });
        }
        const filterData = data?.items?.map(single => ({
          ...single,
          label: single?.UserFirstname,
          value: single?.UserId
        }));
        onStateChangeAction({
          users: filterData,
          filterUsers: filterData,
          isFetching: false
        });
      } else {
        onStateChangeAction({ isFetching: false });
      }
    });
  };

  const handleContentChange = e => {
    const { value, name } = e.target;
    const { messageForm } = props;
    const data = {
      ...messageForm,
      [name]: value
    };
    onStateChangeAction({ messageForm: data });
  };

  const handleSubmitMessage = () => {
    const {
      messageForm: { title, content },
      selectedUsers
    } = props;
    if (isEmpty(title)) {
      notificationAction({
        status: "error",
        message: labels.InfoIProCreateMsgTitleError
      });
      return;
    }
    if (isEmpty(content)) {
      notificationAction({
        status: "error",
        message: labels.InfoIProCreateMsgBodyError
      });
      return;
    }
    if (isEmpty(selectedUsers)) {
      notificationAction({
        status: "error",
        message: labels.InfoIProCreateMsgEmptyReceiverError
      });
      return;
    }
    const message = {
      IsRead: false,
      MessageBody: content,
      MessageId: 0,
      Subject: title,
      UserIds: map(selectedUsers, user => user.UserId),
      UserRequestMessageId: 0
    };

    onStateChangeAction({ isFetching: true });
    sendMessage({ message })
      .then(response => {
        onStateChangeAction({
          selectedUsers: [],
          messageForm: { title: "", content: "" },
          isFetching: false
        });
        notificationAction({
          status: "success",
          message: labels.InfoIProCreateMsgAddSuccessfully
        });
      })
      .catch(error => {
        onStateChangeAction({ isFetching: false });
      });
  };

  const handleSearchChange = e => {
    const searchKey = e.target.value;
    const { users } = props;
    const filterUsers = filter(users, user => {
      if (includes(toLower(user.UserFirstname), toLower(searchKey))) {
        return user;
      }
    });
    onStateChangeAction({ filterUsers });
  };

  const handleSearchListSelected = ({ selectedUser }) => {
    const { selectedUsers } = props;
    const alreadySelected = filter(
      selectedUsers,
      user => user.UserId === selectedUser.UserId
    );
    if (isEmpty(alreadySelected)) {
      onStateChangeAction({
        selectedUsers: [...selectedUsers, selectedUser],
        isSearchFocus: false
      });
      return;
    } else {
      const duplicate = selectedUsers?.filter(
        x => x?.UserId != selectedUser?.UserId
      );
      onStateChangeAction({
        selectedUsers: duplicate,
        isSearchFocus: false
      });
    }
  };

  const handleSearchBlur = () => {
    if (state.searchListHover) return;
    onStateChangeAction({ isSearchFocus: false });
  };

  const handleRemoveSelectedUser = item => {
    let { selectedUsers } = props;
    selectedUsers = filter(selectedUsers, user => user.UserId !== item.UserId);
    onStateChangeAction({ selectedUsers });
  };

  const {
    listCollapsed,
    formCollapsed,
    users,
    messageForm,
    isSearchFocus,
    selectedUsers,
    isFetching,
    filterUsers,
    createMessageCollapsed
  } = props;

  return (
    <PageWrapper className="collaboration">
      <div className="h-full new-design-search w-full flex flex-col">
        <div className="tabs-header-col">
          <div
            className="flex items-center gap-2 cursor-pointer"
            onClick={() => {
              if (state.messageDetail) {
                setState(st => ({ ...st, messageDetail: false }));
              } else {
                navigate(-1);
              }
            }}
          >
            <div>
              <img style={{ height: "16px" }} src={ArrowLeft} alt="" />
            </div>
            <Title level={5} className="!m-0">
              {state.messageDetail ? "New Message" : "Create Message"}
            </Title>
          </div>
        </div>
        <Row className=" h-full" style={{ overflow: "auto" }}>
          <Col xs={24} md={8} className="collboration-column1">
            {state.messageDetail ? (
              <div className="new-collaboration-detail-component message-detail-new">
                <CreateMessage
                  handleContentChange={handleContentChange}
                  messageForm={messageForm}
                  isFetching={isFetching}
                  onMessageSend={handleSubmitMessage}
                  windowWidth={state.windowWidth}
                />
              </div>
            ) : (
              <div className="h-full flex flex-col">
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: "12px",
                    borderBottom: "1px solid #F3F3F3",
                    gap: "10px"
                  }}
                >
                  <Input
                    placeholder={labels?.searchInputPlaceholder}
                    size="medium"
                    bordered={false}
                    style={{ border: "1px solid #F3F3F3" }}
                    onChange={handleSearchChange}
                  />
                  <div>
                    <img style={{ height: "30px" }} src={SearchIcon} alt="" />
                  </div>
                </div>
                <div
                  style={{
                    overflow: "auto",
                    padding: "12px",
                    flex: 1
                  }}
                >
                  {filterUsers?.length > 0 ? (
                    <Space size={[6, 6]} wrap className="short-list-new">
                      {filterUsers?.map(item => {
                        const selected = selectedUsers?.some(
                          x => x?.UserId == item?.UserId
                        );
                        return (
                          <div
                            onClick={() =>
                              handleSearchListSelected({
                                selectedUser: item
                              })
                            }
                            key={item?.UserCompanyId}
                            className={`flex gap-2 justify-between items-center pointer ${
                              selected
                                ? "background-shortlist short-list-item "
                                : "short-list-item"
                            }`}
                          >
                            <div className="flex  gap-2 w-full justify-content-betwee items-center">
                              <Checkbox
                                value={item?.ResumeId}
                                checked={selected}
                                style={{
                                  fontSize: "large"
                                }}
                              />
                              <div className="text-clamp" style={{ flex: 1 }}>
                                <label style={{ fontSize: "14px" }}>
                                  {item?.UserFirstname}
                                </label>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </Space>
                  ) : (
                    <EmptyInfo>{labels.EMBARK_NO_ITEM_TO_DISPLAY}</EmptyInfo>
                  )}
                </div>
                {state.windowWidth < 767 && (
                  <div style={{ margin: "auto" }}>
                    <div className="tabs-header-col mt-3">
                      <button
                        className="tab-btn-coll"
                        style={{ background: "#6C63FF", color: "#fff" }}
                        onClick={() => {
                          setState(st => ({
                            ...st,
                            messageDetail: true
                          }));
                        }}
                      >
                        Confirm & Send Message
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </Col>
          <Col md={16} xs={0} offset={0.5} className={"h-full"}>
            <div
              className=" resume-list h-full"
              style={{
                marginLeft: "16px",
                background: "#fff",
                borderRadius: "12px"
              }}
            >
              <div className="new-collaboration-detail-component message-detail-new">
                <CreateMessage
                  handleContentChange={handleContentChange}
                  messageForm={messageForm}
                  isFetching={isFetching}
                  onMessageSend={handleSubmitMessage}
                  windowWidth={state.windowWidth}
                />
              </div>
            </div>
          </Col>
        </Row>
      </div>
    </PageWrapper>
  );
};

const mapStateToProps = ({ systemLabel, userInfo, navigation, messageNew }) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  const { isHelpActive } = navigation;
  let { IsFreelancer } = user;
  IsFreelancer =
    IsFreelancer !== undefined
      ? IsFreelancer
      : JSON.parse(localStorage.getItem("IsFreelancer"));
  return { labels, IsFreelancer, isHelpActive, ...messageNew };
};

export default connect(mapStateToProps, {
  notificationAction,
  onStateChangeAction
})(MessageNew);
