import { PlusOutlined } from "@ant-design/icons";
import { Button, Grid, Typography } from "antd";
import clsx from "clsx";
import { useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";
import EditOutlined from "../../../assets-alpha/images/svg/edit.svg?react";
import { Profiles } from "../constant";
import { useSelector } from "react-redux";
const { Title } = Typography;

// Languages Section
const LanguagesSection = ({ userData, openDrawer }) => {
  const resume = useSelector(state => state.systemLabel.labels?.resume);
  return (
    <ShowMoreSection
      title={resume?.languages}
      items={userData?.items?.Languages || []}
      openDrawer={openDrawer}
      addEditAction={Profiles.language}
      itemKeyId="LanguageId"
      viewMore
      itemValueKey="LanguageValue"
      emptyMessage={resume?.languagesEmptyMessage}
      addButtonAction={Profiles.language}
      renderItem={language => (
        <div
          key={language.LanguageId}
          className="bg-[#F3F1FD] border border-[#EAE5FC] rounded-lg px-3.5 py-2 text-sm text-[#343333]"
        >
          {language.LanguageValue}
        </div>
      )}
      leftSection
    />
  );
};

export default LanguagesSection;

export const ShowMoreSection = ({
  title,
  items,
  openDrawer,
  addEditAction,
  itemKeyId,
  itemValueKey,
  extraInfoKey,
  emptyMessage,
  addButtonAction,
  renderItem,
  leftSection,
  viewMore
}) => {
  const [showMore, setShowMore] = useState(true);
  const { md } = Grid.useBreakpoint();
  const resume = useSelector(state => state.systemLabel.labels?.resume);
  useEffect(() => {
    if (!viewMore) return;
    setShowMore(items?.length <= 5);
  }, [items, viewMore]);

  return (
    <div
      className={twMerge(
        clsx("border-[#EAE5FC] px-8 py-4 border-0 border-b-1 rounded-none", {
          "px-4 py-5 -mx-4": !md
        })
      )}
    >
      <div
        className={twMerge(
          clsx("flex justify-between items-center mb-4", {
            "mb-2": leftSection || (!md && !items?.length),
            "mb-4": !md
          })
        )}
      >
        <Title level={5} className="!text-[16px] !mb-0 font-semibold">
          {title}
        </Title>
        {items?.length > 0 && (
          <Button
            onClick={() => openDrawer(addEditAction)}
            type="link"
            className="!p-0 flex items-center !h-auto"
          >
            <EditOutlined />
          </Button>
        )}
      </div>

      <div className="flex flex-wrap gap-2 items-center">
        {items?.length > 0 ? (
          <>
            {items
              .slice(0, showMore ? items.length : 5)
              .map(item => renderItem(item))}
            {viewMore && items.length > 5 && (
              <Button
                type="link"
                className="!px-2"
                onClick={() => setShowMore(!showMore)}
              >
                {showMore ? resume?.viewLess : resume?.viewMore}
              </Button>
            )}
          </>
        ) : (
          <div className="text-[#878787] text-xs w-full">
            {emptyMessage}
            <br />
            <div className="flex justify-end mt-2">
              <Button
                onClick={() => openDrawer(addButtonAction)}
                type="link"
                className="!rounded-[8px] !h-[36px] !px-2.5 !border-[#EAE5FC] flex items-center"
              >
                <PlusOutlined className="mr-1" /> {resume?.add}
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
