import moment from "moment";
import { But<PERSON>, Modal, Typography } from "antd";
import { formatDate } from "../../../../../utilities/helpers";
import CloseIcon from "../../../../../assets-alpha/images/svg/close-modal.svg";
import { ExtendContractCalender } from "./ExtendContractCalender";
import { useDispatch, useSelector } from "react-redux";
import { getCurrenciesApi } from "../../../common/collaborationApi";
import { useEffect, useState } from "react";
import { onStateChangeAction } from "../../../common/collaborationAction";
const { Title } = Typography;

const NewCollaborationDetail = props => {
  const dispatch = useDispatch();
  const [allCurrenciesList, setAllCurrenciesList] = useState([]);
  const {
    selectedCollaboration,
    startDateLabel,
    durationLabel,
    onCollaborationAccept,
    onCollaborationDecline,
    onCollaborationDelete,
    onCollaborationClose,
    IsSearcher,
    labels,
    setIsDrawerOpen,
    selectedDate,
    tempSelectedDate,
    setTempSelectedDate,
    onDateSelect,
    showCalendar,
    setShowCalendar,
    handleExtendCollaboration,
    setRefresh
  } = props;
  const getCurrencies = () => {
    getCurrenciesApi()
      .then(res => {
        if (res.success) {
          const allCurrenciesList = res.items.map(item => ({
            ...item,
            value: item.CurrencyId,
            label: item.Name
          }));
          setAllCurrenciesList(allCurrenciesList);
        }
      })
      .catch(err => console.log("Err ", err));
  };
  useEffect(() => {
    getCurrencies();
  }, []);
  const renderDate = date => {
    return moment(date).format("MM/DD/YY");
  };
  const currType = allCurrenciesList?.find(item => {
    return item?.CurrencyId === Number(selectedCollaboration?.HourlyRateType);
  });
  return (
    <>
      <div className="new-collaboration-detail-component">
        <div className="relative w-full md:w-[497px] h-[64px] ">
          <div className="fixed md:w-[497px] w-full h-[64px] border-b border-[#EAE5FC] z-[999] bg-white rounded-tl-2xl rounded-tr-2xl">
            <div className="flex w-full h-[64px] justify-between items-center p-6 border-b border-[#EAE5FC]">
              <div className="flex md:justify-start justify-between w-full items-center gap-4 ">
                <img
                  src={CloseIcon}
                  alt=""
                  onClick={() => {
                    setIsDrawerOpen(false);
                  }}
                  className="pointer order-2 md:order-1"
                />
                <h1 className="!m-0 flex-1 order-1 md:order-2 !text-[18px] !font-semibold md:!text-2xl md:font-medium">
                  {labels?.IproCollaborationDetailHeadingText}
                </h1>
              </div>
              <div className="hidden md:flex order-3">
                {!IsSearcher ? (
                  selectedCollaboration?.Status === 0 ? (
                    <div className="flex gap-2">
                      <Button
                        className="flex !w-[84px] !h-9 !bg-[#FEEDED] items-center justify-center !m-0 !text-[#FF3B30]"
                        onClick={onCollaborationDecline}
                      >
                        {labels?.iProOpportunityNewDetailBtnDecline}
                      </Button>
                      <Button
                        className="flex !w-[64px] !h-9 !bg-[#8E81F5] items-center justify-center !m-0 !text-[#FFFFFF]"
                        onClick={onCollaborationAccept}
                      >
                        {labels?.iProOpportunityNewDetailBtnAccept}
                      </Button>
                    </div>
                  ) : selectedCollaboration?.Status === 2 ? (
                    // End contract
                    <Button
                      className="flex !w-[110px] !h-9 !bg-[#FF3B30] items-center justify-center !m-0 !text-[#FFFFFF]"
                      onClick={onCollaborationClose}
                    >
                      {labels?.End_Contract_Label}
                    </Button>
                  ) : selectedCollaboration?.Status === 3 ? (
                    // declined contract
                    <Button
                      className="flex w-full md:!w-[68px] !h-9 !bg-[#FF3B30] items-center justify-center !m-0 !text-[#FFFFFF]"
                      onClick={onCollaborationDelete}
                    >
                      {labels?.delete_presentation}
                    </Button>
                  ) : selectedCollaboration?.Status === 4 ? (
                    // Expired contract
                    <Button
                      className="flex w-full md:!w-[68px] !h-9 !bg-[#8E81F5] items-center justify-center !m-0 !text-[#FFFFFF]"
                      // onClick={onCollaborationAccept}
                      // iPro can send request to recruiter to extend his contract
                    >
                      {labels?.Request_Extend_Contract_Label}
                    </Button>
                  ) : selectedCollaboration?.Status === 5 ? (
                    // delete contract
                    <Button
                      className="flex w-full md:!w-[68px] !h-9 !bg-[#FF3B30] items-center justify-center !m-0 !text-[#FFFFFF]"
                      onClick={onCollaborationDelete}
                    >
                      {labels?.delete_presentation}
                    </Button>
                  ) : (
                    ""
                  )
                ) : (
                  IsSearcher &&
                  selectedCollaboration?.Status !== 3 && (
                    <Button
                      type="primary"
                      className={`w-auto ${
                        selectedCollaboration?.Status === 4
                          ? "!bg-[#8E81F5]"
                          : "!bg-[#FF3B30]"
                      } !h-9 pl-4 pr-4 ${
                        IsSearcher && selectedCollaboration?.Status === 3
                          ? "hidden"
                          : ""
                      }`}
                    >
                      {IsSearcher && selectedCollaboration?.Status === 1 && (
                        <Title
                          level={5}
                          className="flex items-center justify-center !m-0 !text-[#FFFFFF]"
                          onClick={onCollaborationDelete}
                        >
                          {labels?.Delete_Contract_label}
                        </Title>
                      )}
                      {IsSearcher && selectedCollaboration?.Status === 2 && (
                        <Title
                          level={5}
                          className="flex items-center justify-center !m-0 w-full h-full !text-[#FFFFFF]"
                          onClick={onCollaborationClose}
                        >
                          {labels?.End_Contract_Label}
                        </Title>
                      )}
                      {IsSearcher && selectedCollaboration?.Status === 4 && (
                        <Title
                          level={5}
                          className="flex items-center justify-center !m-0 w-full h-full !text-[#FFFFFF]"
                          onClick={() => {
                            setShowCalendar(true);
                          }}
                        >
                          {labels?.Extend_Contract_Label}
                        </Title>
                      )}
                      {IsSearcher && selectedCollaboration?.Status === 5 && (
                        <Title
                          level={5}
                          className="flex items-center justify-center !m-0 w-full h-full !text-[#FFFFFF]"
                          o
                          onClick={onCollaborationDelete}
                        >
                          {labels?.Delete_Contract_label}
                        </Title>
                      )}
                    </Button>
                  )
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-col h-full border border-[#BDDDF5] rounded-xl m-4 mb-[76px] md:m-[28px_24px] [box-shadow:1px_1px_38px_0px_#BDDDF526_inset,-1px_-1px_38px_0px_#BDDDF526_inset]">
          <div className="h-full mt-2 md:!mt-7 ml-3 mr-3 md:!ml-[22px] md:!mr-[22px] ">
            <div className="flex items-center h-10  border-b border-[#EAE5FC] !pt-4 !pb-4 !gap-[12.5px] ">
              <label className="!min-w-[83px]  ml-4 !font-normal !text-[13px] text-[#878787] whitespace-nowrap">
                {labels?.Section2ProfessionalOpportunityTitle}
              </label>
              <span className="h-[22.5px] border-l border-[#EAE5FC] "></span>
              <p className="text-[13px] text-[#343333] font-normal w-[260px] ">
                {selectedCollaboration.Title
                  ? selectedCollaboration.Title
                  : "N/A"}
              </p>
            </div>
            <div className="flex items-center h-10 border-b border-[#EAE5FC] !pt-4 !pb-4 !gap-[12.5px] ">
              <label className="!min-w-[83px]  ml-4 !font-normal !text-[13px] text-[#878787] whitespace-nowrap">
                {startDateLabel}
              </label>
              <span className="h-[22.5px] border-l border-[#EAE5FC] "></span>
              <p className="text-[13px] text-[#343333] font-normal w-[260px] ">
                {selectedCollaboration.StartDate
                  ? formatDate(selectedCollaboration.StartDate)
                  : "N/A"}
              </p>
            </div>
            <div className="flex items-center h-10 border-b border-[#EAE5FC] !pt-4 !pb-4 !gap-[12.5px] ">
              <label className="!min-w-[83px]  ml-4 !font-normal !text-[13px] text-[#878787] whitespace-nowrap">
                {labels?.END_DATE_LBL}
              </label>
              <span className="h-[22.5px] border-l border-[#EAE5FC] "></span>
              <p className="text-[13px] text-[#343333] font-normal w-[260px] ">
                {selectedCollaboration.EndDate
                  ? formatDate(selectedCollaboration.EndDate)
                  : "N/A"}
              </p>
            </div>
            <div className="flex items-center h-10 border-b border-[#EAE5FC] !pt-4 !pb-4 !gap-[12.5px] ">
              <label className="!min-w-[83px]  ml-4 !font-normal !text-[13px] text-[#878787] whitespace-nowrap">
                {durationLabel}
              </label>
              <span className="h-[22.5px] border-l border-[#EAE5FC] "></span>
              <p className="text-[13px] text-[#343333] font-normal w-[260px] ">
                {selectedCollaboration?.Duration
                  ? selectedCollaboration?.Duration
                  : "N/A"}
              </p>
            </div>
            <div className="flex items-center h-10 border-b border-[#EAE5FC] !pt-4 !pb-4 !gap-[12.5px] ">
              <label className="!min-w-[83px]  ml-4 !font-normal !text-[13px] text-[#878787] whitespace-nowrap">
                {labels?.collHourlyFee}
              </label>
              <span className="h-[22.5px] border-l border-[#EAE5FC] "></span>
              <p className="text-[13px] text-[#343333] font-normal w-[260px] ">
                {selectedCollaboration.HourlyRate || labels.NOT_AVAILABLE_LABEL}
                <span> </span>
                {currType?.label}
              </p>
            </div>
            <div className="flex items-center h-10  border-b border-[#EAE5FC] !pt-4 !pb-4 !gap-[12.5px] ">
              <label className="!min-w-[83px]  ml-4 !font-normal !text-[13px] text-[#878787] whitespace-nowrap">
                {labels?.SearcherSentCollaborationCompanyLabel}
              </label>
              <span className="h-[22.5px] border-l border-[#EAE5FC] "></span>
              <p className="text-[13px] text-[#343333] font-normal w-[260px] ">
                {selectedCollaboration.company.CompanyName
                  ? selectedCollaboration.company.CompanyName
                  : "N/A"}
              </p>
            </div>
            <div className="flex items-center h-10 border-b border-[#EAE5FC] !pt-4 !pb-4 !gap-[12.5px] ">
              <label className="!min-w-[83px]  ml-4 !font-normal !text-[13px] text-[#878787] whitespace-nowrap">
                {labels?.colliPro}
              </label>
              <span className="h-[22.5px] border-l border-[#EAE5FC] "></span>
              <p className="text-[13px] text-[#343333] font-normal w-[260px] ">
                {selectedCollaboration?.IProName
                  ? selectedCollaboration?.IProName
                  : "N/A"}
              </p>
            </div>
            <div className="flex  min-h-10 h-auto pl-4 pt-2">
              <p className="text-[13px] text-[#343333] font-normal w-full ">
                {selectedCollaboration?.userdetail?.Detail
                  ? selectedCollaboration?.userdetail?.Detail
                  : "N/A"}
              </p>
            </div>
          </div>
          <div className="flex justify-center items-center mt-[64px] mb-[66px]">
            <div className="flex justify-between items-cente w-[272px] h-[39px]">
              <div className="flex flex-col items-center justify-between min-w-[90px] h-full border-b-[0.5px] border-[#878787]">
                <p>{labels?.SignUpComboSearcher}</p>
                <p
                  className="text-[#02CAA8] font-bold text-sm leading-[100%]"
                  style={{ fontFamily: "Caveat, cursive" }}
                >
                  {selectedCollaboration?.RecruiterName
                    ? selectedCollaboration?.RecruiterName
                    : ""}
                </p>
              </div>
              <div className="flex flex-col items-center justify-between min-w-[90px] h-full border-b-[0.5px] border-[#878787]">
                <p>{labels?.colliPro}</p>
                <p
                  className="text-[#8E81F5] font-bold text-sm leading-[100%]"
                  style={{ fontFamily: "Caveat, cursive" }}
                >
                  {selectedCollaboration?.IProName
                    ? selectedCollaboration?.IProName
                    : "N/A"}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* )} */}
        <div
          className={`md:!hidden !pl-4 !pr-4 !flex !items-center !justify-center !w-full fixed bottom-0 h-[70px] bg-white ${
            selectedCollaboration?.Status !== 3
              ? "border-t-[0.5px] border-[#EAE5FC]"
              : ""
          }`}
        >
          {!IsSearcher ? (
            selectedCollaboration?.Status === 0 ? (
              <div className="flex gap-2 w-full">
                <Button
                  className="flex w-full md:!w-[84px] !h-9 !bg-[#FEEDED] items-center justify-center !m-0 !text-[#FF3B30]"
                  onClick={onCollaborationDecline}
                >
                  {labels?.iProOpportunityNewDetailBtnDecline}
                </Button>
                <Button
                  className="flex w-full md:!w-[64px] !h-9 !bg-[#8E81F5] items-center justify-center !m-0 !text-[#FFFFFF]"
                  onClick={onCollaborationAccept}
                >
                  {labels?.iProOpportunityNewDetailBtnAccept}
                </Button>
              </div>
            ) : selectedCollaboration?.Status === 2 ? (
              // End contract
              <Button
                className="flex w-full md:!w-[110px] !h-9 !bg-[#FF3B30] items-center justify-center !m-0 !text-[#FFFFFF]"
                onClick={onCollaborationClose}
              >
                {labels?.End_Contract_Label}
              </Button>
            ) : selectedCollaboration?.Status === 3 ? (
              // declined contract
              <Button
                className="flex w-full md:!w-[68px] !h-9 !bg-[#FF3B30] items-center justify-center !m-0 !text-[#FFFFFF]"
                onClick={onCollaborationDelete}
              >
                {labels?.delete_presentation}
              </Button>
            ) : selectedCollaboration?.Status === 4 ? (
              // Expired contract
              <Button
                className="flex w-full md:!w-[68px] !h-9 !bg-[#8E81F5] items-center justify-center !m-0 !text-[#FFFFFF]"
                onClick={() => {
                  setShowCalendar(true);
                }}
              >
                {labels?.Request_Extend_Contract_Label}
              </Button>
            ) : selectedCollaboration?.Status === 5 ? (
              // delete contract
              <Button
                className="flex w-full md:!w-[68px] !h-9 !bg-[#FF3B30] items-center justify-center !m-0 !text-[#FFFFFF]"
                onClick={onCollaborationDelete}
              >
                {labels?.delete_presentation}
              </Button>
            ) : (
              ""
            )
          ) : (
            IsSearcher &&
            selectedCollaboration?.Status !== 3 && (
              <Button
                type="primary"
                className={`w-full md:w-auto ${
                  selectedCollaboration?.Status === 4
                    ? "!bg-[#8E81F5]"
                    : "!bg-[#FF3B30]"
                } !h-9 pl-4 pr-4 ${
                  IsSearcher && selectedCollaboration?.Status === 3
                    ? "hidden"
                    : ""
                }`}
              >
                {IsSearcher && selectedCollaboration?.Status === 1 && (
                  <Title
                    level={5}
                    className="flex items-center justify-center !m-0 !text-[#FFFFFF]"
                    onClick={onCollaborationDelete}
                  >
                    {labels?.Delete_Contract_label}
                  </Title>
                )}
                {IsSearcher && selectedCollaboration?.Status === 2 && (
                  <Title
                    level={5}
                    className="flex items-center justify-center !m-0 w-full h-full !text-[#FFFFFF]"
                    onClick={onCollaborationClose}
                  >
                    {labels?.End_Contract_Label}
                  </Title>
                )}
                {IsSearcher && selectedCollaboration?.Status === 4 && (
                  <Title
                    level={5}
                    className="flex items-center justify-center !m-0 w-full h-full !text-[#FFFFFF]"
                    onClick={() => {
                      setShowCalendar(true);
                    }}
                  >
                    {labels?.Extend_Contract_Label}
                  </Title>
                )}
              </Button>
            )
          )}
        </div>
      </div>
      <Modal
        open={showCalendar}
        onClose={() => {
          setShowCalendar(false);
        }}
        centered
        footer={null}
        closable={false}
        rootClassName="[&_.ant-modal-content]:!p-0
      [&_.ant-modal]:!w-[360px]"
      >
        <ExtendContractCalender
          selectedDate={selectedDate}
          onDateSelect={onDateSelect}
          setShowCalendar={setShowCalendar}
          tempSelectedDate={tempSelectedDate}
          setTempSelectedDate={setTempSelectedDate}
          handleExtendCollaboration={handleExtendCollaboration}
          setRefresh={setRefresh}
        />
      </Modal>
    </>
  );
};

export default NewCollaborationDetail;
