import { connect } from "react-redux";
import Button from "../../../common/Button/Button";
import InputPassword from "../../../common/Input/InputPassword";
import { notificationAction } from "../../../actions/notification";
import { changePasswordApi } from "./../settingsApi";
import { onStateChangeAction } from "../settingAction";

const ChangePassword = ({
  oldPassword,
  newPassword,
  confirmPassword,
  invalidOldPassword,
  invalidNewPassword,
  invalidConfirmPassword,
  labels,
  OldPassCallback,
  onStateChangeAction,
  notificationAction
}) => {
  const handleFormFieldChange = e => {
    const { name, value } = e.target;
    if (name === "OldPassword") {
      OldPassCallback(false);
      onStateChangeAction({ oldPassword: value });
    }
    if (name === "NewPassword") {
      onStateChangeAction({ newPassword: value });
    }
    if (name === "ConfirmPassword") {
      onStateChangeAction({ confirmPassword: value });
    }
  };

  const handleSubmitBtnClick = () => {
    if (oldPassword.trim() === null || oldPassword.trim() === "") {
      const info = {
        message: labels.changePasswordOldPasswordRequried,
        status: "error"
      };
      notificationAction(info);
      return;
    }

    if (
      newPassword.trim() === null ||
      newPassword === "" ||
      newPassword.length < 6
    ) {
      const info = {
        message: labels.changePasswordNewPasswordInvalid,
        status: "error"
      };
      notificationAction(info);
    }

    if (confirmPassword.trim() === null || confirmPassword.trim() === "") {
      const info = {
        message: labels.changePasswordConfirmPasswordRequried,
        status: "error"
      };
      notificationAction(info);
      return;
    }

    if (newPassword !== confirmPassword) {
      const info = {
        message: labels.changePasswordMissMatchPassword,
        status: "error"
      };
      notificationAction(info);
      return;
    }

    var passwordObj = {
      OldPassword: oldPassword,
      NewPassword: newPassword,
      RepeatPassword: confirmPassword
    };

    changePasswordApi(passwordObj).then(result => {
      if (result.success) {
        const info = {
          message: labels.Password_Changed_Success,
          status: "success"
        };
        onStateChangeAction({
          newPassword: "",
          oldPassword: "",
          confirmPassword: ""
        });
        notificationAction(info);
      } else {
        OldPassCallback(true);
        const info = {
          message: labels.USER_SETTING_OLDPASSWORD_VALIDATION_MESSAGE,
          status: "error"
        };
        notificationAction(info);
        return;
      }
    });
  };

  return (
    <div className="userSettings">
      <div className="form-row">
        <label className="form-label">
          {labels.USER_SETTING_CHANGE_PASSWORD_OLDPASS_LABEL}
        </label>
        <InputPassword
          name="OldPassword"
          className={`input-text ${invalidOldPassword ? "inValid" : ""}`}
          value={oldPassword}
          placeholder={labels.USER_SETTING_CHANGE_PASSWORD_OLDPASS_PLACEHOLDER}
          onChange={handleFormFieldChange}
          testId="input-pass-old-settings"
        />
      </div>

      <div className="form-row">
        <label className="form-label">
          {labels.USER_SETTING_CHANGE_PASSWORD_NEWPASS_LABEL}
        </label>
        <InputPassword
          name="NewPassword"
          className={`input-text ${invalidNewPassword ? "inValid" : ""}`}
          value={newPassword}
          placeholder={labels.USER_SETTING_CHANGE_PASSWORD_NEWPASS_PLACEHOLDER}
          onChange={handleFormFieldChange}
          testId="input-pass-new-settings"
        />
      </div>

      <div className="form-row">
        <label className="form-label">
          {labels.USER_SETTING_CHANGE_PASSWORD_CONFIRMPASS_LABEL}
        </label>
        <InputPassword
          name="ConfirmPassword"
          className={`input-text ${invalidConfirmPassword ? "inValid" : ""}`}
          value={confirmPassword}
          placeholder={
            labels.USER_SETTING_CHANGE_PASSWORD_CONFIRMPASS_PLACEHOLDER
          }
          onChange={handleFormFieldChange}
          testId="input-pass-confirm-settings"
        />
      </div>

      <Button
        className="SendButton"
        onClick={handleSubmitBtnClick}
        testId="btnsettingupdate"
        tooltipHelp="Submit"
        tooltipButton="Submit"
      >
        {labels.USER_SETTING_CHANGE_PASSWORD_SUBMIT_BUTTON}
      </Button>
    </div>
  );
};

const mapStateToProps = ({ systemLabel, setting }) => {
  const { labels } = systemLabel;
  return { labels, ...setting };
};

export default connect(mapStateToProps, {
  notificationAction,
  onStateChangeAction
})(ChangePassword);
