import React from "react";
import moment from "moment";
import "rc-time-picker/assets/index.css";
import "moment/locale/en-gb";
import { CustomInputNew } from "../components/CustomInput";
import { Radio } from "antd";
import ScreenshotGallery from "./ScreenshotGallery";
import DatePickerField from "../../../../common/DatePicker/DatePicker";
import DatePicker from "react-datepicker";

const showSecond = true;
const str = showSecond ? "HH:mm:ss" : "HH:mm";

class TimelogDetail extends React.Component {
  state = {
    isStartTimeFocus: false,
    isEndTimeFocus: false
  };

  // Check if timer is active directly from sessionStorage
  isTimerActive = () => {
    try {
      const timer = JSON.parse(sessionStorage.getItem("tictellTimer") || "{}");
      return timer.status === 1;
    } catch (error) {
      console.error("Error checking timer status:", error);
      return false;
    }
  };

  toggleTimePicker = (type, value) => {
    if (type == 1) {
      this.setState({
        isStartTimeFocus: !this.state.isStartTimeFocus,
        isEndTimeFocus: false
      });
    } else if (type == 2) {
      this.setState({
        isStartTimeFocus: false,
        isEndTimeFocus: !this.state.isEndTimeFocus
      });
    } else {
      this.setState({
        isStartTimeFocus: false,
        isEndTimeFocus: false
      });
    }
  };

  render() {
    const {
      invalidStartDate = false,
      handleTimelogDateChange,
      handleTimelogInputChange,
      currentTimelog,
      handleTimelogInputBlur,
      handleTimeChange,
      selectedMainDate,
      labels,
      isDisabled,
      startDate,
      endDate,
      status,
      selectedCollaboration,
      isTimerOn,
      isActive,
      handleTrackingModeChange
    } = this.props;

    // Check if timer is currently active
    const isTimerActive = this.isTimerActive();

    const {
      TimeLogId,
      Comment = "",
      Date,
      Memo = "",
      Amount = "",
      StartTime,
      EndTime,
      ReviewedBy,
      ReviewedDate
    } = currentTimelog || {};

    // Get TimeTracking value
    const timeTrackingValue = selectedCollaboration?.TimeTracking;

    // Determine which options to show
    const showAuto = timeTrackingValue === 0 || timeTrackingValue === 2;
    const showManual = timeTrackingValue === 1 || timeTrackingValue === 2;

    // Set active state based on TimeTracking setting
    let effectiveIsActive = isActive;
    if (timeTrackingValue === 0 && isActive !== "Auto") {
      effectiveIsActive = "Auto";
    } else if (timeTrackingValue === 1 && isActive !== "Manual") {
      effectiveIsActive = "Manual";
    }

    // Create options based on TimeTracking value
    const options = [];
    if (showAuto) {
      options.push({
        label: "Auto",
        value: "Auto"
      });
    }
    if (showManual) {
      options.push({
        label: "Manual",
        value: "Manual"
      });
    }

    return (
      <div className="expense-detail">
        {options.length > 1 ? (
          <div className="top-manual-buttons">
            <Radio.Group
              options={options}
              value={effectiveIsActive}
              defaultValue={options[0]?.value}
              onChange={e => {
                handleTrackingModeChange(e);
              }}
            />
          </div>
        ) : (
          <div className="top-manual-buttons"></div>
        )}

        <CustomInputNew
          label={labels?.Timelog_DateField}
          validate={true}
          value={Date}
        >
          <label
            className="datepicker-wrapper"
            onClick={() => this.toggleTimePicker(3)}
          >
            <DatePickerField
              selected={Date && moment(Date)}
              onChange={handleTimelogDateChange}
              name="expenseDate"
              dateFormat="dd-MM-YYYY"
              className={`${!Date ? "inValid" : ""}`}
              placeholderText={labels?.Timelog_Date_Placeholder}
              maxDate={
                moment(endDate).isoWeek() ===
                moment(selectedCollaboration?.EndDate).isoWeek()
                  ? moment(selectedCollaboration?.EndDate)
                  : moment(endDate)
              }
              minDate={
                moment(startDate).isoWeek() ===
                moment(selectedCollaboration?.StartDate).isoWeek()
                  ? moment(selectedCollaboration?.StartDate)
                  : moment(startDate)
              }
              disabled={effectiveIsActive === "Auto"}
              locale="en-gb"
              autoComplete="off"
            />
          </label>
        </CustomInputNew>

        {effectiveIsActive === "Manual" && (
          <div className="time-range-picker">
            <CustomInputNew
              label={labels?.Timelog_StartTime}
              validate={true}
              value={StartTime}
            >
              <label
                data-testid={"StartTimeInput"}
                className="datepicker-wrapper"
              >
                <DatePicker
                  selected={moment(StartTime).toDate()}
                  onChange={time => handleTimeChange(time, "StartTime")}
                  showTimeSelect
                  showTimeSelectOnly
                  timeIntervals={15}
                  timeCaption="Start Time"
                  dateFormat={"HH:mm"}
                  disabled={isDisabled}
                  timeFormat={"HH:mm"}
                  placeholderText={labels?.Timelog_Time_Placeholder}
                />
              </label>
            </CustomInputNew>
            <label>to</label>
            <CustomInputNew
              label={labels?.Timelog_EndTime}
              validate={true}
              value={EndTime}
            >
              <label className="datepicker-wrapper" data-testid="EndTimeInput">
                <DatePicker
                  selected={moment(EndTime).toDate()}
                  onChange={time => handleTimeChange(time, "EndTime")}
                  showTimeSelect
                  showTimeSelectOnly
                  timeIntervals={15}
                  timeCaption="End Time"
                  dateFormat={"HH:mm"}
                  timeFormat={"HH:mm"}
                  disabled={isDisabled}
                  placeholderText={labels?.Timelog_Time_Placeholder}
                />
              </label>
            </CustomInputNew>
          </div>
        )}

        <CustomInputNew label={labels?.Timelog_Detail}>
          <textarea
            type="text"
            value={!Memo ? "" : Memo}
            onChange={handleTimelogInputChange}
            onBlur={handleTimelogInputBlur}
            onClick={() => this.toggleTimePicker(3)}
            name="Memo"
            className="textarea-field"
            rows="6"
            placeholder={labels?.Timelog_Detail_Placeholder}
            data-testid="input-details"
            disabled={isDisabled}
          />
        </CustomInputNew>

        {(status === 2 || status === 3) && ReviewedBy !== null && (
          <div>
            <CustomInputNew label={labels?.ReviewedBy_Label}>
              <input
                type="text"
                autoComplete="off"
                value={ReviewedBy || ""}
                name="ReviewedBy"
                className="input-text"
                placeholder={labels?.ReviewedBy_Label}
                data-testid="input-text-opportunity-title"
                disabled
              />
            </CustomInputNew>
            <CustomInputNew label={labels?.ReviewedDate_Label}>
              <input
                type="text"
                autoComplete="off"
                value={
                  ReviewedDate ? moment(ReviewedDate).format("DD-MM-YYYY") : ""
                }
                name="ReviewedDate"
                className="input-text"
                placeholder={labels?.ReviewedDate_Label}
                data-testid="input-text-opportunity-title"
                disabled
              />
            </CustomInputNew>
          </div>
        )}

        {currentTimelog?.Screenshots &&
          currentTimelog.Screenshots.length > 0 && (
            <ScreenshotGallery screenshots={currentTimelog.Screenshots} />
          )}
      </div>
    );
  }
}

export default TimelogDetail;
