import moment from "moment";
import CommonDrawer from "../../../../../common-alpha/Drawer/Drawer";
import { But<PERSON> } from "antd";
import ArrowRight from "../../../../../assets-alpha/images/svg/arrow-right.svg?react";
import { List, ListItem } from "../Reports";
import { twMerge } from "tailwind-merge";
const TimelogList = ({
  timelogList,
  onItemClick,
  updateTimeSheetStatus,
  TimeSheetStatusId,
  onClose,
  weekTimelog,
  currentTimeReport
}) => {
  return (
    <CommonDrawer
      open={true}
      width={440}
      title={
        <Title
          TimeSheetStatusId={TimeSheetStatusId}
          updateTimeSheetStatus={updateTimeSheetStatus}
        />
      }
      onClose={onClose}
    >
      <SubHeading>Overall</SubHeading>
      <div className="mb-10 bg-[var(--light-purple)] p-4 rounded-[12px] flex items-center justify-between">
        <div>
          <div className="font-medium text-base mb-1 text-[var(--dark)]">
            {currentTimeReport?.IProName}
          </div>
          <div className="text-[var(--gray-3)] mb-3 text-xs">
            {`${moment(currentTimeReport?.StartDate).format("MMM D")}-${moment(
              currentTimeReport?.EndDate
            ).format("D")}, ${moment(currentTimeReport?.StartDate).format(
              "YYYY"
            )} • Week ${moment(currentTimeReport?.StartDate).format("W")}`}
          </div>
          <div className="text-[var(--gray-3)] text-xs">
            Total time:
            <span className="text-[var(--dark)] ml-1">{weekTimelog?.Time}</span>
          </div>
        </div>
        <div className="flex flex-col items-center">
          <span className="text-[var(--gray-3)] font-medium text-[10px]">
            Total amount
          </span>
          <span className="text-[var(--purple)] font-medium text-[18px]">
            {weekTimelog?.Amount}
          </span>
        </div>
      </div>
      <SubHeading>Time log details</SubHeading>
      <List>
        {timelogList &&
          timelogList.map(item => (
            <ListItem
              onClick={() => onItemClick(item)}
              isSelected={item.isSelected}
              key={item.TimeLogId}
              text={
                <KeyValueDisplay
                  keyLabel="Date:"
                  value={moment(item.Date).format("ddd, MMM DD YYYY")}
                />
              }
              right={`${item.Amount} ${item.Currency}`}
              subText={
                <KeyValueDisplay keyLabel="Time:" value={item.Duration} />
              }
            ></ListItem>
          ))}
      </List>
    </CommonDrawer>
  );
};

export default TimelogList;

export const KeyValueDisplay = ({ keyLabel, value }) => {
  return (
    <div className="flex gap-1 items-center text-[var(--dark)] text-sm">
      <span className="text-[var(--gray-3)]">{keyLabel}</span>
      {value}
    </div>
  );
};
export const SubHeading = ({ children, className }) => {
  return (
    <div className={twMerge("text-[var(--gray-3)] text-sm mb-2", className)}>
      {children}
    </div>
  );
};
export const Title = ({
  TimeSheetStatusId,
  updateTimeSheetStatus,
  title = "Timesheet"
}) => {
  return (
    <div className="flex justify-between items-center">
      <span>{title}</span>

      <div className="flex gap-2 items-center">
        {TimeSheetStatusId == 1 && (
          <Button
            className="!bg-[#FEEDED] !text-[var(--red)] px-3 py-2"
            onClick={() => updateTimeSheetStatus(3)}
          >
            Decline
          </Button>
        )}
        {TimeSheetStatusId == 1 && (
          <Button
            className="!bg-[var(--green-2)] !text-white px-3 py-2"
            onClick={() => updateTimeSheetStatus(2)}
          >
            Approve <ArrowRight />
          </Button>
        )}
      </div>
    </div>
  );
};
