import { useState } from "react";
import { <PERSON>dal, Button, Form, Alert, Select } from "antd";
import classes from "./index.module.scss";
import { getIndustriesApi } from "../../dashboardApi";
import DebounceSelect from "../../../../components/DebounceSelect/DebounceSelect";
import closeIcon from "../../../../assets/images/Icons/close-new.png";
import LanguageImage from "../../../../assets/images/industary-popup.png";

function IndustryPopup({
  isLoading,
  SaveResumeDetails,
  errorMessage,
  labels,
  handleIndustryChange,
  resumeCurrentIndustry
}) {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [error, setError] = useState(null);

  const handleCancel = () => {
    setIsModalVisible(false);
  };
  const onFinish = () => {
    SaveResumeDetails();
    setIsModalVisible(false);
  };

  const IndustryProficiencyList = [
    { value: 1, label: "Beginner" },
    { value: 2, label: "Proficient" },
    { value: 3, label: "Expert" }
  ];
  return (
    <>
      <button className="add-profile" onClick={() => setIsModalVisible(true)}>
        {labels.ADD_SHORTLIST_RESUME_INDUSTRY}
      </button>

      <Modal
        open={isModalVisible}
        // onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
        closable={false}
        className={`new-design-modal ${classes.popUpForm}`}
      >
        <div className="text-center mb-3 header-modal">
          <h5>{labels.ADD_SHORTLIST_RESUME_INDUSTRY}</h5>
          <div onClick={() => handleCancel()} className="pointer">
            <img className="close-icon" src={closeIcon} alt="" />
          </div>
        </div>
        <div className="images-icon">
          <img src={LanguageImage} alt="" />
          <p className="paragraph">
            Please select your education, and your level of experience in this
            industry
          </p>
        </div>
        <Form
          name="normal_login"
          className={`${classes.form} form`}
          onFinish={onFinish}
          size="large"
          // initialValues={{ date: SkillProficiencyList[0] }}
        >
          <Form.Item
            className={`${classes.customSelect}`}
            name="industry"
            rules={[
              {
                required: resumeCurrentIndustry.IndustryId ? false : true,
                message: labels.SELECT_INDUSTRY_VAIDATION_LBL
              }
            ]}
          >
            <DebounceSelect
              mode="multiple"
              selectName={"IndustryId"}
              value={resumeCurrentIndustry.IndustryId}
              placeholder={labels.companyIndustryPlaceholder}
              name="industry"
              fetchOptions={getIndustriesApi}
              handleChange={handleIndustryChange}
            />
          </Form.Item>

          <Form.Item
            rules={[
              {
                required: resumeCurrentIndustry.experienceLevel ? false : true,
                message: labels.SELECT_EXPERIECE_LEVEL_VALIDATION_LBL
              }
            ]}
            className={`${classes.customSelect}`}
            name="experienceLevel"
          >
            <Select
              placeholder={"Level"}
              onChange={e => handleIndustryChange("ExperienceLevel", e)}
              allowClear
              value={resumeCurrentIndustry.experienceLevel}
              name="experienceLevel"
              autoComplete="new-password"
            >
              {IndustryProficiencyList &&
                IndustryProficiencyList.map((item, index) => (
                  <Select.Option key={index} value={item.value}>
                    {item.label}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>

          {error && (
            <Alert
              message={errorMessage?.replace("<br />", "")}
              type="warning"
              closable
            />
          )}

          <Button
            type="primary"
            block
            htmlType="submit"
            className="login-form-button mt-2"
            loading={isLoading}
          >
            Done
          </Button>
        </Form>
      </Modal>
    </>
  );
}

export default IndustryPopup;
