import { Avatar, Dropdown, Switch } from "antd";
import clsx from "clsx";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  helpActiveAction,
  settingModalAction
} from "../../actions/navigationActions";
import AboutIcon from "../../assets-alpha/images/svg/about-us.svg";
import ArrowDown from "../../assets-alpha/images/svg/arrow-down-1.svg";
import ProfileImage from "../../assets-alpha/images/svg/avatar.svg";
import FaqIcon from "../../assets-alpha/images/svg/faq.svg";
import FeedbackIcon from "../../assets-alpha/images/svg/feedback.svg";
import InformationIcon from "../../assets-alpha/images/svg/information.svg";
import LoginIcon from "../../assets-alpha/images/svg/login.svg?react";
import LogoSrc from "../../assets-alpha/images/svg/logo-new.svg";
import ToggleIcon from "../../assets-alpha/images/svg/menu.svg?react";
import SidebarLeft from "../../assets-alpha/images/svg/sidebar-left.svg?react";
import SidebarRight from "../../assets-alpha/images/svg/sidebar-right.svg?react";
import SettingIcon from "../../assets-alpha/images/svg/setting.svg";
import Icon from "../../common-alpha/Icon/Icon";
import { privateRoutes } from "../../Routes/routing";
import Settings from "../../views-alpha/Settings/Settings";
import HeaderNavButton from "./components/HeaderNavButton";
import "./header.scss";
import { useLogout } from "./useHeader";
import { useGetUserProfileApi } from "../../views-alpha/Settings/settingsApi";

const Header = ({ hideUserSetting, onNavigationToggle, isNavShrink }) => {
  const navigate = useNavigate();
  const {
    data: userProfile,
    isLoading: loadingProfile
  } = useGetUserProfileApi();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const { systemLabel, navigation, userInfo } = useSelector(state => state);
  const isSettingOpen = useSelector(state => state.navigation.isSettingOpen);
  const { handleLogout } = useLogout();
  const { labels } = systemLabel;
  const { isHelpActive } = navigation;

  const dispatch = useDispatch();

  const handleHelpClick = e => {
    dispatch(helpActiveAction({ isHelpActive: !isHelpActive }));
    onToggleDropdown(false);
  };

  const { FirstName, userEmail, Email } = userInfo.user;
  const onToggleDropdown = open => {
    setIsDropdownOpen(open => !open);
  };
  return (
    <header
      className="flex items-center py-[.5rem] px-4 bg-white border-b-[1px] [border-image-source:linear-gradient(90deg,#4DA4F4_0%,#04CAA7_9.76%,#3CD4F6_20.36%,#F6AA3A_29.9%,#F17849_39.99%,#F54949_50.22%,#8F82F5_59.65%,#BF90D4_70.02%,#B5F05D_80.36%,#4DA4F4_100%)] [border-image-slice:1] h-[var(--header-height)]"
      data-testid="application-header"
    >
      <div className="flex-1 flex items-center gap-[18px] pl-[6px]">
        {!hideUserSetting && (
          <>
            <button onClick={onNavigationToggle}>
              <Icon
                className="text-[var(--purple)]"
                src={
                  window.innerWidth < 767
                    ? ToggleIcon
                    : !isNavShrink
                    ? SidebarLeft
                    : SidebarRight
                }
                alt="toggle menu"
                renderSvg
              />
            </button>
          </>
        )}
        <div>
          <Icon
            src={LogoSrc}
            alt="Prodoo logo"
            className="cursor-pointer"
            onClick={() => {
              navigate(privateRoutes.dashboard.path);
            }}
          />
        </div>
      </div>

      <div className="flex items-center gap-[24px]">
        {!hideUserSetting && (
          <>
            <Dropdown
              className="flex gap-[4px] cursor-pointer max-lg:hidden"
              onOpenChange={onToggleDropdown}
              overlayClassName="dropdown-overlay"
              open={isDropdownOpen}
              menu={{
                items: [
                  {
                    label: (
                      <HeaderNavButton className="py-[10px]">
                        <Avatar
                          src={
                            userProfile?.items?.ProfilePicture ?? ProfileImage
                          }
                          className="!w-11 !h-11 !m-auto !block min-w-11"
                        />

                        <div className="text-[#343333] text-base">
                          {FirstName}
                          <br />
                          <span className="text-[#878787] text-sm">
                            {userEmail ? userEmail : Email}
                          </span>
                        </div>
                      </HeaderNavButton>
                    ),
                    key: "profile"
                  },
                  {
                    type: "divider"
                  },
                  {
                    label: (
                      <HeaderNavButton>
                        <Icon src={SettingIcon} alt={"settings"} />
                        {labels?.setting_label}
                      </HeaderNavButton>
                    ),
                    key: "setting"
                  },

                  {
                    label: (
                      <HeaderNavButton>
                        <Icon src={AboutIcon} alt={"about us"} />
                        {labels?.LANDING_NAV_ABOUT_US_LBL}
                      </HeaderNavButton>
                    ),
                    key: "about"
                  },

                  {
                    label: (
                      <HeaderNavButton>
                        <Icon src={FaqIcon} alt={"faq"} />
                        {labels?.ABOUT_FAQ_HEADING}
                      </HeaderNavButton>
                    ),
                    key: "faq"
                  },
                  {
                    label: (
                      <HeaderNavButton>
                        <Icon src={FeedbackIcon} alt={"feedback"} />
                        {labels?.IPRO_NAVIGATION_FEEDBACK_LABEL}
                      </HeaderNavButton>
                    ),
                    key: "feedback"
                  },
                  {
                    type: "divider"
                  },

                  {
                    label: (
                      <HeaderNavButton>
                        <Icon src={InformationIcon} alt={"guide"} />
                        {labels?.IPRO_NAVIGATION_GUIDE_LABEL}
                        <Switch className="!ml-auto" checked={isHelpActive} />
                      </HeaderNavButton>
                    ),
                    key: "guide"
                  },
                  {
                    label: (
                      <HeaderNavButton className="text-[#ff3b30]">
                        <Icon src={LoginIcon} alt={"logout"} renderSvg />
                        {labels?.HEADER_LOGOUT_ICON_HELP}
                      </HeaderNavButton>
                    ),
                    key: "logout"
                  }
                ],
                onClick: e => {
                  if (e.key == "guide") {
                    handleHelpClick(e);
                    return;
                  } else if (e.key == "setting") {
                    dispatch(settingModalAction(true));
                  } else if (e.key == "logout") {
                    handleLogout();
                  } else if (e.key == "feedback") {
                    navigate(privateRoutes.feedback.path);
                  } else if (e.key == "about") {
                    navigate(privateRoutes.about.path);
                  } else if (e.key == "faq") {
                    navigate(privateRoutes.faq.path);
                  }
                }
              }}
              trigger={["click"]}
            >
              <span>
                <Avatar
                  src={userProfile?.items?.ProfilePicture ?? ProfileImage}
                  className="!w-11 !h-11 !m-auto !block min-w-11"
                />
                <Icon
                  className={clsx("w-[16px] transition-all", {
                    "rotate-[180deg]": isDropdownOpen
                  })}
                  src={ArrowDown}
                  alt="arrow"
                />
              </span>
            </Dropdown>
          </>
        )}
      </div>

      {isSettingOpen && (
        <Settings
          open={isSettingOpen}
          onClose={() => dispatch(settingModalAction(false))}
        />
      )}
    </header>
  );
};

export default Header;
