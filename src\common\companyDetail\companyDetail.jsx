import { useSelector } from "react-redux";

const CompanyDetailNew = ({ selectedCompanyy, allJobsList }) => {
  const selectedCompany = selectedCompanyy?.[0];
  const labels = useSelector((state) => state.systemLabel.labels);
  return (
    <div className="flex flex-col bg-[#F3F1FD] rounded-xl mt-3 md:ml-6 md:mr-6 ml-4 mr-4">
      <div className="flex pl-4 pr-4 h-auto min-h-10 !border-b !border-[#EAE5FC]">
        <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
          {labels?.TITLE_LABEL}
        </label>
        <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
        <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
          {selectedCompany?.Company?.CompanyName
            ? selectedCompany?.Company?.CompanyName
            : "N/A"}
        </p>
      </div>
      <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
        <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
          {labels?.IproOpportunityNewCompanyAddressLabel}
        </label>
        <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
        <p className="!ml-[12.5px] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
          {selectedCompany?.Company?.CompanyAddress
            ? selectedCompany?.Company?.CompanyAddress
            : "N/A"}
        </p>
      </div>
      <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
        <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
          {labels?.SHORTLIST_RESUME_INDUSTRY}
        </label>
        <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
        <p className="!ml-[12.5px] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
          {selectedCompany?.Company?.Industry?.IndustryValue
            ? selectedCompany?.Company?.Industry?.IndustryValue
            : "N/A"}
        </p>
      </div>
      <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
        <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
          {labels?.LANDING_SIGNUP_COUNTRY_INPUT_LABL}
        </label>
        <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
        <p className="!ml-[12.5px] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
          {selectedCompany?.Company?.CompanyCountry?.CountryName
            ? selectedCompany?.Company?.CompanyCountry?.CountryName
            : "N/A"}
        </p>
      </div>
      <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
        <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
          {labels?.SearcherSentCollaborationPhoneLabel}
        </label>
        <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
        <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
          {selectedCompany?.Company?.UserPhonenumber?.UserPhonenumberValue
            ? selectedCompany?.Company?.UserPhonenumber?.UserPhonenumberValue
            : "N/A"}
        </p>
      </div>
      <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
        <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
          {labels?.IproOpportunityNewCompanyWebUrlLabel}
        </label>
        <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
        <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
          {selectedCompany?.Company?.WebUrl
            ? selectedCompany?.Company?.WebUrl
            : "N/A"}
        </p>
      </div>
      <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
        <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
          {labels?.IproOpportunityNewCompanyVatLabel}
        </label>
        <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
        <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
          {selectedCompany?.Company?.Vat
            ? selectedCompany?.Company?.Vat
            : "N/A"}
        </p>
      </div>
      <div className="flex pl-4 pr-4 pt-[14px] min-h-10 h-auto pb-3">
        <label className="flex  !w-[72px] h-full text-[#878787] text-[13px] font-normal ">
          {labels?.ViewTitleFeedBackDetail}
        </label>
        <span className="h-[22.5px] border border-[#EAE5FC]  ml-[21.5px]"></span>
        <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal  text-[#343333]">
          {selectedCompany?.Company?.Detail
            ? selectedCompany?.Company?.Detail
            : "N/A"}
        </p>
      </div>
    </div>
  );
};

export default CompanyDetailNew;
