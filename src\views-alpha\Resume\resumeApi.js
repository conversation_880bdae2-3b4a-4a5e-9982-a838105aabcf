import { useMemo } from "react";
import {
  useClientMutation,
  useClientQueies,
  useClientQuery
} from "../../api-alpha/api-service";
import { ApiUrl } from "../../api-alpha/apiUrls";

export const useGetResume = () => {
  return useClientQuery({
    url: ApiUrl.ResumeEdit.GetMyResume
  });
};

export const useGetSuggestedProfile = ({ id, type }) => {
  return useClientQuery({
    url: ApiUrl.ResumeEdit.RoleSuggestionsLookup({
      Id: id,
      type: type
    }),
    enabled: !!id && !!type
  });
};

export const useGetSuggestedSkills = ({ id, type }) => {
  return useClientQuery({
    url: ApiUrl.ResumeEdit.SkillSuggestionsLookup({
      Id: id,
      type: type
    }),
    enabled: !!id && !!type
  });
};

export const useGetSuggestedCertifications = ({ id, type }) => {
  return useClientQuery({
    url: ApiUrl.ResumeEdit.CertificationSuggestionsLookup({
      Id: id,
      type: type
    }),
    enabled: !!id && !!type
  });
};
export const useGetResumeProfile = ({ url, keys }) => {
  const query = useClientQuery({
    url: url
  });

  // memoize the filtered items array
  const items = useMemo(() => {
    const keyMap = keys;
    return (
      query.data?.items?.map((item, i) => {
        const mappedItem = { ...item };
        for (const newKey in keyMap) {
          const originalKey = keyMap[newKey];
          mappedItem[newKey] = item[originalKey];
        }
        return mappedItem;
      }) ?? []
    );
  }, [query.data?.items, keys]);

  // memoize the whole return to keep object identity
  return useMemo(
    () => ({
      ...query,
      data: items
    }),
    [query, items]
  );
};

export const useUploadResume = () => {
  return useClientMutation({
    url: ApiUrl.Resume.GetMyPDFResume,
    method: "POST"
  });
};

export const useSaveParseResume = () => {
  return useClientMutation({
    url: ApiUrl.ResumeEdit.SaveParsedResume,
    method: "POST"
  });
};

export const useDeleteResumeApi = () => {
  return useClientMutation({
    url: ApiUrl.ResumeEdit.DeleteResumeDetail,
    method: "POST"
  });
};

export const useResumeProfile = () => {
  return useClientMutation({
    url: "",
    queryKeys: ["ResumeProfile"],
    method: "POST"
  });
};
export const useResumeAvailability = () => {
  return useClientMutation({
    url: ApiUrl.ResumeEdit.UpdateResumeAvailability,
    method: "PUT"
  });
};

export const useResumeLookup = ({ urls = [], keys = [], searchKeys = [] }) => {
  const resolvedUrls = urls.map((u, i) =>
    typeof u === "function" ? u({ searchKey: searchKeys[i] || "a" }) : u
  );

  const queries = useClientQueies({
    urls: resolvedUrls
  });
  const items = useMemo(() => {
    return queries.map((query, i) => {
      const keyMap = keys[i] || {};
      return (
        query.data?.items?.map(item => {
          const mappedItem = { ...item };
          for (const newKey in keyMap) {
            const originalKey = keyMap[newKey];
            mappedItem[newKey] = item[originalKey];
          }
          return mappedItem;
        }) ?? []
      );
    });
  }, [queries, keys]);

  return useMemo(
    () => ({
      data: items,
      isLoading: queries.some(q => q.isLoading),
      isError: queries.some(q => q.isError),
      queries // expose full query info if needed
    }),
    [items, queries]
  );
};
