import { Col, Progress, Row, Typography } from "antd";
import { filter, find, map } from "lodash";
import moment from "moment";
import { useEffect, useState } from "react";
import { connect, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { v1 as uuidv1 } from "uuid";
import { onStateChangeActions } from "../../Routes/phillipModalAction";
import { notificationAction } from "../../actions/notification";
import { StorageService } from "../../api/storage";
import ArrowRight from "../../assets/images/Icons/arrow-right.png";
import QuickIcon from "../../assets/images/quick.png";
import RankingIcon from "../../assets/images/ranking.png";
import RebortIcon from "../../assets/images/rebort.png";
import SearchNormalIcon from "../../assets/images/search-normal.png";
import SearcherPhillip from "../../components/Phillip/SearcherPhillip";
import { updatePhillipPopupStatusApi } from "../../components/Phillip/phillipApi";
import {
  isNullOrUndefined,
  loadImageOrientation
} from "../../utilities/helpers";
import PresentationWidget from "./components/PresentationWidget";
import ResumeWidget from "./components/ResumeWidget";
import ShortlistWidget from "./components/ShortlistWidget";
import UserCompanyWidget from "./components/UserCompanyWidget";

import IproPhillip from "../../components/Phillip/IproPhillip";
import {
  getMyResumeDetails as getMyResumeDetailsApi,
  getResumesApi,
  // getSocialMediaApi,
  getUserDataForDashboard,
  // getUserSocialMediaLinks as getUserSocialMediaLinksApi,
  getUserWidgets,
  saveUpdatedResumeApi,
  SaveUserDashboardDetails
} from "./dashboardApi";
import "./new-dashboard.scss";
import EditCertificationPopup from "./popups/EditCertificationPopup/EditCertificationPopup";
import EditEducationPopUp from "./popups/EditEducationPopUp/EditEducationPopUp";
import EditLanguagePopUp from "./popups/EditLanguagePopUp/EditLanguagePopUp";
import EditLocationPopUp from "./popups/EditLocationPopUp/EditLocationPopUp";
import EditSkillPopup from "./popups/EditSkillPopup/EditSkillPopup";
import ExperiencePopup from "./popups/ExperiencePopup/ExperiencePopup";
import IndustryPopup from "./popups/IndustryPopup/IndustryPopup";
import KeywordPopup from "./popups/KeywordPopup/KeywordPopup";
import MissingRoleSkill from "./popups/MissingRoleAndSkill/MissingRoleSkill";
import OtherAchievementPopup from "./popups/OtherAchievementPopup/OtherAchievementPopup";
import RolePopup from "./popups/RolePopup/RolePopup";

const { Title, Text } = Typography;

const NewDashboard = props => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [state, setState] = useState({
    userDetails: {
      CountryName: "",
      UserFirstname: "",
      UserLastname: "",
      ProfilePicture: "",
      UserEmail: "",
      UserPhonenumberValue: ""
    },
    userFormDetail: {},
    resumeCurrentIndustry: {},
    currentRole: {},
    otherAchievementForm: { ResumeOtherAchivenmentId: "0" },
    resumeExperienceEdit: {
      isEndDate: false,
      Description: "",
      EndDate: null,
      CompanyWorked: null,
      StartDate: null,
      IndustryId: null,
      ProfileId: null,
      ResumeExperienceId: null,
      ResumeId: null
    },
    userResumeDetails: {},
    socialMedias: [],
    socialLinks: [],
    UserSocialMediaLinks: [],
    isLoading: false,
    errorMessage: "",
    linkedInClientId: "",
    facebookClientId: "",
    fetchingUserDetails: false,
    error: false,
    fetchingPresentation: false,
    isReloadResumeShortlistWidget: false,
    isReloadCompanyPresentationsWidget: false,
    showPhillip: false,
    isReloadResumeWidget: false,
    languageList: {},
    keywordsList: {},
    otherAchivenmentsList: {},
    skillsList: {},
    certificationsList: {},
    educationsList: {
      EducationTypeId: 0,
      EducationYear: 0,
      EducationId: 0
    },
    industriesList: {},
    experiencesList: {},
    resumeEducation: {},
    resumeLocation: {},
    currentKeyword: {},
    resumeCertificationEdit: {},
    resumeSkillCurrent: {},
    resumes: {
      Profiles: [],
      Skills: [],
      Educations: [],
      Certifications: [],
      Industries: [],
      Languages: [],
      ResumeExperience: []
    },
    UserDetailsWidget: false,
    IProProfileProgress: false,
    PhilipsIpro: false,
    PresentationIpro: false,
    ResumeIpro: false,
    CompanySearcher: false,
    PhilipsSearcher: false,
    ResumeShortlists: false,
    SearcherResources: false,
    resumeLanguage: {
      ExperienceLevel: 1
    }
  });

  useEffect(() => {
    const { User } = props;
    setState(st => ({
      ...st,
      fetchingUserDetails: true,
      fetchingPresentation: true
    }));
    getUserDetails();
    // getSocialMedia();
    if (User.IsFreelancer) {
      getMyResumeDetails();
      getResume();
    }
    GetWIdgets(User.IsFreelancer);
  }, []);

  useEffect(() => {
    const { User } = props;
    if (User) {
      if (User.IsFreelancer) {
        getMyResumeDetails();
        getResume();
      }
      GetWIdgets(User.IsFreelancer);
    }
  }, [props.User]);

  const GetWIdgets = IsFreelancer => {
    var requiredFeaturesNames = [];
    if (IsFreelancer) {
      requiredFeaturesNames = [
        "ResumeIpro",
        "PhilipsIpro",
        "PresentationIpro",
        "IProProfileProgress",
        "UserDetailsWidget"
      ];
    } else {
      requiredFeaturesNames = [
        "PhilipsSearcher",
        "CompanySearcher",
        "ResumeShortlists",
        "UserDetailsWidget",
        "SearcherResources"
      ];
    }
    getUserWidgets(requiredFeaturesNames)
      .then(response => {
        if (response.success) {
          let dashboardWidgets = {};
          response.items.map(item => {
            dashboardWidgets[item.Name] = item.IsActive;
          });
          setState(st => ({ ...st, ...dashboardWidgets }));
        }
      })
      .catch(e => {});
  };

  // const getResume = () => {
  //   setState(st => ({ ...st, isLoading: true }));
  //   getResumesApi()
  //     .then(data => {
  //       if (data.success) {
  //         setState(st => ({
  //           ...st,
  //           resumes: data.items
  //         }));
  //         // ...existing code...
  //       }
  //       setState(st => ({ ...st, isLoading: false }));
  //     })
  //     .catch(e => {});
  // };

  const getResume = () => {
    setState(st => ({ ...st, isLoading: true }));

    getResumesApi()
      .then(data => {
        if (data.success) {
          setState(st => ({
            ...st
          }));
          // const InitialAvailabilityDate = isNullOrUndefined(
          //   data.items.AvailabilityDate
          // )
          //   ? null
          //   : moment(data.items.AvailabilityDate);
          // const InitialAvailabilityType =
          //   data.items.AvailablityType && data.items.AvailablityType;
          const locationList = data.items.Regions.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isInvalid: false
          }));
          //Language
          const languageList = data.items.Languages.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isInvalid: false,
            isCandidate: false
          }));
          const candidatelanguageList = data.items.ResumeCandidateLanguage.map(
            item => ({
              ExperienceLevel: item.ExperienceLevel,
              LanguageId: item.ResumeCandidateLanguageId,
              LanguageValue: item.ResumeCandidateLanguageValue,
              uniqueId: uuidv1(),
              isInvalid: false,
              isCandidate: true
            })
          );
          languageList.push(...candidatelanguageList);
          ////////
          //Role Mapping
          const rolesList = data.items.Profiles.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isInvalid: false,
            isCandidate: false,
            isActive: false
          }));
          const candidateRolesList = data.items.ResumeCandidateProfiles.map(
            item => ({
              ExperienceLevel: item.ExperienceLevel,
              ProfileId: item.ResumeCandidateProfileId,
              ProfileValue: item.ResumeCandidateProfileName,
              uniqueId: uuidv1(),
              isInvalid: false,
              isCandidate: true,
              isActive: false
            })
          );

          rolesList.push(...candidateRolesList);
          // setState(st => ({
          //   ...st,
          // }));
          ///skill mapping
          const skillsList = data.items.Skills.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isInvalid: false,
            isCandidate: false,
            isActive: false
          }));
          const candidateSkillsList = data.items.ResumeCandidateSkills.map(
            item => ({
              ExperienceLevel: item.ExperienceLevel,
              SkillId: item.ResumeCandidateSkillId,
              SkillValue: item.ResumeCandidateSkillName,
              uniqueId: uuidv1(),
              isInvalid: false,
              isCandidate: true,
              isActive: false
            })
          );
          skillsList.push(...candidateSkillsList);
          //another array
          //skills mapping ends here

          //Certification Mapping
          const certificationsList = data.items.Certifications.map(item => ({
            ...item,
            CertificationDate: moment(
              item.CertificationDate,
              "YYYY-MM-DDTHH:mm:ss"
            ).format("MM/DD/YYYY"),
            uniqueId: uuidv1(),
            isCandidate: false,
            isInvalid: false,
            isActive: false
          }));
          const candidateCertificationList = data.items.ResumeCandidateCertifications.map(
            item => ({
              CertificationDate: moment(
                item.CertificationDate,
                "YYYY-MM-DDTHH:mm:ss"
              ).format("MM/DD/YYYY"),
              CertificationId: item.ResumeCandidateCertificationId,
              CertificationValue: item.ResumeCandidateCertificationValue,
              uniqueId: uuidv1(),
              isInvalid: false,
              isCandidate: true,
              isActive: false
            })
          );
          certificationsList.push(...candidateCertificationList);

          //another array
          //Education Mapping
          const educationsList = data.items.Educations.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isInvalid: false
          }));
          const candidateEducationList = data.items.ResumeCandidateEducations.map(
            item => ({
              EducationYear: item.EducationYear,
              EducationId: item.ResumeCandidateEducationId,
              EducationValue: item.ResumeCandidateEducationValue,
              EducationTypeId: item.EducationTypeId,
              uniqueId: uuidv1(),
              isInvalid: false,
              isCandidate: true
            })
          );
          educationsList.push(...candidateEducationList);

          //////////
          //Keyword mapping
          const keywordsList = data.items.Keywords.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isCandidate: false,
            isInvalid: false
          }));
          const candidateKeywordList = data.items.ResumeCandidateKeywords.map(
            item => ({
              KeywordId: item.ResumeCandidateKeywordId,
              KeywordValue: item.ResumeCandidateKeywordValue,
              uniqueId: uuidv1(),
              isInvalid: false,
              isCandidate: true
            })
          );
          keywordsList.push(...candidateKeywordList);
          ///////////////////////

          //Industruy
          const industriesList = data.items.Industries.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isInvalid: false,
            isCandidate: false
          }));
          const candidateIndustriesList = data.items.ResumeCandidateIndustries.map(
            item => ({
              ExperienceLevel: item.ExperienceLevel,
              IndustryId: item.ResumeCandidateIndustryId,
              IndustryValue: item.ResumeCandidateIndustryValue,
              uniqueId: uuidv1(),
              isInvalid: false,
              isCandidate: true
            })
          );
          industriesList.push(...candidateIndustriesList);
          ////////
          const otherAchivenmentsList = data.items.ResumeOtherAchivenments.map(
            item => ({
              ...item,
              uniqueId: uuidv1(),
              isInvalid: false
            })
          );
          const experiencesList = data.items.ResumeExperience.map(item => ({
            ...item,
            uniqueId: uuidv1(),
            isWorking: isNullOrUndefined(item.EndDate) ? true : false,
            StartDate: moment(item.StartDate, "YYYY-MM-DDTHH:mm:ssZ").format(
              "MM/DD/YYYY"
            ),
            EndDate: !isNullOrUndefined(item.EndDate)
              ? moment(item.EndDate, "YYYY-MM-DDTHH:mm:ssZ").format(
                  "MM/DD/YYYY"
                )
              : moment(new Date(), "MM/DD/YYYY"),
            isInvalid: false,
            isRoleInvalid: false,
            isIndustryInvalid: false
          }));
          setState(st => ({
            ...st,
            IsAvailable: data.items.IsAvailable,
            AvailablityType: data.items.AvailablityType,
            busyUntil: moment(data.items.AvailabilityDate),
            locationList,
            keywordsList,
            otherAchivenmentsList,
            certificationsList,
            educationsList,
            industriesList,
            experiencesList,
            skillsList,
            languageList,
            rolesList,
            resumes: data.items,
            isLoading: false
          }));
        }
        setState(st => ({ ...st, isLoading: false }));
      })
      .catch(e => {});
  };

  const SaveUserDetails = () => {
    const { userFormDetail, socialLinks } = state;
    const {
      UserPhonenumberId,
      UserPhonenumberValue,
      UserEmailId,
      UserEmail,
      UserDetailId,
      UserFirstname,
      UserLastname,
      isPhoneAvailable,
      ProfilePicture
    } = userFormDetail;

    if (
      (!UserPhonenumberValue && isPhoneAvailable) ||
      !UserEmail ||
      !UserLastname ||
      !UserFirstname
    ) {
      setState(st => ({
        ...st,
        errorMessage: props.labels.FILL_REQUIRED_FIELD_LBL
      }));
      return;
    }
    let dataObject = {
      UserPhonenumberId: isPhoneAvailable ? UserPhonenumberId : null,
      UserPhonenumberValue: isPhoneAvailable ? UserPhonenumberValue : null,
      UserEmailId: UserEmailId,
      UserEmail: UserEmail,
      UserFirstname: UserFirstname,
      UserLastname: UserLastname,
      ProfilePicture: ProfilePicture,
      SocialLinks: socialLinks
        .filter(x => x.URL)
        .map(item => ({
          ...item,
          UserDetailId: UserDetailId
        }))
    };
    setState(st => ({ ...st, isLoading: true }));
    SaveUserDashboardDetails(dataObject)
      .then(data => {
        if (data.success) {
          // getSocialMedia();
          getUserDetails();
          setState(st => ({ ...st, isLoading: false }));
        }
      })
      .catch(() => {});
    setState(st => ({ ...st, isLoading: false }));
  };

  const handleResumeSave = () => {
    const {
      IsAvailable,
      AvailablityType,
      busyUntil,
      languageList,
      resumeLanguage,
      resumeCertificationEdit,
      resumeExperienceEdit,
      rolesList,
      currentRole,
      keywordsList,
      currentKeyword,
      otherAchivenmentsList,
      otherAchievementForm,
      skillsList,
      resumeSkillCurrent,
      certificationsList,
      educationsList,
      resumeEducation,
      industriesList,
      resumeCurrentIndustry,
      experiencesList,
      resumes,
      resumeLocation
    } = state;
    let resume = {
      AvailablityType: AvailablityType,
      IsAvailable: IsAvailable,
      Regions: [],
      ResumeCandidateCertifications: [],
      ResumeCandidateEducations: [],
      ResumeCandidateIndustries: [],
      ResumeCandidateKeywords: [],
      ResumeCandidateLanguages: [],
      ResumeCandidateProfiles: [],
      ResumeCandidateSkills: [],
      ResumeCertifications: [],
      ResumeEducations: [],
      ResumeExperiences: [],
      ResumeId: resumes.ResumeId,
      ResumeIndustries: [],
      ResumeKeywords: [],
      ResumeLanguages: [{}],
      ResumeOtherAchivenment: [],
      ResumeProfiles: [],
      ResumeSkills: []
    };
    setState(st => ({ ...st, isLoading: true }));
    if (props.AvailablityType === 3) {
      resume = {
        ...resume,
        AvailabilityDate: moment(busyUntil)
      };
    }
    let locationList = state.locationList;
    if (resumeLocation.CountryId > 0) {
      locationList = [];
      locationList.push(resumeLocation);
    }

    const locationListNew = state.locationList.map(item => ({
      ...item,
      isInvalid: item.CountryId === 0 ? true : item.isInvalid
    }));
    setState(st => ({ ...st, locationList: locationListNew }));

    if (resumeLanguage.LanguageId > 0) {
      languageList.push(resumeLanguage);
    }

    const languageListNew = state.languageList.map(item => ({
      ...item,
      isInvalid:
        item.LanguageId === 0 && !item.isCandidate ? true : item.isInvalid
    }));
    setState(st => ({ ...st, languageList: languageListNew }));

    if (currentRole.ProfileId > 0) {
      rolesList.push(currentRole);
    }
    const roleListNew = state.rolesList.map(item => ({
      ...item,
      isInvalid:
        item.ProfileId === 0 && !item.isCandidate ? true : item.isInvalid
    }));
    setState(st => ({ ...st, rolesList: roleListNew }));
    if (resumeSkillCurrent.SkillId > 0) {
      skillsList.push(resumeSkillCurrent);
    }
    const skillListNew = state.skillsList.map(item => ({
      ...item,
      isInvalid: item.SkillId === 0 && !item.isCandidate ? true : item.isInvalid
    }));
    setState(st => ({ ...st, skillsList: skillListNew }));
    if (resumeCertificationEdit.CertificationId > 0) {
      certificationsList.push(resumeCertificationEdit);
    }

    const certificationListNew = state.certificationsList.map(item => ({
      ...item,
      isInvalid:
        item.CertificationId === 0 && !item.isCandidate ? true : item.isInvalid
    }));
    setState(st => ({ ...st, certificationsList: certificationListNew }));

    if (currentKeyword.KeywordId > 0) {
      keywordsList.push(currentKeyword);
    }

    const keywordListNew = state.keywordsList.map(item => ({
      ...item,
      isInvalid:
        item.KeywordId === 0 && !item.isCandidate ? true : item.isInvalid
    }));
    setState(st => ({ ...st, keywordsList: keywordListNew }));

    if (resumeCurrentIndustry.IndustryId > 0) {
      industriesList.push(resumeCurrentIndustry);
    }
    const industryListNew = state.industriesList.map(item => ({
      ...item,
      isInvalid:
        item.IndustryId === 0 && !item.isCandidate ? true : item.isInvalid
    }));
    setState(st => ({ ...st, industriesList: industryListNew }));

    if (resumeExperienceEdit.IndustryId > 0) {
      experiencesList.push(resumeExperienceEdit);
    }

    const experienceListNew = state.experiencesList.map(item => ({
      ...item,
      isInvalid: item.CompanyWorked === "" ? true : item.isInvalid,
      isIndustryInvalid:
        item.IndustryId === 0 && !item.isCandidate
          ? true
          : item.isIndustryInvalid,
      isRoleInvalid:
        item.ProfileId === 0 && !item.isCandidate ? true : item.isRoleInvalid
    }));
    setState(st => ({ ...st, experiencesList: experienceListNew }));

    if (otherAchievementForm.AchivenmentId > 0) {
      otherAchivenmentsList.push(otherAchievementForm);
    }
    const otherAcheivementListNew = state.otherAchivenmentsList.map(item => ({
      ...item,
      isInvalid: item.Title === "" ? true : item.isInvalid
    }));
    setState(st => ({
      ...st,
      otherAchivenmentsList: otherAcheivementListNew
    }));

    if (resumeEducation.EducationId > 0) {
      educationsList.push(resumeEducation);
    }
    const educationListNew = state.educationsList.map(item => ({
      ...item,
      isInvalid: item.EducationId === 0 ? true : item.isInvalid
    }));
    //Validation
    if (
      locationListNew.findIndex(x => x.isInvalid) > -1 ||
      languageListNew.findIndex(x => x.isInvalid) > -1 ||
      roleListNew.findIndex(x => x.isInvalid) > -1 ||
      skillListNew.findIndex(x => x.isInvalid) > -1 ||
      certificationListNew.findIndex(x => x.isInvalid) > -1 ||
      educationListNew.findIndex(x => x.isInvalid) > -1 ||
      industryListNew.findIndex(x => x.isInvalid) > -1 ||
      keywordListNew.findIndex(x => x.isInvalid) > -1 ||
      experienceListNew.findIndex(
        x => x.isRoleInvalid || x.isIndustryInvalid || x.isInvalid
      ) > -1 ||
      otherAcheivementListNew.findIndex(x => x.isInvalid) > -1
    ) {
      const info = {
        message: props.labels.RESUME_EDIT_FIX_VALIDATION_MESSAGE,
        status: "error"
      };
      props.notificationAction(info);
      setState(st => ({
        ...st,
        isLoading: false,
        error: true,
        errorMessage: props.labels.RESUME_EDIT_FIX_VALIDATION_MESSAGE
      }));
      return;
    }
    //Validation Ends

    resume.Regions = map(locationList, item => ({
      CountryId: item.CountryId
    }));

    //Language
    let filteredLanguages = filter(languageList, item => {
      if (!item.isCandidate && item.LanguageId !== 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          LanguageId: item.LanguageId
        };
        return newItem;
      }
    });
    resume.ResumeLanguages = map(filteredLanguages, item => ({
      ExperienceLevel: item.ExperienceLevel,
      LanguageId: item.LanguageId
    }));
    filteredLanguages = filter(languageList, item => {
      if (item.isCandidate || item.LanguageId === 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          LanguageId: item.LanguageId,
          LanguageValue: item.LanguageValue
        };
        return newItem;
      }
    });
    {
      filteredLanguages &&
        (resume.ResumeCandidateLanguages = map(filteredLanguages, item => {
          const newItem = {
            ExperienceLevel: item.ExperienceLevel,
            ResumeCandidateLanguageId: "0",
            ResumeCandidateLanguageValue: item.LanguageValue
          };
          return newItem;
        }));
    }
    //////////////
    let filteredProfiles = filter(rolesList, item => {
      if (!item.isCandidate && item.ProfileId !== 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          ProfileId: item.ProfileId
        };
        return newItem;
      }
    });
    resume.ResumeProfiles = map(filteredProfiles, item => ({
      ExperienceLevel: item.ExperienceLevel,
      ProfileId: item.ProfileId
    }));
    filteredProfiles = filter(rolesList, item => {
      if (item.isCandidate || item.ProfileId === 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          ProfileId: item.ProfileId,
          ProfileValue: item.ProfileValue
        };
        return newItem;
      }
    });
    {
      filteredProfiles &&
        (resume.ResumeCandidateProfiles = map(filteredProfiles, item => {
          const newItem = {
            ExperienceLevel: item.ExperienceLevel,
            ResumeCandidateProfileId: "0",
            ResumeCandidateProfileName: item.ProfileValue
          };
          return newItem;
        }));
    }
    //skills
    let filteredSkills = filter(skillsList, item => {
      if (!item.isCandidate && item.SkillId !== 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          SkillId: item.SkillId
        };
        return newItem;
      }
    });
    resume.ResumeSkills = map(filteredSkills, item => ({
      ExperienceLevel: item.ExperienceLevel,
      SkillId: item.SkillId
    }));
    filteredSkills = filter(skillsList, item => {
      if (item.isCandidate || item.SkillId === 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          SkillId: item.SkillId,
          SkillValue: item.SkillValue
        };
        return newItem;
      }
    });
    {
      filteredSkills &&
        (resume.ResumeCandidateSkills = map(filteredSkills, item => {
          const newItem = {
            ExperienceLevel: item.ExperienceLevel,
            ResumeCandidateSkillId: "0",
            ResumeCandidateSkillName: item.SkillValue
          };
          return newItem;
        }));
    }

    ////industry
    let filteredIndustries = filter(industriesList, item => {
      if (!item.isCandidate && item.IndustryId !== 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          IndustryId: item.IndustryId
        };
        return newItem;
      }
    });
    resume.ResumeIndustries = map(filteredIndustries, item => ({
      ExperienceLevel: item.ExperienceLevel,
      IndustryId: item.IndustryId
    }));
    filteredIndustries =
      industriesList && industriesList.length > 0
        ? filter(industriesList, item => {
            if (item.isCandidate || item.IndustryId === 0) {
              const newItem = {
                ExperienceLevel: item.ExperienceLevel,
                IndustryId: item.IndustryId,
                IndustryValue: item.IndustryValue
              };
              return newItem;
            }
          })
        : [];
    {
      filteredIndustries &&
        filteredIndustries.length > 0 &&
        (resume.ResumeCandidateIndustries = map(filteredIndustries, item => {
          const newItem = {
            ExperienceLevel: item.ExperienceLevel,
            ResumeCandidateIndustryId: "0",
            ResumeCandidateIndustryValue: item.IndustryValue
          };
          return newItem;
        }));
    }
    //
    ////certification
    let filteredCertifications = filter(certificationsList, item => {
      if (!item.isCandidate && item.IndustryId !== 0) {
        const newItem = {
          CertificationDate: moment(
            item.CertificationDate,
            "MM/DD/YYYY"
          ).format("YYYY-MM-DDTHH:mm:ss"),
          CertificationId: item.CertificationId
        };
        return newItem;
      }
    });
    resume.ResumeCertifications = map(filteredCertifications, item => ({
      CertificationDate: moment(item.CertificationDate, "MM/DD/YYYY").format(
        "YYYY-MM-DDTHH:mm:ss"
      ),
      CertificationId: item.CertificationId
    }));
    filteredCertifications = filter(certificationsList, item => {
      if (item.isCandidate || item.IndustryId === 0) {
        const newItem = {
          CertificationDate: item.CertificationDate,
          CertificationId: item.CertificationId,
          CertificationValue: item.CertificationValue
        };
        return newItem;
      }
    });
    {
      filteredCertifications &&
        (resume.ResumeCandidateCertifications = map(
          filteredCertifications,
          item => {
            const newItem = {
              CertificationDate: item.CertificationDate,
              ResumeCertificationId: "0",
              ResumeCandidateCertificationValue: item.CertificationValue
            };
            return newItem;
          }
        ));
    }

    //Education

    let filteredEducations = filter(educationsList, item => {
      if (item.EducationId !== 0) {
        const newItem = {
          EducationId: item.EducationId,
          EducationTypeId: item.EducationTypeId,
          EducationYear: Number(item.EducationYear)
        };
        return newItem;
      }
    });
    resume.ResumeEducations = map(filteredEducations, item => ({
      EducationTypeId: item.EducationTypeId,
      EducationYear: Number(item.EducationYear),
      EducationId: item.EducationId
    }));
    filteredEducations = filter(educationsList, item => {
      if (item.isCandidate || item.EducationId === 0) {
        const newItem = {
          ExperienceLevel: item.ExperienceLevel,
          EducationId: item.EducationId,
          EducationValue: item.EducationValue
        };
        return newItem;
      }
    });
    {
      filteredEducations &&
        (resume.ResumeCandidateEducations = map(filteredEducations, item => {
          const newItem = {
            ExperienceLevel: item.ExperienceLevel,
            ResumeCandidateEducationId: "0",
            ResumeCandidateEducationValue: item.EducationValue,
            EducationTypeId: item.EducationTypeId,
            EducationYear: Number(item.EducationYear)
          };
          return newItem;
        }));
    }
    ////
    let filteredKeywords = filter(keywordsList, item => {
      if (!item.isCandidate && item.KeywordId !== 0) {
        const newItem = {
          KeywordId: item.KeywordId
        };
        return newItem;
      }
    });
    resume.ResumeKeywords = map(filteredKeywords, item => ({
      KeywordId: item.KeywordId
    }));
    filteredKeywords = filter(keywordsList, item => {
      if (item.isCandidate || item.KeywordId === 0) {
        const newItem = {
          KeywordId: item.KeywordId,
          KeywordValue: item.KeywordValue
        };
        return newItem;
      }
    });
    {
      filteredKeywords &&
        (resume.ResumeCandidateKeywords = map(filteredKeywords, item => {
          const newItem = {
            ResumeCandidateKeywordId: "0",
            ResumeCandidateKeywordValue: item.KeywordValue
          };
          return newItem;
        }));
    }

    resume.ResumeExperiences = map(experiencesList, item => ({
      CompanyWorked: item.CompanyWorked,
      Description: item.Description,
      EndDate: item.isWorking ? null : item.EndDate,
      IndustryId: item.IndustryId,
      ProfileId: item.ProfileId,
      ResumeExperienceId: item.ResumeExperienceId || "0",
      ResumeId: resumes.ResumeId,
      StartDate: item.StartDate
    }));
    resume.ResumeOtherAchivenment = map(otherAchivenmentsList, item => ({
      AchivenmentId: item.AchivenmentId,
      Description: item.Description,
      ResumeId: resume.ResumeId,
      ResumeOtherAchivenmentId: "" + item.ResumeOtherAchivenmentId || "0",
      Title: item.Title,
      Year: Number(item.Year)
    }));
    saveUpdatedResumeApi({ resume }).then(response => {
      if (response.success) {
        const info = {
          message: props.labels[response.message],
          status: "success",
          className: `${
            response.message == "resumeFirstEditSuccessMessage"
              ? "first-resume-save"
              : "resume-save"
          }`
        };
        props.notificationAction(info);
        getMyResumeDetails();
        getResume();
      }
      setState(st => ({
        ...st,
        isLoading: false
      }));
    });
  };

  // const getSocialMedia = () => {
  //   getSocialMediaApi().then(data => {
  //     if (data.success) {
  //       const socialMediaList = data.items
  //         .filter(
  //           x =>
  //             x.Name == "facebook" ||
  //             x.Name == "twitter" ||
  //             x.Name == "instagram" ||
  //             x.Name == "googleplus"
  //         )
  //         .map(item => ({
  //           ...item,
  //           URL: "",
  //           SocialMediaId: item.Id
  //         }));
  //       setState(st => ({
  //         ...st,
  //         socialMedias: socialMediaList
  //       }));
  //       // getUserSocialMediaLinks();
  //     }
  //   });
  // };

  const getMyResumeDetails = () => {
    getMyResumeDetailsApi()
      .then(res => {
        if (res.success) {
          let trueVlues = Object.values(res.items).reduce(
            (a, item) => a + (item === true ? 1 : 0),
            0
          );
          let falseVlues = Object.values(res.items).reduce(
            (a, item) => a + (item === false ? 1 : 0),
            0
          );
          setState(st => ({
            ...st,
            userResumeDetails: { ...res.items, trueVlues, falseVlues }
          }));
        }
      })
      .catch(err => console.log("Err ", err));
  };

  const handleUserForm = e => {
    const { userFormDetail } = state;

    setState(st => ({
      ...st,
      userFormDetail: {
        ...userFormDetail,
        [e.target.name]: e.target.value
      }
    }));
  };

  const handleEducationChange = (name, e) => {
    const { resumeEducation } = state;
    setState(st => ({
      ...st,
      resumeEducation: {
        ...resumeEducation,
        [name]: e
      }
    }));
  };

  const handleDescriptionChange = e => {
    const { resumeExperienceEdit } = state;
    setState(st => ({
      ...st,
      resumeExperienceEdit: {
        ...resumeExperienceEdit,
        Description: e
      }
    }));
  };

  const handleExperienceChange = (name, value) => {
    const { resumeExperienceEdit } = state;
    if (name === "isEndDate") {
      setState(st => ({
        ...st,
        resumeExperienceEdit: {
          ...resumeExperienceEdit,
          [name]: !value,
          EndDate: null
        }
      }));
      return;
    }

    setState(st => ({
      ...st,
      resumeExperienceEdit: {
        ...resumeExperienceEdit,
        [name]: value
      }
    }));
  };

  const handleStartDate = (date, dateString) => {
    const { resumeExperienceEdit } = state;
    setState(st => ({
      ...st,
      resumeExperienceEdit: {
        ...resumeExperienceEdit,
        StartDate: moment(dateString, "YYYY/MM/DD")
      }
    }));
  };

  const handleEndDate = (date, dateString) => {
    const { resumeExperienceEdit } = state;
    setState(st => ({
      ...st,
      resumeExperienceEdit: {
        ...resumeExperienceEdit,
        EndDate: moment(dateString, "YYYY/MM/DD")
      }
    }));
  };
  const handleLocationChange = (name, e) => {
    const { resumeLocation } = state;
    setState(st => ({
      ...st,
      resumeLocation: { ...resumeLocation, [name]: e }
    }));
  };
  const handleCertificationChange = (name, e) => {
    const { resumeCertificationEdit } = state;
    if (name === "CertificationDate") {
      var newdate = moment(new Date(), "YYYY-MM-DDTHH:mm:ss").year(e);
      setState(st => ({
        ...st,
        resumeCertificationEdit: {
          ...resumeCertificationEdit,
          [name]: newdate
        }
      }));
      return;
    }
    setState(st => ({
      ...st,
      resumeCertificationEdit: { ...resumeCertificationEdit, [name]: e }
    }));
  };
  const handleLanguageChange = (name, e) => {
    const { resumeLanguage } = state;
    setState(st => ({
      ...st,
      resumeLanguage: { ...resumeLanguage, [name]: e }
    }));
  };

  const handleSkillCHange = (name, e) => {
    const { resumeSkillCurrent } = state;
    setState(st => ({
      ...st,
      resumeSkillCurrent: { ...resumeSkillCurrent, [name]: e }
    }));
  };

  const handleRoleChange = (name, e) => {
    const { currentRole } = state;
    setState(st => ({
      ...st,
      currentRole: { ...currentRole, [name]: e }
    }));
  };

  const handleOtherAchievementChange = (name, e) => {
    const { otherAchievementForm } = state;
    setState(st => ({
      ...st,
      otherAchievementForm: { ...otherAchievementForm, [name]: e }
    }));
  };

  const handleIndustryChange = (name, e) => {
    const { resumeCurrentIndustry } = state;
    setState(st => ({
      ...st,
      resumeCurrentIndustry: { ...resumeCurrentIndustry, [name]: e }
    }));
  };
  const handleKeywordChange = (name, e) => {
    const { currentKeyword } = state;
    setState(st => ({
      ...st,
      currentKeyword: { ...currentKeyword, [name]: e }
    }));
  };

  const handleSocialMediaChange = (selectedItem, value) => {
    const { socialLinks } = state;
    const SocialLinks = socialLinks.map(item => ({
      ...item,
      URL: selectedItem.Id === item.Id ? value : item.URL
    }));
    setState(st => ({
      ...st,
      socialLinks: SocialLinks
    }));
  };

  const mergeSocialMediaList = UserSocialMediaLinks => {
    const { socialMedias } = state;
    var socialLinks = map(socialMedias, function(item) {
      return {
        ...item,
        ...find(UserSocialMediaLinks, ["SocialMediaId", item.Id])
      };
    });
    setState(st => ({ ...st, socialLinks: socialLinks }));

    handleSocialMediaActive(socialLinks[0]);
  };

  const handleSocialMediaActive = activeItem => {
    const { socialLinks } = state;
    const SocialLinks = socialLinks.map(item => ({
      ...item,
      isActive: item.SocialMediaId == activeItem.SocialMediaId ? true : false
    }));
    setState(st => ({
      ...st,
      socialLinks: SocialLinks
    }));
  };

  const handleShowPhllip = () => {
    // if (!props?.User?.IsFreelancer) {
    //   props.onStateChangeActions({ show: "large" });
    // } else {
    setState(st => ({ ...st, showPhillip: true }));
    // }
  };
  const handleIproPhillipSkip = () => {
    const { IsFreelancer } = props.User;
    updatePhillipPopupStatusApi({ isFreelancer: IsFreelancer }).then(
      response => {
        if (response.success) {
          const info = {
            message: props.labels.iProOnboardingSkipButtonInfo,
            status: "success"
          };
          props.notificationAction(info);
        }
      }
    );
    setState(st => ({ ...st, showPhillip: false, firstTimeClosed: true }));
  };
  const reloadResumeWidgetHandler = () => {
    setState(st => ({
      ...st,
      isReloadResumeWidget: !st.isReloadResumeWidget
    }));
  };

  const handleClosePhillip = () => {
    const { IsFreelancer } = props.User;
    updatePhillipPopupStatusApi({
      isFreelancer: IsFreelancer
    }).then(response => {});
    setState(st => ({ ...st, showPhillip: false, firstTimeClosed: true }));
  };

  const handleUpdateResumeShortlistWidgetWidget = () => {
    setState(st => ({
      ...st,
      isReloadResumeShortlistWidget: !st.isReloadResumeShortlistWidget
    }));
  };

  const handleUpdateCompanyPresentationsWidget = () => {
    setState(st => ({
      ...st,
      isReloadCompanyPresentationsWidget: !st.isReloadCompanyPresentationsWidget
    }));
  };

  // const getUserSocialMediaLinks = () => {
  //   getUserSocialMediaLinksApi().then(data => {
  //     if (data.success) {
  //       setState(st => ({
  //         ...st,
  //         UserSocialMediaLinks: data.items
  //       }));
  //       mergeSocialMediaList(data.items);
  //     }
  //   });
  // };

  const getUserDetails = () => {
    setState(st => ({ ...st, fetchingUserDetails: true }));
    const { labels, User, history } = props;

    getUserDataForDashboard(!User.IsFreelancer)
      .then(res => {
        if (res.success) {
          var userDetailsItem = !res.items
            ? { ...state.userDetails }
            : {
                ...res.items,
                isPhoneAvailable: res.items.UserPhonenumberId ? true : false,
                isAvailable: true
              };
          setState(st => ({
            ...st,
            fetchingUserDetails: false,
            userFormDetail: userDetailsItem,
            userDetails: res.items
          }));
        }
      })
      .catch(err => console.log("Err ", err));
  };

  const updateAvatar = image => {
    const { userFormDetail } = state;

    setState(st => ({
      ...st,
      userFormDetail: {
        ...userFormDetail,
        ProfilePicture: image
      }
    }));
  };

  const handleAvatarChange = e => {
    if (e) {
      loadImageOrientation(e, updateAvatar);
    }
  };

  const {
    socialLinks,
    userFormDetail,
    userDetails = {},
    isLoading,
    errorMessage,
    fetchingUserDetails,
    showPhillip,
    linkedInClientId,
    facebookClientId,
    userResumeDetails,
    resumeExperienceEdit,
    resumeCurrentIndustry,
    otherAchievementForm,
    currentKeyword,
    currentRole,
    resumes,
    UserDetailsWidget,
    IProProfileProgress,
    PhilipsIpro,
    PresentationIpro,
    ResumeIpro,
    CompanySearcher,
    PhilipsSearcher,
    ResumeShortlists,
    SearcherResources,
    resumeEducation,
    resumeLocation,
    resumeCertificationEdit,
    resumeLanguage,
    resumeSkillCurrent
  } = state;
  const { labels, User, history } = props;
  return (
    <div className={" container-fluid new-dashboard"}>
      {isLoading && (
        <div id="loader-wrapper">
          <div className="loader-container">
            <div className="loader">
              <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
                <defs>
                  <filter id="goo">
                    <feGaussianBlur
                      in="SourceGraphic"
                      stdDeviation="6"
                      result="blur"
                    />
                    <feColorMatrix
                      in="blur"
                      mode="matrix"
                      values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                      result="goo"
                    />
                    <feBlend in="SourceGraphic" in2="goo" />
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </div>
      )}

      {showPhillip && User.IsFreelancer && (
        <IproPhillip
          open={showPhillip}
          onSkip={handleIproPhillipSkip}
          onCloseModal={handleClosePhillip}
          facebookClientId={facebookClientId}
          linkedInClientId={linkedInClientId}
          onResumeUpdate={reloadResumeWidgetHandler}
        />
      )}

      {showPhillip && !User.IsFreelancer && (
        <SearcherPhillip
          open={showPhillip}
          onSkip={handleIproPhillipSkip}
          onCloseModal={handleClosePhillip}
          facebookClientId={facebookClientId}
          linkedInClientId={linkedInClientId}
          onCompanyPresentationsUpdate={handleUpdateCompanyPresentationsWidget}
          onResumeShortlistUpdate={handleUpdateResumeShortlistWidgetWidget}
        />
      )}

      <div className={"firstContainer row"}>
        {/* <div className="first-container-child-warper col-md-6">
                        {fetchingUserDetails ? (
                            <LoadingMaskRow />
                        ) : (
                            UserDetailsWidget && (
                                <ProfileWidget
                                    handleAvatarChange={handleAvatarChange}
                                    errorMessage={errorMessage}
                                    userFormDetail={userFormDetail}
                                    socialLinks={socialLinks}
                                    handleUserForm={handleUserForm}
                                    SaveUserDetails={SaveUserDetails}
                                    handleSocialMediaActive={handleSocialMediaActive}
                                    handleSocialMediaChange={handleSocialMediaChange}
                                    isLoading={isLoading}
                                    userDetails={userDetails}
                                    Avatar={Avatar}
                                    labels={labels}
                                />
                            )
                        )}
                        {User.IsFreelancer
                            ? PresentationIpro && <PresentationWidget labels={labels} />
                            : CompanySearcher && <UserCompanyWidget labels={labels} />}
                    </div> */}
        {/* <div className="first-container-child-warper col-md-6">
                        {(PhilipsIpro || PhilipsSearcher) && (
                            <div className="do-for-you">
                                <div>
                                    <Title className="mb-0" level={4}>
                                        {labels.WHAT_CAN_PHILIP_DO}
                                    </Title>
                                    <Text className="text-muted">
                                        {labels.PHILLIP_AI_DETAIL_INFO}
                                    </Text>
                                    <CButton
                                        whiteBackground
                                        className="mt-2 button-blick-effect"
                                        title="Getting Started"
                                        onclick={handleShowPhllip}
                                    />
                                    <img className="waves-image" src="assets/images/waves.svg" />
                                </div>
                            </div>
                        )}

                        {User && User.IsFreelancer && ResumeIpro && (
                            <ResumeWidget
                                resumes={resumes}
                                labels={labels}
                                userDetails={userDetails}
                            />
                        )}

                        {User && !User.IsFreelancer && ResumeShortlists && (
                            <ShortlistWidget labels={labels} />
                        )}
                    </div> */}
      </div>

      {User.IsFreelancer
        ? IProProfileProgress && (
            <>
              <div className="main-dashboard">
                <div className="cards-new">
                  <Row gutter={[0, 12]}>
                    <Col xs={24}>
                      <div className="flex profile items-center gap-4">
                        <div>
                          <Title
                            level={3}
                            style={{ marginBottom: 0, whiteSpace: "pre" }}
                          >
                            Profile Completion
                          </Title>
                        </div>
                        <div className="flex w-full flex-fill items-center gap-4">
                          <Progress
                            strokeColor="#8F82F5"
                            className="progress-container"
                            style={{ flex: 1, height: "12px" }}
                            percent={userResumeDetails.Score}
                            size="small"
                            showInfo={false}
                          />
                          <Text
                            style={{ color: "#8E81F5", fontWeight: 600 }}
                            className="fnt-s-16"
                          >
                            {userResumeDetails.Score}%
                          </Text>
                        </div>
                      </div>
                    </Col>
                  </Row>
                  <div className="flex gap-2 mt-3 flex-wrap items-center">
                    {userResumeDetails.Score < 100 && (
                      <Text className="fnt-s-16">
                        Complete the following steps to boost your profile:
                      </Text>
                    )}

                    {!userResumeDetails.Score ||
                    userResumeDetails.Score < 100 ? (
                      <>
                        {(userResumeDetails.IsProfileEmpty ||
                          userResumeDetails.IsSkillEmpty) && (
                          <MissingRoleSkill
                            handleRoleChange={handleRoleChange}
                            labels={labels}
                            currentRole={currentRole}
                          />
                        )}

                        {userResumeDetails.IsLoctionEmpty && (
                          <EditLocationPopUp
                            handleLocationChange={handleLocationChange}
                            labels={labels}
                            resumeLocation={resumeLocation}
                            SaveResumeDetails={handleResumeSave}
                          />
                        )}

                        {userResumeDetails.IsLanguageEmpty && (
                          <EditLanguagePopUp
                            handleLanguageChange={handleLanguageChange}
                            labels={labels}
                            resumeLanguage={resumeLanguage}
                            SaveResumeDetails={handleResumeSave}
                          />
                        )}

                        {userResumeDetails.IsProfileEmpty && (
                          <RolePopup
                            handleRoleChange={handleRoleChange}
                            labels={labels}
                            currentRole={currentRole}
                            SaveResumeDetails={handleResumeSave}
                          />
                        )}

                        {userResumeDetails.IsSkillEmpty && (
                          <EditSkillPopup
                            handleSkillCHange={handleSkillCHange}
                            labels={labels}
                            resumeSkillCurrent={resumeSkillCurrent}
                            SaveResumeDetails={handleResumeSave}
                          />
                        )}

                        {userResumeDetails.IsKeywordEmpty && (
                          <KeywordPopup
                            handleKeywordChange={handleKeywordChange}
                            labels={labels}
                            currentKeyword={currentKeyword}
                            SaveResumeDetails={handleResumeSave}
                          />
                        )}

                        {userResumeDetails.IsCertificationEmpty && (
                          <EditCertificationPopup
                            handleCertificationChange={
                              handleCertificationChange
                            }
                            labels={labels}
                            resumeCertificationEdit={resumeCertificationEdit}
                            SaveResumeDetails={handleResumeSave}
                          />
                        )}

                        {userResumeDetails.IsEducationEmpty && (
                          <EditEducationPopUp
                            handleEducationChange={handleEducationChange}
                            labels={labels}
                            resumeEducation={resumeEducation}
                            SaveResumeDetails={handleResumeSave}
                          />
                        )}

                        {userResumeDetails.IsIndustryEmpty && (
                          <IndustryPopup
                            handleIndustryChange={handleIndustryChange}
                            labels={labels}
                            resumeCurrentIndustry={resumeCurrentIndustry}
                            SaveResumeDetails={handleResumeSave}
                          />
                        )}

                        {userResumeDetails.IsExperienceEmpty && (
                          <ExperiencePopup
                            handleExperienceChange={handleExperienceChange}
                            handleDescriptionChange={handleDescriptionChange}
                            handleEndDate={handleEndDate}
                            handleStartDate={handleStartDate}
                            labels={labels}
                            resumeExperienceEdit={resumeExperienceEdit}
                            SaveResumeDetails={handleResumeSave}
                          />
                        )}

                        {userResumeDetails.IsOtherAchievementEmpty && (
                          <OtherAchievementPopup
                            handleOtherAchievementChange={
                              handleOtherAchievementChange
                            }
                            handleDescriptionChange={handleDescriptionChange}
                            labels={labels}
                            otherAchievementForm={otherAchievementForm}
                            SaveResumeDetails={handleResumeSave}
                          />
                        )}
                      </>
                    ) : (
                      <Text className="fnt-s-16">
                        {labels?.ipro_dashboard_profile_complete}
                      </Text>
                    )}
                  </div>
                </div>
                <Row
                  gutter={[
                    { xs: 10, md: 16 },
                    { xs: 10, md: 16 }
                  ]}
                >
                  <Col
                    xs={24}
                    md={16}
                    className="gutter-row !flex flex-col gap-4"
                  >
                    <PresentationWidget labels={labels} history={history} />
                    <div className="cards-new">
                      <div className="flex gap-4 what-phillips">
                        <div className="flex-1">
                          <Title level={3}>Hey! How Can I Help You?</Title>
                          <Text className="fnt-s-16">
                            Meet Phillip, the ProDoo AI a premium service
                            offered by ProDoo. Phillip is your ultimate
                            solution, handling the entire process of finding
                            your perfect gig and keeping you informed with
                            real-time progress updates along the way.
                          </Text>
                          <button
                            className="button-getting-started position-abs"
                            onClick={handleShowPhllip}
                          >
                            Getting Started <img src={ArrowRight} alt="" />
                          </button>
                        </div>
                        <div className="flex-1" style={{ textAlign: "right" }}>
                          <img
                            className="img-rebort"
                            src={RebortIcon}
                            alt="rebort"
                          />
                        </div>
                      </div>
                    </div>
                  </Col>
                  <Col md={8} xs={24} className="gutter-row">
                    <ResumeWidget
                      resumes={resumes}
                      labels={labels}
                      userDetails={userDetails}
                      history={history}
                    />
                  </Col>
                </Row>
              </div>
            </>
          )
        : SearcherResources && (
            <div className="main-dashboard">
              <div className="cards-new">
                <Row gutter={[0, 12]}>
                  <Col xs={24}>
                    <Title level={3}>Resources</Title>
                  </Col>
                </Row>
                <Row
                  gutter={[
                    { xs: 10, md: 16 },
                    { xs: 10, md: 16 }
                  ]}
                >
                  <Col className="gutter-row" xs={24} md={8}>
                    <div
                      className="inner-card pointer"
                      onClick={() => {
                        navigate("/resume-search");
                      }}
                    >
                      <div>
                        <img src={SearchNormalIcon} alt="search" />
                      </div>
                      <div>
                        <Title level={5}>Find Resources</Title>
                        <Text>
                          Find the perfect match from thousands of candidates.
                        </Text>
                      </div>
                    </div>
                  </Col>
                  <Col className="gutter-row" xs={24} md={8}>
                    <div
                      className="inner-card pointer"
                      onClick={() => {
                        navigate("/create-opportunity");
                      }}
                    >
                      <div>
                        <img src={RankingIcon} alt="search" />
                      </div>
                      <div>
                        <Title level={5}>Create an Opportunity</Title>
                        <Text>
                          Define an appealing possibility to attract top talent.
                        </Text>
                      </div>
                    </div>
                  </Col>
                  <Col className="gutter-row" xs={24} md={8}>
                    <div
                      className="inner-card pointer"
                      onClick={() => {
                        navigate("/create-collaboration");
                      }}
                    >
                      <div>
                        <img src={QuickIcon} alt="search" />
                      </div>
                      <div>
                        <Title level={5}>Quick Collaboration</Title>
                        <Text>
                          Start working together in a simple and initiative
                          manner.{" "}
                        </Text>
                      </div>
                    </div>
                  </Col>
                </Row>
              </div>
              <Row
                gutter={[
                  { xs: 12, sm: 12, lg: 16, md: 16 },
                  { xs: 12, sm: 12, lg: 16, md: 16 }
                ]}
              >
                <Col
                  xs={24}
                  md={16}
                  className="gutter-row !flex flex-col gap-4"
                >
                  <UserCompanyWidget labels={labels} history={history} />
                  <div className="cards-new">
                    <div className="flex gap-4 what-phillips">
                      <div>
                        <Title level={3}>What can Philip do for you?</Title>
                        <Text className="fnt-s-16">
                          Meet Phillip, the ProDoo AI—a premium service offered
                          by ProDoo. Phillip is your ultimate solution, handling
                          the entire process of finding your perfect gig and
                          keeping you informed with real-time progress updates
                          along the way.
                        </Text>
                        <button
                          className="button-getting-started position-abs"
                          onClick={handleShowPhllip}
                        >
                          Getting Started <img src={ArrowRight} alt="" />
                        </button>
                      </div>
                      <div style={{ textAlign: "right" }}>
                        <img
                          className="img-rebort"
                          src={RebortIcon}
                          alt="rebort"
                        />
                      </div>
                    </div>
                  </div>
                </Col>
                <Col md={8} xs={24} className="gutter-row">
                  <ShortlistWidget labels={labels} history={history} />
                </Col>
              </Row>
            </div>
          )}
    </div>
  );
};

const mapStateToProps = ({ systemLabel, userInfo }) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  const User = user ? user : StorageService.getUser();
  return { labels, User };
};
const actions = {
  notificationAction,
  onStateChangeActions
};
export default connect(mapStateToProps, actions)(NewDashboard);
