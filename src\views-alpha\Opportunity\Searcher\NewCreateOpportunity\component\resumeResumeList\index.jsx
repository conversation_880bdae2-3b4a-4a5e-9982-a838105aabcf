import { useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import { <PERSON><PERSON>, Button } from "antd";
import { join } from "lodash";
import EnvironmentOutlined from "../../../../../../assets-alpha/images/svg/location.svg?react";
import RankingIcon from "../../../../../../assets-alpha/images/svg/ranking.svg?react";
import ProfileIcon from "../../../../../../assets-alpha/images/svg/profile.svg?react";
import LoadingMaskRow from "../../../../../../common/LoadingMask/LoadingMaskRow";
import DeleteIcon from "../../../../../../assets-alpha/images/svg/delete.svg?react";
import ArrowLeft from "../../../../../../assets-alpha/images/svg/arrow-left.svg?react";
import AvatarIcon from "../../../../../../assets-alpha/images/svg/avatar-white.svg";
import CancelIcon from "../../../../../../assets-alpha/images/svg/close-modal.svg?react";
import ArrowDown from "../../../../../../assets-alpha/images/svg/arrow-down-icon.svg";
import { onStateChangeAction } from "../../../createNewOpportunityAction";

import SingleResume from "../SingleResume";
import { useDispatch, useSelector } from "react-redux";

const ResumeListComponentNewDesign = ({
  resumeList,
  state,
  fetchMoreResumes,
  handleModalClick,
  onOpportunityUpdate,
  onOpportunitySend,
  setCreateJobInvitation,
  setEditModalOpen,
  setEditInvitation,
  isEditable,
  hideActionBtns,
  setViewResumeActive,
  DeleteResumeFromShortlist,
  resetFormState
}) => {
  const dispatch = useDispatch();
  const [hideBtns, setHideBtns] = useState(
    hideActionBtns ? hideActionBtns : "false"
  );
  const [resumeSelected, setResumeSelected] = useState([]);
  const [previewResumeDetail, setPreviewResumeDetail] = useState(false);
  const renderValue = resume => {
    const ProfileValue = resume.map(item => item.ProfileValue);
    return join(ProfileValue, ", ");
  };
  const labels = useSelector(state => state.systemLabel.labels);
  const filteredResume = resumeList?.filter(item => {
    return item?.ResumeId === resumeSelected;
  });
  return (
    <>
      {isEditable && (
        <div className="relative w-full md:w-[497px] h-[64px] md:hidden block">
          <div className="fixed md:w-[497px] w-full h-[64px] border-b border-[#EAE5FC] z-[999] bg-white rounded-tl-2xl rounded-tr-2xl">
            <div className="flex w-full h-[64px] justify-between items-center border-b border-[#EAE5FC]">
              <div className="flex md:justify-start justify-between w-full items-center gap-4 pl-4 pr-4">
                <img
                  src={ArrowDown}
                  alt=""
                  onClick={() => {
                    // setCreateJobInvitation(false);
                    {
                      window?.innerWidth < 767 && setViewResumeActive(false);
                    }
                  }}
                  className="pointer order-2 md:order-1"
                />
                <h1 className="!m-0 flex-1 order-1 md:order-2 !text-[16px] !font-semibold md:!text-2xl md:font-medium">
                  {labels?.Shorlist_Resume_Label}
                </h1>
              </div>
            </div>
          </div>
        </div>
      )}
      <div
        className={`md:ml-6 ${isEditable ? "md:mr-0" : "md:mr-6"} ml-4 mr-4`}
      >
        <div
          className={`flex flex-col h-auto w-full md:mb-0 mb-[0px] 
        `}
        >
          {previewResumeDetail && filteredResume ? (
            <div className="flex gap-4 mt-3">
              <ArrowLeft
                className="cursor-pointer"
                onClick={() => {
                  setPreviewResumeDetail(false);
                }}
              />
              <h1 className="!m-0 !text-[20px] !font-semibold !text-[#343333] !leading-[100%] mt-2">
                {labels?.ViewTitleResumeDetails}
              </h1>
            </div>
          ) : (
            <div>
              {isEditable ? (
                <div className="hidden md:flex w-full items-center justify-between ">
                  <div>
                    {isEditable ? (
                      <h1 className="hidden md:block !m-0 !text-[20px] !font-semibold !text-[#343333] !leading-[100%] mt-2">
                        {labels?.Shorlist_Resume_Label}
                      </h1>
                    ) : (
                      ""
                    )}
                  </div>
                  <div>
                    <CancelIcon
                      className="cursor-pointer"
                      onClick={() => {
                        dispatch(onStateChangeAction(resetFormState()));
                        setCreateJobInvitation(false);
                        setEditModalOpen(false);

                        dispatch(
                          onStateChangeAction({
                            shortlistResumes: null,
                            selectedResume: null,
                            isFetchingShortlistResume: false
                          })
                        );
                        {
                          window?.innerWidth < 767 &&
                            setViewResumeActive(false);
                        }
                      }}
                    />
                  </div>
                </div>
              ) : (
                ""
              )}
            </div>
          )}
          {previewResumeDetail && filteredResume ? (
            <SingleResume
              labels={labels}
              filteredResume={filteredResume}
              setPreviewResumeDetail={setPreviewResumeDetail}
              isEditable={isEditable}
            />
          ) : (
            <div>
              {/* {isEditable ? (
                <h1 className="hidden md:block !m-0 !text-[20px] !font-semibold !text-[#343333] !leading-[100%] mt-2">
                  {labels?.Shorlist_Resume_Label}
                </h1>
              ) : (
                ""
              )} */}
              <div
                className={`bg-white h-full overflow-y-auto ${
                  isEditable
                    ? "h-full md:!h-[653px] mb-[70px] md:mb-0"
                    : "!h-full !mb-6"
                }  [scrollbar-width:'none'] [-ms-overflow-style:'none'] [&::-webkit-scrollbar]:hidden mt-3 }`}
              >
                {resumeList?.length > 0 && (
                  <InfiniteScroll
                    dataLength={resumeList?.length}
                    hasMore={state?.hasMore}
                    next={fetchMoreResumes}
                    loadMore={fetchMoreResumes}
                    loader={<LoadingMaskRow />}
                    scrollThreshold="100px"
                    className="flex flex-col w-full gap-3 "
                    height={"100%"}
                  >
                    {resumeList?.map((single, id) => (
                      <div
                        key={id}
                        className=" relative pointer !p-4 flex bg-[#F3F1FD] rounded-[10px] h-auto"
                        onClick={() => {
                          setResumeSelected(single?.ResumeId);
                          // setPreviewResumeDetail is used to show detail of shortlisted Resume
                          setPreviewResumeDetail(true);
                        }}
                      >
                        <div>
                          <div className="flex flex-col gap-4 md:gap-3">
                            <div className="flex !h-auto md:h-[48px] gap-3">
                              <Avatar
                                className="min-w-[52px] !bg-white rounded-[50%]"
                                size={52}
                                src={AvatarIcon}
                              />
                              <div className="flex flex-col w-full h-auto">
                                <h1 className="order-1 md:block hidden !m-0 font-semibold !text-base text-[#2F2F2F]">
                                  {(() => {
                                    const text = (single?.Profiles || [])
                                      .map(item => item?.ProfileValue)
                                      .join(", ");
                                    const trimmed =
                                      text.length > 30
                                        ? text
                                            .slice(0, 30)
                                            .replace(/,\s*$/, "") + "..."
                                        : text;
                                    return trimmed;
                                  })()}
                                </h1>
                                <div className="order-2 md:order-1 flex flex-wrap flex-col h-auto md:flex-row gap-2 justify-center md:justify-start md:items-center md:mt-[9px] ">
                                  <p className="flex gap-1 items-center">
                                    <ProfileIcon className="w-[16px] h-auto text-[#878787]" />
                                    <span className="text-[#878787] !text-sm font-normal">
                                      {single?.UserFirstName}
                                    </span>
                                  </p>
                                  <p className="flex gap-1 items-center">
                                    <EnvironmentOutlined className="w-4 min-w-4 text-[#8E81F5]" />
                                    <span className="text-[#8E81F5] !text-sm font-normal">
                                      {single?.Region}
                                    </span>
                                  </p>
                                  <p className="flex gap-1 items-center">
                                    <RankingIcon className="text-[#34C759]" />
                                    <span className="text-[#34C759] !text-sm font-normal">
                                      {`Match Score (${
                                        single?.matchScore
                                          ? single?.matchScore
                                          : "0"
                                      }) `}
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                            <h1 className="block md:hidden !m-0 font-semibold !text-base text-[#2F2F2F]">
                              {(() => {
                                const text = (single?.Profiles || [])
                                  .map(item => item?.ProfileValue)
                                  .join(", ");
                                const trimmed =
                                  text.length > 30
                                    ? text.slice(0, 30).replace(/,\s*$/, "") +
                                      "..."
                                    : text;
                                return trimmed;
                              })()}
                            </h1>
                            {isEditable ? (
                              <div className="absolute top-[16px] right-[16px] w-6">
                                <DeleteIcon
                                  onClick={e => {
                                    e.stopPropagation();
                                    DeleteResumeFromShortlist({
                                      resumeIds: [single?.ResumeId]
                                    });
                                  }}
                                />
                              </div>
                            ) : (
                              ""
                            )}
                          </div>

                          <div
                            onClick={() => {
                              handleModalClick(single);
                            }}
                          >
                            <div className="flex flex-wrap w-full  gap-2 md:mt-6 mt-[14px]">
                              {single?.Skills?.slice(0, 3)?.map(
                                (row, index) => (
                                  <button className="h-8 !w-auto pl-4 pr-4 bg-white border border-[#EAE5FC] rounded-lg">
                                    <span className="!text-[#343333] text-xs font-normal leading-[100%]">
                                      {row?.SkillValue}
                                    </span>
                                  </button>
                                )
                              )}
                              {single?.Skills?.length > 3 && (
                                <button className="h-8 !w-auto pl-4 pr-4 bg-white border border-[#EAE5FC] rounded-lg">
                                  <span className="!text-[#343333] text-xs font-normal leading-[100%]">
                                    {`+ ${single?.Skills?.length - 3} more`}
                                  </span>
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </InfiniteScroll>
                )}
              </div>
            </div>
          )}
        </div>

        {isEditable ? (
          <div className="flex w-full max-w-full h-[64px] bg-white fixed md:relative bottom-0 justify-center items-center  md:border-none border-t-[0.5px] border-[#EAE5FC] -ml-4 md:ml-0">
            {resumeList?.length > 0 && (
              <div className="flex w-full justify-end gap-2 md:mt-6 mt-0 pl-4 pr-4 md:pl-0 md:pr-0">
                <Button
                  type="default"
                  className="w-full md:w-[112px] !h-10 md:h-9 !text-sm"
                  onClick={onOpportunityUpdate}
                >
                  {labels?.save_draft_oppportunity || "Save as draft"}
                </Button>
                <Button
                  type="primary"
                  className="w-full md:w-[132px] !h-10 md:h-9 !text-sm"
                  onClick={onOpportunitySend}
                >
                  {labels?.send_opportunity || "Send invitations"}
                </Button>
              </div>
            )}
          </div>
        ) : (
          ""
        )}
      </div>
    </>
  );
};

export default ResumeListComponentNewDesign;
