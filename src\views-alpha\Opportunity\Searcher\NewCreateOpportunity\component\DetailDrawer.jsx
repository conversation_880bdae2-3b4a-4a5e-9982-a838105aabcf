import NewOpportunityDetail from "../../common/NewOpportunityDetail";
const DetailDrawer = ({ showDetail }) => {
  return (
    <div>
      {showDetail ? (
        <>
          <div className="flex items-center justify-between w-full gap-2 ">
            <div
              className="flex text-clamp items-center gap-2 pointer pointer"
              onClick={() => {
                setState(prevState => ({
                  ...prevState,
                  isResumeDetail: false
                }));
              }}
            >
              <div>
                <img style={{ height: "16px" }} src={ArrowLeft} alt="" />
              </div>
              <h1 level={5} className="!m-0 text-clamp">
                {`Selected Resumes (${shortlistResumes?.length || 0})`}
              </h1>
            </div>
          </div>
          {isResumeDetail ? (
            ""
          ) : (
            <>
              {selectedOpportunity?.RequestName ? (
                <NewOpportunityDetail
                  declinedRequests={declinedRequests}
                  acceptedRequests={acceptedRequests}
                  pendingRequests={pendingRequests}
                  selectedOpportunity={selectedOpportunity}
                  labels={labels}
                  onAcceptedResumes={handleAcceptedResumes}
                  onDeclinedResumes={handleDeclinedResumes}
                  onNoActionResumes={handleNoActionResumes}
                  handleListOpenMobile={handleListOpenMobile}
                  selectedShortlists={selectedShortlists}
                  selectedAction={selectedAction}
                  status={state?.status}
                  handleStatusChange={handleStatus}
                  onSelectedActionChange={({ selectedAction }) =>
                    dispatch(
                      onStateChangeAction({
                        selectedAction
                      })
                    )
                  }
                />
              ) : (
                ""
              )}
            </>
          )}
        </>
      ) : (
        ""
      )}
    </div>
  );
};

export default DetailDrawer;
