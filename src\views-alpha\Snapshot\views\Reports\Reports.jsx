import moment from "moment";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { notificationAction } from "../../../../actions/notification";
import CustomSelect from "../../../../common-alpha/Select/Select";
import {
  getAllTimeSheetsApi,
  GetWeekAllExpenseLogs,
  GetWeekAllTimeLogs,
  UpdateExpenseSheetStatusApi,
  UpdateTimeSheetStatusApi
} from "../../snapshotApi";
import ExpenseDetail from "./components/ExpenseDetail";
import ExpenseList from "./components/ExpenseList";
import TimelogList from "./components/TimelogList";
import TimelogDetail from "./components/TimestampDetail";
import EmptyWorkplacePreview from "../../../../assets-alpha/images/view/empty-workplace-preview.svg?react";
import { twMerge } from "tailwind-merge";
import clsx from "clsx";
import EmptyView from "../../../../common-alpha/EmptyView/EmptyView";

const Reports = props => {
  const dispatch = useDispatch();
  const labels = useSelector(state => state.systemLabel.labels);
  const isHelpActive = useSelector(state => state.navigation.isHelpActive);

  const { token, Currencies = [], ExpenseCategories = [] } = props;

  const [options] = useState([
    { label: labels.pendingForApproval, value: 1 },
    { label: labels.CollaborationsApproved, value: 2 },
    { label: labels.collaborationsRejected, value: 3 }
  ]);
  const [allTimeSheets, setAllTimeSheets] = useState([]);
  const [weekTimelogsList, setWeekTimelogsList] = useState([]);
  const [weekTimelog, setWeekTimelog] = useState(null);
  const [weekExpenselogsList, setWeekExpenselogsList] = useState([]);
  const [currentExpenselog, setCurrentExpenselog] = useState({});
  const [currentTimeReport, setCurrentTimeReport] = useState({
    StatusId: 1,
    isEmpty: true,
    Amount: "",
    Time: ""
  });
  const [currentTimelog, setCurrentTimelog] = useState({});
  const [TimeSheetStatusId, setTimeSheetStatusId] = useState(2);
  const [ExpenseSheetStatusId, setExpenseSheetStatusId] = useState(2);
  const [isLoading, setIsLoading] = useState(false);
  const [TimelogIds, setTimelogIds] = useState({});
  const [ExpenseIds, setExpenseIds] = useState({});
  const [StatusId, setStatusId] = useState("");
  const [selectedOption, setSelectedOption] = useState({
    value: 1,
    label: labels.pendingForApproval
  });
  useEffect(() => {
    getAllTimeSheetsApi(selectedOption.value, token).then(res => {
      if (res.success) {
        setAllTimeSheets(res.items || []);
        // if (res.items && res.items.length > 0) {
        //   handleTimeReportClick(res.items[0]);
        // }
      }
    });
     
  }, [token, selectedOption?.value]);

  const handleOptionChange = option => {
    setSelectedOption(option);
  };

  const renderOptionClass = option => {
    if (!option) return "";
    switch (option.value) {
      case 1:
        return "yellow";
      case 2:
        return "green";
      case 3:
        return "red";
      default:
        return "";
    }
  };

  function handleTimeReportClick(item) {
    if (!item) {
      setWeekTimelogsList([]);
      setWeekTimelog(null);
      setCurrentTimeReport({ isEmpty: true });
      setCurrentTimelog({});
      return;
    }

    if (item.WeeklyTimeSheetId > 0) {
      GetWeekAllTimeLogs(
        item.CollaborationId,
        moment(item.StartDate).format("DD/MM/YYYY"),
        moment(item.EndDate).format("DD/MM/YYYY"),
        token
      ).then(res => {
        if (res.success) {
          const weekList = res.items.TimeLogs.map(a => ({
            ...a,
            Currency: !isNaN(a.Currency)
              ? `${Currencies.find(b => b.CurrencyId == a.Currency).Name}`
              : a.Currency
          }));
          setTimelogIds(res.items.TimeLogs.map(id => id.TimeLogId));
          setWeekTimelogsList(weekList);
          setWeekTimelog(res.items);
          setStatusId(res.items.StatusId);
          setAllTimeSheets(prev =>
            prev.map(a =>
              a.WeeklyTimeSheetId == item.WeeklyTimeSheetId
                ? { ...a, isSelected: true, isActive: true }
                : { ...a, isSelected: false, isActive: false }
            )
          );
          setCurrentTimeReport({
            ...item,
            Amount: !isNaN(res.items.Amount.split(" ")[1])
              ? `${res.items.Amount.split(" ")[0]} ${
                  Currencies.find(
                    b => b.CurrencyId == res.items.Amount.split(" ")[1]
                  ).Name
                }`
              : res.items.Amount,
            Time: res.items.Time
          });
          setTimeSheetStatusId(res.items.StatusId);
          // if (res.items.TimeLogs && res.items.TimeLogs.length > 0) {
          //   handleTimelogItemClick(res.items.TimeLogs[0]);
          // }
        }
      });
    } else {
      GetWeekAllExpenseLogs(
        item.CollaborationId,
        moment(item.StartDate).format("DD/MM/YYYY"),
        moment(item.EndDate).format("DD/MM/YYYY"),
        token
      ).then(res => {
        if (res.success) {
          setExpenseIds(res.items.ExpenseLogs.map(e => e.ExpenseId));
          setWeekExpenselogsList(
            res.items.ExpenseLogs.map(ex => ({
              ...ex,
              CategoryName: ExpenseCategories.find(
                a => a.ExpenseCategoryId == ex.ExpenseCategoryId
              ),
              CurrencyName: Currencies.find(a => a.CurrencyId == ex.CurrencyId)
            }))
          );
          setCurrentTimeReport({ ...item, Amount: res.items.Amount, Time: "" });
          setAllTimeSheets(prev =>
            prev.map(a =>
              a.ExpenseSheetId == item.ExpenseSheetId
                ? { ...a, isSelected: true, isActive: true }
                : { ...a, isSelected: false, isActive: false }
            )
          );
          setExpenseSheetStatusId(res.items.StatusId);
          // if (res.items.ExpenseLogs && res.items.ExpenseLogs.length > 0) {
          //   handleExpenselogItemClick(res.items.ExpenseLogs[0]);
          // }
        }
      });
    }
  }

  function handleTimelogItemClick(item) {
    if (!item) {
      setCurrentTimelog({});
      return;
    }
    setCurrentTimelog({
      ...item,
      StartTime: `${item.StartTime.split(":")[0]}:${
        item.StartTime.split(":")[1]
      }`,
      EndTime: `${item.EndTime.split(":")[0]}:${item.EndTime.split(":")[1]}`,
      Screenshots: item.Screenshots || []
    });
    setWeekTimelogsList(prev =>
      prev.map(a =>
        a.TimeLogId == item.TimeLogId
          ? { ...a, isSelected: true, isActive: true }
          : { ...a, isSelected: false, isActive: false }
      )
    );
  }

  function handleExpenselogItemClick(item) {
    if (!item) {
      setCurrentExpenselog({});
      return;
    }
    setCurrentExpenselog(item);
    // setWeekExpenselogsList(prev =>
    //   prev.map(a =>
    //     a.ExpenseId == item.ExpenseId
    //       ? { ...a, isSelected: true, isActive: true }
    //       : { ...a, isSelected: false, isActive: false }
    //   )
    // );
  }

  const updateTimeSheetStatus = status => {
    setIsLoading(true);
    UpdateTimeSheetStatusApi(
      currentTimeReport.WeeklyTimeSheetId,
      status,
      TimelogIds,
      token
    )
      .then(res => {
        if (res.success) {
          setAllTimeSheets(prev =>
            prev.filter(
              a => a.WeeklyTimeSheetId != currentTimeReport.WeeklyTimeSheetId
            )
          );
          setTimeSheetStatusId(status);
          setCurrentTimeReport({ isEmpty: true });
          setWeekTimelogsList([]);
          setWeekTimelog(null);
          setCurrentTimelog({});
          setIsLoading(false);
          const info = {
            status: "success",
            message:
              status == 3
                ? labels.timesheetRejectedMsg
                : labels.timesheetApprovedMsg
          };
          dispatch(notificationAction(info));
        } else {
          setIsLoading(false);
          const info = { status: "error", message: res.message };
          dispatch(notificationAction(info));
        }
      })
      .catch(Err => {
        setIsLoading(false);
        console.log("Err ", Err);
      });
  };

  const updateExpenseSheetStatus = status => {
    setIsLoading(true);
    UpdateExpenseSheetStatusApi(
      currentTimeReport.ExpenseSheetId,
      status,
      ExpenseIds,
      token
    )
      .then(res => {
        if (res.success) {
          setAllTimeSheets(prev =>
            prev.filter(
              a => a.ExpenseSheetId != currentTimeReport.ExpenseSheetId
            )
          );
          setExpenseSheetStatusId(status);
          setCurrentTimeReport({ isEmpty: true });
          setCurrentExpenselog({});
          // clear other lists
          setWeekTimelogsList([]);
          setWeekTimelog(null);
          setCurrentTimelog({});
          setIsLoading(false);
          const info = {
            status: "success",
            message:
              status == 3
                ? labels.expensesheetRejectmsg
                : labels.expenseSheetApprovedMsg
          };
          dispatch(notificationAction(info));
        } else {
          setIsLoading(true);
          const info = { status: "error", message: res.message };
          dispatch(notificationAction(info));
        }
      })
      .catch(Err => {
        setIsLoading(false);
        console.log("Err ", Err);
      });
  };
  const handleTimeLogClose = () => {
    handleTimelogDetailClose();
    setWeekTimelog(null);

    setCurrentTimeReport({
      StatusId: 1,
      isEmpty: true,
      ExpenseSheetId: 0
    });
  };
  const handleTimelogDetailClose = () => {
    setCurrentTimelog({});
  };
  return (
    <div className="flex">
      <div className="flex-1/3 max-xl:flex-2/3 h-[calc(100vh-272px)] max-md:h-auto overflow-y-auto border-r-1 border-r-[#C6C6C6] pr-4 max-md:pr-0 max-md:border-r-0">
        <CustomSelect
          value={selectedOption?.value}
          placeholder={labels.presentPrimaryRolePlaceholder}
          onChange={handleOptionChange}
          options={options}
        />

        <List className="mt-3">
          {!allTimeSheets?.length && (
            <div className="text-[var(--gray-3)] text-sm mt-4">
              No reports found
            </div>
          )}
          {allTimeSheets?.map(item => (
            <ListItem
              key={item.ExpenseSheetId || item.WeeklyTimeSheetId}
              isSelected={
                (currentTimeReport?.ExpenseSheetId > 0 &&
                  currentTimeReport?.ExpenseSheetId == item.ExpenseSheetId) ||
                (currentTimeReport?.WeeklyTimeSheetId > 0 &&
                  currentTimeReport?.WeeklyTimeSheetId ==
                    item.WeeklyTimeSheetId)
              }
              onClick={() => handleTimeReportClick(item)}
              text={item.IProName}
              subText={
                <>
                  {`${moment(item?.StartDate).format("MMM D")}-${moment(
                    item?.EndDate
                  ).format("D")}, ${moment(item?.StartDate).format(
                    "YYYY"
                  )} • Week ${moment(item?.StartDate).format("W")}`}
                </>
              }
              rightClassName={
                item.ExpenseSheetId > 0
                  ? "bg-[#E6FAF6] text-[#02CAA8]"
                  : "bg-[#F3F1FD] text-[#8E81F5]"
              }
              right={item.ExpenseSheetId > 0 ? "Expenses" : "Timesheet"}
            ></ListItem>
          ))}
        </List>
      </div>

      <div className="flex-2/3 h-[calc(100vh-272px)] overflow-y-auto max-md:hidden">
        <EmptyView
          icon={<EmptyWorkplacePreview />}
          title={"Nothing selected yet"}
          noBorder
          detail={
            "Click on an item from the list on the left to see full details and take action."
          }
        />
      </div>
      <div className="">
        {weekTimelog && (
          <TimelogList
            timelogList={weekTimelogsList}
            onItemClick={handleTimelogItemClick}
            handleAddNewTimelog={() => {}}
            TimeSheetStatusId={TimeSheetStatusId}
            updateTimeSheetStatus={updateTimeSheetStatus}
            isHelpActive={isHelpActive}
            weekTimelog={weekTimelog}
            onClose={handleTimeLogClose}
            currentTimeReport={currentTimeReport}
          />
        )}
      </div>

      {currentTimelog?.TimeLogId && (
        <TimelogDetail
          currentTimelog={currentTimelog}
          onClose={handleTimelogDetailClose}
          snapshotDate={labels.snapshotDate}
          StartTimeLabel={labels.snapshotStartTime}
          EndTimeLabel={labels.snapshotEndtime}
          DurationLabel={labels.snapshotDuration}
          AmountLabel={labels.snapshotAmount}
          MemoLabel={labels.snapshotMemo}
          CommentLabel={labels.snapshotComment}
          Dateformatelabel={labels.snapshotDateFormate}
          ReviewedBylabel={labels.ReviewedBy_Label}
          ReviewedDatelabel={labels.ReviewedDate_Label}
          Status={StatusId}
          labels={labels}
          Screenshots={currentTimelog.Screenshots || []}
        />
      )}

      {currentTimeReport.ExpenseSheetId > 0 && (
        <ExpenseList
          currentTimeReport={currentTimeReport}
          labels={labels}
          expenseList={weekExpenselogsList}
          onItemClick={handleExpenselogItemClick}
          handleExpenseApprove={() => {}}
          ExpenseSheetStatusId={ExpenseSheetStatusId}
          updateExpenseSheetStatus={updateExpenseSheetStatus}
          isHelpActive={isHelpActive}
          onClose={() => {
            setCurrentTimeReport({
              StatusId: 1,
              isEmpty: true,
              ExpenseSheetId: 0
            });
            setCurrentExpenselog({});
          }}
          currentExpenselog={currentExpenselog}
        />
      )}

      {currentExpenselog?.ExpenseId > 0 && (
        <ExpenseDetail
          snapshotExpenseDate={labels.snapshotExpenseDate}
          Dateformatelabel={labels.snapshotDateFormate}
          CategoryLabel={labels.snapshot_category_label}
          AmountLabel={labels.snapshotAmount}
          CurrencyLabel={labels.snapshotCurrencyLabel}
          DetailsLabel={labels.snapshotDetailsLabel}
          CommentLabel={labels.snapshotComment}
          ReviewedBylabel={labels.ReviewedBy_Label}
          ReviewedDatelabel={labels.ReviewedDate_Label}
          AttachmentsLabel={labels.snapshotAttachmentsLabel}
          currentExpense={currentExpenselog}
          currenciesList={Currencies}
          Status={StatusId}
          expenseCategoriesList={ExpenseCategories}
          onClose={() => {
            setCurrentExpenselog({});
          }}
        />
      )}
    </div>
  );
};

export default Reports;

export const ListItem = ({
  key,
  isSelected,
  onClick,
  text,
  subText,
  right,
  children,
  className,
  detail,
  rightClassName
}) => {
  return (
    <li
      key={key}
      onClick={onClick}
      className={twMerge(
        clsx(
          "border-1 border-[var(--light-gray)] cursor-pointer p-3 rounded-[12px] flex flex-col",
          {
            "bg-[var(--light-purple)] border-[var(--light-purple)]": isSelected
          },
          className
        )
      )}
    >
      <div className="flex items-center justify-between">
        <div>
          <div className="text-[var(--dark)] text-[13px]">{text}</div>
          <div className="flex gap-1 text-[var(--gray-3)] text-xs items-center">
            {subText}
            {children}
          </div>
        </div>
        <div
          className={twMerge(
            "flex items-center bg-[var(--light-purple)] text-[var(--purple)] rounded-full px-2.5 py-1.5",
            rightClassName
          )}
        >
          {right}
        </div>
      </div>
      {detail && <div className="text-[var(--gray-3)] text-xs">{detail}</div>}
    </li>
  );
};

export const List = ({ children, className }) => {
  return (
    <div className={twMerge("flex flex-col gap-1.5", className)}>
      {children}
    </div>
  );
};
