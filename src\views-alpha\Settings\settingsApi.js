import axios from "axios";
import { ApiUrl } from "../../api/apiUrls";
import { StorageService } from "../../../src/api/storage";
import { useClientMutation, useClientQuery } from "../../api-alpha/api-service";
import { useMemo } from "react";
import { UserNotificationList } from "./components/UserNotifications";
import { useSelector } from "react-redux";

export const useGetUserProfileApi = () => {
  return useClientQuery({
    url: ApiUrl.Settings.GetUserProfile
  });
};
export const useCountriesApi = () => {
  const query = useClientQuery({ url: ApiUrl.Country.AllLocationsLookup });
  const queryApi = useMemo(() => {
    if (!query.data) return query;
    return {
      ...query,
      data: query.data?.items.map(item => ({
        ...item,
        value: item.CountryId,
        label: item.CountryName
      }))
    };
  }, [query]);

  return queryApi;
};

export const useUpdateUserProfileApi = () => {
  return useClientMutation({ url: ApiUrl.Settings.UpdateUserProfile });
};

export const useUserPhoneNumberApi = () => {
  return useClientQuery({ url: ApiUrl.Settings.GetUserPhoneNumbers });
};

export const useUserEmailAddressApi = () => {
  return useClientQuery({ url: ApiUrl.Settings.GetUserEmailAddress });
};

export const useUserProfileSettingApi = () => {
  return useClientMutation({
    url: ApiUrl.Settings.UpdateUserSettings
  });
};

export const useUpdatePhoneNumberSettingApi = () => {
  return useClientMutation({ url: ApiUrl.Settings.UpdatePhoneSettings });
};

export const updateEmailSettingApi = fileData => {
  return axios
    .post(ApiUrl.Settings.UpdateEmailSettings, fileData)
    .then(({ data }) => {
      return data;
    });
};

export const useSaveSocialMediaLink = () => {
  return useClientMutation({ url: ApiUrl.Settings.SaveSocialMediaLink });
};

export const deleteUserEmailApi = ({ emailId }) => {
  return axios
    .delete(ApiUrl.Settings.DeleteUserEmail({ emailId }))
    .then(({ data }) => data)
    .catch(response => response);
};

export const deleteUserPhoneApi = ({ phoneId }) => {
  return axios
    .delete(ApiUrl.Settings.DeleteUserPhoneNumber({ phoneId }))
    .then(({ data }) => data)
    .catch(response => response);
};

export const useChangePasswordApi = () => {
  return useClientMutation({ url: ApiUrl.Settings.ChangePassword });
};

export const logOutApi = () => {
  return axios.get(ApiUrl.Settings.Logout).then(({ data }) => {
    StorageService.clearAll();
    return data;
  });
};

export const useUserNotificationsApi = () => {
  const query = useClientQuery({ url: ApiUrl.Settings.UserNotifications });
  const user = useSelector(state => state.userInfo.user);

  const notifications = useMemo(() => {
    if (!query.data) return [];
    return UserNotificationList.filter(
      item =>
        (!item.IsFreelancer || user.IsFreelancer) &&
        (!item.IsSearcher || !user.IsFreelancer)
    ).map(item => ({
      ...item,
      checked: query.data.items[item.name]
    }));
  }, [query.data, user?.IsFreelancer]);
  return { ...query, notifications };
};

export const useUpdateUserNotificationsApi = () => {
  return useClientMutation({
    url: ApiUrl.Settings.UserNotificationUpdate,
    method: "PUT"
  });
};

export const useSocialMediaApi = () => {
  const query = useClientQuery({
    url: `${ApiUrl.SocialMedia}?page=1&start=0&limit=25`
  });
  const queryApi = useMemo(() => {
    if (!query.data) return query;
    return {
      ...query,
      data: query.data?.items.map(item => ({
        ...item,
        value: item.Id,
        label: item.Name
      }))
    };
  }, [query]);

  return queryApi;
};

export const GetUserDataForDashboard = () => {
  return axios
    .get(ApiUrl.Settings.GetUserDefaultPresentation)
    .then(({ data }) => {
      return data;
    });
};
