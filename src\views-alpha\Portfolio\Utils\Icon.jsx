export const BlurBgPortfolio = () => {
  return (
    <>
      <svg
        width="376"
        height="100"
        viewBox="0 0 376 100"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient
            id="paint0_linear_7899_46911"
            x1="188"
            y1="0"
            x2="188"
            y2="98.8902"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopOpacity="0" />
            <stop offset="1" />
          </linearGradient>
        </defs>

        <rect
          width="376"
          height="98.8902"
          transform="translate(0 0.945312)"
          fill="url(#paint0_linear_7899_46911)"
          fillOpacity="0.8"
        />
      </svg>
    </>
  );
};
