import { Form, Typography, DatePicker as Date<PERSON>icker<PERSON>ield } from "antd";
import dayjs from "dayjs";
import { twMerge } from "tailwind-merge";
import CalendarIcon from "../../assets-alpha/images/svg/calendar.svg?react";

const { Text } = Typography;

const DatePicker = ({
  name,
  label,
  suffixText,
  rules,
  onChange,
  listField = {},
  suffixInput,
  suffixIcon,
  disabled,
  PrefixIcon,
  prefixIconClass,
  placeholder,
  className,
  onBlur,
  onFocus,
  onKeyDown,
  value,
  allowClear,
  layout,
  onClear,
  format,
  inputClassName,
  suffixIconClassName,
  colon
}) => {
  const prefixIconDefaultClass = twMerge(
    "text-[var(--gray-3)] h-full !flex items-center w-[20px] justify-center",
    prefixIconClass
  );

  return (
    <Form.Item
      className={twMerge(
        "!mb-0 [&_.ant-picker]:!w-full [&_.ant-form-item-label]:!pb-[6px]",
        className
      )}
      label={label}
      name={name}
      rules={rules}
      extra={
        suffixText && (
          <Text className="!text-xs !mt-2 flex !text-[var(--gray-3)]">
            {suffixText}
          </Text>
        )
      }
      colon={colon}
      layout={layout}
      {...listField}
    >
      <DatePickerField
        value={value ? dayjs(value) : null} // Only pass valid date
        onChange={onChange}
        onKeyDown={onKeyDown}
        disabled={disabled}
        className={twMerge("max-md:!h-[40px]", inputClassName)}
        PrefixIcon={
          PrefixIcon && <PrefixIcon className={prefixIconDefaultClass} />
        }
        suffixIcon={
          <CalendarIcon
            className={twMerge("text-[var(--dark)]", suffixIconClassName)}
          />
        }
        format={format}
        suffixInput={suffixInput}
        allowClear={allowClear}
        onClear={onClear}
        onFocus={onFocus}
        onBlur={onBlur}
        placeholder={placeholder}
      />
    </Form.Item>
  );
};

export default DatePicker;
