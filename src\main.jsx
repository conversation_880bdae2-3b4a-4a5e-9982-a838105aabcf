import { createRoot } from "react-dom/client";
import "rc-menu/assets/index.css";
import "tippy.js/themes/light.css";
import "tippy.js/themes/light-border.css";
import "tippy.js/themes/google.css";
import "tippy.js/themes/translucent.css";
import "rc-slider/assets/index.css";
import "react-datepicker/dist/react-datepicker.css";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import "pace-progressbar/themes/blue/pace-theme-minimal.css";
import "pace-progressbar";

import "./assets/sass/style.scss";
import "./main.css";
import "./style.scss";
import App from "./App";

createRoot(document.getElementById("root")).render(<App />);
