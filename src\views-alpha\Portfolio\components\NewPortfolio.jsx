import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import LoadingMask from "../../../common/LoadingMask/LoadingMask";
import ImageUpload from "../../../assets-alpha/images/svg/image-upload.svg";
import EmptyIcon from "../../../assets-alpha/images/view/portfolio-new-emptyscreen.svg?.react";
import DeleteIcon from "../../../assets-alpha/images/svg/delete-portfolio.svg?.react";
import { HelpTooltip } from "../../../common-alpha/Tooltip/Tooltip";
import InformationIcon from "../../../assets-alpha/images/svg/information.svg?react";
import Icon from "../../../common-alpha/Icon/Icon";
import ArrowDown from "../../../assets-alpha/images/svg/arrow-down-1.svg?react";
import {
  But<PERSON>,
  Checkbox,
  Divider,
  Input,
  Select,
  Space,
  Typography,
  Upload
} from "antd";
import { PlusOutlined } from "@ant-design/icons";
import CloseIcon from "../../../assets-alpha/images/svg/close-modal.svg";
import EmptyView from "./EmptyView";
import AddNewPhoneNumber from "../../Workplace/components/addNewNumber";
import AddEmail from "./AddEmail";
const { TextArea } = Input;
const { Title } = Typography;
const CustomInput = ({
  value,
  label,
  placeholder,
  name = "",
  handleChange,
  maxLength = { maxLength },
  className = ""
}) => {
  return (
    <div>
      <p className="!mb-[6px] !text-[13px] md:text-[14px] text-[#878787] leading-[100%]">
        {label}
      </p>
      <Input
        placeholder={placeholder}
        value={value}
        name={name}
        maxLength={maxLength}
        onChange={e => handleChange(e)}
        className={`placeholder:!text-[#878787] placeholder:!text-[12px] md:placeholder:!text-[13px] md:!text-[14px] !text-[12px] !h-11 ${className}`}
      />
    </div>
  );
};
const NewPortfolio = props => {
  const isHelpActive = useSelector(state => state.navigation.isHelpActive);
  const [addNewNumber, setAddNewNumber] = useState(false);
  const [addEmail, setAddEmail] = useState(false);
  const navigate = useNavigate();
  const hideAddNumberModel = () => {
    setAddNewNumber(false);
  };
  const hideAddEmailModal = () => {
    setAddEmail(false);
  };
  const viewRender = () => {
    const {
      presentForm,
      labels,
      profiles,
      onFormFieldChange,
      userEmails,
      userPhonenumbers,
      getUserPhonenumbers,
      onFormSelectChange,
      onSelectBlur,
      onFileChange,
      onFormFieldBlur,
      handleSave,
      setIsModalOpen,
      isEditForm,
      setRefresh
    } = props;

    if (
      (presentForm && presentForm.UserDetailId && profiles?.length >= 1) ||
      isEditForm
    ) {
      const {
        invalidTitle,
        Profiles,
        invalidRole,
        UserEmails,
        WebUrl,
        invalidWebUrl,
        Detail,
        selectedPhoneNumber,
        IsChecked,
        IsDefault
      } = presentForm;
      const uploadProps = {
        beforeUpload: (file, fileList) => {
          const currentCount =
            props.fileList?.filter(f => !f.Delete).length || 0;
          const allowedCount = 10 - currentCount;

          if (allowedCount <= 0) {
            conosle.error("Maximum 10 images can be uploaded.");
            return Upload.LIST_IGNORE;
          }

          const selectedIndex = fileList.findIndex(f => f.uid === file.uid);

          if (selectedIndex >= allowedCount) {
            return Upload.LIST_IGNORE;
          }

          props.onFileChange(file);
          return false;
        },
        maxCount: 10,
        showUploadList: false,
        accept: "image/*",
        multiple: true
      };
      return (
        <div className="flex flex-col gap-7 w-full">
          <AddNewPhoneNumber
            open={addNewNumber}
            setOpen={setAddNewNumber}
            hideAddNumberModel={hideAddNumberModel}
            title={labels?.Add_Phone_Label}
            getNumber={getUserPhonenumbers}
          />
          <AddEmail
            open={addEmail}
            setOpen={setAddEmail}
            hideAddEmailModal={hideAddEmailModal}
            title={labels?.USER_SETTING_DETAILS_ADD_EMAIL}
            getEmails={userEmails}
            setRefresh={setRefresh}
          />
          <div className="h-full flex flex-col">
            <div className="!flex !flex-col !h-full !overflow-x-hidden relative">
              <div className="relative w-full sm:w-[497px] h-[64px] ">
                <div className="fixed sm:w-[497px] w-full h-[64px] border-b border-[#EAE5FC] z-[999] bg-white rounded-tl-2xl rounded-tr-2xl">
                  <div className="flex w-full h-[64px] justify-between items-center p-6">
                    <div className="flex md:justify-start justify-between w-full items-center gap-4 ">
                      <img
                        src={CloseIcon}
                        alt=""
                        onClick={() => {
                          setIsModalOpen(false);
                          props?.setFileList([]);
                        }}
                        className="pointer order-2 md:order-1"
                      />
                      <Title
                        level={3}
                        className="!m-0 flex-1 order-1 md:order-2 !text-[18px] !font-semibold md:!text-2xl md:font-medium"
                      >
                        {presentForm?.UserDetailId != -1
                          ? labels?.IPRO_Edit_Button_Text
                          : labels?.IPRO_Portfolio_Button_Text}
                      </Title>
                    </div>
                    <div className="hidden md:flex order-3">
                      <Button
                        type="primary"
                        className=" w-[57px] !h-9 pl-4 pr-4"
                        onClick={() => {
                          handleSave();
                        }}
                      >
                        {labels?.SearcherOpportunityDraftDetailSave}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-[16px] md:ml-6 md:mr-6 ml-4 mr-4">
                <div className="md:mb-4 mb-3 ">
                  <CustomInput
                    label={labels.collTitle}
                    placeholder={labels?.Write_Title_Label}
                    name="Title"
                    maxLength={100}
                    handleChange={onFormFieldChange}
                    value={presentForm?.Title ? presentForm?.Title : ""}
                    className={`placeholder:!text-[#878787] placeholder:!text-[12px] md:placeholder:!text-[13px] md:!text-[14px] !text-[12px] !h-11 md:!h-11 ${
                      invalidTitle ? "!border !border-red-500" : ""
                    }`}
                  />
                </div>
                <div className="flex md:justify-between md:flex-row flex-col gap-3 md:mb-4 mb-3 ">
                  <div className="w-full !max-w-full md:!max-w-[210px]">
                    <div className="flex justify-between mr-1">
                      <p className="!mb-[6px] md:!text-[14px] !text-[13px]">
                        {labels.ROLE_LABEL}
                      </p>
                      {isHelpActive && (
                        <HelpTooltip
                          content={labels?.IPRO_Portfolio_Role_Tooltip}
                        >
                          <Icon
                            className="!z-10 text-green-500"
                            src={InformationIcon}
                            renderSvg
                          />
                        </HelpTooltip>
                      )}
                    </div>
                    <Select
                      name="Profiles"
                      value={Profiles && Profiles.ProfileValue}
                      placeholder={labels.presentPrimaryRolePlaceholder}
                      onChange={(e, selectedOption) =>
                        onFormSelectChange(
                          "Profiles",
                          "ProfileValue",
                          selectedOption
                        )
                      }
                      onBlur={onSelectBlur}
                      options={profiles}
                      clearable={false}
                      size="large"
                      showSearch
                      showArrow
                      bordered={false}
                      optionFilterProp="children"
                      suffixIcon={<ArrowDown className="w-[20px]" />}
                      filterOption={(input, option) =>
                        option?.props?.label
                          ?.toLowerCase()
                          ?.indexOf(input?.toLowerCase()) >= 0
                      }
                      className={`!w-full md:!max-w-[210px]   !h-11 md:!h-11 !rounded-[99px] !bg-[#F3F1FD] hover:!border[0.5px] ${
                        invalidRole ? "inValid" : ""
                      }`}
                      rootClassName="
                      [&_.ant-select-selection-item]:!pl-[6px]
                       [&_.ant-select-selection-placeholder]:!pl-[6px]
                       [&_.ant-select-selection-search]:!pl-[6px]
                       [&_.ant-select-selection-item]:!max-w-[180px]
                       md:[&_.ant-select-selection-placeholder]:!text-[13px]
                       [&_.ant-select-selection-placeholder]:!text-[12px]
                       [&_.ant-select-selection-placeholder]:!text-[#878787]
                       [&_.ant-select-selector]:!text-[12px]
                       md:[&_.ant-select-selector]:!text-[14px]
                       [&_.ant-select-selector]:!text-[#343333]"
                    ></Select>
                  </div>
                  <div className="w-full">
                    <div>
                      <p className="!mb-[6px] text-[13px] md:text-[14px]">
                        {labels.PHONE_LABEL}
                      </p>
                    </div>
                    <Select
                      name="UserPhonenumbers"
                      value={selectedPhoneNumber && selectedPhoneNumber}
                      placeholder={labels.presentPhoneNumberPlaceholder}
                      onChange={(e, selectedOption) =>
                        onFormSelectChange(
                          "UserPhonenumbers",
                          "UserPhonenumberValue",
                          selectedOption
                        )
                      }
                      autoBlur={true}
                      options={userPhonenumbers}
                      size="large"
                      showSearch
                      showArrow
                      bordered={false}
                      optionFilterProp="children"
                      filterOption={(input, option) =>
                        option?.props?.label
                          ?.toLowerCase()
                          ?.indexOf(input?.toLowerCase()) >= 0
                      }
                      suffixIcon={<ArrowDown className="w-[20px]" />}
                      dropdownRender={menu => (
                        <>
                          {menu}
                          <Divider style={{ margin: "8px 0" }} />
                          <Space
                            className="flex w-full justify-center"
                            style={{ padding: " 0px 4px" }}
                          >
                            <Button
                              type="text"
                              icon={<PlusOutlined />}
                              onClick={() => {
                                setAddNewNumber(true);
                              }}
                            >
                              {labels?.Add_Phonenumber_Label}
                            </Button>
                          </Space>
                        </>
                      )}
                      className="!w-full md:!max-w-[210px]  !h-11 md:!h-11 !rounded-[99px] !bg-[#F3F1FD] hover:!border[0.5px]"
                      rootClassName="
                      [&_.ant-select-selection-item]:!pl-[6px]
                      [&_.ant-select-selection-placeholder]:!pl-[6px]
                      [&_.ant-select-selection-search]:!pl-[6px]
                      md:[&_.ant-select-selection-placeholder]:!text-[13px]
                      [&_.ant-select-selection-placeholder]:!text-[12px]
                      [&_.ant-select-selection-placeholder]:!text-[#878787]
                      md:[&_.ant-select-selector]:!text-[14px]
                      [&_.ant-select-selector]:!text-[12px]
                      [&_.ant-select-selector]:!text-[#343333]"
                    ></Select>
                  </div>
                </div>
                <div className="flex w-full md:flex-row flex-col gap-3 md:mb-4 mb-3 ">
                  <div className="w-full md:max-w-[50%]">
                    <div>
                      <p className="!mb-[6px] text-[13px] md:text-[14px]">
                        {labels.EMAIL_LABEL}
                      </p>
                    </div>
                    <Select
                      name="UserEmails"
                      value={UserEmails && UserEmails.UserEmailValue}
                      placeholder={labels.presentEmailPlaceholder}
                      onChange={(e, selectedOption) =>
                        onFormSelectChange(
                          "UserEmails",
                          "UserEmailValue",
                          selectedOption
                        )
                      }
                      options={userEmails}
                      clearable={false}
                      size="large"
                      showSearch
                      showArrow
                      bordered={false}
                      optionFilterProp="children"
                      filterOption={(input, option) =>
                        option?.props?.label
                          ?.toLowerCase()
                          ?.indexOf(input?.toLowerCase()) >= 0
                      }
                      suffixIcon={<ArrowDown className="w-[20px]" />}
                      dropdownRender={menu => (
                        <>
                          {menu}
                          <Divider style={{ margin: "8px 0" }} />
                          <Space
                            className="flex w-full justify-center"
                            style={{ padding: " 0px 4px" }}
                          >
                            <Button
                              type="text"
                              icon={<PlusOutlined />}
                              onClick={() => {
                                setAddEmail(true);
                              }}
                            >
                              Add Email
                            </Button>
                          </Space>
                        </>
                      )}
                      className="!w-full   !h-11 md:!h-11 !rounded-[99px] !bg-[#F3F1FD] hover:!border[0.5px]"
                      rootClassName="
                      [&_.ant-select-selection-item]:!pl-[6px]
                      [&_.ant-select-selection-placeholder]:!pl-[6px]
                      [&_.ant-select-selection-search]:!pl-[6px]
                      md:[&_.ant-select-selection-placeholder]:!text-[13px]
                      [&_.ant-select-selection-placeholder]:!text-[12px]
                      [&_.ant-select-selection-placeholder]:!text-[#878787]
                      md:[&_.ant-select-selector]:!text-[14px]
                      [&_.ant-select-selector]:!text-[12px]
                      [&_.ant-select-selector]:!text-[#343333]"
                    ></Select>
                  </div>
                  <div className="w-full md:max-w-[50%]">
                    <div>
                      <p className="!mb-[6px] text-[13px] md:text-[14px]">
                        {labels.WEB_URL_LABEL}
                      </p>
                    </div>
                    <Input
                      name="WebUrl"
                      value={WebUrl ? WebUrl : ""}
                      placeholder={labels.presentURLPlaceholder}
                      onChange={onFormFieldChange}
                      onBlur={onFormFieldBlur}
                      testId="input-text-present-weburl"
                      maxLength={"100"}
                      size="large"
                      autoComplete="off"
                      bordered={false}
                      className={`!h-11 !w-full md:!max-w-[210px]  md:!h-11 !bg-[#F3F1FD] md:!text-[14px] !text-[12px] placeholder:!text-[12px] md:placeholder:!text-[13px] placeholder:!text-[#878787] !pl-[16px] !rounded-[99px] ${
                        invalidWebUrl ? "inValid" : ""
                      }`}
                    />
                  </div>
                </div>
                <div className="mb-3">
                  <div>
                    <p className="!mb-[6px] text-[13px] md:text-[14px]">
                      {labels.collDescription}
                    </p>
                    <TextArea
                      name="Detail"
                      value={Detail ? Detail : ""}
                      placeholder={labels.Portfolio_Description_Placeholder}
                      onChange={onFormFieldChange}
                      maxLength={300}
                      data-testid="input-button-company-detail"
                      rows={6}
                      className="!bg-[#F3F1FD] md:!text-[14px] !text-[12px] placeholder:!text-[12px] md:placeholder:!text-[13px] placeholder:!text-[#878787] !rounded-2xl h-[132px] md:!h-[167px] !resize-none !pt-[10px]"
                      bordered={false}
                    />
                  </div>
                </div>
                <div>
                  <div>
                    <Checkbox
                      className="md:text-[14px] text-[13px]"
                      value={IsDefault}
                      name="IsDefault"
                      checked={!!IsDefault}
                      onChange={onFormFieldChange}
                      data-testid="input-checkbox-company-defaultCompany"
                      disabled={IsChecked}
                    >
                      {labels.PresentationCheckboxMakeDefault}
                    </Checkbox>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="h-full w-[calc(100%-32px)] md:w-[calc(100%-48px)] !ml-4 md:!ml-6 flex">
            {props?.fileList?.length < 1 ? (
              <div className="flex !w-full justify-center items-center h-[184px] border border-[#8E81F5] border-dashed rounded-2xl bg-[#F3F1FD]  md:ml-0 md:mr-0 mb-[96px] md:mb-8">
                <div className="flex flex-col justify-center w-[72%]">
                  <label className="flex text-center text-[12px] md:text-[14px]">
                    {labels?.IPRO_ImageUpload_Description}
                  </label>
                  <div className="flex w-full justify-center mt-5">
                    <Upload {...uploadProps} multiple={true}>
                      <Button
                        className="!w-[169px] !h-[37px] !bg-white"
                        type="default"
                      >
                        {labels?.IPRO_Portfolio_Image_Upload_Text}
                      </Button>
                    </Upload>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex md:ml-0 md:mr-0 justify-start gap-[8px] md:gap-[9px] flex-wrap w-full [&>*:last-child]:!mb-[96px] md:[&>*:last-child]:!mb-6">
                {props?.fileList
                  ?.map((file, originalIndex) => ({ file, originalIndex }))
                  ?.filter(({ file }) => !file.Delete)
                  ?.map(({ file, originalIndex }) => {
                    const imageSrc = file?.Image
                      ? file.Image
                      : file instanceof File
                      ? URL.createObjectURL(file)
                      : null;

                    const isThumbnail =
                      file.isThumb ||
                      file.IsThumb ||
                      props.thumbnailIndex === originalIndex;

                    return (
                      <div
                        key={originalIndex}
                        className={`sm:max-w-[210px] sm:w-[210px] sm:h-[210px] w-[160px] h-[160px] rounded-[10px] border border-[#EAE5FC] relative group ${
                          isThumbnail ? "ring-2 ring-[#8E81F5]" : ""
                        }`}
                      >
                        {imageSrc && (
                          <img
                            src={imageSrc}
                            alt={`Uploaded ${originalIndex}`}
                            className="w-full h-full object-cover !rounded-[10px]"
                          />
                        )}

                        {isThumbnail && (
                          <div className="absolute top-2 left-2 bg-[#8E81F5] text-white text-xs px-2 py-1 rounded-md">
                            Thumbnail
                          </div>
                        )}

                        <div className="absolute inset-0 flex items-center justify-center bg-[#00000066] rounded-[10px] opacity-0 group-hover:opacity-100 transition-opacity duration-200 ease-in-out">
                          <div className="flex flex-col gap-4 items-center">
                            {!isThumbnail && (
                              <Button
                                className="w-[143px] h-[42px] transform translate-y-3 group-hover:translate-y-0 transition-transform duration-200 !bg-white hover:!text-[#8E81F5]"
                                type="default"
                                onClick={() =>
                                  props?.setThumbnailIndex(originalIndex)
                                }
                              >
                                {labels?.IPRO_Portfolio_Thumbnail}
                              </Button>
                            )}
                            <div
                              className="flex justify-center pointer items-center w-[40px] h-[40px] rounded-[50%] transform translate-y-3 group-hover:translate-y-0 transition-transform duration-200 !bg-white hover:!text-[#8E81F5]"
                              onClick={() =>
                                props?.handleDeleteImage(originalIndex)
                              }
                            >
                              <img src={DeleteIcon} alt="" />
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}

                {props?.fileList?.filter(file => !file.Delete)?.length < 10 && (
                  <div className="flex justify-center items-center max-w-[249px] md:w-[210px] md:h-[210px] w-[160px] h-[160px] rounded-[10px] border border-[#EAE5FC] border-dashed hover:bg-[#eae5fc33] transition">
                    <Upload
                      {...uploadProps}
                      multiple={true}
                      showUploadList={false}
                    >
                      <img
                        src={ImageUpload}
                        alt="upload image svg"
                        className="pointer"
                      />
                    </Upload>
                  </div>
                )}
              </div>
            )}
          </div>
          <div className="md:hidden flex w-full max-w-full sm:max-w-[497px] h-[64px] bg-white fixed bottom-0 justify-center items-center  border-t-[0.5px] border-[#EAE5FC]">
            <Button
              type="primary"
              className="w-full ml-4 mr-4 !h-10 pl-4 pr-4"
              onClick={() => {
                handleSave();
              }}
            >
              {labels?.save_presentation}
            </Button>
          </div>
        </div>
      );
    }
    return (
      <div className="flex flex-col w-full justify-center items-center">
        <div className="flex md:gap-6 gap-0 justify-between md:justify-start w-full sm:w-[497px] !mt-8 !ml-4 !mr-4">
          <img
            src={CloseIcon}
            alt="close Icon"
            className="pointer md:order-1 order-2 md:!mr-0 !mr-4 !ml-4 md:ml-0"
            onClick={() => setIsModalOpen(false)}
          />
          <h1 className="order-1 md:order-2 !m-0 !text-[#343333] !text-[16px] md:!text-2xl !font-medium md:!ml-0 !ml-4">
            {labels?.New_Portfolio_Label}
          </h1>
        </div>
        <div className="flex w-full h-full justify-center items-center">
          <EmptyView
            icon={<img src={EmptyIcon} alt="Empty" />}
            title={labels?.IPRO_Portfolio_Role_Error}
            description={labels?.IPRO_Portfolio_Role_Error_Description}
            actionText={labels?.IPRO_Portfolio_Navigate_Resume}
            onAction={() => navigate("/resume")}
          />
        </div>
      </div>
    );
  };

  return (
    <div className="company-form-editor">
      {props.isLoading && <LoadingMask />}
      {viewRender()}
    </div>
  );
};

export default NewPortfolio;
