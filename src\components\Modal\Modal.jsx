import { Modal as ModalPopup } from "antd";
import "./modal.scss";

const Title = ({ children }) => <div className="modal-title">{children}</div>;
const Body = ({ children }) => <div className="modal-body">{children}</div>;
const Footer = ({ children }) => <div className="modal-footer">{children}</div>;

const Modal = ({ open, onCloseModal, children, className }) => {
  return (
    <ModalPopup
      wrapClassName="modal-popup-wrapper"
      className={
        `modal-popup !p-0 ${className}`

        //   {
        //   modal: `modal-popup ${className}`,
        //   overlay: "modal-overlay",
        //   closeIcon: "close-icon",
        //   closeButton: "close-button"
        // }
      }
      open={open}
      onClose={onCloseModal}
      // centered
      closable={false}
      width={800}
      footer={false}
    >
      {children}
    </ModalPopup>
  );
};

Modal.defaultProps = {
  className: "",
  onCloseModal: () => {}
};

Modal.Title = Title;
Modal.Body = Body;
Modal.Footer = Footer;

export default Modal;
