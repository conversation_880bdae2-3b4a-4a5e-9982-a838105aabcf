import { Profiles } from "../constant";
import { ShowMoreSection } from "./LanguagesSection";
import { useSelector } from "react-redux";

// Target Regions Section
const TargetRegionsSection = ({ userData, openDrawer }) => {
  const resume = useSelector(state => state.systemLabel.labels?.resume);
  return (
    <ShowMoreSection
      title={resume?.country}
      items={userData?.items?.Regions || []}
      openDrawer={openDrawer}
      addEditAction={Profiles.region}
      itemKeyId="CountryId"
      viewMore
      itemValueKey="CountryName"
      emptyMessage={resume?.targetRegionsEmptyMessage}
      addButtonAction={Profiles.region}
      renderItem={region => (
        <div
          key={region.CountryId}
          className="bg-[#F3F1FD] border border-[#EAE5FC] rounded-lg px-3.5 py-2 text-sm text-[#343333]"
        >
          {region.CountryName}
        </div>
      )}
      leftSection
    />
  );
};
export default TargetRegionsSection;
