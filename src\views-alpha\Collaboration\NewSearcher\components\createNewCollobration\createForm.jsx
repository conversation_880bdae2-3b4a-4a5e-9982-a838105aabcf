import { useEffect } from "react";
import LoadingMask from "../../../../../common/LoadingMask/LoadingMask";
import Slider from "rc-slider";
import moment from "moment";
import CalendarIcon from "../../../../../assets-alpha/images/svg/calendar-icon.svg?react";
import { Input, Select, Button, DatePicker } from "antd";
import ArrowDown from "../../../../../assets-alpha/images/svg/arrow-down-1.svg?react";
import CloseIcon from "../../../../../assets-alpha/images/svg/close-modal.svg";
import { MinusOutlined, PlusOutlined } from "@ant-design/icons";

import { isEmpty } from "lodash";
import { useDispatch } from "react-redux";
import { onStateChangeAction } from "../../../searcher/SearcherCollaborationCreateAction";
import dayjs from "dayjs";
const { TextArea } = Input;
const Handle = Slider.Handle;

const CreateNewCollaborationForm = props => {
  const dispatch = useDispatch();
  const {
    collaborationForm,
    labels,
    onFormFieldChange,
    onFormSelectChange,
    onDateChange,
    onSliderChange,
    handleSliderChange,
    opportunityList,
    iprosList,
    onCollaborationSave,
    Currencies,
    handleListOpenMobile,
    isLoading,
    activeShowMore,
    setActiveShowMore,
    setCreateNewCollaboration,
    selectedIpro,
    showUserDetail,
    setShowUserDetail,
    onClearSelectedIpro,
    jobInviteData,
    selectedCurrency,
    setSelectedCurrency
  } = props;
  const Data = jobInviteData[0];
  useEffect(() => {
    if (!isEmpty(Data)) {
      dispatch(
        onStateChangeAction({
          collaborationForm: {
            ...collaborationForm,
            Title: Data.RequestName || Data.RequestName || "",
            StartDate: Data.StartDate ? moment(Data.StartDate) : null,
            HourlyRate: Data.HourlyRate || "",
            HourlyRateType: Data.HourlyRateType || "",
            Duration: Data.Duration || "",
            DurationType: Data.DurationType || "Months",
            Description: Data.Decription || ""
          }
        })
      );
    }
  }, [Data]);

  useEffect(() => {
    if (!Currencies?.length) return;

    const selectedCurrencyNew =
      Currencies.find(
        item => item.CurrencyId === Number(Data?.HourlyRateType)
      ) || Currencies.find(item => item.Name === "USD");
    setSelectedCurrency(selectedCurrencyNew);
  }, [Data?.HourlyRateType, Currencies]);
  useEffect(() => {
    if (Data?.StartDate) {
      dispatch(
        onStateChangeAction({
          collaborationForm: {
            ...collaborationForm,
            StartDate: Data.StartDate
          }
        })
      );
    }
  }, [Data?.StartDate]);

  const handle = props => {
    const { value, dragging, index, ...restProps } = props;
    return (
      <Handle value={value} key={index} {...restProps}>
        {value}
      </Handle>
    );
  };

  const {
    Title,
    invalidTitle,
    StartDate,
    invalidStartDate,
    DurationType,
    invalidDurationType,
    HourlyRate,
    invalidHourlyRate,
    HourlyRateType,
    invalidHourlyRateType,
    RequestId,
    invalidRequestId,
    ResumeId,
    invalidResumeId,
    Description,
    Duration
  } = collaborationForm;

  const durationTypeList = [
    { value: "Days", label: "Days" },
    { value: "Weeks", label: "Weeks" },
    { value: "Months", label: "Months" },
    { value: "Years", label: "Years" }
  ];
  return (
    <>
      {isLoading && <LoadingMask />}
      <div>
        <div className="flex w-full justify-between items-center md:h-[29px] h-[64px] md:border-none border-b border-[#EAE5FC] pl-4 pr-4 md:pl-0">
          <h1 className="!m-0 md:!text-2xl !text-[18px] text-[#343333] font-semibold leading-[100%]">
            {labels?.searcher_network_connected_btn2_text}
          </h1>
          <div className="flex gap-5">
            {activeShowMore && (
              <Button
                type="link"
                icon={<MinusOutlined />}
                onClick={() => {
                  setActiveShowMore(false);
                }}
              >
                {labels?.Hide_More_Label}
              </Button>
            )}
            <img
              src={CloseIcon}
              alt=""
              onClick={() => {
                setCreateNewCollaboration(false);
                onClearSelectedIpro();
              }}
              className="pointer md:hidden block order-2 md:order-1"
            />
          </div>
        </div>
        <div className="flex flex-col md:!mt-9 !mt-4 mb-4 w-full  md:!p-0 !pl-4 !pr-4">
          <p className="!font-medium !text-[14px] !text-[#878787] !mb-[6px]">
            {labels.Job_invite_label}
          </p>

          <div className="relative w-full">
            <Select
              name="OpprtunityId"
              placeholder={labels?.Select_Invitation_Label}
              options={opportunityList}
              value={collaborationForm.RequestId || null}
              onChange={(value, selectedOption) =>
                onFormSelectChange("OpprtunityId", selectedOption)
              }
              className={`!h-10 md:!h-11 !rounded-[99px] w-full pr-[70px] ${
                invalidRequestId ? "inValid" : ""
              }`}
              rootClassName="
                      [&_.ant-select-selection-item]:!pl-[6px]
                       [&_.ant-select-selection-placeholder]:!pl-[6px]
                       [&_.ant-select-selection-search]:!pl-[6px]
                       [&_.ant-select-selection-item]:!max-w-[180px]
                       md:[&_.ant-select-selection-placeholder]:!text-[13px]
                       [&_.ant-select-selection-placeholder]:!text-[12px]
                       [&_.ant-select-selection-placeholder]:!text-[#878787]
                       [&_.ant-select-selector]:!text-[12px]
                       md:[&_.ant-select-selector]:!text-[14px]
                       [&_.ant-select-selector]:!text-[#343333]"
              showSearch
              optionFilterProp="label"
              suffixIcon={<ArrowDown className="w-[20px]" />}
            />

            {!isEmpty(selectedIpro) && (
              <button
                type="button"
                className="md:hidden absolute right-[34px] top-1/2 -translate-y-1/2 text-[#8E81F5] text-xs font-medium z-10 mr-4"
                onClick={() => {
                  props.setShowUserDetail(true);
                }}
              >
                {labels?.Workplace_View_Label}
              </button>
            )}
          </div>
        </div>
        <div className="flex flex-col mb-4 w-full  md:!p-0 !pl-4 !pr-4">
          <p className="!font-medium !text-[14px] !text-[#878787] !mb-[6px]">
            {labels.colliPro}
          </p>
          <Select
            name="ResumeId"
            placeholder={labels?.Select_Ipro_Label}
            options={iprosList}
            value={collaborationForm.ResumeId || null}
            onChange={(value, selectedOption) =>
              onFormSelectChange("ResumeId", selectedOption)
            }
            className={`!h-10 md:!h-11 !rounded-[99px] w-full ${
              invalidResumeId ? "inValid" : ""
            }`}
            rootClassName="
                      [&_.ant-select-selection-item]:!pl-[6px]
                       [&_.ant-select-selection-placeholder]:!pl-[6px]
                       [&_.ant-select-selection-search]:!pl-[6px]
                       [&_.ant-select-selection-item]:!max-w-[180px]
                       md:[&_.ant-select-selection-placeholder]:!text-[13px]
                       [&_.ant-select-selection-placeholder]:!text-[12px]
                       [&_.ant-select-selection-placeholder]:!text-[#878787]
                       [&_.ant-select-selector]:!text-[12px]
                       md:[&_.ant-select-selector]:!text-[14px]
                       [&_.ant-select-selector]:!text-[#343333]"
            showSearch
            optionFilterProp="label"
            suffixIcon={<ArrowDown className="w-[20px]" />}
          />
        </div>
        {activeShowMore ? (
          <div className="md:!p-0 !pl-4 !pr-4">
            <div className="!mb-4 ">
              <p className="!font-medium !text-[14px] !text-[#878787]  !mb-[6px]">
                {labels.Job_Title_Label}
              </p>
              <Input
                autoFocus={invalidTitle}
                type="text"
                name="Title"
                className={`!bg-[#F3F1FD] !rounded-[99px] md:!text-[14px] !text-[12px] !h-10 md:!h-11 placeholder:!text-[#878787] placeholder:!text-sm placeholder:!font-normal  ${
                  invalidTitle ? "inValid" : ""
                }`}
                value={Data?.label ? Data?.label : Title}
                bordered={false}
                size="large"
                placeholder={labels.collFormTitle}
                onChange={onFormFieldChange}
                onBlur={onFormFieldChange}
                data-testid="input-text-collaboration-title"
              />
            </div>
            <div className="mb-4 ">
              <p className="font-medium text-[14px] text-[#878787] !mb-[6px]">
                {labels.collStartDate}
              </p>
              <DatePicker
                name="StartDate"
                format="DD-MM-YYYY"
                value={
                  collaborationForm?.StartDate
                    ? dayjs(collaborationForm.StartDate)
                    : null
                }
                onChange={date => {
                  const finalDate = date
                    ? date
                        .set("hour", dayjs().hour())
                        .set("minute", dayjs().minute())
                        .set("second", dayjs().second())
                    : null;

                  dispatch(
                    onStateChangeAction({
                      collaborationForm: {
                        ...collaborationForm,
                        StartDate: finalDate
                          ? finalDate.format("YYYY-MM-DD[T]HH:mm:ss")
                          : null,
                        invalidStartDate: !date
                      }
                    })
                  );
                }}
                size="large"
                placeholder={labels.collStartDate}
                className={`
    w-full md:!text-[14px] !text-[12px]
    !bg-[#F3F1FD] !rounded-[99px] !h-10 md:!h-11
    [&_.ant-picker-input>input::placeholder]:!text-[#878787]
    [&_.ant-picker-input>input]:!text-xs
    [&_.ant-picker-input>input::placeholder]:!font-normal
    ${collaborationForm?.invalidStartDate ? "inValid" : ""}
  `}
                suffixIcon={<CalendarIcon className="w-5 h-5" />}
                bordered={false}
                allowClear
              />
            </div>
            <div className="flex gap-2 w-full  mb-4">
              <div className="w-[50%]">
                <p className="!font-medium !text-[14px] !text-[#878787] !mb-[6px]">
                  {labels.collHourlyFee}
                </p>

                <div className="relative w-full">
                  <div className="flex items-center bg-[#F3F1FD] rounded-[99px] h-11">
                    <Input
                      size="large"
                      autoFocus={invalidHourlyRate}
                      type="text"
                      name="HourlyRate"
                      className={`
                        !bg-transparent md:!text-[14px] !rounded-[99px] !text-[12px] !border-none !h-10 md:!h-11 !pr-[85px]
                        placeholder:!text-[#878787] placeholder:!text-sm placeholder:!font-normal
                        ${invalidHourlyRate ? "inValid" : ""}
                      `}
                      value={Data?.HourlyRate ? Data?.HourlyRate : HourlyRate}
                      placeholder={
                        labels?.SearcherOpportunityDraftDetailHourlyFee
                      }
                      onChange={onFormFieldChange}
                      onBlur={onFormFieldChange}
                      data-testid="input-txt-opportunity-hourlyRate"
                      bordered={false}
                    />

                    <div className="absolute right-[2px] h-[40px] top-[2px] w-[70px] flex items-center bg-white rounded-r-[99px] border-l border-l-[#F3F1FD]">
                      <Select
                        bordered={false}
                        size="small"
                        className="w-full !rounded-[99px] !h-full [&_.ant-select-selector]:!h-full [&_.ant-select-selection-item]:!flex [&_.ant-select-selection-item]:!items-center"
                        dropdownStyle={{ fontSize: 12 }}
                        dropdownClassName="!rounded-lg"
                        name="HourlyRateType"
                        defaultValue="USD"
                        // value={
                        //   HourlyRateType
                        //     ? HourlyRateType
                        //     : currdefault?.CurrencyId
                        // }
                        value={selectedCurrency}
                        onChange={(e, selectedOption) =>
                          onFormSelectChange("HourlyRateType", selectedOption)
                        }
                        options={Currencies}
                        suffixIcon={<ArrowDown className="w-[20px]" />}
                        popupMatchSelectWidth={false}
                        rootClassName="
                        [&_.ant-select-selection-item]:!text-[12px]
                        [&_.ant-select-selection-item]:!font-normal
                        "
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="w-[50%]">
                <p className="!font-medium !text-[14px] !text-[#878787] !mb-[6px]">
                  {labels.collDuration}
                </p>
                <div className="relative w-full">
                  <div className="flex items-center bg-[#F3F1FD] rounded-[99px] h-11 group">
                    <Input
                      type="text"
                      name="Duration"
                      className="!bg-transparent md:!text-[14px] !text-[12px] !border-none !h-10 md:!h-11 !pr-[85px]
               placeholder:!text-[#878787] placeholder:!text-sm placeholder:!font-normal"
                      value={Data?.Duration ? Data?.Duration : Duration}
                      placeholder={labels?.collDuration}
                      onChange={onFormFieldChange}
                      onBlur={onFormFieldChange}
                      bordered={false}
                    />

                    <div
                      className="absolute right-[2px] h-[40px] top-[2px] w-[82px] 
                    flex items-center bg-white rounded-r-[99px] border-l border-l-[#F3F1FD]"
                    >
                      <Select
                        name="DurationType"
                        size="small"
                        bordered={false}
                        className="w-[82px]  !h-full [&_.ant-select-selector]:!h-full [&_.ant-select-selection-item]:!flex [&_.ant-select-selection-item]:!items-center"
                        dropdownStyle={{ fontSize: 12 }}
                        dropdownClassName="!rounded-lg"
                        defaultValue="Months"
                        value={
                          Data?.DurationType ? Data?.DurationType : "Months"
                        }
                        onChange={(value, selectedOption) =>
                          onFormSelectChange("DurationType", selectedOption)
                        }
                        options={durationTypeList}
                        suffixIcon={<ArrowDown className="w-[20px]" />}
                        showSearch
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          (option?.label ?? "")
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        rootClassName="
                      [&_.ant-select-selection-item]:!text-[12px]
                      [&_.ant-select-selection-item]:!font-normal
                      "
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-col ">
              <p className="!font-medium !text-[14px] !text-[#878787] !mb-[6px]">
                {labels?.collDescription}
              </p>
              <div>
                <TextArea
                  rows={6}
                  name="Description"
                  placeholder={labels?.collDescription}
                  value={Data?.Decription ? Data?.Decription : Description}
                  onChange={onFormFieldChange}
                  className="!bg-[#F3F1FD] md:!text-[14px] !text-[12px] placeholder:!text-[13px] md:placeholder:!text-[13px] placeholder:!text-[#878787] !rounded-2xl h-[132px] md:!h-[167px] !resize-none !pt-[10px]"
                  bordered={false}
                  readOnly
                />
              </div>
            </div>
            <div className="md:hidden ml-[-16px]  flex w-full  h-[64px] bg-white fixed bottom-0 justify-center items-center  border-t-[0.5px] border-[#EAE5FC]">
              <Button
                type="primary"
                className="w-full  !h-10 ml-4 mr-4"
                onClick={onCollaborationSave}
              >
                {labels.Send_Contract_Label}
              </Button>
            </div>
          </div>
        ) : (
          <Button
            type="link"
            className="!m-0 !h-[17px] !text-sm !font-medium  md:!p-0 !pl-4 !pr-4"
            icon={<PlusOutlined />}
            onClick={() => {
              setActiveShowMore(true);
            }}
          >
            {labels?.Show_More_label}
          </Button>
        )}
      </div>
    </>
  );
};

export default CreateNewCollaborationForm;
