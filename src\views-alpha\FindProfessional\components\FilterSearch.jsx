import { LoadingOutlined } from "@ant-design/icons";
import { Button, List, Typography } from "antd";
import clsx from "clsx";
import { capitalize } from "lodash";
import { useSelector } from "react-redux";

export default function FilterSearch({
  roles,
  onSelect,
  loading,
  name = "",
  selectedRoles = [],
  icon,
  onRemove,
  loadingDelete,
  singleLine
}) {
  const findProfessional = useSelector(
    state => state.systemLabel.labels?.findProfessional
  );
  return (
    <div className="absolute top-full left-0 right-0 z-30 p-6 bg-white rounded-lg shadow-lg lg:ml-[44px] lg:mr-[100px] mx-auto mt-10">
      {loading ? (
        <LoadingOutlined className="text-3xl !block m-auto" />
      ) : (
        <>
          {name && (
            <Typography.Text className="text-gray-500 font-medium">
              {capitalize(name)}
            </Typography.Text>
          )}

          <List
            itemLayout="horizontal"
            dataSource={roles}
            renderItem={item => (
              <List.Item
                className={clsx(
                  `cursor-pointer !flex !flex-nowrap items-center !justify-start gap-2`,
                  {
                    "!bg-[#fafafa]": selectedRoles?.some(
                      role => role.value === item.value
                    )
                  }
                )}
                onClick={() => onSelect(item)}
              >
                {icon}
                <span
                  className={clsx({
                    "overflow-hidden whitespace-nowrap overflow-ellipsis": singleLine
                  })}
                >
                  {item.label}
                </span>
                {onRemove && (
                  <Button
                    type="text"
                    className="!text-[var(--red)] ml-auto !p-0 !px-2 !h-auto"
                    loading={loadingDelete}
                    onClick={e => {
                      e.stopPropagation();
                      onRemove(item);
                    }}
                  >
                    {findProfessional?.delete}
                  </Button>
                )}
              </List.Item>
            )}
          />
        </>
      )}
    </div>
  );
}
