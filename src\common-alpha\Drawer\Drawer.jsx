import { Drawer as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "antd";
import clsx from "clsx";
import { twMerge } from "tailwind-merge";
import CloseModal from "../../assets-alpha/images/svg/close-modal.svg?react";
import { useHistoryStateHandler } from "../../utilities-alpha/hooks";

const CommonDrawer = ({
  children,
  className = "",
  rootClassName = "",
  closable = true,
  onClose,
  open,
  width = 400,
  placement = "right",
  title,
  closeIcon,
  mask = true,
  footer,
  getContainer,
  maskClosable = true,
  keyboard = true,
  zIndex,
  destroyOnClose = false,
  size,
  extra,
  onChange,
  drawerKey = "drawer"
}) => {
  // Common root class styles for the drawer
  const commonRootClass = clsx(
    "[&_.ant-drawer-content-wrapper]:!inset-[8px]",
    "[&_.ant-drawer-content-wrapper]:!left-[auto]",
    "[&_.ant-drawer-content-wrapper]:max-md:!w-full",
    "[&_.ant-drawer-content-wrapper]:max-md:!top-[8px]",
    "[&_.ant-drawer-content-wrapper]:max-md:!bottom-[0px]",
    "[&_.ant-drawer-content-wrapper]:max-md:!inset-x-[0px]"
  );

  // Common class names for the drawer content
  const commonClassName = clsx(
    "[&_.ant-drawer-header]:!pb-5 [&_.ant-drawer-header]:max-md:!pb-3 [&_.ant-drawer-header]:max-md:!px-4",
    "rounded-[16px] rounded-l-[16px] max-md:rounded-b-none max-md:rounded-t-[16px]",
    "[&_.ant-drawer-close]:!rounded-full [&_.ant-drawer-close]:!mr-4 [&_.ant-drawer-close]:max-md:order-1 [&_.ant-drawer-close]:max-md:!mr-0",
    "[&_.ant-drawer-title]:!text-[24px] max-md:[&_.ant-drawer-title]:!text-[18px] [&_.ant-drawer-title]:!font-[500]",
    "[&_.ant-drawer-body]:!flex [&_.ant-drawer-body]:!flex-col",
    "[&_.ant-drawer-body]:!py-5 [&_.ant-drawer-body]:max-md:!p-4 [&_.ant-drawer-body]:max-md:!pb-[65px]"
  );

  // Use the custom hook to handle history state when the drawer opens or closes
  useHistoryStateHandler(!!open, onClose, drawerKey);

  return (
    <AntDrawer
      title={title}
      placement={placement}
      closable={closable}
      onClose={onClose}
      open={open}
      width={width}
      closeIcon={closeIcon ? closeIcon : <CloseModal />} // Custom close icon
      mask={mask}
      rootClassName={twMerge(clsx(commonRootClass, rootClassName))}
      className={twMerge(clsx(commonClassName, className))}
      footer={footer} // Custom footer content
      getContainer={getContainer} // Optional container for the drawer
      maskClosable={maskClosable} // Whether mask click closes the drawer
      keyboard={keyboard} // Enable keyboard control
      zIndex={zIndex} // Drawer z-index
      destroyOnClose={destroyOnClose} // Whether to destroy on close
      size={size} // Drawer size (small, default, large)
      extra={extra} // Extra content in header
      onChange={onChange} // Event for drawer open state change
    >
      {children}
    </AntDrawer>
  );
};
export default CommonDrawer;
