import { Button, Checkbox, Divider, Input, Radio, Select } from "antd";
import { useGoogleLogin } from "@react-oauth/google";
import GoogleIcon from "../../../assets-alpha/images/svg/google-icon.svg?react";
import ArrowLeft from "../../../assets-alpha/images/svg/arrow-left.svg?react";
import ArrowDown from "../../../assets-alpha/images/svg/arrow-down-1.svg?react";
import SMS from "../../../assets-alpha/images/svg/sms-signup.svg?react";

import { useEffect, useState } from "react";
import {
  CheckDuplicateUserApi,
  getAllCountriesApi,
  getEmailConfirmationTokenApi,
  getSignUpConfigsApi,
  RolesLookup,
  signUpApi,
  SkillsLookup
} from "../landingPageApi";
import Password from "antd/es/input/Password";
import { notificationAction } from "../../../actions-alpha/notification";
import { useDispatch, useSelector } from "react-redux";
import { isValidPassword } from "../../../utilities/helpers";
import { useLocation, useNavigate } from "react-router-dom";
import Slider from "./components/Slider";
import { privateRoutes, publicRoutes } from "../../../Routes/routing";
import { loginAuth } from "./loginAction";
import PrivacyPolicy from "../components/PrivacyPolicy/PrivacyPolicy";
import UserAgreement from "../components/UserAgreement/UserAgreement";
import TermsAndServices from "../components/TermsAndServices/TermsAndServices";

const Signup = () => {
  const labels = useSelector(state => state.systemLabel.labels);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const credentials = location?.state?.credentials;
  const [roleSearch, setRoleSearch] = useState("a");
  const [skillSearch, setSkillSearch] = useState("a");
  const [dialogMessage, setDialogMessage] = useState(null);
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [skillOptions, setSkillOptions] = useState([]);
  const [jobRoles, setJobRoles] = useState([]);
  const [countries, setCountries] = useState([]);
  const [termsAndServicesDrawerOpen, setTermsAndServicesDrawerOpen] = useState(
    false
  );
  const [privacyPolicyDrawerOpen, setPrivacyPolicyDrawerOpen] = useState(false);
  const [userAgreementDrawerOpen, setUserAgreementDrawerOpen] = useState(false);

  const [signUpData, setSignUpData] = useState([]);
  const [formData, setFormData] = useState({
    email: credentials?.email,
    firstName: credentials?.given_name || "",
    lastName: credentials?.family_name || "",
    password: credentials?.id || "",
    confirmPassword: credentials?.id || "",
    selectedCountry: null,
    isFreelancer: true,
    primaryJobRole: null,
    skills: [null, null, null],
    agreedToTerms: false
  });

  const loginToCore = (
    email,
    password,
    socialLogin = false,
    googleCredentials = {}
  ) => {
    const loginModel = { email, password };
    setLoading(true);

    dispatch(loginAuth(loginModel)).then(response => {
      if (response.success) {
        const redirectAppPath = privateRoutes.dashboard.path;
        setLoading(false);
        navigate(redirectAppPath);
      } else {
        if (socialLogin && response?.message?.includes("incorrect")) {
          navigate(publicRoutes.signup.path, {
            state: { credentials: googleCredentials }
          });
          return;
        }

        setLoading(false);
        dispatch(
          notificationAction({
            message: response.message || labels?.Login_Failed_Label,
            status: "error"
          })
        );
      }
    });
  };

  const handleGoogleSignIn = useGoogleLogin({
    flow: "implicit",
    onSuccess: tokenResponse => {
      fetch(
        `https://www.googleapis.com/oauth2/v1/userinfo?access_token=${tokenResponse.access_token}`
      )
        .then(res => res.json())
        .then(data => {
          const { email, id, given_name, family_name } = data;

          // First try to login with these credentials
          loginToCore(email, id, true, data);
        });
    },
    onError: () => {
      console.error("Google login failed");
    }
  });

  useEffect(() => {
    getSignUpConfigsData();
    fetchCountries();
    fetchRoles();
    fetchSkills();
  }, []);

  // Handle autofill for password fields
  useEffect(() => {
    const checkAutofill = setInterval(() => {
      const passwordInput = document.querySelector('input[name="password"]');
      const confirmInput = document.querySelector(
        'input[name="confirmPassword"]'
      );

      if (passwordInput?.value && passwordInput.value !== formData.password) {
        handleInputChange({
          target: { name: "password", value: passwordInput.value }
        });
      }

      if (
        confirmInput?.value &&
        confirmInput.value !== formData.confirmPassword
      ) {
        handleInputChange({
          target: { name: "confirmPassword", value: confirmInput.value }
        });
      }
    }, 300);

    return () => clearInterval(checkAutofill);
  }, [formData.password, formData.confirmPassword]);

  const fetchCountries = async () => {
    try {
      const res = await getAllCountriesApi();
      setCountries(res);
    } catch (error) {
      console.log({ error });
    }
  };

  const fetchRoles = async (query = "a") => {
    try {
      const res = await RolesLookup(query);
      if (res?.success) {
        const formattedRoles = res.items.map(role => ({
          label: role.ProfileValue,
          value: role.ProfileId.toString()
        }));
        setJobRoles(formattedRoles);
      }
    } catch (error) {
      console.log({ error });
    }
  };

  const fetchSkills = async (query = "a") => {
    try {
      const res = await SkillsLookup(query);
      if (res?.success) {
        const options = res.items.map(skill => ({
          label: skill.SkillValue,
          value: skill.SkillId.toString()
        }));
        setSkillOptions(options);
      }
    } catch (err) {
      console.error({ err });
    }
  };
  const CheckDuplicateUser = async email => {
    try {
      if (email) {
        setLoading(true);
        const res = await CheckDuplicateUserApi(email);
        if (res?.data?.success && res?.data?.message == "UserExisted") {
          const info = {
            message: labels?.Email_Already_Register_Msg,
            status: "error"
          };
          dispatch(notificationAction(info));
          setLoading(false);
        } else {
          setCurrentStep(prev => prev + 1);
          setLoading(false);
        }
      } else {
        const info = {
          message: labels?.setting.emailRequired,
          status: "error"
        };
        dispatch(notificationAction(info));
      }
    } catch (err) {
      console.log({ err });
    }
  };
  const getSignUpConfigsData = async () => {
    const res = await getSignUpConfigsApi();
    setSignUpData(res?.items);
  };
  const formattedCountries = countries?.items?.map(country => ({
    label: country.CountryName,
    value: country.CountryId
  }));

  const handleInputChange = e => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  const handleRoleSearch = value => {
    setRoleSearch(value || "a");
    fetchRoles(value || "a");
  };
  const handleSkillChange = (index, value, searchQuery) => {
    const newSkills = [...formData.skills];
    newSkills[index] = value;
    setFormData(prev => ({
      ...prev,
      skills: newSkills
    }));

    if (searchQuery !== undefined) {
      fetchSkills(searchQuery || "a");
    }
  };
  const handleCheckboxChange = e => {
    setFormData(prev => ({
      ...prev,
      agreedToTerms: e.target.checked
    }));
  };

  const handleNextStep = () => {
    if (currentStep === 1) {
      CheckDuplicateUser(formData?.email);
    } else {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevStep = () => {
    if (currentStep === 1) {
      navigate("/");
    } else {
      setCurrentStep(prev => prev - 1);
    }
  };

  const cancel = e => {
    setDialogMessage(null);
  };

  const confirm = async () => {
    const res = await getEmailConfirmationTokenApi(formData?.email);
    if (res?.success) {
      const info = {
        message: labels?.Email_Sent_Message,
        status: "success"
      };
      dispatch(notificationAction(info));
    }
  };

  const handleSubmit = async () => {
    try {
      let payload = {
        Email: formData.email,
        CountryId: formData.selectedCountry,
        FirstName: formData.firstName,
        IsFreelancer: formData.isFreelancer,
        LastName: formData.lastName,
        Password: formData.password
      };

      if (formData.isFreelancer) {
        payload = {
          ...payload,
          roleId: formData.primaryJobRole,
          skills: formData.skills.filter(skill => skill !== null)
        };
      }

      const res = await signUpApi(payload);
      if (res?.success) {
        setDialogMessage("Resend verification email");
      }
    } catch (error) {
      dispatch(
        notificationAction({
          message:
            error.response?.data?.message ||
            "An error occurred during registration",
          status: "error"
        })
      );
    }
  };

  const selectStyles = {
    rootClassName: `
      [&_.ant-select-selection-item]:!pl-[6px]
      [&_.ant-select-selection-item]:!flex
      [&_.ant-select-selection-placeholder]:!text-left
      [&_.ant-select-selection-placeholder]:!pl-[6px]
      [&_.ant-select-selection-search]:!pl-[6px]
      md:[&_.ant-select-selection-placeholder]:!text-[13px]
      [&_.ant-select-selection-placeholder]:!text-[12px]
      [&_.ant-select-selection-placeholder]:!text-[#878787]
      [&_.ant-select-selector]:!text-[12px]
      md:[&_.ant-select-selector]:!text-[14px]
      [&_.ant-select-selector]:!text-[#343333]
      [&_.ant-select-selector]:!bg-white
      [&_.ant-select-selector]:!rounded-[12px]
    `
  };

  return (
    <div className="flex md:flex-row flex-col p-3  w-full min-h-[100vh] gap-3 bg-[url('/assets/images/background.webp')] bg-cover bg-no-repeat bg-center">
      {/* Left Side - Slider */}
      <div className="flex md:flex-col h-[56px] md:h-auto flex-row justify-between md:justify-start items-center md:items-start w-full md:w-[56%] md:bg-[#FFFFFF80] md:rounded-2xl md:border-[1.5px] md:border-[#FFFFFF] relative">
        <span
          className="md:ml-8 md:mt-8 cursor-pointer"
          onClick={() => {
            navigate("/");
          }}
        >
          <img src="/assets/images/logo-prodoo.png" alt="prodoo-logo" />
        </span>
        <div className="hidden md:block absolute w-full max-w-[509px] top-[50%] left-[50%] transform -translate-x-1/2 -translate-y-1/2">
          <Slider />
        </div>
        <div className="block md:hidden">
          <p
            onClick={() => {
              navigate("/");
            }}
            className="text-[#8E81F5] font-medium text-sm"
          >
            {labels?.Goback_Label}
          </p>
        </div>
      </div>
      <div className="relative flex justify-center items-start lg:pt-0 pt-[26px] pb-3 lg:items-center w-full lg:w-[44%] bg-[#FFFFFF80] rounded-2xl border-[1.5px] border-[#FFFFFF]">
        {!dialogMessage && (
          <div
            className="hidden md:block absolute left-[24px] top-[24px] cursor-pointer"
            onClick={handlePrevStep}
          >
            <ArrowLeft />
          </div>
        )}
        {dialogMessage ? (
          <div className="flex flex-col items-center w-full px-8">
            <SMS />
            <h1 className="!m-0 !font-semibold !text-[20px] lg:!text-2xl text-[#0C1421]">
              {labels?.Confirm_Email_Message}
            </h1>
            {/* <p className="!m-0 text-[#343333] text-[20px] font-normal mt-8">
              {labels?.Welcome_Prodoo_Message}{" "}
            </p> */}
            <label className="!font-inter !m-0 text-[#878787] text-[13px] lg:!text-[16px] !font-normal !mt-3">
              {labels?.Correct_Email_Verify_Message}
            </label>
            <Button
              type="primary"
              className="lg:!h-12 !h-11 mt-6"
              onClick={() => {
                const domain = formData?.email?.split("@")[1];
                let redirectUrl = "";
                if (domain === "gmail.com")
                  redirectUrl = "https://mail.google.com";
                else if (
                  domain === "outlook.com" ||
                  domain === "hotmail.com" ||
                  domain === "live.com"
                )
                  redirectUrl = "https://outlook.live.com";
                else if (domain === "yahoo.com")
                  redirectUrl = "https://mail.yahoo.com";
                else redirectUrl = `https://${domain}`;
                window.open(redirectUrl, "_blank");
              }}
            >
              {labels?.Confirm_Email_Message}
            </Button>
            <span
              className="cursor-pointer text-[#8E81F5] !text-[16px] font-semibold !mt-5"
              onClick={confirm}
            >
              {labels?.Resend_Email_Message}
            </span>
          </div>
        ) : (
          <div className="flex flex-col justify-start lg:w-[77%]  lg:justify-center px-3 lg:px-0 w-full  h-full relative text-center ">
            <div className="mt-16 lg:mt-0 ">
              <h1 className="!m-0 !font-semibold !text-[20px] lg:!text-2xl text-[#0C1421]">
                {currentStep === 1
                  ? labels?.LANDING_GET_FREE_ACCOUNT_LBL
                  : labels?.LANDING_COMPLETE_FREE_ACCOUNT_LBL}
              </h1>
              <p className="!font-inter text-[#878787] text-[13px] lg:!text-[16px] !font-normal !mt-3">
                {currentStep === 1
                  ? labels?.LANDING_LOGIN_ENER_CRIDS_LBL
                  : formData?.email}
              </p>
            </div>

            {/* Stepper */}
            <div className="flex justify-center gap-5 mt-6 lg:mt-11">
              {[1, 2, 3].map(step => (
                <div
                  key={step}
                  className={`
              flex h-8 lg:h-11 w-full max-w-[140px] justify-center items-start border-b-[3px]
              transition-colors duration-300 ease-in-out
              ${
                currentStep >= step || (step === 3 && currentStep === 3)
                  ? "border-[#8E81F5]"
                  : "border-[#C6C6C6]"
              }
            `}
                >
                  <span
                    className={`
                font-medium text-sm lg:text-[18px]
                transition-colors duration-300 ease-in-out
                ${
                  currentStep >= step || (step === 3 && currentStep === 3)
                    ? "text-[#8E81F5]"
                    : "text-[#C6C6C6]"
                }
              `}
                  >
                    {labels?.Step_Label} {step}
                  </span>
                </div>
              ))}
            </div>

            {/* Step 1 */}
            {currentStep === 1 && (
              <div className="mt-6 flex flex-col gap-5">
                <div className="flex flex-col items-start gap-2">
                  <label className="flex w-full text-sm lg:text-[16px] font-normal">
                    {labels?.EMAIL_LABEL}
                  </label>
                  <Input
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    disabled={!!credentials}
                    placeholder={labels?.LANDING_EMAIL_LBL_PLACEHOLDER}
                    className="!bg-white !rounded-[12px] !h-11 lg:!h-12"
                  />
                </div>

                <Button
                  type="primary"
                  className="rounded-xl !h-11 lg:!h-12"
                  onClick={handleNextStep}
                  loading={loading}
                >
                  {labels?.LANDING_LOGIN_CONTINUE_WITH_LBL}
                </Button>

                <div className="flex items-center h-9">
                  <Divider plain className="!m-0">
                    {labels?.Or_Label}
                  </Divider>
                </div>

                <div
                  onClick={() => handleGoogleSignIn()}
                  className="flex gap-2 w-full justify-center items-center bg-white !h-11 lg:!h-12 rounded-xl cursor-pointer"
                >
                  <GoogleIcon />
                  <span className="text-sm lg:text-[16px] font-normal text-[#878787]">
                    {labels?.LANDING_CONTINUE_WITH_GOOGLE_BTN_LBL}
                  </span>
                </div>

                <div>
                  <p className="!m-0 text-sm lg:text-[16px] font-normal text-[#343333]">
                    {labels?.LANDING_SIGNUP_ALREADY_HAVE_ACC_LBL}{" "}
                    <span
                      className="text-[#8E81F5] cursor-pointer"
                      onClick={() => {
                        navigate("/login");
                      }}
                    >
                      {labels?.LANDING_LOGIN_LABEL}
                    </span>
                  </p>
                </div>
              </div>
            )}
            {/* Step 2 */}
            {currentStep === 2 && (
              <div className="mt-6 flex flex-col gap-5">
                <div className="flex justify-between gap-3">
                  <div className="flex w-full flex-col items-start gap-2">
                    <label className="text-sm lg:text-[16px] font-normal">
                      {labels?.FIRSTNAME_LABEL}
                    </label>
                    <Input
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      placeholder={labels?.USER_SETTING_FIRSTNAME_PLACEHOLDER}
                      className="!bg-white !rounded-[12px] !h-11 lg:!h-12"
                    />
                  </div>
                  <div className="flex w-full flex-col items-start gap-2">
                    <label className="text-sm lg:text-[16px] font-normal">
                      {labels?.LASTNAME_LABEL}
                    </label>
                    <Input
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      placeholder={labels?.USER_SETTING_LASTNAME_PLACEHOLDER}
                      className="!bg-white !rounded-[12px] !h-11 lg:!h-12"
                    />
                  </div>
                </div>

                {!credentials && (
                  <>
                    <div className="flex flex-col items-start gap-2">
                      <label className="text-sm lg:text-[16px] font-normal">
                        {labels?.loginPassword}
                      </label>
                      <Password
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        placeholder={labels?.Input_Password_Placeholder}
                        className="!bg-white !rounded-[12px] !h-11 lg:!h-12"
                      />
                      {formData.password &&
                        !isValidPassword(formData.password) && (
                          <span className="text-xs text-red-500">
                            {labels?.Password_Validation_Message}
                          </span>
                        )}
                    </div>

                    <div className="flex flex-col items-start gap-2">
                      <label className="text-sm lg:text-[16px] font-normal">
                        {labels?.LANDING_CONFIRM_PASSWORD_LBL}
                      </label>
                      <Password
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        placeholder={labels?.Confirm_Password_Label}
                        className="!bg-white !rounded-[12px] !h-11 lg:!h-12"
                      />
                      {formData.password &&
                        formData.confirmPassword &&
                        formData.password !== formData.confirmPassword && (
                          <span className="text-xs text-red-500">
                            {labels?.setting.passwordMismatch}
                          </span>
                        )}
                    </div>
                  </>
                )}
                <div className="flex flex-col items-start gap-2">
                  <label className="text-sm lg:text-[16px] font-normal">
                    {labels?.Select_Country_Label}
                  </label>
                  <Select
                    showSearch
                    allowClear
                    placeholder={labels?.Select_Country_Placeholder}
                    className="!w-full !h-11 lg:!h-12 !rounded-[12px]"
                    options={formattedCountries}
                    filterOption={(input, option) =>
                      option?.label?.toLowerCase().includes(input.toLowerCase())
                    }
                    filterSort={(optionA, optionB) => {
                      const input =
                        document
                          .querySelector(".ant-select-selection-search-input")
                          ?.value?.toLowerCase() || "";
                      const aStarts = optionA.label
                        .toLowerCase()
                        .startsWith(input);
                      const bStarts = optionB.label
                        .toLowerCase()
                        .startsWith(input);
                      if (aStarts && !bStarts) return -1;
                      if (!aStarts && bStarts) return 1;
                      return optionA.label.localeCompare(optionB.label);
                    }}
                    value={formData.selectedCountry}
                    onChange={value =>
                      handleSelectChange("selectedCountry", value)
                    }
                    suffixIcon={<ArrowDown className="w-[14px]" />}
                    rootClassName={selectStyles.rootClassName}
                  />
                </div>

                <Button
                  type="primary"
                  className="rounded-xl !h-11 lg:!h-12"
                  onClick={handleNextStep}
                  disabled={
                    !formData.firstName ||
                    !formData.lastName ||
                    (!credentials &&
                      (!formData.password || !formData.confirmPassword)) ||
                    !formData.selectedCountry ||
                    (formData.password !== formData.confirmPassword &&
                      !credentials) ||
                    (!isValidPassword(formData.password) && !credentials)
                  }
                >
                  {labels?.LANDING_LOGIN_CONTINUE_WITH_EMAIL_LBL}
                </Button>
              </div>
            )}

            {/* Step 3 */}
            {currentStep === 3 && (
              <div className="flex flex-col mt-11">
                <p className="flex w-full justify-start !mb-2">
                  {labels?.I_am_a_Label}
                </p>
                <Radio.Group
                  value={formData.isFreelancer ? "freelancer" : "hiringManager"}
                  onChange={e =>
                    handleSelectChange(
                      "isFreelancer",
                      e.target.value === "freelancer"
                    )
                  }
                  className="w-full"
                >
                  <div className="flex w-full gap-3">
                    <div className="flex pl-4 w-full items-center !h-11 lg:!h-12 !bg-white rounded-[12px]">
                      <Radio value="freelancer">
                        {labels?.LANDING_WORK_AS_IPRO}
                      </Radio>
                    </div>
                    <div className="flex pl-4 w-full items-center !h-11 lg:!h-12 !bg-white rounded-[12px]">
                      <Radio value="hiringManager">
                        {labels?.LANDING_WORK_AS_SEARCHER}
                      </Radio>
                    </div>
                  </div>
                </Radio.Group>

                {formData?.isFreelancer && (
                  <div>
                    <div className="flex flex-col items-start mt-5 gap-2">
                      <label className="!text-[16px] font-normal text-[#343333]">
                        {labels?.Primary_Job_Label}
                      </label>
                      <Select
                        placeholder={labels?.Job_Role_Placeholder}
                        className="!w-full !h-11 lg:!h-12 !rounded-[12px]"
                        options={jobRoles}
                        value={formData.primaryJobRole}
                        onChange={value =>
                          handleSelectChange("primaryJobRole", value)
                        }
                        onSearch={handleRoleSearch}
                        showSearch
                        filterOption={false}
                        suffixIcon={<ArrowDown className="w-[14px]" />}
                        rootClassName={selectStyles.rootClassName}
                      />
                    </div>

                    <div className="flex flex-col items-start mt-5 gap-2">
                      <label className="!text-[16px] font-normal text-[#343333]">
                        Skills
                      </label>
                      <div className="flex w-full gap-3">
                        {[0, 1, 2].map(index => (
                          <Select
                            key={index}
                            options={skillOptions}
                            placeholder={`${labels?.Skill_Label} ${index + 1}`}
                            className="!w-full max-w-[33%] !h-11 lg:!h-12 !rounded-[12px]"
                            value={formData.skills[index]}
                            onChange={value => handleSkillChange(index, value)}
                            onSearch={value =>
                              handleSkillChange(index, null, value)
                            }
                            showSearch
                            filterOption={false}
                            suffixIcon={<ArrowDown className="w-[14px]" />}
                            rootClassName={`
                            ${selectStyles.rootClassName}
                            [&_.ant-select-selection-item]:!max-w-[calc(100%-20px)]
                            [&_.ant-select-selection-item]:!overflow-hidden
                            [&_.ant-select-selection-item]:!text-ellipsis
                            [&_.ant-select-selection-item]:!whitespace-nowrap
                          `}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex h-auto items-start text-start w-full mt-5">
                  <Checkbox
                    checked={formData.agreedToTerms}
                    onChange={handleCheckboxChange}
                    rootClassName="
                [&_.ant-checkbox-inner]:!border-[#878787]
                [&_.ant-checkbox-inner]:!rounded-[4px]
              "
                  />
                  <span className="pl-2 text-sm lg:text-[16px]">
                    {labels?.SIGNUP_TERMS_P1}{" "}
                    <span
                      className="text-[#8E81F5]"
                      onClick={() => {
                        setTermsAndServicesDrawerOpen(true);
                        setUserAgreementDrawerOpen(false);
                        setPrivacyPolicyDrawerOpen(false);
                      }}
                    >
                      {labels?.SIGNUP_TERMS_P3}
                    </span>
                    {labels?.SIGNUP_TERMS_P4}{" "}
                    <span
                      className="text-[#8E81F5]"
                      onClick={() => {
                        setTermsAndServicesDrawerOpen(false);
                        setUserAgreementDrawerOpen(true);
                        setPrivacyPolicyDrawerOpen(false);
                      }}
                    >
                      {labels?.SIGNUP_TERMS_P6}
                    </span>{" "}
                    {labels?.And_Label}{" "}
                    <span
                      className="text-[#8E81F5]"
                      onClick={() => {
                        setTermsAndServicesDrawerOpen(false);
                        setUserAgreementDrawerOpen(false);
                        setPrivacyPolicyDrawerOpen(true);
                      }}
                    >
                      {labels?.SIGNUP_TERMS_P7}
                    </span>
                    .
                  </span>
                </div>

                <Button
                  type="primary"
                  className="rounded-xl !h-11 lg:!h-12 mt-6"
                  onClick={handleSubmit}
                  disabled={
                    !formData.agreedToTerms
                    // ||
                    // (formData.isFreelancer &&
                    //   (!formData.primaryJobRole ||
                    //     formData.skills.some((skill) => !skill)))
                  }
                >
                  {labels?.Create_Account_Label}
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
      {/* Terms and Services */}
      {termsAndServicesDrawerOpen && (
        <div
          className="fixed inset-0 z-[1000] bg-[#********] flex justify-center items-end"
          onClick={() => setTermsAndServicesDrawerOpen(false)}
        >
          <div
            className={`w-full bg-white shadow-lg transition-all duration-300 ease-in-out rounded-t-2xl overflow-hidden ${
              termsAndServicesDrawerOpen ? "translate-y-0" : "translate-y-full"
            }`}
            style={{ maxHeight: "90vh" }}
            onClick={e => e.stopPropagation()}
          >
            <div className="h-full max-h-[90vh] overflow-y-scroll [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']">
              <TermsAndServices
                signUpData={signUpData}
                setTermsAndServicesDrawerOpen={setTermsAndServicesDrawerOpen}
              />
            </div>
          </div>
        </div>
      )}

      {/* Privacy Policy */}
      {privacyPolicyDrawerOpen && (
        <div
          className="fixed inset-0 z-[1000] bg-[#********] flex justify-center items-end"
          onClick={() => setPrivacyPolicyDrawerOpen(false)}
        >
          <div
            className={`w-full bg-white shadow-lg transition-all duration-300 ease-in-out rounded-t-2xl overflow-hidden ${
              privacyPolicyDrawerOpen ? "translate-y-0" : "translate-y-full"
            }`}
            style={{ maxHeight: "90vh" }}
            onClick={e => e.stopPropagation()}
          >
            <div className="h-full max-h-[90vh] overflow-y-scroll [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']">
              <PrivacyPolicy
                signUpData={signUpData}
                setPrivacyPolicyDrawerOpen={setPrivacyPolicyDrawerOpen}
              />
            </div>
          </div>
        </div>
      )}

      {/* User Agreement */}
      {userAgreementDrawerOpen && (
        <div
          className="fixed inset-0 z-[1000] bg-[#********] flex justify-center items-end"
          onClick={() => setUserAgreementDrawerOpen(false)}
        >
          <div
            className={`w-full bg-white shadow-lg transition-all duration-300 ease-in-out rounded-t-2xl overflow-hidden ${
              userAgreementDrawerOpen ? "translate-y-0" : "translate-y-full"
            }`}
            style={{ maxHeight: "90vh" }}
            onClick={e => e.stopPropagation()}
          >
            <div className="h-full max-h-[90vh] overflow-y-scroll [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']">
              <UserAgreement
                signUpData={signUpData}
                setUserAgreementDrawerOpen={setUserAgreementDrawerOpen}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Signup;
