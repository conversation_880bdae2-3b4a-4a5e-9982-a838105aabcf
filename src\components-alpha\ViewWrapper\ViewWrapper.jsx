import { useEffect, useState } from "react";
import Header from "../Header/Header";
import Navigation from "../Navigation/Navigation";
import { Grid } from "antd";
import Mask from "../../common-alpha/Mask/Mask";

const { useBreakpoint } = Grid;
const ViewWrapper = ({ children }) => {
  const [isNavShrink, setIsNavShrink] = useState(false);
  const screens = useBreakpoint();
  const handleNavigationToggle = () => {
    setIsNavShrink(!isNavShrink);
  };

  const handleSubMenuOpen = () => {
    if (isNavShrink && window.innerWidth > 1240) {
      setIsNavShrink(false);
    }
  };

  const removeNavShrink = () => {
    if (isNavShrink && window.innerWidth <= 1240) {
      setIsNavShrink(false);
    }
  };

  const handleMobileNavShrink = () => {
    if (!isNavShrink && window.innerWidth <= 1240) {
      setIsNavShrink(false);
    }
  };

  useEffect(() => {
    if (screens.lg) {
      setIsNavShrink(false);
    } else {
      setIsNavShrink(true);
    }
  }, [screens.lg]);

  return (
    <div className="flex flex-col">
      <Header
        onNavigationToggle={handleNavigationToggle}
        isNavShrink={isNavShrink}
      />
      {!isNavShrink && (
        <Mask
          onClick={() => setIsNavShrink(nav => !nav)}
          className="hidden max-lg:block max-lg:fixed z-90"
        />
      )}

      <div className="flex">
        <Navigation
          isNavShrink={isNavShrink}
          setIsNavShrink={setIsNavShrink}
          onSubMenuOpen={handleSubMenuOpen}
          onMobileNavShrink={handleMobileNavShrink}
          onNavShrink={removeNavShrink}
          onNavigationToggle={handleNavigationToggle}
        />
        <div className="relative flex-1 flex">{children}</div>
      </div>
    </div>
  );
};

export default ViewWrapper;
