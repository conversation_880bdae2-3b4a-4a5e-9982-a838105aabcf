import { useState, useEffect } from "react";
import { connect } from "react-redux";
import { useNavigate, useLocation } from "react-router-dom";
import find from "lodash/find";
import isEmpty from "lodash/isEmpty";
import head from "lodash/head";
import filter from "lodash/filter";
import includes from "lodash/includes";
import {
  isValidURL,
  isValidPhoneNumber,
  loadImageOrientation
} from "../../utilities/helpers";
import {
  getCompaniesApi,
  addCompanyApi,
  updateCompanyApi,
  deleteCompanyApi,
  getCountriesApi,
  getIndustriesApi,
  getUserEmailsApi,
  getUserPhoneNumbersApi,
  DeleteUserCompanyandCompanyUsers
} from "./WorkplaceApi";
import { notificationAction } from "../../actions/notification";
import { onStateChangeAction } from "./WorkplaceAction";
import EmptyIcon from "../../assets-alpha/images/view/empty-view-workplace.svg";
import EmptyWorkplacePreview from "../../assets-alpha/images/view/empty-workplace-preview.svg";
import SearchIcon from "../../assets-alpha/images/svg/search-normal.svg?react";
import toLower from "lodash/toLower";
import { Button, Drawer, Input, message } from "antd";
import ConfirmDialog from "../../common/ConfirmDialog/ConfirmDialog";
import { PlusOutlined } from "@ant-design/icons";
import PageWrapper from "../../components-alpha/PageWrapper/PageWrapper";
import EmptyView from "../../common-alpha/EmptyViewUpdated/EmptyView";
import Showcase from "./components/Showcase";
import AddWorkplace from "./components/AddWorkplace";
import AvatarIcon from "../../assets-alpha/images/svg/workplace-avatar.svg";
import LoadingMask from "../../common/LoadingMask/LoadingMask";

const Workplace = ({
  labels,
  isHelpActive,
  notificationAction,
  onStateChangeAction,
  ...props
}) => {
  const [loading, setIsLoading] = useState(false);
  const [state, setState] = useState({
    delCompanywithUserDM: "",
    isMobileOpen: false,
    delete: {
      show: false
    }
  });

  const [addNewWorkplace, setAddNewWorkPlace] = useState({
    show: false,
    view: null
  });
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [avatar, setAvatar] = useState("");
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isEditDrawerOpen, setIsEditDrawerOpen] = useState(false);
  const [RemoveLogo, setRemoveLogo] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    props.isFetching && getCompanyList();
    getUserPhoneNumbers();
    getCountries();
    getIndustries();
    getUserEmails();
    if (location?.state?.new) {
      handleCreateNewClick();
    }
  }, [location]);

  const getIndustries = () => {
    getIndustriesApi().then(data => {
      const industries = data.items.map(item => ({
        ...item,
        value: item.IndustryId,
        label: item.IndustryValue
      }));
      onStateChangeAction({ industries });
    });
  };

  const getCountries = () => {
    getCountriesApi().then(data => {
      const filterCountries = data.items.filter(item => item.CountryName);
      const countries = filterCountries.map(item => ({
        ...item,
        value: item.CountryId,
        label: item.CountryName
      }));
      onStateChangeAction({ countries });
    });
  };

  const getUserEmails = () => {
    getUserEmailsApi().then(data => {
      const filterEmail = data.items.filter(item => item.UserEmailValue);
      const userEmails = filterEmail.map(item => ({
        ...item,
        value: item.UserEmailValue,
        label: item.UserEmailValue
      }));
      onStateChangeAction({ userEmails });
    });
  };

  const handleFormFieldBlur = e => {
    let { name, value } = e.target;
    const { companyForm } = props;
    if (name === "WebUrl") {
      if (
        !isEmpty(value) &&
        value.indexOf("http") < 0 &&
        value.indexOf("https") < 0 &&
        value.indexOf("ftp") < 0
      ) {
        value = "http://" + value;
        onStateChangeAction({
          companyForm: {
            ...companyForm,
            WebUrl: value
          }
        });
      }
      validateUrl({ name, value });
      return;
    }
  };

  const getUserPhoneNumbers = () => {
    getUserPhoneNumbersApi().then(data => {
      const filterPhone = data.items.filter(item => item.UserPhonenumberValue);
      const userPhoneNumbers = filterPhone.map(item => ({
        ...item,
        value: item.UserPhonenumberId,
        label: item.UserPhonenumberValue
      }));

      onStateChangeAction({ userPhoneNumbers });
    });
  };

  const getCompanyList = () => {
    setIsLoading(true);
    onStateChangeAction({ isFetching: true });
    setAddNewWorkPlace({
      show: false,
      view: null
    });
    getCompaniesApi()
      .then(data => {
        setIsLoading(false);
        let companies = data.items;

        const currentCompany =
          location.state &&
          companies.find(
            item => item.UserCompanyId === location.state.com.UserCompanyId
          );
        const defaultCompany = companies.find(item => item.IsDefaultCompany);
        onStateChangeAction({
          companyList: companies,
          filteredCompanies: companies,
          companyForm: null,
          isLoading: false,
          isFetching: false
        });
      })
      .catch(() => {
        setIsLoadin(false);
        onStateChangeAction({ isLoading: false, isFetching: false });
      });
  };

  const handleCreateNewClick = () => {
    setIsEditDrawerOpen(true);
    setAvatar(null);
    setRemoveLogo(false);
    const companyForm = { UserCompanyId: -1 };
    onStateChangeAction({
      companyForm
    });
    setAddNewWorkPlace(st => ({
      ...st,
      show: true,
      view: "add"
    }));
  };

  const handleCompanyClick = id => {
    setIsDrawerOpen(true);
    setAvatar(null);
    const { filteredCompanies, userPhoneNumbers } = props;
    const companyForm = find(filteredCompanies, { UserCompanyId: id });
    const companyPhoneNumbers = companyForm.UserPhonenumberId
      ? userPhoneNumbers.filter(
          number => number.value === companyForm.UserPhonenumberId
        )
      : [];
    if (isEmpty(companyPhoneNumbers)) {
      companyForm.UserPhonenumberId = "";
      companyForm.UserPhonenumberValue = "";
    } else {
      companyForm.UserPhonenumberValue = companyPhoneNumbers[0].label;
    }

    onStateChangeAction({
      companyForm,
      currentCompanyPhoneNumbers: companyPhoneNumbers
    });
  };
  const handleDeleteCompany = ({ company }) => {
    setDeleteDialog(true);
    onStateChangeAction({
      selectedCompany: company,
      dialogMessage: labels?.companyDeleteConfirmation
    });
    setState(st => ({
      ...st,
      delete: {
        ...st.delete,
        show: true
      }
    }));
  };

  const [messageApi, contextHolder] = message.useMessage();

  const handleYesClick = () => {
    const {
      selectedCompany: { UserCompanyId, IsDefaultCompany }
    } = props;

    onStateChangeAction({ dialogMessage: "" });

    if (IsDefaultCompany) {
      const info = {
        message: labels?.defaultCompanyNotDeletedMessage,
        status: "error"
      };
      notificationAction(info);
      return;
    }
    onStateChangeAction({ isLoading: true });
    deleteCompanyApi({ id: UserCompanyId })
      .then(data => {
        if (data.success) {
          const info = {
            message: data?.message,
            status: "success"
          };
          notificationAction(info);
          setDeleteDialog(false);
          setIsDrawerOpen(false);
          setIsEditDrawerOpen(false);
          getCompanyList();
          return;
        }
        setIsDrawerOpen(false);

        setIsEditDrawerOpen(false);
        setDeleteDialog(false);
        const info = {
          message: data?.message,
          status: "error"
        };
        notificationAction(info);
        if (
          !data.success &&
          data.message == labels?.companyAssociatedWithCompanyUsersMessage
        ) {
          setDeleteDialog(false);
          setState({
            delCompanywithUserDM:
              labels?.companyWithCompanyUsersDeleteConfirmation
          });
          onStateChangeAction({ isLoading: false });
          return;
        }

        onStateChangeAction({ isLoading: false });
      })
      .catch(() => {
        setDeleteDialog(false);
        onStateChangeAction({ isLoading: false });
      })
      .finally(() => {
        setState(st => ({
          ...st,
          delete: {
            ...st.delete,
            show: false
          }
        }));
      });
  };

  const handleCompanyDelWithusersYesClick = () => {
    const {
      selectedCompany: { UserCompanyId, IsDefaultCompany }
    } = props;

    const { companyDeleteSuccessfully } = props.labels;
    setState({ delCompanywithUserDM: "" });
    onStateChangeAction({ isLoading: true });
    DeleteUserCompanyandCompanyUsers({ id: UserCompanyId })
      .then(data => {
        if (data.success) {
          const info = {
            message: data?.message,
            status: "success"
          };
          notificationAction(info);
          getCompanyList();
          return;
        }
        const info = {
          message: data?.message,
          status: "error"
        };
        notificationAction(info);
        onStateChangeAction({ isLoading: false });
      })
      .catch(() => {
        onStateChangeAction({ isLoading: false });
      });
  };

  const handleNoClick = () => {
    onStateChangeAction({
      dialogMessage: "",
      selectedCompany: null
    });
  };

  const handleFormSelectChange = (name, selectedOption) => {
    const { companyForm, userPhoneNumbers } = props;
    if (!selectedOption) {
      onStateChangeAction({
        companyForm: {
          ...companyForm,
          [name]: null,
          Phone: null,
          UserPhonenumberId: null,
          UserPhonenumberValue: null
        }
      });
      return;
    }
    const { value, UserPhonenumberId, UserPhonenumberValue } = selectedOption;

    if (name === "UserPhonenumberId") {
      if (UserPhonenumberId) {
        onStateChangeAction({
          companyForm: {
            ...companyForm,
            Phone: UserPhonenumberId,
            UserPhonenumberId,
            UserPhonenumberValue
          }
        });
        return;
      }

      const newPhoneNumber = head(userPhoneNumbers);
      if (
        newPhoneNumber &&
        newPhoneNumber.value &&
        !isValidPhoneNumber(newPhoneNumber.value)
      ) {
        userPhoneNumbers.splice(0, 1);
        onStateChangeAction({
          userPhoneNumbers: [...userPhoneNumbers]
        });
        return;
      } else {
        onStateChangeAction({
          companyForm: {
            ...companyForm,
            [name]: value,
            newPhoneCreated: true,
            Phone: value,
            UserPhonenumberValue: value
          }
        });
        return;
      }
    }

    onStateChangeAction({
      companyForm: {
        ...companyForm,
        [name]: value
      }
    });
  };

  const validateField = ({ name, value }) => {
    const { companyForm } = props;
    onStateChangeAction({
      companyForm: {
        ...companyForm,
        [name]: value,
        [`invalid${name}`]: !value
      }
    });
  };

  const validateUrl = ({ name, value }) => {
    const { companyForm } = props;
    onStateChangeAction({
      companyForm: {
        ...companyForm,
        [name]: value,
        [`invalid${name}`]: !(value && isValidURL(value))
      }
    });
  };

  const handleFormFieldChange = e => {
    const { name, value } = e.target;
    if (name === "CompanyName") {
      validateField({ name, value });
      return;
    }
    if (name === "WebUrl") {
      validateUrl({ name, value });
      return;
    }
    const { companyForm } = props;
    if (name === "IsDefaultCompany") {
      onStateChangeAction({
        companyForm: {
          ...companyForm,
          [name]: e.target.checked
        }
      });
      return;
    }
    onStateChangeAction({
      companyForm: {
        ...companyForm,
        [name]: value
      }
    });
  };

  const updateAvatar = image => {
    const { companyForm } = props;
    onStateChangeAction({
      companyForm: {
        ...companyForm,
        Logo: image
      }
    });
  };

  const handleAvatarChange = file => {
    if (file) {
      loadImageOrientation(file, updateAvatar);
      file = "";
    }
  };

  const handleCompanySave = () => {
    const { companyForm } = props;
    const {
      CompanyAddress = "",
      CompanyName = "",
      CountryId = "",
      Detail = "",
      IndustryId = "",
      IsDefaultCompany = false,
      Logo = "resources/images/spacer.png",
      // Logo = "",
      Phone = null,
      UserCompanyId = 0,
      UserPhonenumberId = "",
      newPhoneCreated,
      UserPhonenumberValue = "",
      Vat = ""
    } = companyForm;
    let { WebUrl } = companyForm;
    const company = {
      CompanyAddress,
      CompanyName,
      CountryId,
      Detail,
      IndustryId,
      IsDefaultCompany,
      Logo,
      Phone,
      UserCompanyId,
      UserPhonenumberId: newPhoneCreated ? null : UserPhonenumberId,
      UserPhonenumberValue,
      Vat,
      WebUrl,
      RemoveLogo
    };
    if (isEmpty(CompanyName)) {
      const info = {
        message: labels?.companyFormValidation,
        status: "error"
      };
      validateField({ name: "CompanyName", value: CompanyName });
      notificationAction(info);
      return;
    }

    if (
      !isEmpty(WebUrl) &&
      WebUrl.indexOf("http") < 0 &&
      WebUrl.indexOf("https") < 0 &&
      WebUrl.indexOf("ftp") < 0
    ) {
      WebUrl = "http://" + WebUrl;
      onStateChangeAction({
        companyForm: {
          ...companyForm,
          WebUrl: WebUrl
        }
      });
      company.WebUrl = WebUrl;
    }
    if (!isEmpty(WebUrl) && !isValidURL(WebUrl)) {
      const info = {
        message: props.labels.companyURLInvalid,
        status: "error"
      };
      notificationAction(info);
      return;
    }
    onStateChangeAction({ isLoading: true });
    if (UserCompanyId < 0) {
      addCompanyApi({ company })
        .then(() => {
          setIsEditDrawerOpen(false);
          setRemoveLogo(false);
          getUserPhoneNumbers();
          getCompanyList();
          const info = {
            message: labels?.companySuccessAdded,
            status: "success"
          };
          notificationAction(info);
          setState(st => ({ ...st, isMobileOpen: false }));
          setAddNewWorkPlace(st => ({
            ...st,
            show: false,
            view: null
          }));
        })
        .catch(() => {
          onStateChangeAction({ isLoading: false });
        });
      return;
    }
    updateCompanyApi({ company })
      .then(() => {
        setIsEditDrawerOpen(false);
        setRemoveLogo(false);
        getUserPhoneNumbers();
        getCompanyList();
        setState(st => ({ ...st, isMobileOpen: false }));
        const info = {
          message: labels?.companySuccessEdited,
          status: "success"
        };
        notificationAction(info);
        setAddNewWorkPlace(st => ({
          ...st,
          show: false,
          view: null
        }));
      })
      .catch(() => {
        onStateChangeAction({ isLoading: false });
      });
  };

  const handleCompanySearch = e => {
    const searchKey = e.target.value;
    onStateChangeAction({ searchKey });
    const { companyList } = props;
    const filteredCompanies = filter(companyList, company => {
      if (includes(toLower(company.CompanyName), toLower(searchKey))) {
        return company;
      }
    });
    onStateChangeAction({ filteredCompanies });
  };

  const handleGetImgSrc = () => {
    const { companyForm } = props;
    onStateChangeAction({
      companyForm: {
        ...companyForm,
        Logo: null
      }
    });
  };

  const handleCancelDeleteModal = () => {
    setState(st => ({
      ...st,
      delete: { show: false }
    }));
  };

  const handleClose = () => {
    setIsEditDrawerOpen(false);
    setRemoveLogo(false);
    setIsDrawerOpen(false);

    setAddNewWorkPlace(st => ({
      ...st,
      show: false,
      view: null
    }));
  };

  const {
    filteredCompanies,
    companyForm,
    countries,
    industries,
    userEmails,
    userPhoneNumbers,
    isLoading,
    companyList
  } = props;
  const { delCompanywithUserDM } = state;
  return (
    <>
      {loading && <LoadingMask />}
      {contextHolder}

      <div className="!w-full [&_.ant-drawer-body]:[-ms-overflow-style:none] [&_.ant-drawer-body]:[scrollbar-width:none] [&_.ant-drawer-body::-webkit-scrollbar]:[display:none] [&_.ant-drawer-body::-webkit-scrollbar]:[width:0] [&_.ant-drawer-body::-webkit-scrollbar]:[height:0]">
        <Drawer
          open={isEditDrawerOpen}
          placement="right"
          headerStyle={{ display: "none" }}
          onClose={() => {
            setIsDrawerOpen(false);
          }}
          rootClassName=" 
          sm:[&_.ant-drawer-content-wrapper]:!m-[8px]
          [&_.ant-drawer-content-wrapper]:!m-[0px]
          [&_.ant-drawer-content-wrapper]:!mt-[8px] 
          md:[&_.ant-drawer-content]:!rounded-[16px]
          [&_.ant-drawer-content]:!rounded-tl-[16px]
          [&_.ant-drawer-content]:!rounded-tr-[16px]
          md:[&_.ant-drawer-content-wrapper]:!w-[497px]
          [&_.ant-drawer-content-wrapper]:!w-[100vw]
          [&_.ant-drawer-content-wrapper]:!rounded-[16px]
          [&_.ant-drawer-body]:!p-[0px]"
        >
          <div className="!h-full !w-full">
            <AddWorkplace
              onFormSelectChange={handleFormSelectChange}
              industries={industries}
              userEmails={userEmails}
              userPhoneNumbers={userPhoneNumbers}
              companyForm={companyForm}
              countries={countries}
              onFormFieldChange={handleFormFieldChange}
              onFormFieldBlur={handleFormFieldBlur}
              onCompanySave={handleCompanySave}
              onAvatarChange={handleAvatarChange}
              labels={labels}
              isLoading={isLoading}
              getImgSrc={handleGetImgSrc}
              handleDelete={handleDeleteCompany}
              getNumber={getUserPhoneNumbers}
              handleClose={handleClose}
              createNew={true}
              addNewWorkplace={addNewWorkplace}
              setIsEditDrawerOpen={setIsEditDrawerOpen}
              onStateChangeAction={onStateChangeAction}
              avatar={avatar}
              setAvatar={setAvatar}
              setRemoveLogo={setRemoveLogo}
            />
          </div>
        </Drawer>
        <div className="[&_.ant-drawer-body]:[-ms-overflow-style:none] [&_.ant-drawer-body]:[scrollbar-width:none] [&_.ant-drawer-body::-webkit-scrollbar]:[display:none] [&_.ant-drawer-body::-webkit-scrollbar]:[width:0] [&_.ant-drawer-body::-webkit-scrollbar]:[height:0]">
          <Drawer
            open={isDrawerOpen}
            placement="right"
            headerStyle={{ display: "none" }}
            onClose={() => {
              setIsDrawerOpen(false);
            }}
            rootClassName=" 
          sm:[&_.ant-drawer-content-wrapper]:!m-[8px]
          [&_.ant-drawer-content-wrapper]:!mt-[8px] 
          md:[&_.ant-drawer-content]:!rounded-[16px]
          [&_.ant-drawer-content]:!rounded-tl-[16px]
          [&_.ant-drawer-content]:!rounded-tr-[16px]
          md:[&_.ant-drawer-content-wrapper]:!w-[497px]
          [&_.ant-drawer-content-wrapper]:!w-[100%]
          [&_.ant-drawer-content-wrapper]:!rounded-[16px]
          [&_.ant-drawer-body]:!p-[0px]"
          >
            <Showcase
              labels={labels}
              setIsDrawerOpen={setIsDrawerOpen}
              isEditDrawerOpen={isEditDrawerOpen}
              setIsEditDrawerOpen={setIsEditDrawerOpen}
              handleDelete={handleDeleteCompany}
              filteredCompanies={filteredCompanies}
              companyForm={companyForm}
              countries={countries}
              industries={industries}
              userEmails={industries}
              userPhoneNumbers={userPhoneNumbers}
              handleCancelDeleteModal={handleCancelDeleteModal}
              handleYesClick={handleYesClick}
              deleteDialog={deleteDialog}
              setDeleteDialog={setDeleteDialog}
              isLoading={isLoading}
              setAvatar={setAvatar}
            />
          </Drawer>
        </div>
        {delCompanywithUserDM && (
          <ConfirmDialog testId="company-confirm-diloag">
            <ConfirmDialog.Message>
              {delCompanywithUserDM}
            </ConfirmDialog.Message>
            <ConfirmDialog.ActionButtons>
              <ConfirmDialog.Button
                className="dialog-btn"
                testId="company-delete-yes"
                onClick={handleCompanyDelWithusersYesClick}
              >
                {labels?.companyDeleteCurtainYESBtnText}
              </ConfirmDialog.Button>
              <ConfirmDialog.Button
                className="dialog-btn"
                testId="company-delete-no"
                onClick={handleNoClick}
              >
                {labels?.companyDeleteCurtainNOBtnText}
              </ConfirmDialog.Button>
            </ConfirmDialog.ActionButtons>
          </ConfirmDialog>
        )}

        {companyList?.length > 0 ? (
          <div>
            <div className="flex w-full h-[calc(100vh-64px)] bg-white">
              <div className="!h-full w-full md:w-[34.7%] flex flex-col md:!p-6 p-4">
                <div className=" !pb-4">
                  <div className="flex w-full justify-between items-center md:mb-4 mb-0">
                    <h2 className="!m-0 !text-[18px] font-semibold md:!text-2xl text-[var(--dark)] whitespace-pre md:!font-bold leading-[100%]">
                      {labels?.SEARCHER_NAVIGATION_COMPANY_LABEL}
                    </h2>
                  </div>
                  <div className="hidden md:block">
                    <Input
                      placeholder={labels?.companySearchFieldPlaceholder}
                      size="medium"
                      bordered={false}
                      className=" !border !border-[#F3F3F3] !bg-[#F3F1FD] !h-11 placeholder:text-[#878787] ![--ant-input-padding-inline:12px] [&_.ant-input-affix-wrapper]:!p-[3px_3px_3px_16px] [&_.ant-input-prefix]:!border-0 [&_.ant-input-prefix]:!mr-0 hover:!border-[#8E81F5]"
                      rootClassName="
                      [&_.ant-input]:!ml-[6px]"
                      onChange={handleCompanySearch}
                      prefix={<SearchIcon />}
                    />
                  </div>
                </div>
                <div className="flex flex-col overflow-y-auto  [&::-webkit-scrollbar]:hidden md:mb-0 mb-[64px]">
                  {filteredCompanies?.length > 0 ? (
                    <div className="flex flex-col gap-[6px]">
                      {filteredCompanies?.map(item => (
                        <div
                          onClick={() =>
                            handleCompanyClick(item?.UserCompanyId)
                          }
                          key={item?.UserCompanyId}
                          className={`flex gap-4 !p-3 !rounded-2xl md:h-[68px] h-[56px] justify-between items-center pointer ${
                            companyForm?.UserCompanyId ===
                              item?.UserCompanyId && isDrawerOpen
                              ? "border border-[var(--purple)] "
                              : " border border-[#EAE5FC]"
                          }`}
                        >
                          <div className="flex items-center justify-between w-full">
                            <div className="flex gap-4 items-center">
                              <div className="md:!w-11 md:!h-11 !h-10 !w-10 !rounded-[50%]">
                                <img
                                  className="md:!w-11 md:!h-11 !h-10 !w-10 !rounded-[50%]"
                                  src={item?.Logo ? item?.Logo : AvatarIcon}
                                  alt={"Logo"}
                                />
                              </div>
                              <div className="flex flex-col gap-2">
                                <label className="text-[var(--dark)] text-sm font-medium leading-[100%]">
                                  {item?.CompanyName?.length > 20
                                    ? `${item.CompanyName.slice(0, 20)}...`
                                    : item?.CompanyName}
                                </label>

                                <p className="text-[var(--gray-3)] text-xs font-normal leading-[100%]">
                                  {item?.CompanyAddress?.length > 20
                                    ? `${item.CompanyAddress.slice(0, 20)}...`
                                    : item?.CompanyAddress}
                                </p>
                              </div>
                            </div>
                            {item?.IsDefaultCompany && (
                              <div className="flex justify-center items-center w-[69px] h-[27px] rounded-[99px] bg-[#F3F1FD]">
                                <span className="text-[#8E81F5] text-xs font-normal leading-[100%]">
                                  Default
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    ""
                  )}
                </div>
              </div>
              <div className=" hidden md:block w-[65.3%] border-l-[0.5px] border-[#C6C6C6]">
                <PageWrapper
                  contentClassName=" h-full"
                  className=" max-xl:p-[16px]"
                >
                  <div className=" flex items-center justify-center h-full">
                    <div className="w-full h-full flex justify-center items-center">
                      <EmptyView
                        icon={<img src={EmptyWorkplacePreview} alt="Empty" />}
                        title={labels?.Workplace_NothingSelected_Label}
                        description={
                          labels?.Workpalce_NothingSelected_Description
                        }
                        btnIcon={<PlusOutlined />}
                        actionText={labels?.Add_Workplace_Label}
                        onAction={handleCreateNewClick}
                      />
                    </div>
                  </div>
                </PageWrapper>
              </div>
              <div className="md:!hidden !flex !items-center !justify-center !w-full fixed bottom-0 h-[70px] border-t-[0.5px] border-[#EAE5FC]">
                <Button
                  type="primary"
                  className="!w-full !h-10 !ml-4 !mr-4"
                  onClick={handleCreateNewClick}
                  icon={<PlusOutlined />}
                >
                  {labels?.Add_Workplace_Label}
                </Button>
              </div>
            </div>
            <Drawer
              open={isEditDrawerOpen}
              placement="right"
              headerStyle={{ display: "none" }}
              onClose={() => {
                setIsEditDrawerOpen(false);
                setRemoveLogo(false);
              }}
              rootClassName=" 
              sm:[&_.ant-drawer-content-wrapper]:!m-[8px]
              [&_.ant-drawer-content-wrapper]:!mt-[8px] 
              md:[&_.ant-drawer-content]:!rounded-[16px]
              [&_.ant-drawer-content]:!rounded-tl-[16px]
              [&_.ant-drawer-content]:!rounded-tr-[16px]
              md:[&_.ant-drawer-content-wrapper]:!w-[497px]
              [&_.ant-drawer-content-wrapper]:!w-[100vw]
              [&_.ant-drawer-content-wrapper]:!rounded-[16px]
              [&_.ant-drawer-body]:!p-[0px]"
            >
              <AddWorkplace
                onFormSelectChange={handleFormSelectChange}
                industries={industries}
                userEmails={userEmails}
                userPhoneNumbers={userPhoneNumbers}
                companyForm={companyForm}
                countries={countries}
                onFormFieldChange={handleFormFieldChange}
                onFormFieldBlur={handleFormFieldBlur}
                onCompanySave={handleCompanySave}
                onAvatarChange={handleAvatarChange}
                labels={labels}
                isLoading={isLoading}
                getImgSrc={handleGetImgSrc}
                handleDelete={handleDeleteCompany}
                getNumber={getUserPhoneNumbers}
                createNew={false}
                setIsEditDrawerOpen={setIsEditDrawerOpen}
                onStateChangeAction={onStateChangeAction}
                avatar={avatar}
                setAvatar={setAvatar}
                setRemoveLogo={setRemoveLogo}
              />
            </Drawer>
          </div>
        ) : (
          <PageWrapper
            contentClassName="max-xl:p-[16px] items-center"
            title={"Workplace"}
            titleClassName={"!text-[16px] md:!text-[24px] !font-semibold"}
          >
            <div className="flex w-full h-full justify-center items-center">
              <EmptyView
                icon={<img src={EmptyIcon} alt="Empty" />}
                title={labels?.Workpalce_EmptyScreen_Label}
                loading={isLoading}
                description={labels?.Workpalce_EmptyScreen_Description}
                btnIcon={<PlusOutlined />}
                actionText={labels?.Add_Workplace_Label}
                onAction={handleCreateNewClick}
              />
            </div>
          </PageWrapper>
        )}
      </div>
    </>
  );
};

const mapStateToProps = ({ systemLabel, navigation, company }) => {
  const { labels } = systemLabel;
  const { isHelpActive } = navigation;
  return { labels, isHelpActive, ...company };
};
const mapActionToProps = {
  notificationAction,
  onStateChangeAction
};
export default connect(mapStateToProps, mapActionToProps)(Workplace);
