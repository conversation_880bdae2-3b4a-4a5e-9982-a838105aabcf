import axios from "axios";
import { ApiUrl } from "../../api/apiUrls";
import { useClientQuery, useGetMutation } from "../../api-alpha/api-service";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { loginAcionUpdate } from "../../store/initialConfig";
import { privateRoutes } from "../../Routes/routing";

//deprecated
// export const getNavigationMenusApi = ({ appTypeId, featureTypeId }) => {
//   return axios
//     .get(ApiUrl.SideNavigation.GetUserFeatures({ appTypeId, featureTypeId }))
//     .then(({ data }) => data);
// };

export const useNavigationMenusApi = ({ appTypeId, featureTypeId }) => {
  return useClientQuery({
    url: ApiUrl.SideNavigation.GetUserFeatures({ appTypeId, featureTypeId })
  });
};

export const getNavigationMenusForVisitorApi = ({
  appTypeId,
  featureTypeId,
  isVisitor
}) => {
  return axios
    .get(
      ApiUrl.SideNavigation.GetUserFeaturesForVisitor({
        appTypeId,
        featureTypeId,
        isVisitor
      })
    )
    .then(({ data }) => data);
};
//deprecated
// export const changeProfileApi = ({ IsFreelancer }) => {
//   return axios
//     .get(ApiUrl.User.ChangeProfile({ IsFreelancer }))
//     .then(({ data }) => data);
// };

export const useChangeProfileApi = ({ IsFreelancer }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const url = ApiUrl.User.ChangeProfile({ IsFreelancer });
  const query = useGetMutation({
    url
  });
  const { data: response } = query;

  useEffect(() => {
    if (response?.success) {
      const resultUpdate = {
        ...response.items,
        isProfileChanged: true
      };
      dispatch(loginAcionUpdate({ user: resultUpdate }));
      navigate(privateRoutes.dashboard.path);
    }
  }, [response, dispatch, navigate]);

  return query;
};

export const getUserApi = () => {
  return axios.get(ApiUrl.User.GetUser).then(({ data }) => data);
};

export const logoutApi = () => {
  return axios.get(ApiUrl.Account.Logout).then(({ data }) => data);
};

export const getIporOpportunityCountsApi = () => {
  return axios
    .get(ApiUrl.User.GetIporOpportunityCounts)
    .then(({ data }) => data);
};

export const getMessageCountApi = () => {
  return axios
    .get(ApiUrl.SideNavigation.GetMessageCount)
    .then(({ data }) => data);
};

export const getCollaborationCountApi = () => {
  return axios
    .get(ApiUrl.SideNavigation.GetCollaborationCount)
    .then(({ data }) => data);
};

export const getIProCollaborationCountApi = () => {
  return axios
    .get(ApiUrl.SideNavigation.GetIProCollaborationCount)
    .then(({ data }) => {
      return data;
    });
};

export const getOpportunityCountApi = ({ IsFreelancer }) => {
  return axios
    .get(ApiUrl.SideNavigation.GetOpportunityCount({ IsFreelancer }))
    .then(({ data }) => data);
};
