import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import PageWrapper from "../../components/PageWrapper/PageWrapper";
import { notificationAction } from "../../actions/notification";
import "./philip.scss";
import { privateRoutes } from "../../Routes/routing";
import { StorageService } from "../../api/storage";
import { updatePhillipPopupStatusApi } from "../../components/Phillip/phillipApi";
import IproPhillip from "./components/IproPhillip";

const Snapshot = props => {
  const [state, setState] = useState({
    UserFeatures: [],
    token: "",
    ExpenseCategories: [],
    Currencies: [],
    isLoading: true,
    UserId: -1,
    dialogMessage: "",
    showPhillip: true,
    facebookClientId: "",
    linkedInClientId: ""
  });

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { labels, isHelpActive, User } = useSelector(state => ({
    labels: state.systemLabel.labels,
    isHelpActive: state.navigation.isHelpActive,
    User: state.userInfo.user ? state.userInfo.user : StorageService.getUser()
  }));

  useEffect(() => {
    let { UserEmail } = User;
    let Email = UserEmail;
    if (!Email) {
      const User = StorageService.getUser();
      Email = User.Email;
    }
    setState(prevState => ({
      ...prevState,
      UserId: User.UserId
    }));
  }, []);

  const handleOkClick = () => {
    navigate(privateRoutes.dashboard.path);
  };

  const handleClosePhillip = () => {
    updatePhillipPopupStatusApi({ isFreelancer: true }).then(response => {});
    setState(prevState => ({ ...prevState, showPhillip: false }));
  };

  const handleShowPhillip = () => {
    setState(prevState => ({ ...prevState, showPhillip: true }));
  };

  const handleSearcherPhillipSkip = () => {
    updatePhillipPopupStatusApi({ isFreelancer: true }).then(response => {
      if (response.success) {
        const info = {
          message: props.searcherPhillipSkip,
          status: "success"
        };
        dispatch(notificationAction(info));
      }
    });
    setState(prevState => ({ ...prevState, showPhillip: false }));
  };

  const handleUpdateCompanyPresentationsWidget = () => {
    setState(prevState => ({
      ...prevState,
      isReloadCompanyPresentationsWidget: !prevState.isReloadCompanyPresentationsWidget
    }));
  };

  const handleUpdateResumeShortlistWidgetWidget = () => {
    setState(prevState => ({
      ...prevState,
      isReloadResumeShortlistWidget: !prevState.isReloadResumeShortlistWidget
    }));
  };

  const reloadResumeWidgetHandler = () => {
    setState(prevState => ({
      ...prevState,
      isReloadResumeWidget: !prevState.isReloadResumeWidget
    }));
  };

  const {
    showPhillip,
    facebookClientId,
    linkedInClientId,
    UserFeatures,
    token,
    Currencies,
    dialogMessage,
    ExpenseCategories,
    isLoading,
    UserId,
    pendingtimesheet,
    Rejectedtimesheet,
    Rejectedexpense,
    Pendingexpense
  } = state;

  const currentViewHash =
    window.location.hash.toLowerCase().split("/")[1] == "apps"
      ? window.location.hash.toLocaleLowerCase().split("/")[3]
      : window.location.hash.toLocaleLowerCase().split("/")[2];

  return (
    <PageWrapper className="snapshot-page">
      {showPhillip && (
        <IproPhillip
          open={showPhillip}
          onCloseModal={handleClosePhillip}
          onSkip={handleSearcherPhillipSkip}
          facebookClientId={facebookClientId}
          linkedInClientId={linkedInClientId}
          onResumeUpdate={reloadResumeWidgetHandler}
        />
      )}
    </PageWrapper>
  );
};

export default Snapshot;
