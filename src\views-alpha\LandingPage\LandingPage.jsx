import React, { useEffect, useState } from "react";
import { Route, Routes } from "react-router-dom";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { privateRoutes, publicRoutes } from "../../Routes/routing";
import { StorageService } from "../../api/storage";
import { logoutApi } from "../../components/Navigation/navigationApi";
import {
  emailTokenConfirmationApi,
  getSignUpConfigsApi
} from "./landingPageApi";
import About from "./components/About/About";
import Contact from "./components/Contact/Contact";
import Header from "./components/Header/Header";
import Home from "./components/Home/Home";
import WhyProdoo from "./components/WhyProdoo/WhyProdoo";
import { ArrowUpOutlined } from "@ant-design/icons";
import { Button } from "antd";
import Footer from "./components/Footer/Footer";
import { notificationAction } from "../../actions-alpha/notification";
import { redirectLoginAuth } from "./Auth/loginAction";
import { logoutAction } from "../../store/initialConfig";
import { useDispatch, useSelector } from "react-redux";
import TermsAndServices from "./components/TermsAndServices/TermsAndServices";
import PrivacyPolicy from "./components/PrivacyPolicy/PrivacyPolicy";
import UserAgreement from "./components/UserAgreement/UserAgreement";

const LandingPage = () => {
  const labels = useSelector(state => state.systemLabel.labels);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const moveToTop = React.useRef(null);
  const [openDrawerMenu, setOpenDrawerMenu] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [termsAndServicesDrawerOpen, setTermsAndServicesDrawerOpen] = useState(
    false
  );
  const [privacyPolicyDrawerOpen, setPrivacyPolicyDrawerOpen] = useState(false);
  const [userAgreementDrawerOpen, setUserAgreementDrawerOpen] = useState(false);
  const [signUpData, setSignUpData] = useState([]);

  const emailConfirmation = token => {
    emailTokenConfirmationApi(token)
      .then(response => {
        if (response.success) {
          const info = {
            message: labels.EMAIL_VERIFICATION_ACCOUNT_SUCCESS,
            status: "success"
          };
          navigate(publicRoutes.login.path);
          dispatch(notificationAction(info));
        } else {
          const info = {
            message: labels.EMAIL_VERIFICATION_ACCONT_FAIL,
            status: "error"
          };
          dispatch(notificationAction(info));
          navigate(publicRoutes.default.path);
        }
      })
      .catch(err => {
        const info = {
          message: labels.EMAIL_VERIFICATION_ACCONT_FAIL,
          status: "error"
        };
        dispatch(notificationAction(info));
      });
  };

  useEffect(() => {
    if (location.pathname.indexOf(publicRoutes.redirectedLogin.path) >= 0) {
      redirectLoginAuthCHeck();
    } else {
      checkUser();
    }
  }, []);
  useEffect(() => {
    if (
      privacyPolicyDrawerOpen ||
      userAgreementDrawerOpen ||
      termsAndServicesDrawerOpen
    ) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }

    return () => {
      document.body.style.overflow = "auto";
    };
  }, [
    privacyPolicyDrawerOpen,
    userAgreementDrawerOpen,
    termsAndServicesDrawerOpen
  ]);
  const redirectLoginAuthCHeck = () => {
    const query = new URLSearchParams(location.search);
    const token = query.get("token");
    redirectLoginAuth(token).then(response => {
      navigate(privateRoutes.dashboard.path);
    });
  };
  useEffect(() => {
    const query = new URLSearchParams(window.location.search);
    const verificationToken = query.get("verification_token");
    if (verificationToken) {
      const user = JSON.parse(localStorage.getItem("User"));
      window.history.replaceState({}, document.title, window.location.pathname);
      if (user) {
        logoutApi().then(response => {
          if (response.success) {
            logoutAction();
            StorageService.clearAll();
            emailConfirmation(verificationToken);
          }
        });
      } else {
        emailConfirmation(verificationToken);
      }
    }
  }, []);

  const checkUser = () => {
    const user = StorageService.getUser();
    if (user) {
      navigate(privateRoutes.dashboard.path);
    } else {
      StorageService.clearAll();
      navigate("/");
      // window.location.href = RESET_LANDINGPAGE_URL;
    }
  };
  useEffect(() => {
    getSignUpConfigsData();
    const handleScroll = () => {
      setShowScrollButton(window.pageYOffset > 150);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  const getSignUpConfigsData = async () => {
    const res = await getSignUpConfigsApi();
    setSignUpData(res?.items);
  };
  return (
    <>
      <div ref={moveToTop}>
        <Header
          openDrawerMenu={openDrawerMenu}
          setOpenDrawerMenu={setOpenDrawerMenu}
        />
        <Routes>
          <Route
            path="/"
            element={
              <div>
                <section
                  id="home"
                  className="!min-h-screen bg-[url('/assets/images/background-mbl.png')] sm:bg-[url('/assets/images/background.webp')] bg-cover bg-no-repeat bg-center"
                >
                  <Home labels={labels} />
                </section>
                <section id="whyprodoo" className="min-h-screen">
                  <WhyProdoo />
                </section>
                <section id="aboutus">
                  <About />
                </section>
                <section id="contactus">
                  <Contact
                    setPrivacyPolicyDrawerOpen={setPrivacyPolicyDrawerOpen}
                    setTermsAndServicesDrawerOpen={
                      setTermsAndServicesDrawerOpen
                    }
                  />
                </section>
              </div>
            }
          />
        </Routes>
        <Footer
          setTermsAndServicesDrawerOpen={setTermsAndServicesDrawerOpen}
          setPrivacyPolicyDrawerOpen={setPrivacyPolicyDrawerOpen}
          setUserAgreementDrawerOpen={setUserAgreementDrawerOpen}
        />
        <div className="fixed z-[999] bottom-13 right-13">
          {showScrollButton && (
            <Button
              onClick={() =>
                moveToTop.current?.scrollIntoView({
                  behavior: "smooth"
                })
              }
              type="primary"
              className="!rounded-[50%] !w-12 !h-12 !p-0"
            >
              <ArrowUpOutlined className="[&_svg]:!w-6 [&_svg]:!h-6" />
            </Button>
          )}
        </div>
      </div>
      {/* Terms and Services */}
      {termsAndServicesDrawerOpen && (
        <div
          className="fixed inset-0 z-[1000] bg-[#00000066] flex justify-center items-end"
          onClick={() => setTermsAndServicesDrawerOpen(false)}
        >
          <div
            className={`w-full bg-white shadow-lg transition-all duration-300 ease-in-out rounded-t-2xl overflow-hidden ${
              termsAndServicesDrawerOpen ? "translate-y-0" : "translate-y-full"
            }`}
            style={{ maxHeight: "90vh" }}
            onClick={e => e.stopPropagation()}
          >
            <div className="h-full max-h-[90vh] overflow-y-scroll [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']">
              <TermsAndServices
                signUpData={signUpData}
                setTermsAndServicesDrawerOpen={setTermsAndServicesDrawerOpen}
              />
            </div>
          </div>
        </div>
      )}

      {/* Privacy Policy */}
      {privacyPolicyDrawerOpen && (
        <div
          className="fixed inset-0 z-[1000] bg-[#00000066] flex justify-center items-end"
          onClick={() => setPrivacyPolicyDrawerOpen(false)}
        >
          <div
            className={`w-full bg-white shadow-lg transition-all duration-300 ease-in-out rounded-t-2xl overflow-hidden ${
              privacyPolicyDrawerOpen ? "translate-y-0" : "translate-y-full"
            }`}
            style={{ maxHeight: "90vh" }}
            onClick={e => e.stopPropagation()}
          >
            <div className="h-full max-h-[90vh] overflow-y-scroll [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']">
              <PrivacyPolicy
                signUpData={signUpData}
                setPrivacyPolicyDrawerOpen={setPrivacyPolicyDrawerOpen}
              />
            </div>
          </div>
        </div>
      )}

      {/* User Agreement */}
      {userAgreementDrawerOpen && (
        <div
          className="fixed inset-0 z-[1000] bg-[#00000066] flex justify-center items-end"
          onClick={() => setUserAgreementDrawerOpen(false)}
        >
          <div
            className={`w-full bg-white shadow-lg transition-all duration-300 ease-in-out rounded-t-2xl overflow-hidden ${
              userAgreementDrawerOpen ? "translate-y-0" : "translate-y-full"
            }`}
            style={{ maxHeight: "90vh" }}
            onClick={e => e.stopPropagation()}
          >
            <div className="h-full max-h-[90vh] overflow-y-scroll [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']">
              <UserAgreement
                signUpData={signUpData}
                setUserAgreementDrawerOpen={setUserAgreementDrawerOpen}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default LandingPage;
