import CloseIcon from "../../../../assets-alpha/images/svg/close-modal.svg";
import ArrowDown from "../../../../assets-alpha/images/svg/arrow-down-icon.svg";
import AvatarIcon from "../../../../assets-alpha/images/svg/avatar-white.svg";
import LocationIcon from "../../../../assets-alpha/images/svg/location.svg?react";
import RankingIcon from "../../../../assets-alpha/images/svg/ranking.svg?react";
import ProfileIcon from "../../../../assets-alpha/images/svg/profile.svg?react";
import { useEffect, useState } from "react";
import { LoadingOutlined } from "@ant-design/icons";
const ResumeStatusDetail = ({
  resumeList,
  acceptedResume,
  pendingResume,
  rejectedResume,
  setAcceptedResume,
  setRejectedResume,
  setPendingResume
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [currentStatus, setCurrentStatus] = useState("");
  useEffect(() => {
    if (acceptedResume) {
      setCurrentStatus({ text: "Accepted", color: "text-[#34C759]" });
    } else if (pendingResume) {
      setCurrentStatus({ text: "Pending", color: "text-[#FF9500]" });
    } else if (rejectedResume) {
      setCurrentStatus({ text: "Rejected", color: "text-[#FF3B30]" });
    }
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, [acceptedResume, pendingResume, rejectedResume]);

  const handleClose = () => {
    if (acceptedResume) {
      setAcceptedResume(false);
    } else if (pendingResume) {
      setPendingResume(false);
    } else {
      setRejectedResume(false);
    }
  };

  return (
    <div className=" overflow-y-auto  [scrollbar-width:'none'] [-ms-overflow-style:'none'] [&::-webkit-scrollbar]:hidden">
      <div className="relative w-full sm:w-[497px] h-[64px] ">
        <div className="fixed sm:w-[497px] w-full h-[64px] border-b border-[#EAE5FC] z-[999] bg-white rounded-tl-2xl rounded-tr-2xl">
          <div className="flex w-full h-[64px] justify-between items-center p-6">
            <div className="flex md:justify-start justify-between w-full items-center gap-4 ">
              <h1
                className={`!m-0 flex-1 order-1 md:order-2 !text-[18px] !font-semibold md:!text-2xl md:font-medium ${currentStatus.color}`}
              >
                {currentStatus.text}
              </h1>
              <img
                src={window.innerWidth > 767 ? CloseIcon : ArrowDown}
                alt=""
                onClick={() => {
                  handleClose();
                }}
                className="pointer order-2 md:order-1"
              />
            </div>
          </div>
        </div>
      </div>
      {isLoading ? (
        <div className="flex w-full  justify-center items-center mt-6">
          <LoadingOutlined className="!w-5 !h-5" />
        </div>
      ) : (
        <div className="flex flex-col gap-2 md:ml-6 ml-4 md:mr-6 mr-4 mt-4 mb-4  overflow-y-auto  [scrollbar-width:'none'] [-ms-overflow-style:'none'] [&::-webkit-scrollbar]:hidden">
          {resumeList
            ?.filter(single => {
              if (acceptedResume) {
                return single.IsConfirmed === true;
              } else if (pendingResume) {
                return (
                  single.IsConfirmed === false && single.IsDeclined === false
                );
              } else if (rejectedResume) {
                return single.IsDeclined === true;
              }
              return true;
            })
            ?.map((single, id) => (
              <div
                key={id}
                className=" relative pointer !p-4 flex bg-[#F3F1FD] rounded-[10px] h-auto"
              >
                <div>
                  <div className="flex flex-col gap-4 md:gap-3">
                    <div className="flex !h-auto gap-2">
                      <div className="w-12 h-12">
                        <img
                          src={AvatarIcon}
                          alt="Image"
                          className="w-12 h-12 rounded-[50%]"
                        />
                      </div>
                      <div className="flex flex-col w-full h-auto">
                        <h1 className="order-1 md:block hidden !m-0 font-semibold !text-base text-[#2F2F2F]">
                          {(() => {
                            const text = (single?.Profiles || [])
                              .map(item => item?.ProfileValue)
                              .join(", ");
                            const trimmed =
                              text.length > 30
                                ? text.slice(0, 30).replace(/,\s*$/, "") + "..."
                                : text;
                            return trimmed;
                          })()}
                        </h1>
                        <div className="order-2 md:order-1 flex flex-wrap flex-col md:flex-row gap-2 justify-center md:justify-start md:items-center md:mt-[9px]">
                          <p className="flex gap-1 items-center">
                            <ProfileIcon className="w-[15px] h-[15px] text-[#878787]" />
                            <span className="text-[#878787] !text-sm font-normal">
                              {single?.UserFirstName}
                            </span>
                          </p>
                          <p className="flex gap-1 items-center">
                            <LocationIcon className="!w-[15px] !h-[15px] text-[#8E81F5]" />
                            <span className="text-[#8E81F5] !text-sm font-normal !w-[100%]">
                              {single?.Region}
                            </span>
                          </p>
                          <p className="flex gap-1 items-center">
                            <RankingIcon className=" w-4 h-4 text-[#34C759]" />
                            <span className="text-[#34C759] !text-sm font-normal">
                              {`Match Score (${single?.matchScore || "0"}) `}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                    <h1 className="block md:hidden !m-0 font-semibold !text-base text-[#2F2F2F]">
                      {(() => {
                        const text = (single?.Profiles || [])
                          .map(item => item?.ProfileValue)
                          .join(", ");
                        const trimmed =
                          text.length > 30
                            ? text.slice(0, 30).replace(/,\s*$/, "") + "..."
                            : text;
                        return trimmed;
                      })()}
                    </h1>
                  </div>
                  <div>
                    <div className="flex flex-wrap w-full  gap-2 md:mt-6 mt-[14px]">
                      {single?.Skills?.slice(0, 3)?.map((row, index) => (
                        <button className="h-8 !w-auto pl-4 pr-4 bg-white border border-[#EAE5FC] rounded-lg">
                          <span className="!text-[#343333] text-xs font-normal leading-[100%]">
                            {row?.SkillValue}
                          </span>
                        </button>
                      ))}
                      {single?.Skills?.length > 3 && (
                        <button className="h-8 !w-auto pl-4 pr-4 bg-white border border-[#EAE5FC] rounded-lg">
                          <span className="!text-[#343333] text-xs font-normal leading-[100%]">
                            {`+ ${single?.Skills?.length - 3} more`}
                          </span>
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
        </div>
      )}
    </div>
  );
};

export default ResumeStatusDetail;
