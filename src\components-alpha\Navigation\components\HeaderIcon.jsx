import LogoSrc from "../../../assets-alpha/images/svg/logo-new.svg";
import CloseIcon from "../../../assets-alpha/images/svg/close.svg?react";

import Icon from "../../../common-alpha/Icon/Icon";

const HeaderIcon = ({ onNavShrink }) => {
  return (
    <div className="hidden max-lg:block">
      <div className="flex items-center justify-between gap-[16px]">
        <Icon className="max-h-[24px]" src={LogoSrc} alt="Prodoo logo" />
        <Icon
          onClick={onNavShrink}
          src={CloseIcon}
          className="text-[var(--dark)] cursor-pointer"
          renderSvg
        />
      </div>
    </div>
  );
};

export default HeaderIcon;
