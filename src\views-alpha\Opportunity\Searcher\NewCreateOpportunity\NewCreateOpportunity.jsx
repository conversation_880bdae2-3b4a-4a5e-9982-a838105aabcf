import { useState, useEffect, useCallback } from "react";
import { useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { isEmpty, find, map, filter, head, first, last } from "lodash";
import ConfirmDialog from "../../../../common/ConfirmDialog/ConfirmDialog";
import { notificationAction } from "../../../../actions/notification";
import {
  getShortlistsApi,
  getShortlistResumesApi,
  getOpportunityApi,
  deleteOpportunityApi,
  deleteOpportunityAndAssociationsApi,
  getSavedSearchesApi,
  saveOpportunity,
  sendOpportunityApi,
  getAllLocationsApi,
  getAllLanguagesApi,
  getDraftOpportunityDetailApi,
  removeResumesFromShortlistApi
} from "../../opportunityApi";

import moment from "moment";
import { onStateChangeAction } from "../createNewOpportunityAction";
import { getCurrenciesApi } from "../../../Collaboration/common/collaborationApi";
import { Typography, Modal, Button, Drawer } from "antd";
import NewSearcherOpportunityForm from "./component/NewSearcherOpportunityForm";
import ShortListResume from "./component/resumeResumeList";
import { isNullOrUndefined } from "../../../../utilities/helpers";
import NewSearcherOpportunitySent from "../NewSearcherOpportunitySent";
import NewSearcherOpportunityDraft from "../NewSearcherOpportunityDraft";
import ArrowLeftIcon from "../../../../assets-alpha/images/svg/arrow-left.svg?react";
import { PlusOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import { getCompaniesApi } from "../../../Workplace/WorkplaceApi";

const { Title } = Typography;

const CreateNewOpportunity = props => {
  const {
    createJobInvitation,
    setCreateJobInvitation,
    filteredJobList,
    setFilteredJobList,
    handleOnAction,
    sentInviteActive,
    setSentInviteActive,
    draftActive,
    setDraftActive,
    shortlistId,
    setShortlistId,
    refresh,
    setRefresh
  } = props;
  const [draftLength, setDraftLength] = useState(0);
  const [sentLength, setSentLength] = useState(0);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [loadDraft, setLoadDraft] = useState(false);
  const location = useLocation();
  useEffect(() => {
    if (draftLength < sentLength) {
      setFilteredJobList(sentLength);
    } else if (draftLength > sentLength) {
      setFilteredJobList(draftLength);
    } else setFilteredJobList(sentLength);
  }, [sentLength, draftLength]);
  const [state, setState] = useState({
    detailModal: false,
    windowWidth: window?.innerWidth,
    active: null,
    isResumeDetail: false,
    validation: {}
  });
  const dispatch = useDispatch();
  const {
    labels,
    selectedOpportunity,
    shortlistResumes,
    selectedSaveSearch,
    savedSearches,
    userCompanies,
    languages,
    opportunitySaveLoading,
    allLocationsList,
    allLanguagesList,
    shortlists,
    allCurrenciesList,
    selectedShortlists,
    selectedCompany,
    dialogMessage,
    yesClickCount,
    isLoading
  } = useSelector(state => ({
    labels: state.systemLabel.labels,
    ...state.createNewOpportunity
  }));

  useEffect(() => {
    getCurrencies();
    setRequestName();
    getShortlists();
    getCompanies();
    getSavedSearches();
    getAllLocations();
    getAllLanguages();
    const selectedOpp = sessionStorage.getItem("opportunity");
    if (selectedOpp) {
      const selectedOpportunity = JSON.parse(selectedOpp);
      handleOpportunityClick({ selectedOpportunity });
    }
    window.addEventListener("resize", handleResize);
    return () => {
      sessionStorage.removeItem("opportunity-new");
      sessionStorage.removeItem("opportunity");
      sessionStorage.removeItem("shortList");
      window.removeEventListener("resize", handleResize);
    };
  }, []);
  const resetFormState = () => {
    return {
      selectedOpportunity: {
        RequestName: "",
        Description: "",
        Duration: "",
        HourlyFee: "",
        StartDate: "",
        FeeCurrencyType: null,
        DurationType: "Months",
        selectedLanguages: [],
        selectedLocation: [],
        selectedShortlists: null,
        invalidRequestName: false,
        invalidStartDate: false,
        invalidSelectedCompany: false,
        invalidShortList: false,
        Countries: [],
        Language: [],
        selectedSavedSearch: "",
        invalidDurationType: "",
        invalidCompanyId: ""
      },
      shortlistResumes: [],
      selectedResume: null,
      // selectedCompany: userCompanies.length > 0 ? userCompanies[0] : {},
      selectedSaveSearch: {},
      selectedShortlists: [],
      isFetchingShortlistResume: false
    };
  };
  const handleResize = () => {
    setState(prevState => ({ ...prevState, windowWidth: window.innerWidth }));
  };

  const getShortlists = () => {
    getShortlistsApi()
      .then(response => {
        const allShortList = response?.items?.map(item => ({
          ...item,
          value: item?.ShortlistId,
          label: item?.ShortlistName
        }));
        if (response.success) {
          dispatch(
            onStateChangeAction({
              shortlists: allShortList,
              filterShortlists: allShortList
            })
          );
        }
        if (
          sessionStorage.getItem("opportunity-new") ||
          sessionStorage.getItem("opportunity")
        ) {
          const selectedItem = first(allShortList);
          handleSelectedShortlistClick(selectedItem);
        }
      })
      .catch(response => response);
  };
  const DeleteResumeFromShortlist = ({ resumeIds }) => {
    // Show loading state if needed
    dispatch(onStateChangeAction({ isLoading: true }));

    // First delete the resume
    removeResumesFromShortlistApi(shortlistId, resumeIds)
      .then(deleteResponse => {
        if (!deleteResponse.success) {
          throw new Error("Failed to delete resume");
        }

        // Then refresh the shortlist
        return getShortlistResumesApi({ ShortlistId: shortlistId });
      })
      .then(refreshResponse => {
        if (refreshResponse.success) {
          // Update state with new resume list
          dispatch(
            onStateChangeAction({
              shortlistResumes: refreshResponse.items, // Fixed variable name
              selectedResume: head(refreshResponse.items),
              isLoading: false
            })
          );
        }
      })
      .catch(error => {
        dispatch(
          onStateChangeAction({
            isLoading: false,
            error: "Failed to delete resume"
          })
        );
      });
  };
  const getCompanies = () => {
    getCompaniesApi().then(response => {
      if (response.success) {
        const { items } = response;
        dispatch(
          onStateChangeAction({
            userCompanies: map(items, ({ CompanyName, UserCompanyId }) => {
              return { label: CompanyName, value: UserCompanyId };
            }),
            selectedCompany:
              items.length > 0
                ? { label: items[0].CompanyName, value: items[0].UserCompanyId }
                : {}
          })
        );
      }
    });
  };

  const getSavedSearches = () => {
    getSavedSearchesApi().then(response => {
      if (response.success) {
        const { items } = response;
        const lastItem = last(response.items);

        if (
          sessionStorage.getItem("opportunity-new") ||
          sessionStorage.getItem("opportunity")
        ) {
          dispatch(
            onStateChangeAction({
              savedSearches: map(items, ({ SearchName, SavedSearchId }) => {
                return { label: SearchName, value: SavedSearchId };
              }),
              selectedSaveSearch:
                items.length > 0
                  ? {
                      label: lastItem.SearchName,
                      value: lastItem.SavedSearchId
                    }
                  : {}
            })
          );
        } else {
          dispatch(
            onStateChangeAction({
              savedSearches: map(items, ({ SearchName, SavedSearchId }) => {
                return { label: SearchName, value: SavedSearchId };
              })
            })
          );
        }
      }
    });
  };

  const getAllLocations = () => {
    getAllLocationsApi().then(response => {
      if (response.success) {
        const allLocationsList = response.items.map(item => ({
          ...item,
          value: item.CountryId,
          label: item.CountryName
        }));
        dispatch(onStateChangeAction({ allLocationsList }));
      }
    });
  };

  const getAllLanguages = () => {
    getAllLanguagesApi().then(response => {
      if (response.success) {
        const allLanguagesList = response.items.map(item => ({
          ...item,
          value: item.LanguageId,
          label: item.LanguageValue
        }));
        dispatch(onStateChangeAction({ allLanguagesList }));
      }
    });
  };

  const setRequestName = () => {
    if (
      sessionStorage.getItem("opportunity-new") ||
      sessionStorage.getItem("opportunity")
    ) {
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            RequestName: `Quick Opportunity at ${moment(new Date()).format(
              "D MMMM YYYY"
            )} at ${moment(new Date()).format("LTS")}`
          }
        })
      );
    }
  };

  const getCurrencies = () => {
    getCurrenciesApi()
      .then(res => {
        if (res.success) {
          const allCurrenciesList = res.items.map(item => ({
            ...item,
            value: item.CurrencyId,
            label: item.Name
          }));

          dispatch(onStateChangeAction({ allCurrenciesList }));
        }
      })
      .catch(err => console.log("Err ", err));
  };
  const handleOpportunityClick = ({ selectedOpportunity }) => {
    const { RequestId } = selectedOpportunity;
    dispatch(
      onStateChangeAction({
        isFetchingShortlist: true
      })
    );
    getDraftOpportunityDetailApi({ RequestId }).then(response => {
      if (response.success) {
        const { items } = response;
        const {
          ShortLists,
          UserCompanyId,
          SavedSearchedId,
          Languages,
          DurationType,
          Countries,
          StartDate,
          FeeCurrencyType,
          ...rest
        } = items;
        const UserCompany = filter(
          userCompanies,
          company => company.value === UserCompanyId
        )[0];
        const SavedSearches = filter(
          savedSearches,
          search => search.value === SavedSearchedId
        )[0];

        dispatch(
          onStateChangeAction({
            selectedOpportunity: {
              ...selectedOpportunity,
              selectedLanguages: Languages?.map(single => single?.LanguageId),
              DurationType: DurationType,
              StartDate: StartDate,
              FeeCurrencyType: FeeCurrencyType,
              selectedShortlists: ShortLists?.[0]?.ShortlistId,
              selectedLocation: Countries?.map(single => single?.CountryId),
              ...rest
            },
            selectedCompany: UserCompany ? UserCompany : {},
            selectedSaveSearch: SavedSearches ? SavedSearches : {},
            isFetchingShortlist: false
          })
        );
        if (ShortLists?.length) {
          handleSelectedShortlistClick(ShortLists?.[0]);
        }
      }
    });
  };

  const handleDateChange = date => {
    try {
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            invalidStartDate: false,
            StartDate: date
          }
        })
      );
    } catch (e) {
      console.log("e", e?.message);
    }
  };

  const handleOpportunityDelete = ({ selectedOpportunity, e }) => {
    e.stopPropagation();
    dispatch(
      onStateChangeAction({
        selectedOpportunity,
        dialogMessage:
          labels.InfoSearcherOpportunityDraftOpportunityDeleteConformationMsg
      })
    );
  };

  const handleYesClick = () => {
    const { RequestId } = selectedOpportunity;

    if (yesClickCount === 0) {
      dispatch(
        onStateChangeAction({
          dialogMessage:
            labels.InfoSearcherOpportunityDraftOpportunityDeleteAssociationsConformationMsg,
          yesClickCount: yesClickCount + 1
        })
      );
      return;
    }
    dispatch(
      onStateChangeAction({
        dialogMessage: ""
      })
    );

    dispatch(onStateChangeAction({ isLoading: true }));
    getOpportunityApi({ RequestId })
      .then(response => {
        if (response.success) {
          setDraftActive(false);
          const { items } = response;
          const { ShortLists, SavedSearchedId } = items;
          if (isEmpty(ShortLists)) {
            deleteOpportunityApi({ RequestId })
              .then(response => {
                if (response.success) {
                  dispatch(onStateChangeAction({ isLoading: false }));
                }
              })
              .catch(response => {
                dispatch(onStateChangeAction({ isLoading: false }));
              });
            return;
          }
          deleteOpportunityAndAssociationsApi({
            RequestId
          })
            .then(response => {
              if (response.success) {
                dispatch(onStateChangeAction({ isLoading: false }));
              }
            })
            .catch(response => {
              dispatch(onStateChangeAction({ isLoading: false }));
            });
        }
      })
      .catch(response => {
        dispatch(onStateChangeAction({ isLoading: false }));
      });
  };

  const handleNoClick = () => {
    dispatch(onStateChangeAction({ dialogMessage: "" }));
  };

  const handleFormSelectChange = (name, selectedOption) => {
    if (name === "selectedSavedSearch") {
      dispatch(
        onStateChangeAction({
          selectedSaveSearch: isNullOrUndefined(selectedOption)
            ? null
            : selectedOption
        })
      );
      return;
    }
    if (!selectedOption) return;
    if (name === "SelectedCompany") {
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            invalidSelectedCompany: false
          },
          selectedCompany: selectedOption
        })
      );
      return;
    }
    if (name === "DurationType" || name === "FeeCurrencyType") {
      setState(prevState => ({
        ...prevState,
        validation: {
          ...prevState.validation,
          feeCurrencyType: false
        }
      }));
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            [name]: selectedOption
          }
        })
      );
      return;
    }
    dispatch(
      onStateChangeAction({
        selectedOpportunity: {
          ...selectedOpportunity,
          [name]: selectedOption
        }
      })
    );
  };

  const handleLanguageSelect = (e, name, option) => {
    dispatch(
      onStateChangeAction({
        selectedOpportunity: {
          ...selectedOpportunity,
          selectedLanguages: e,
          Language: option
        }
      })
    );
  };

  const handleLocationSelect = (e, name, option) => {
    dispatch(
      onStateChangeAction({
        selectedOpportunity: {
          ...selectedOpportunity,
          selectedLocation: e,
          Countries: option
        }
      })
    );
  };

  const handleSelectedShortlistClick = useCallback(
    option => {
      dispatch(onStateChangeAction({ isLoading: true }));
      getShortlistResumesApi({ ShortlistId: option?.ShortlistId })
        .then(response => {
          if (response.success) {
            const { items } = response;
            const selectedResume = head(items);
            if (isEmpty(items)) {
              const info = {
                message: labels.InfoSearcherOpportunityDraftEmptyShortlist,
                status: "error"
              };
              dispatch(notificationAction(info));
            }
            dispatch(
              onStateChangeAction({
                selectedShortlists: [option],
                shortlistResumes: items,
                selectedResume,
                isLoading: false,
                selectedOpportunity: {
                  selectedShortlists: option?.ShortlistId,
                  [`invalidShortList`]: false
                }
              })
            );
          }
        })
        .catch(response => {
          dispatch(
            onStateChangeAction({
              isLoading: false
            })
          );
        });
    },
    [dispatch, labels.InfoSearcherOpportunityDraftEmptyShortlist]
  );
  useEffect(() => {
    if (!location?.state?.newInvitation) return;
    handleSelectedShortlistClick({
      ShortlistId:
        location?.state?.selectedShortlist?.ShortlistId ||
        first(shortlists)?.ShortlistId
    });
  }, [
    location?.state?.newInvitation,
    location?.state?.selectedShortlist,
    shortlists,
    handleSelectedShortlistClick
  ]);
  const validateField = ({ name, value }) => {
    dispatch(
      onStateChangeAction({
        selectedOpportunity: {
          ...selectedOpportunity,
          [`invalid${name}`]: !value,
          [name]: value
        }
      })
    );
  };

  const setFieldValue = ({ name, value }) => {
    dispatch(
      onStateChangeAction({
        selectedOpportunity: {
          ...selectedOpportunity,
          [name]: value
        }
      })
    );
  };

  const handleFormFieldChange = e => {
    const { name, value } = e.target;
    if (name === "RequestName") {
      validateField({ name, value });
      return;
    }
    if (name === "HourlyFee") {
      if (isNaN(value)) {
        return;
      }
      setState(prevState => ({
        ...prevState,
        validation: {
          ...prevState.validation,
          hourlyRate: false
        }
      }));
    }
    setFieldValue({ name, value });
  };

  const handleResumeSelect = selectedResume => {
    selectedResume = find(shortlistResumes, {
      ResumeId: selectedResume.ResumeId
    });
    dispatch(onStateChangeAction({ selectedResume }));
  };

  const handleSliderChange = value => {
    if (value) {
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            Duration: value
          }
        })
      );
    }
  };

  const makeMandatoryFieldRed = () => {
    dispatch(
      onStateChangeAction({
        selectedOpportunity: {
          ...selectedOpportunity,
          invalidRequestName: !undefined,
          RequestName: undefined,
          invalidStartDate: !undefined,
          StartDate: undefined,
          invalidSelectedCompany: !undefined,
          selectedCompany: undefined,
          invalidShortList: !undefined,
          shortlists: undefined
        }
      })
    );
    if (!selectedOpportunity?.HourlyFee) {
      setState(prevState => ({
        ...prevState,
        validation: { ...prevState.validation, hourlyRate: true }
      }));
    }
    if (!selectedOpportunity?.feeCurrencyType) {
      setState(prevState => ({
        ...prevState,
        validation: { ...prevState.validation, feeCurrencyType: true }
      }));
    }
  };

  const handleOpportunitySend = ({ IsSent }) => {
    const {
      RequestName,
      StartDate,
      FeeCurrencyType,
      HourlyFee,
      Duration
    } = selectedOpportunity;
    if (!RequestName && !StartDate) {
      const info = {
        message: labels.OPPORTUNITY_CREATE_REQURIED_FIELD_MESSAGE,
        status: "error"
      };
      makeMandatoryFieldRed();
      dispatch(notificationAction(info));
      return;
    }

    if (!RequestName) {
      const info = {
        message: labels.InfoSearcherOpportunityDraftTitleRequiredMsg,
        status: "error"
      };
      validateField({ name: "RequestName", value: RequestName });
      dispatch(notificationAction(info));
      return;
    }
    if (!HourlyFee) {
      const info = {
        message: labels.searcher_phillip_automate_oppertunity_hourlyrate_alert,
        status: "error"
      };
      dispatch(notificationAction(info));
      setState(prevState => ({
        ...prevState,
        validation: { ...prevState.validation, hourlyRate: true }
      }));
      return;
    }
    if (!Duration) {
      const info = {
        message: labels.Duration_Error_Message,
        status: "error"
      };
      dispatch(notificationAction(info));
      setState(prevState => ({
        ...prevState,
        validation: { ...prevState.validation, duration: true }
      }));
      return;
    }
    if (!StartDate) {
      const info = {
        message: labels.collStartDateRequired,
        status: "error"
      };
      dispatch(notificationAction(info));
      validateField({ name: "StartDate", value: StartDate });
      return;
    }
    if (!FeeCurrencyType) {
      const info = {
        message: labels.collCurrancyTypeRequired,
        status: "error"
      };
      dispatch(notificationAction(info));
      setState(prevState => ({
        ...prevState,
        validation: { ...prevState.validation, feeCurrencyType: true }
      }));
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            [`invalidFeeCurrencyType`]: !FeeCurrencyType,
            FeeCurrencyType: FeeCurrencyType
          }
        })
      );
      return;
    }
    if (isEmpty(selectedCompany)) {
      const info = {
        message: labels.oppCompanyValidationMessage,
        status: "error"
      };
      dispatch(notificationAction(info));
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            [`invalidSelectedCompany`]: true,
            selectedCompany: selectedCompany
          }
        })
      );
      validateField({ name: "selectedCompany", value: selectedCompany });
      return;
    }
    if (isEmpty(selectedShortlists)) {
      const info = {
        message: labels.InfoSearcherOpportunityDraftSendEmptyShortlistMsg,
        status: "error"
      };
      dispatch(
        onStateChangeAction({
          selectedOpportunity: {
            ...selectedOpportunity,
            [`invalidShortList`]: true
          }
        })
      );
      dispatch(notificationAction(info));
      return;
    }
    dispatch(
      onStateChangeAction({
        dialogMessage: labels.InfoSearcherOpportunityDraftSendConformationMsg
      })
    );
  };

  const handleOkClick = () => {
    let IsSent = true;
    const {
      RequestName,
      Description,
      Duration,
      HourlyFee,
      StartDate,
      FeeCurrencyType,
      DurationType
    } = selectedOpportunity;
    const LanguageIds = map(
      selectedOpportunity.Language,
      language => language.LanguageId
    );
    const LocationsIds = map(
      selectedOpportunity.Countries,
      country => country.CountryId
    );

    const ShortListIds = map(
      selectedShortlists,
      shortlist => shortlist.ShortlistId
    );
    const opportunity = {
      RequestName,
      Description,
      Duration,
      HourlyFee,
      StartDate: dayjs(StartDate).format("YYYY-MM-DD"),
      FeeCurrencyType: FeeCurrencyType?.value,
      DurationType: DurationType?.value ? DurationType?.value : "Months",
      LanguageIds,
      LocationsIds,
      ShortListIds,
      UserCompanyId: selectedCompany.value,
      SavedSearchedId: selectedSaveSearch && selectedSaveSearch.value,
      EndDate: dayjs(StartDate).format("YYYY-MM-DD"),
      IsSent,
      RequestCountries: null
    };
    dispatch(onStateChangeAction({ opportunitySaveLoading: true }));
    sendOpportunityApi({ opportunity }).then(response => {
      if (response.success) {
        setCreateJobInvitation(false);
        dispatch(onStateChangeAction(resetFormState()));
        setRefresh(prev => !prev);
        const info = {
          message: labels.InfoSearcherOpportunityCreateSent,
          status: "success"
        };
        dispatch(notificationAction(info));
        dispatch(onStateChangeAction({ opportunitySaveLoading: false }));
        dispatch(
          onStateChangeAction({
            selectedOpportunity: undefined,
            selectedShortlists: [],
            shortlistResumes: [],
            selectedResume: {},
            selectedSaveSearch: {}
          })
        );
        return;
      }
      const info = {
        message: response.message,
        status: "error"
      };
      dispatch(notificationAction(info));
      dispatch(onStateChangeAction({ opportunitySaveLoading: false }));
    });
    dispatch(onStateChangeAction({ dialogMessage: "" }));
  };
  const handleOpportunityUpdate = ({ IsSent = false }) => {
    const {
      RequestName,
      Description,
      Duration,
      HourlyFee,
      StartDate,
      FeeCurrencyType,
      DurationType
    } = selectedOpportunity;
    if (!RequestName || RequestName === null) {
      const info = {
        message: labels.InfoSearcherOpportunityDraftTitleRequiredMsg,
        status: "error"
      };
      validateField({ name: "RequestName", value: RequestName });
      dispatch(notificationAction(info));
      return;
    }

    const LanguageIds = map(
      selectedOpportunity.Language,
      language => language.LanguageId
    );
    const LocationsIds = map(
      selectedOpportunity.Countries,
      country => country.CountryId
    );

    const ShortListIds = map(
      selectedShortlists,
      shortlist => shortlist.ShortlistId
    );

    const opportunity = {
      RequestName,
      Description,
      Duration,
      HourlyFee,
      StartDate: StartDate,
      FeeCurrencyType: FeeCurrencyType?.value,
      DurationType: DurationType || "Months",
      LanguageIds,
      LocationsIds,
      ShortListIds,
      UserCompanyId: selectedCompany && selectedCompany.value,
      SavedSearchedId: selectedSaveSearch && selectedSaveSearch.value,
      EndDate: StartDate,
      IsSent: false,
      RequestCountries: null
    };
    dispatch(onStateChangeAction({ opportunitySaveLoading: true }));
    saveOpportunity({ opportunity }).then(response => {
      if (response.success) {
        setRefresh(prev => !prev);
        dispatch(onStateChangeAction(resetFormState()));
        setLoadDraft(prev => !prev);
        setCreateJobInvitation(false);
        setDraftActive(true);
        const info = {
          message: labels.InfoSearcherOpportunityDraftSavedSuccessfullyMsg,
          status: "success"
        };
        dispatch(notificationAction(info));
        dispatch(onStateChangeAction({ opportunitySaveLoading: false }));
        dispatch(
          onStateChangeAction({
            selectedOpportunity: {
              invalidRequestName: "",
              invalidStartDate: "",
              Countries: [],
              Language: [],
              selectedSavedSearch: "",
              FeeCurrencyType: "",
              DurationType: "Months",
              Duration: "",
              invalidDurationType: "",
              invalidCompanyId: ""
            },
            selectedSaveSearch: {},
            selectedShortlists: [],
            shortlistResumes: [],
            selectedResume: {}
          })
        );
      }
    });
  };

  const handleModalClick = data => {
    setState(prevState => ({
      ...prevState,
      data: data,
      detailModal: true
    }));
  };

  const handleCloseModal = () => {
    setState(prevState => ({
      ...prevState,
      data: "",
      detailModal: false
    }));
  };

  const handleListOpenMobile = () => {
    setState(prevState => ({ ...prevState, isResumeDetail: true }));
  };
  const { startDate } = selectedOpportunity;
  return (
    <>
      <div className="flex w-full bg-white overflow-auto ">
        {dialogMessage && (
          <ConfirmDialog testId="confirm-diloag">
            <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
            <ConfirmDialog.ActionButtons>
              {dialogMessage ===
              labels.InfoSearcherOpportunityDraftSendConformationMsg ? (
                <ConfirmDialog.Button
                  className="dialog-btn"
                  testId="opportunity-sent-ok"
                  onClick={handleOkClick}
                >
                  Yes
                </ConfirmDialog.Button>
              ) : (
                <ConfirmDialog.Button
                  className="dialog-btn"
                  testId="opportunity-sent-yes"
                  onClick={handleYesClick}
                >
                  Yes
                </ConfirmDialog.Button>
              )}
              <ConfirmDialog.Button
                className="dialog-btn"
                onClick={handleNoClick}
              >
                No
              </ConfirmDialog.Button>
            </ConfirmDialog.ActionButtons>
          </ConfirmDialog>
        )}

        {state?.isResumeDetail ? (
          <ShortListResume
            resumeList={shortlistResumes}
            score={0}
            handleModalClick={handleModalClick}
            name="opportunity"
            label="No Resume found"
            setEditModalOpen={setEditModalOpen}
          />
        ) : (
          <>
            <div>
              {window.innerWidth > 767 ? (
                <Modal
                  title={null}
                  footer={null}
                  closeIcon={null}
                  closable={false}
                  open={createJobInvitation}
                  centered
                  onCancel={() => {
                    dispatch(onStateChangeAction(resetFormState()));
                    setRefresh(prev => !prev);
                    setCreateJobInvitation(false);
                    dispatch(
                      onStateChangeAction({
                        selectedOpportunity: null,
                        shortlistResumes: null,
                        selectedResume: null,
                        isFetchingShortlistResume: false
                      })
                    );
                  }}
                  rootClassName={`
                   ${
                     shortlistResumes?.length > 0
                       ? "md:[&_.ant-modal]:!w-[80%] "
                       : "md:[&_.ant-modal]:!w-[40%]"
                   }                    
                   [&_.ant-modal-content]:!p-[0px]

                 `}
                >
                  <NewSearcherOpportunityForm
                    state={state}
                    shortlist={shortlists}
                    handleSelectedShortlistClick={handleSelectedShortlistClick}
                    selectedOpportunity={selectedOpportunity}
                    selectedCompany={selectedCompany}
                    selectedSaveSearch={selectedSaveSearch}
                    userCompanies={userCompanies}
                    savedSearches={savedSearches}
                    languages={languages}
                    labels={labels}
                    isLoading={opportunitySaveLoading}
                    allLocations={allLocationsList}
                    allLanguages={allLanguagesList}
                    onFormSelectChange={handleFormSelectChange}
                    onFormFieldChange={handleFormFieldChange}
                    onDateChange={handleDateChange}
                    onSliderChange={handleSliderChange}
                    onLanguageSelect={handleLanguageSelect}
                    onLocationSelect={handleLocationSelect}
                    onOpportunityUpdate={handleOpportunityUpdate}
                    onOpportunitySend={handleOpportunitySend}
                    allCurrencies={allCurrenciesList}
                    handleListOpenMobile={handleListOpenMobile}
                    resumeList={shortlistResumes}
                    score={0}
                    handleModalClick={handleModalClick}
                    name="opportunity"
                    label="No Resume found"
                    setCreateJobInvitation={setCreateJobInvitation}
                    setRefresh={setRefresh}
                    refresh={refresh}
                    setShortlistId={setShortlistId}
                    DeleteResumeFromShortlist={DeleteResumeFromShortlist}
                    draftActive={draftActive}
                    setEditModalOpen={setEditModalOpen}
                    resetFormState={resetFormState}
                  />
                </Modal>
              ) : (
                <Drawer
                  placement="right"
                  closable={true}
                  open={createJobInvitation}
                  key="placement"
                  headerStyle={{ display: "none" }}
                  onClose={() => {
                    setRefresh(prev => !prev);
                    setCreateJobInvitation(false);

                    dispatch(
                      onStateChangeAction({
                        selectedOpportunity: null,
                        shortlistResumes: null,
                        selectedResume: null,
                        isFetchingShortlistResume: false
                      })
                    );
                  }}
                  bodyStyle={{
                    padding: 0,
                    maxHeight: "955px"
                  }}
                  rootClassName=" 
                  [&_.ant-drawer-content-wrapper]:!mt-[8px]
                  [&_.ant-drawer-content]:!rounded-tl-[16px]
                  [&_.ant-drawer-content]:!rounded-tr-[16px]
                  [&_.ant-drawer-content-wrapper]:!w-[100%]
                  [&_.ant-drawer-content-wrapper]:!rounded-[16px]
                  [&_.ant-drawer-body]:p-[0px]
                  [&_.ant-drawer-body]:!scrollbar-width-none"
                >
                  <NewSearcherOpportunityForm
                    state={state}
                    shortlist={shortlists}
                    handleSelectedShortlistClick={handleSelectedShortlistClick}
                    selectedOpportunity={selectedOpportunity}
                    selectedCompany={selectedCompany}
                    selectedSaveSearch={selectedSaveSearch}
                    userCompanies={userCompanies}
                    savedSearches={savedSearches}
                    languages={languages}
                    labels={labels}
                    isLoading={opportunitySaveLoading}
                    allLocations={allLocationsList}
                    allLanguages={allLanguagesList}
                    onFormSelectChange={handleFormSelectChange}
                    onFormFieldChange={handleFormFieldChange}
                    onDateChange={handleDateChange}
                    onSliderChange={handleSliderChange}
                    onLanguageSelect={handleLanguageSelect}
                    onLocationSelect={handleLocationSelect}
                    onOpportunityUpdate={handleOpportunityUpdate}
                    onOpportunitySend={handleOpportunitySend}
                    allCurrencies={allCurrenciesList}
                    handleListOpenMobile={handleListOpenMobile}
                    resumeList={shortlistResumes}
                    score={0}
                    handleModalClick={handleModalClick}
                    name="opportunity"
                    label="No Resume found"
                    setCreateJobInvitation={setCreateJobInvitation}
                    setRefresh={setRefresh}
                    refresh={refresh}
                    setShortlistId={setShortlistId}
                    DeleteResumeFromShortlist={DeleteResumeFromShortlist}
                    draftActive={draftActive}
                    setEditModalOpen={setEditModalOpen}
                    resetFormState={resetFormState}
                  />
                </Drawer>
              )}
            </div>
          </>
        )}
        {/* <div className="flex flex-col w-full md:h-[calc(100vh_-_164px)] h-[calc(100vh_-_156px)]"> */}
        <div className="flex flex-col w-full ">
          <div className="mb-4 mt-4">
            <div
              type="link"
              className={`text-[#8E81F5] font-medium text-sm cursor-pointer ${
                draftActive ? "w-[132px]" : "w-[93px] "
              }`}
              onClick={() => {
                if (draftActive) {
                  setDraftActive(false);
                  setSentInviteActive(true);
                } else {
                  setDraftActive(true);
                  setSentInviteActive(false);
                }
              }}
            >
              {filteredJobList > 0 && (
                <div>
                  {draftActive ? (
                    <span className="flex gap-2">
                      <ArrowLeftIcon />
                      {labels?.BackTOSent_Label}
                    </span>
                  ) : (
                    labels?.View_Drafts_Label
                  )}
                </div>
              )}
            </div>
          </div>
          {draftActive ? (
            // <div className="overflow-y-auto  [scrollbar-width:'none'] [-ms-overflow-style:'none'] [&::-webkit-scrollbar]:hidden">
            <div className="overflow-y-auto">
              <NewSearcherOpportunityDraft
                // setFilteredJobList={setFilteredJobList}
                setDraftLength={setDraftLength}
                setSentLength={setSentLength}
                setCreateJobInvitation={setCreateJobInvitation}
                createJobInvitation={createJobInvitation}
                setDraftActive={setDraftActive}
                setSentInviteActive={setSentInviteActive}
                setShortlistId={setShortlistId}
                draftActive={draftActive}
                editModalOpen={editModalOpen}
                setEditModalOpen={setEditModalOpen}
                loadDraft={loadDraft}
                setLoadDraft={setLoadDraft}
                refresh={refresh}
                setRefresh={setRefresh}
                resetFormState={resetFormState}
              />
            </div>
          ) : (
            // <div className="overflow-y-auto  [scrollbar-width:'none'] [-ms-overflow-style:'none'] [&::-webkit-scrollbar]:hidden">
            <div className="overflow-y-auto">
              <NewSearcherOpportunitySent
                setSentLength={setSentLength}
                setDraftLength={setDraftLength}
                setCreateJobInvitation={setCreateJobInvitation}
                refresh={refresh}
                setRefresh={setRefresh}
                setShortlistId={setShortlistId}
                draftActive={draftActive}
                resetFormState={resetFormState}
              />
            </div>
          )}
          {filteredJobList > 0 && (
            <div className="md:!hidden !-ml-4 !flex !items-center !justify-center !w-full fixed bottom-0 h-[70px] bg-white border-t-[0.5px] border-[#EAE5FC]">
              <Button
                type="primary"
                className="!w-full !h-10 ml-4 mr-4"
                icon={<PlusOutlined />}
                onClick={() => {
                  dispatch(
                    onStateChangeAction({
                      selectedOpportunity: {
                        ...selectedOpportunity,
                        RequestName: "",
                        StartDate: "",
                        selectedLocation: "",
                        selectedShortlists: "",
                        Duration: ""
                      }
                    })
                  );
                  handleOnAction();
                  setCreateJobInvitation(true);
                }}
              >
                {filteredJobList > 0
                  ? labels.Create_Job_Invite_label
                  : labels.Create_Job_invite_title}
              </Button>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default CreateNewOpportunity;
