import { useLocation, useNavigate } from "react-router-dom";
import SegmentedUi from "../../../common-alpha/SegmentedUi/SegmentedUi";
import { useSelector } from "react-redux";
import { capitalize } from "lodash";

const SnapshotNavigation = ({ UserFeatures, disabled }) => {
  const navigate = useNavigate();
  const labels = useSelector(state => state.systemLabel.labels);
  const { pathname } = useLocation();
  const features = UserFeatures.filter(
    element => element.Url && element.Url !== "collaborations"
  );
  const selectedTab = capitalize(pathname?.split("/")[2]);
  const onTabChange = name => {
    navigate(name?.toLowerCase());
  };
  return (
    <div className={"flex items-center justify-center"}>
      <SegmentedUi
        className="!mt-[24px] !mb-[40px]"
        options={features?.map(item => labels[`${item.Name}-Tooltip`])}
        onChange={onTabChange}
        value={selectedTab}
        disabled={disabled}
      />
    </div>
  );
};

export default SnapshotNavigation;
