
const Role = props => {
  return (
    <svg
      width="35"
      height="35"
      viewBox="0 0 35 35"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M17.8142 1.4091C18.4821 1.56624 19.0871 1.84123 19.4878 2.42265C20.077 3.2712 20.1635 4.17474 19.6606 5.07829C19.1578 6.00541 18.3328 6.43754 17.2957 6.35111C16.2664 6.26469 15.5279 5.7147 15.1822 4.73259C14.6558 3.23977 15.5122 1.77838 17.0757 1.44839C17.115 1.44053 17.1543 1.41696 17.1857 1.40125C17.3978 1.4091 17.61 1.4091 17.8142 1.4091ZM17.5 5.43185C18.3485 5.43185 19.0399 4.7483 19.0478 3.89975C19.0556 3.05906 18.3564 2.35979 17.5157 2.35193C16.675 2.35193 15.9757 3.04334 15.9679 3.89189C15.96 4.74044 16.6514 5.43185 17.5 5.43185Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.7"
      />
      <path
        d="M1.40874 17.1858C1.56587 16.5337 1.84087 15.9523 2.39085 15.528C3.48297 14.6716 5.17221 14.9466 5.9579 16.1251C6.73574 17.2958 6.36646 18.9458 5.16435 19.6293C3.64011 20.4936 1.83301 19.6843 1.45588 17.9636C1.44802 17.9165 1.42445 17.8694 1.40088 17.8144C1.40874 17.6101 1.40874 17.3979 1.40874 17.1858ZM5.43149 17.5079C5.43149 16.6594 4.74793 15.968 3.89938 15.9601C3.05869 15.9523 2.35157 16.6515 2.35157 17.4922C2.35157 18.3329 3.04298 19.0322 3.88367 19.04C4.73222 19.0479 5.43149 18.3565 5.43149 17.5079Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.7"
      />
      <path
        d="M20.1712 17.9008C18.9298 20.03 16.0463 19.9671 14.8364 17.885C14.2707 18.1286 13.6735 18.325 13.1314 18.6236C12.5971 18.9143 12.4243 19.4486 12.4243 20.0378C12.4243 21.2949 12.4243 22.5521 12.4243 23.8092C12.4243 24.2963 12.275 24.5241 11.9607 24.5241C11.6464 24.5241 11.4814 24.2806 11.4814 23.8092C11.4814 22.5599 11.4814 21.3185 11.4814 20.0693C11.4814 18.8043 12.055 17.9715 13.2257 17.5079C13.8307 17.2722 14.4278 17.0286 15.0406 16.8087C15.3706 16.6908 15.5356 16.4787 15.5121 16.1251C15.5121 16.0701 15.5042 16.023 15.5121 15.968C15.5513 15.5044 15.4728 15.1194 15.1192 14.7501C14.6714 14.2709 14.5299 13.6345 14.4985 12.9745C14.4907 12.8566 14.4671 12.7152 14.4042 12.6209C13.3514 10.9317 14.4435 8.5196 16.7613 8.46461C17.5391 8.44889 18.3327 8.37032 19.1027 8.61389C20.7055 9.12459 21.4676 11.026 20.6584 12.4952C20.5641 12.6602 20.5012 12.8645 20.4934 13.053C20.4462 13.8387 20.1634 14.5144 19.6134 15.0801C19.5427 15.1508 19.4955 15.2766 19.4877 15.3787C19.472 15.638 19.4877 15.9051 19.4798 16.1644C19.472 16.4787 19.6212 16.6751 19.9041 16.7851C20.5169 17.0208 21.1298 17.2644 21.7426 17.5001C22.9369 17.9715 23.5026 18.7886 23.5104 20.0771C23.5104 21.3264 23.5104 22.5678 23.5104 23.817C23.5104 23.8799 23.5104 23.9427 23.5104 24.0056C23.479 24.3199 23.2826 24.5241 23.0233 24.5163C22.7562 24.5084 22.5754 24.312 22.5754 23.982C22.5676 23.1963 22.5754 22.4106 22.5754 21.6249C22.5754 21.0828 22.5754 20.5328 22.5754 19.9907C22.5676 19.2286 22.2219 18.6943 21.5069 18.4193C21.0591 18.2465 20.6112 18.0736 20.1712 17.9008ZM19.9984 11.4817C20.1791 10.5388 19.5191 9.57243 18.5763 9.46243C17.9399 9.38386 17.2799 9.38386 16.6356 9.43101C15.6535 9.50172 15.0249 10.1853 14.9542 11.1438C14.9385 11.3245 14.9385 11.4659 15.1664 11.5681C15.2999 11.6231 15.4021 11.8509 15.4256 12.0159C15.4649 12.2909 15.4413 12.5816 15.4413 12.8645C15.4413 14.4751 16.8713 15.3158 18.1127 14.9308C19.0005 14.6559 19.5191 13.878 19.5584 12.8409C19.5662 12.5266 19.5034 12.3931 19.1498 12.3459C18.1598 12.2045 17.2327 11.8509 16.337 11.4188C16.062 11.2852 15.9599 11.0102 16.0699 10.7667C16.1799 10.5388 16.4392 10.4524 16.722 10.5624C16.9106 10.6331 17.0834 10.7274 17.2642 10.806C18.1363 11.1517 19.032 11.4188 19.9984 11.4817ZM16.4549 15.8187C16.4942 16.5022 16.3763 17.1229 15.7242 17.5236C16.1563 18.1836 16.7456 18.5372 17.5077 18.5372C18.262 18.5293 18.8434 18.1836 19.2598 17.5236C18.5998 17.1308 18.5056 16.4865 18.537 15.8265C18.152 15.8737 17.822 15.9444 17.492 15.9444C17.1699 15.9365 16.8399 15.8658 16.4549 15.8187Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.3"
      />
      <path
        d="M4.55957 21.075C4.80314 21.075 4.90528 21.2792 4.99956 21.5149C5.34527 22.3242 5.63597 23.157 6.06025 23.9192C7.68663 26.8262 10.0987 28.814 13.2415 29.9218C13.3436 29.9533 13.4379 29.9847 13.54 30.024C13.8622 30.1497 13.9957 30.3775 13.9093 30.6525C13.8229 30.9118 13.5558 31.0297 13.2336 30.9118C12.7229 30.7311 12.2044 30.5583 11.7094 30.3304C7.95376 28.5783 5.41598 25.7341 4.09601 21.8056C4.06459 21.7192 4.04102 21.6249 4.04102 21.5307C4.04102 21.2714 4.23744 21.075 4.55957 21.075Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.7"
      />
      <path
        d="M31.0059 21.4758C30.8801 21.8608 30.7859 22.1751 30.668 22.4736C29.0023 26.6535 26.0481 29.4742 21.7818 30.912C21.4518 31.022 21.1926 30.9277 21.0983 30.6763C20.9883 30.3856 21.1297 30.142 21.5068 30.0163C23.1096 29.4899 24.5632 28.712 25.8596 27.6356C27.7767 26.0485 29.1359 24.0686 29.9452 21.7194C29.9923 21.5701 30.0473 21.4208 30.118 21.2872C30.2359 21.0908 30.4402 21.0201 30.6366 21.1065C30.7859 21.1772 30.8959 21.3501 31.0059 21.4758Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.7"
      />
      <path
        d="M4.50425 13.9331C4.15069 13.9331 3.96212 13.6267 4.08783 13.2338C4.43353 12.1338 4.91281 11.0889 5.52565 10.1068C7.3406 7.21541 9.87053 5.2276 13.1076 4.12763C13.2254 4.08835 13.3669 4.01764 13.469 4.05692C13.6261 4.11192 13.8147 4.22192 13.8933 4.35548C13.9954 4.53619 13.9247 4.74833 13.7361 4.87404C13.6261 4.94475 13.4926 4.9919 13.3669 5.03904C9.25769 6.48471 6.48419 9.25821 5.03852 13.3595C4.94424 13.6267 4.88138 13.9252 4.50425 13.9331Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.7"
      />
      <path
        d="M21.4831 3.99402C21.8681 4.12759 22.1824 4.21401 22.481 4.33187C26.6451 5.99753 29.4579 8.94388 30.8957 13.1945C31.0136 13.548 30.9193 13.8073 30.6443 13.9016C30.3615 13.9959 30.1258 13.8545 30.0001 13.4773C29.2772 11.2852 28.083 9.39172 26.3859 7.8282C25.0188 6.57109 23.4552 5.64397 21.6953 5.0547C21.5538 5.00756 21.4124 4.96042 21.2867 4.88971C21.0746 4.77185 20.996 4.55186 21.0981 4.34758C21.1846 4.20616 21.3653 4.10402 21.4831 3.99402Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.7"
      />
      <path
        d="M31.0057 15.0252C32.3257 14.9309 33.4885 15.9995 33.5828 17.3901C33.6693 18.7101 32.6243 19.8572 31.2179 19.9672C29.9136 20.0693 28.7194 19.0087 28.6329 17.6494C28.5387 16.2744 29.5679 15.1352 31.0057 15.0252ZM32.6479 17.5158C32.6557 16.6752 31.9643 15.9759 31.1157 15.9602C30.275 15.9523 29.5679 16.6437 29.5679 17.4844C29.5601 18.333 30.2436 19.0244 31.0922 19.0401C31.9329 19.0479 32.64 18.3565 32.6479 17.5158Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.7"
      />
      <path
        d="M19.9669 31.2418C19.904 32.6561 18.6705 33.6617 17.3662 33.5753C15.952 33.481 14.907 32.2946 15.0327 30.9511C15.1584 29.5212 16.3291 28.5076 17.7041 28.6333C19.0555 28.759 20.0847 29.9454 19.9669 31.2418ZM17.5155 32.6482C18.3562 32.6403 19.0555 31.9332 19.0397 31.0925C19.0319 30.244 18.3405 29.5604 17.4919 29.5683C16.6434 29.5762 15.952 30.2754 15.9598 31.1161C15.9677 31.9647 16.667 32.6561 17.5155 32.6482Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.7"
      />
      <path
        d="M14.4357 23.2828C14.4357 24.0213 14.4436 24.7677 14.4357 25.5063C14.4279 25.9305 14.0429 26.1741 13.7443 25.9463C13.6186 25.852 13.5164 25.6556 13.5164 25.5063C13.5007 24.0292 13.5007 22.5599 13.5086 21.0828C13.5086 20.7529 13.705 20.5721 13.98 20.5721C14.2707 20.5721 14.4357 20.7607 14.4436 21.1221C14.4436 21.845 14.4357 22.5599 14.4357 23.2828Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.7"
      />
      <path
        d="M20.564 23.275C20.564 22.5522 20.5562 21.8293 20.564 21.1143C20.564 20.7451 20.7526 20.5565 21.0669 20.5722C21.3262 20.5879 21.4912 20.7686 21.5069 21.0908C21.5226 21.4129 21.5147 21.735 21.5147 22.065C21.5147 23.1179 21.5147 24.1785 21.5147 25.2314C21.5147 25.3178 21.5147 25.3964 21.5147 25.4828C21.4912 25.8207 21.3104 26.0328 21.0355 26.0328C20.7605 26.0328 20.5798 25.8207 20.5798 25.475C20.5562 24.7364 20.564 24.0057 20.564 23.275Z"
        fill={props.color || "#8F82F5"}
        stroke={props.color || "#8F82F5"}
        strokeWidth="0.7"
      />
    </svg>
  );
};

export default Role;
