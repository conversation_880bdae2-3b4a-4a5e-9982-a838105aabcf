import axios from "axios";
import { ApiUrl } from "../../../../api-alpha/apiUrls";
export const UploadAndGetRolesAndSkillsFromRandomResumeApi = fileData => {
  return axios
    .post("Resumes/UploadAndGetRolesAndSkillsFromRandomResume", {
      fileData
    })
    .then(({ data }) => data);
};
export const GetSuggestedProfilesForNewResumeApi = roleIds => {
  return axios
    .post(`${ApiUrl.NextStep.GetSuggestedProfilesForNewResume}`, roleIds)
    .then(({ data }) => data)
    .catch(response => response);
};

export const GetSuggestedSkillsForNewResumeApi = roleIds => {
  return axios
    .post(ApiUrl.NextStep.GetSuggestedSkillsForNewResume, roleIds)
    .then(({ data }) => data)
    .catch(response => response);
};
export const GetMarketTopSalariesBasedOnUserRolesApi = body => {
  return axios
    .post(ApiUrl.NextStep.GetMarketTopSalariesBasedOnUserRoles, body)
    .then(({ data }) => data)
    .catch(response => response);
};
