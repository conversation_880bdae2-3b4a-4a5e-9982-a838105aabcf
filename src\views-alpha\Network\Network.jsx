import { useState, useEffect } from "react";
import {
  getNetworkusers,
  networkInvitationSend,
  sendNetworkSignupInvitation,
  getInvitedNetworkusers,
  getInvitationsNetworkusers,
  acceptOpportunity<PERSON>pi,
  declineNetworkInvite<PERSON>pi,
  declineNetworkInvitation<PERSON>pi,
  getResumeByUserId,
  acceptOpportunityApiThroughNetwork,
  getUserDetailApi,
  postShortlist<PERSON><PERSON>,
  removeNetworkConnectionApi
} from "./networkApi";
import { connect, useDispatch } from "react-redux";
import moment from "moment";
import { find } from "lodash";
import NetworkList from "./components/NetworkList";
import NetworkForm from "./components/NetworkForm";
import { notificationAction } from "../../actions/notification";
import { privateRoutes } from "../../Routes/routing";
import { useLocation, useNavigate } from "react-router-dom";
import toLower from "lodash/toLower";
import filter from "lodash/filter";
import includes from "lodash/includes";
import { onStateChangeAction } from "../../views-alpha/Portfolio/portfolioAction";
import { <PERSON><PERSON>, Drawer, Modal, Select } from "antd";
import { getNetworkPresentApi } from "../../views-alpha/Portfolio/portfolioApi";
import { getNetworkUserCompanyApi } from "../../views-alpha/Workplace/WorkplaceApi";
import { sendOpportunityApi } from "../../views-alpha/Opportunity/opportunityApi";
import PageWrapper from "../../components-alpha/PageWrapper/PageWrapper";
import EmptyView from "../../common-alpha/EmptyViewUpdated/EmptyView";
import EmptyViewNetwork from "../../assets-alpha/images/view/network-empty-view.svg?react";
import EmptyViewNetworkMain from "../../assets-alpha/images/view/network-empty-view-main.svg?react";
import { PlusOutlined } from "@ant-design/icons";
import Showcase from "./components/Showcase";
import LoadingMask from "../../common/LoadingMask/LoadingMask";

const Network = props => {
  const location = useLocation();
  const dispatch = useDispatch();
  const [status, setStatus] = useState(
    location.pathname === "/invited-networks"
      ? "invited"
      : location.pathname === "/connected-networks"
      ? "connected"
      : location.pathname === "/invitations-networks"
      ? "invitations"
      : ""
  );
  const [dataLoading, setDataLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showAddNetworkDrawer, setShowAddNetworkDrawer] = useState(false);
  const [showAddNetworkModal, setShowAddNetworkModal] = useState(false);
  const [state, setState] = useState({
    dialogMessage: "",
    networkUsers: [],
    selectedUser: [],
    filteredList: [],
    companyList: [],
    currentPresent: [],
    Profiles: [],
    Emails: [],
    isConnectedView: false,
    isInvitedView: false,
    isInvitationsView: false,
    networkData: { email: "" },
    detailCollapsed: false,
    isCreateNew: false,
    listCollapsed: false,
    active: "",
    searchKey: "",
    ShortListId: [],
    windowWidth: window?.innerWidth
  });

  const navigate = useNavigate();

  const handleResize = () => {
    setState(st => ({ ...st, windowWidth: window.innerWidth }));
  };

  useEffect(() => {
    getNetUsersApi();
    window.addEventListener("resize", handleResize);
    setState(st => ({ ...st, active: window.location.hash?.slice(1) }));
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const getNetworkUserCompanyList = selectedUser => {
    props.onStateChangeAction({ isFetching: true, isLoading: true });
    getNetworkUserCompanyApi(selectedUser?.UserId)
      .then(data => {
        let companies = data.items;
        let defaultCompany =
          companies.Companies.find(item => item.IsDefaultCompany) ||
          companies[0];
        setState(st => ({
          ...st,
          companyList: defaultCompany
        }));
        props.onStateChangeAction({
          companyList: defaultCompany,
          isLoading: false,
          isFetching: false
        });
      })
      .catch(() => {
        props.onStateChangeAction({ isLoading: false, isFetching: false });
      });
  };

  const getNetworkUserPresent = selectedUser => {
    props.onStateChangeAction({ isFetching: true, isLoading: true });
    getNetworkPresentApi(selectedUser?.UserId)
      .then(data => {
        let presentation = data.items[0];
        setState(st => ({
          ...st,
          currentPresent: presentation,
          Profiles: presentation?.Profiles,
          Emails: presentation?.UserEmails
        }));
        props.onStateChangeAction({
          isLoading: false,
          isFetching: false
        });
      })
      .catch(err => {
        props.onStateChangeAction({
          isFetching: false,
          isLoading: false
        });
      });
  };

  const getNetUsersApi = () => {
    setDataLoading(true);
    const { isConnectedView, isInvitedView, isInvitationsView } = props;
    if (isConnectedView) {
      getNetworkusers().then(data => {
        if (data.success) {
          setDataLoading(false);
          setState(st => ({
            ...st,
            networkUsers: data.items,
            filteredList: data.items,
            isConnectedView: isConnectedView,
            isInvitedView: isInvitedView,
            isInvitationsView: isInvitationsView,
            isFetching: false
          }));
          if (data.items.length > 0) {
            handleItemClick(data.items[0].UserId);
          }
        } else {
          setDataLoading(false);
        }
      });
    } else if (isInvitedView) {
      getInvitedNetworkusers().then(data => {
        setDataLoading(true);
        if (data.success) {
          setDataLoading(false);
          setState(st => ({
            ...st,
            networkUsers: data.items,
            filteredList: data.items,
            isConnectedView: isConnectedView,
            isInvitedView: isInvitedView,
            isInvitationsView: isInvitationsView,
            isFetching: false
          }));
          if (data.items.length > 0) {
            handleItemClick(data.items[0].UserId);
          }
        } else {
          setDataLoading(false);
        }
      });
    } else if (isInvitationsView) {
      getInvitationsNetworkusers().then(data => {
        setDataLoading(true);
        if (data.success) {
          setDataLoading(false);
          setState(st => ({
            ...st,
            networkUsers: data.items,
            filteredList: data.items,
            isConnectedView: isConnectedView,
            isInvitedView: isInvitedView,
            isInvitationsView: isInvitationsView,
            isFetching: false
          }));
          if (data.items.length > 0) {
            handleItemClick(data.items[0].UserId);
          }
        } else {
          setDataLoading(false);
        }
      });
    }
  };

  const handleAcceptNetwork = obj => {
    setLoading(true);
    acceptOpportunityApi(obj.UserId).then(data => {
      if (data.success) {
        setLoading(false);
        setShowAddNetworkDrawer(false);
        let users = getNetUsersApi();
        if (users == null) {
          setState(st => ({
            ...st,
            selectedUser: [],
            companyList: [],
            currentPresent: [],
            Profiles: [],
            Emails: []
          }));
        }
        const info = {
          message: data.message,
          status: "success"
        };
        props.notificationAction(info);
      } else {
        setLoading(false);
        const info = {
          message: data.message,
          status: "error"
        };
        props.notificationAction(info);
      }
    });
  };

  const handleDeleteNetwork = obj => {
    setLoading(true);
    const { isInvitedView, isInvitationsView } = props;
    if (isInvitedView) {
      declineNetworkInviteApi(obj.UserId).then(data => {
        if (data.success) {
          setShowAddNetworkDrawer(false);
          setLoading(false);
          let users = getNetUsersApi();
          if (users == null) {
            setState(st => ({
              ...st,
              selectedUser: [],
              companyList: [],
              currentPresent: [],
              Profiles: [],
              Emails: []
            }));
          }
          const info = {
            message: data.message,
            status: "success"
          };
          props.notificationAction(info);
        } else {
          const info = {
            message: data.message,
            status: "error"
          };
          props.notificationAction(info);
        }
      });
    } else if (isInvitationsView) {
      declineNetworkInvitationApi(obj.UserId).then(data => {
        if (data.success) {
          setShowAddNetworkDrawer(false);
          setLoading(false);
          let users = getNetUsersApi();
          if (users == null) {
            setState(st => ({
              ...st,
              selectedUser: [],
              companyList: [],
              currentPresent: [],
              Profiles: [],
              Emails: []
            }));
          }
          const info = {
            message: data.message,
            status: "success"
          };
          props.notificationAction(info);
        } else {
          setLoading(false);
          const info = {
            message: data.message,
            status: "error"
          };
          props.notificationAction(info);
        }
      });
    }
  };

  const handleSendSignUpInvitationClick = () => {
    setLoading(true);
    const { Email } = state.networkData;
    sendNetworkSignupInvitation(Email).then(data => {
      if (data.success) {
        setLoading(false);
        const info = {
          message: data.message,
          status: "success"
        };
        setState(st => ({
          ...st,
          networkData: {
            ...st.networkData,
            Email: ""
          }
        }));
        props.notificationAction(info);
      } else {
        setLoading(false);
        const info = {
          message: data.message,
          status: "error"
        };
        props.notificationAction(info);
      }
    });
    setState(st => ({ ...st, dialogMessage: "" }));
  };

  const handleItemClick = id => {
    const { networkUsers } = state;
    const selectedUser = find(networkUsers, { UserId: id });
    if (!selectedUser) {
      return;
    }
    getNetworkUserCompanyList(selectedUser);
    getNetworkUserPresent(selectedUser);
    setState(st => ({
      ...st,
      selectedUser,
      isCreateNew: false
    }));
  };

  const handleFormFieldChange = e => {
    const { name, value } = e.target;
    const { networkData } = state;
    setState(st => ({
      ...st,
      networkData: {
        ...networkData,
        [name]: value
      }
    }));
  };

  const handleNetworkInvitationSend = () => {
    setLoading(true);

    const { Email } = state.networkData;
    if (!Email) {
      const info = {
        message: "Please Enter Email",
        status: "error"
      };
      props.notificationAction(info);
      return;
    }
    networkInvitationSend(Email).then(data => {
      if (data.success) {
        setLoading(false);
        setShowAddNetworkModal(false);
        const { isInvitedView } = props;
        if (isInvitedView) {
          getInvitedNetworkusers().then(data => {
            if (data.success) {
              setLoading(false);
              setState(st => ({
                ...st,
                networkUsers: data.items,
                filteredList: data.items,
                isInvitedView: isInvitedView,
                isFetching: false
              }));
              if (data.items.length > 0) {
                handleItemClick(data.items[0].UserId);
              }
            }
          });
        }
        const info = {
          message: data.message,
          status: "success"
        };
        setState(st => ({
          ...st,
          networkData: {
            ...st.networkData,
            Email: ""
          }
        }));
        props.notificationAction(info);
      } else {
        setLoading(false);
        setShowAddNetworkModal(false);
        const info = {
          message: data.message,
          status: "error"
        };
        if (
          info.message
            .toLowerCase()
            .includes("given email does not exist at prodoo")
        )
          handleSendSignUpInvitationClick();
        else props.notificationAction(info);
      }
    });
  };
  const getShortlist = (user, ResumeIds, resumeShortlistId) => {
    let loggedUser = JSON.parse(localStorage.getItem("User"));
    getNetworkUserCompanyApi(loggedUser.UserId)
      .then(company => {
        let companies = company.items;
        let companyList =
          companies.Companies?.find(item => item.IsDefaultCompany) ||
          companies.Companies[0];
        if (!companyList) {
          const info = {
            message: props.labels.NETWORK_NO_COMPANY_MESSAGE,
            status: "error"
          };
          props.notificationAction(info);
          return;
        }
        const opportunity = {
          RequestName: `Quick Opportunity from Network for ${
            user.UserFirstname
          } ${user.UserLastname} at ${moment(new Date()).format(
            "D MMMM YYYY"
          )} at ${moment(new Date()).format("LTS")}`,
          ShortListIds: resumeShortlistId,
          UserCompanyId: companyList.UserCompanyId,
          IsSent: true
        };
        sendOpportunityApi({ opportunity })
          .then(() => {
            getOpportunity(ResumeIds, user);
          })
          .catch(err => {
            const info = {
              message: props.labels.NETWORK_OPPORTUNITY_ERROR_MESSAGE,
              status: "error"
            };
            props.notificationAction(info);
          });
      })
      .catch(err => {
        const info = {
          message: props.labels.NETWORK_COMPANY_ERROR_MESSAGE,
          status: "error"
        };
        props.notificationAction(info);
      });
  };

  const getOpportunity = (ResumeIds, user) => {
    getOpportunity()
      .then(data => {
        if (data.success) {
          JSON.stringify(
            localStorage.setItem("OpportunityId", data.items.RequestId)
          );
          getUserDetailApi(user.UserId)
            .then(UserDetail => {
              if (!UserDetail.items) {
                const info = {
                  message: props.labels.presentationNotFound,
                  status: "error"
                };
                props.notificationAction(info);
                return;
              } else {
                const acceptOpportunity = {
                  RequestId: data.items.RequestId,
                  ResumeId: ResumeIds[0],
                  UserDetailId: UserDetail.items.UserDetailId
                };
                acceptOpportunityApiThroughNetwork(acceptOpportunity)
                  .then(() => {
                    navigate(privateRoutes.searcherCreateCollaboration.path);
                  })
                  .catch(err => {
                    props.notificationAction(err);
                  });
              }
            })
            .catch(err => {
              props.notificationAction(err);
            });
        }
      })
      .catch(err => {
        props.notificationAction(err);
      });
  };
  const { selectedUser, filteredList, networkData } = state;
  const { labels } = props;

  const handleRemoveConnectedNetwork = () => {
    setLoading(true);
    removeNetworkConnectionApi(selectedUser?.UserId).then(data => {
      if (data?.success) {
        setLoading(false);
        const info = {
          message: data?.message,
          status: "success"
        };
        dispatch(notificationAction(info));
        setShowAddNetworkDrawer(false);
        getNetUsersApi();
      } else {
        setLoading(false);
      }
    });
  };
  return (
    <>
      {dataLoading ? (
        <LoadingMask />
      ) : (
        <PageWrapper className={"!p-0"}>
          <div className="flex w-full h-full">
            <div
              className={`${
                filteredList?.length > 0 ? "w-full md:w-[44%]" : "w-full"
              } md:p-6 p-4 `}
            >
              <div className="flex flex-col h-full">
                <h1 className="!m-0 !font-semibold !text-2xl !text-[#343333]">
                  Network
                </h1>
                <div className="flex gap-[6px] w-full rounded-[99px] bg-[#F4F2FE] max-w-[333px] min-h-[36px] mt-3">
                  <span
                    className={`flex justify-center items-center cursor-pointer text-sm text-[#878787] font-medium leading-4 w-[116px] ${
                      status === "connected" ? "!bg-[#8E81F5] !text-white" : ""
                    } rounded-tl-[99px] rounded-bl-[99px]`}
                    onClick={() => {
                      navigate(privateRoutes.connectedNetwork.path);
                    }}
                  >
                    Connected
                  </span>
                  <span
                    className={`flex justify-center items-center cursor-pointer text-sm text-[#878787] font-medium leading-4 w-[89px] ${
                      status === "invited" ? "!bg-[#8E81F5] text-white" : ""
                    }`}
                    onClick={() => {
                      navigate(privateRoutes.invitedNetwork.path);
                    }}
                  >
                    Invited
                  </span>
                  <span
                    className={`flex justify-center items-center cursor-pointer text-sm text-[#878787] font-medium leading-4 w-[116px] ${
                      status === "invitations" ? "!bg-[#8E81F5] text-white" : ""
                    } rounded-tr-[99px] rounded-br-[99px]`}
                    onClick={() => {
                      navigate(privateRoutes.invitationNetwork.path);
                    }}
                  >
                    Invitations
                  </span>
                </div>

                {filteredList?.length > 0 ? (
                  <NetworkList
                    UsersList={filteredList}
                    handleNetworkClick={handleItemClick}
                    handleDeleteNetwork={handleDeleteNetwork}
                    isInvitedView={state.isInvitedView}
                    selectedUser={selectedUser}
                    labels={props?.labels}
                    setShowAddNetworkDrawer={setShowAddNetworkDrawer}
                    state={state}
                    dataLoading={dataLoading}
                  />
                ) : (
                  <div className="flex w-full justify-center items-center !h-full">
                    <EmptyView
                      icon={<EmptyViewNetworkMain />}
                      title={"Your network, your power 💼"}
                      description={
                        "Start connecting with professionals to grow your circle. Once a connection accepts your invite, you'll see them."
                      }
                      actionText={"Add network"}
                      btnIcon={<PlusOutlined />}
                      onAction={() => {
                        setShowAddNetworkModal(true);
                      }}
                    />
                  </div>
                )}
              </div>
            </div>
            {filteredList?.length > 0 && (
              <div className="w-[66%] h-full hidden md:flex justify-center items-center border-l-[0.5px] border-[#EAE5FC]">
                <EmptyView
                  icon={<EmptyViewNetwork />}
                  title={
                    "Click on someone from the list on the left to see more info and start interacting."
                  }
                  description={
                    "Want to create a new one? Click the button below"
                  }
                  actionText={"Add network"}
                  btnIcon={<PlusOutlined />}
                  onAction={() => {
                    setShowAddNetworkModal(true);
                  }}
                />
              </div>
            )}
          </div>
          {filteredList?.length > 0 && (
            <div className="md:hidden flex w-full h-[64px] bg-white fixed bottom-0 justify-center items-center  border-t-[0.5px] border-[#EAE5FC] gap-[9px] pl-4 pr-4">
              <Button
                type="primary"
                className="!m-0 !w-full !h-10 pointer"
                onClick={() => {
                  setShowAddNetworkModal(true);
                }}
              >
                <PlusOutlined /> Add Network
              </Button>
            </div>
          )}
          <div className="[&_.ant-drawer-body]:[-ms-overflow-style:none] [&_.ant-drawer-body]:[scrollbar-width:none] [&_.ant-drawer-body::-webkit-scrollbar]:[display:none] [&_.ant-drawer-body::-webkit-scrollbar]:[width:0] [&_.ant-drawer-body::-webkit-scrollbar]:[height:0]">
            <Drawer
              open={showAddNetworkDrawer}
              placement="right"
              headerStyle={{ display: "none" }}
              onClose={() => {
                setShowAddNetworkDrawer(false);
              }}
              rootClassName=" 
          sm:[&_.ant-drawer-content-wrapper]:!m-[8px]
          [&_.ant-drawer-content-wrapper]:!mt-[8px] 
          md:[&_.ant-drawer-content]:!rounded-[16px]
          [&_.ant-drawer-content]:!rounded-tl-[16px]
          [&_.ant-drawer-content]:!rounded-tr-[16px]
          md:[&_.ant-drawer-content-wrapper]:!w-[371px]
          [&_.ant-drawer-content-wrapper]:!w-[100%]
          [&_.ant-drawer-content-wrapper]:!rounded-[16px]
          [&_.ant-drawer-body]:!p-[0px]"
            >
              <Showcase
                selectedUser={selectedUser}
                UsersList={filteredList}
                setShowAddNetworkDrawer={setShowAddNetworkDrawer}
                setShowAddNetworkModal={setShowAddNetworkModal}
                state={state}
                status={status}
                loading={loading}
                handleAcceptNetwork={handleAcceptNetwork}
                handleDeleteNetwork={handleDeleteNetwork}
                handleRemoveConnectedNetwork={handleRemoveConnectedNetwork}
              />
            </Drawer>
          </div>
          {window.innerWidth > 767 ? (
            <div>
              <Modal
                zIndex={9999}
                open={showAddNetworkModal}
                onCancel={() => {
                  setShowAddNetworkModal(false);
                }}
                centered
                footer={null}
                width={448}
                closable={false}
                rootClassName="[&_.ant-modal-content]:!p-0"
              >
                <NetworkForm
                  onFormFieldChange={handleFormFieldChange}
                  networkData={networkData}
                  labels={labels}
                  setShowAddNetworkModal={setShowAddNetworkModal}
                  handleNetworkInvitationSend={handleNetworkInvitationSend}
                  loading={loading}
                />
              </Modal>
            </div>
          ) : (
            <Drawer
              open={showAddNetworkModal}
              placement="right"
              headerStyle={{ display: "none" }}
              onClose={() => {
                setShowAddNetworkModal(false);
              }}
              rootClassName=" 
          sm:[&_.ant-drawer-content-wrapper]:!m-[8px]
          [&_.ant-drawer-content-wrapper]:!mt-[8px] 
          md:[&_.ant-drawer-content]:!rounded-[16px]
          [&_.ant-drawer-content]:!rounded-tl-[16px]
          [&_.ant-drawer-content]:!rounded-tr-[16px]
          md:[&_.ant-drawer-content-wrapper]:!w-[371px]
          [&_.ant-drawer-content-wrapper]:!w-[100%]
          [&_.ant-drawer-content-wrapper]:!rounded-[16px]
          [&_.ant-drawer-body]:!p-[0px]"
            >
              <NetworkForm
                onFormFieldChange={handleFormFieldChange}
                networkData={networkData}
                labels={labels}
                setShowAddNetworkModal={setShowAddNetworkModal}
                handleNetworkInvitationSend={handleNetworkInvitationSend}
                loading={loading}
              />
            </Drawer>
          )}
        </PageWrapper>
      )}
    </>
  );
};

const mapStateToProps = ({ systemLabel, navigation, feedback }) => {
  const { labels } = systemLabel;
  const { isHelpActive } = navigation;
  return { labels, isHelpActive, ...feedback };
};

const mapActionToProps = {
  onStateChangeAction,
  notificationAction
};

export default connect(mapStateToProps, mapActionToProps)(Network);
