import { Button } from "antd";

const EmptyView = ({
  icon,
  title,
  description,
  actionText,
  onAction,
  btnicon
}) => {
  return (
    <div className="flex flex-col items-center w-[303px] md:w-[491px]  !gap-5">
      <div>{icon}</div>
      <div className="flex flex-col w-full items-center gap-2 ">
        <h1 className="!m-0 !font-semibold !text-[14px] md:!text-[16px] !text-[#343333] !leading-[100%]">
          {title}
        </h1>
        <p className="!font-medium !text-[13px] text-center md:!text-[14px] !leading-[21px] !text-[#878787] pl-0 pr-0 md:pl-9 md:pr-9">
          {description}
        </p>
      </div>
      <div>
        <Button
          type="primary"
          className="w-[156px] !h-10"
          onClick={onAction}
          icon={btnicon}
        >
          {actionText}
        </Button>
      </div>
    </div>
  );
};

export default EmptyView;
