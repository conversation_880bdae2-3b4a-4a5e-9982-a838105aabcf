.column-secend-main {
  // height: 100%;
  overflow: auto;
  flex: 1;
  .column-checkbox {
    display: flex;
    justify-content: center;
    @media (max-width: 767.98px) {
      display: none;
    }
  }
  .column-resume-list {
    margin-left: 16px;
    background: #fff;
    border-radius: 12px;
    padding: 12px;
    cursor: pointer;
    &:hover {
      box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    }
    @media (max-width: 767.98px) {
      margin-left: 0px;
    }
  }
  .infinite-scroll-component__outerdiv {
    height: 100%;
    width: 100%;
    .infinite-scroll-component {
      background: #f3f1fd;
      border-radius: 12px;
      padding: 10px 16px;
      @media (max-width: 767.98px) {
        padding: 10px 6px;
      }
    }
  }

  .bg-gray {
    background: #f3f1fd;
  }
  .bg-white {
    background: #fff;
  }
}
