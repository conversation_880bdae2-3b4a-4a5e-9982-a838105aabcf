import { Component } from "react";
import { connect } from "react-redux";
import "./about.scss";
import Img1 from "../../../../assets/images/snapshot/robert.png";
import Img2 from "../../../../assets/images/snapshot/henrik.png";
const localLabels = {
  aboutPageTitle: "About Prodoo",
  aboutPageSubHeading:
    "ProDoo is a global freelancing platform to find online jobs, earn from home, and grow your career with fast payments, low fees, and remote work ease.",
  ceoName: "<PERSON> - CEO",
  ceoEmail: "<EMAIL>",
  cfoName: "<PERSON>",
  cfoEmail: "<EMAIL>",
  ctoName: "Nauman Sharif - CTO",
  ctoEmail: "<EMAIL>"
};
class Professional extends Component {
  render() {
    const { labels } = this.props;
    return (
      <div className="about-page mainContent darkBg" id="aboutUs">
        <section className="contentContainer">
          <h1 className="proHeading">{localLabels.aboutPageTitle}</h1>
          <div className="aboutPanel">
            <h6 className="subHeading">{localLabels.aboutPageSubHeading}</h6>
            <ul className="tabList">
              <li className="tabItem">
                <img className="tabImg purple" src={Img1} alt="img" />
                <h5>{localLabels.ceoName}</h5>
                <p>{localLabels.ceoEmail}</p>
              </li>
              <li className="tabItem">
                <img className="tabImg orange" src={Img2} alt="img" />
                <h5>{localLabels.cfoName}</h5>
                <p>{localLabels.cfoEmail}</p>
              </li>
              {/* <li className="tabItem">
                <img className="tabImg green" src={Img3} alt="img" />
                <h5>{localLabels.ctoName}</h5>
                <p>{localLabels.ctoEmail}</p>
              </li> */}
            </ul>
          </div>
        </section>
      </div>
    );
  }
}

const mapStateToProps = ({ systemLabel }) => {
  const { labels } = systemLabel;
  return { labels };
};
export default connect(mapStateToProps)(Professional);
