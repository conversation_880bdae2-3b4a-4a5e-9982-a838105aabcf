@use "../../../../assets/sass/variables" as *;
@use "../../../../assets/sass/mixins" as *;

.socialLinks {
  display: flex;
  gap: 15px;
  align-items: center;
  .socialLink {
    cursor: pointer;
    font-size: 25px;
    &.activeSocialLink,
    &:hover {
      color: $lightPurple;
    }
  }
}

.editProfileButton {
  @include breakpoint("screen991") {
    position: absolute !important;
    right: 10px;
    top: 10px;
  }
}
.experience_debounce_select {
  display: "inline-block" !important;
  width: calc(50% - 12px) !important;
  margin-right: 5px !important;
}
.experienceDescriptionInput {
  height: 130px;
  margin-bottom: 55px;
}

.RichTextEditor {
  border: solid;
}
.date_input {
  display: inline-block;
  width: calc(50% - 12px);
  margin-right: 5px;
}
.checkbox_style {
  display: inline-block;
  margin-top: 6%;
  width: calc(20% - 12px);
}
.popUpForm {
  h5 {
    font-size: 18px;
    font-weight: 400;
  }
  .customSelect {
    span {
      font-size: 12px;
    }
  }
}
