import MessagesReact from "./MessagesReact";

const Messages = () => {
  return <MessagesReact />;

  // Validate user before connecting
  const validateUser = async (userId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/users/${userId}/validate`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const result = await response.json();
      return result;
    } catch (error) {
      console.error('❌ User validation failed:', error);
      return { userId: userId, isValid: false };
    }
  };

  // Initialize SignalR connection
  const initializeSignalR = useCallback(async () => {
    const newConnection = new HubConnectionBuilder()
      .withUrl(`${API_BASE_URL}/hubs/messages`)
      .withAutomaticReconnect()
      .configureLogging(LogLevel.Information)
      .build();

    // Connection events
    newConnection.onclose(() => {
      setConnectionStatus({ connected: false, message: "Disconnected" });
    });

    newConnection.onreconnecting(() => {
      setConnectionStatus({ connected: false, message: "Reconnecting..." });
    });

    newConnection.onreconnected(() => {
      setConnectionStatus({ connected: true, message: "Connected" });
      if (currentUserId) {
        joinUserGroup(currentUserId);
      }
    });

    // SignalR event handlers
    newConnection.on("ConversationCreated", (data) => {
      console.log("New conversation created:", data);
      handleNewConversation(data);
    });

    newConnection.on("NewMessage", (data) => {
      console.log("New message received:", data);
      handleNewMessage(data);
    });

    newConnection.on("UserStatusChanged", (data) => {
      console.log("👤 Received UserStatusChanged notification:", data);
      updateUserStatus(parseInt(data.UserId), data.IsOnline);
    });

    setConnection(newConnection);
    return newConnection;
  }, [API_BASE_URL, currentUserId]);

  // Join user group for notifications
  const joinUserGroup = async (userId) => {
    if (connection && connection.state === "Connected") {
      try {
        await connection.invoke("JoinUserGroup", userId.toString());
        console.log(`Joined user group for User ${userId}`);
        await loadCurrentUserStatus(userId);
      } catch (err) {
        console.error("Failed to join user group:", err);
      }
    }
  };

  // Load current user's status
  const loadCurrentUserStatus = async (userId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/users/${userId}/status`);
      if (response.ok) {
        const status = await response.json();
        setUserStatuses(prev => ({
          ...prev,
          [userId]: {
            isOnline: status.isOnline,
            lastSeen: new Date(status.lastSeenUtc)
          }
        }));
        console.log(`📊 Current user status loaded - User ${userId}: ${status.isOnline ? 'Online' : 'Offline'}`);
      } else {
        console.error('Failed to load current user status:', response.status);
      }
    } catch (error) {
      console.error('Error loading current user status:', error);
    }
  };

  // Load conversations for a user
  const loadConversations = async (userId) => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/conversations/initiator/${userId}`);
      if (response.ok) {
        const conversationsData = await response.json();
        setConversations(conversationsData);
        await loadUserStatuses(conversationsData);
      } else {
        console.error('Failed to load conversations:', response.status);
        antMessage.error('Failed to load conversations');
      }
    } catch (error) {
      console.error("Error loading conversations:", error);
      antMessage.error('Unable to connect to server. Please check your connection.');
    } finally {
      setLoading(false);
    }
  };

  // Load user statuses for all conversation participants
  const loadUserStatuses = async (conversationsData) => {
    if (conversationsData.length === 0) return;

    const userIds = [...new Set(
      conversationsData.flatMap(conv => [conv.initiatorId, conv.recipientId])
        .filter(id => id !== currentUserId)
    )];

    if (userIds.length === 0) return;

    try {
      const response = await fetch(`${API_BASE_URL}/users/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ userIds })
      });

      if (response.ok) {
        const statuses = await response.json();
        const statusMap = {};
        statuses.forEach(status => {
          statusMap[status.userId] = {
            isOnline: status.isOnline,
            lastSeen: new Date(status.lastSeenUtc)
          };
        });
        setUserStatuses(prev => ({ ...prev, ...statusMap }));
        console.log('📊 User statuses loaded:', statusMap);
      } else {
        console.error('Failed to load user statuses:', response.status);
      }
    } catch (error) {
      console.error('Error loading user statuses:', error);
    }
  };

  // Update user status and refresh UI
  const updateUserStatus = (userId, isOnline) => {
    setUserStatuses(prev => ({
      ...prev,
      [userId]: {
        isOnline: isOnline,
        lastSeen: new Date()
      }
    }));
    console.log(`👤 User ${userId} is now ${isOnline ? 'online' : 'offline'}`);
  };

  // Load messages for a conversation
  const loadMessages = async (conversationId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/messages/conversation/${conversationId}`);
      if (response.ok) {
        const messagesData = await response.json();
        setMessages(messagesData);
      } else {
        console.error('Failed to load messages:', response.status);
        antMessage.error('Failed to load messages');
      }
    } catch (error) {
      console.error("Error loading messages:", error);
      antMessage.error('Unable to load messages');
    }
  };

  // Send a message
  const sendMessage = async () => {
    const content = messageInput.trim();
    if (!content || !currentConversationId || !currentUserId) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          conversationId: currentConversationId,
          senderId: currentUserId,
          content: content,
          type: 'Text'
        })
      });

      if (response.ok) {
        // Add message to UI immediately for the sender
        const newMessage = {
          senderId: currentUserId,
          content: content,
          sentDate: new Date().toISOString(),
          messageType: 'Text'
        };

        setMessages(prev => [...prev, newMessage].sort((a, b) => new Date(a.sentDate) - new Date(b.sentDate)));
        setMessageInput('');
      } else {
        antMessage.error('Failed to send message. Please try again.');
      }
    } catch (error) {
      console.error("Error sending message:", error);
      antMessage.error('Failed to send message. Please try again.');
    }
  };

  // Handle new conversation from SignalR
  const handleNewConversation = (data) => {
    if (data.initiatorId === currentUserId || data.recipientId === currentUserId) {
      const existingConv = conversations.find(c => c.conversationId === data.conversationId);
      if (!existingConv) {
        const newConversation = {
          conversationId: data.conversationId,
          title: data.title,
          initiatorId: data.initiatorId,
          recipientId: data.recipientId,
          createdDate: data.timestamp,
          lastMessage: 'New conversation started'
        };
        setConversations(prev => [newConversation, ...prev]);
      }
    }
  };

  // Handle new message from SignalR
  const handleNewMessage = (data) => {
    // Update conversation list with latest message
    setConversations(prev => {
      const updatedConversations = prev.map(conv => {
        if (conv.conversationId === data.conversationId) {
          return {
            ...conv,
            lastMessage: data.content,
            lastMessageTime: data.timestamp
          };
        }
        return conv;
      });

      // Move updated conversation to top
      const updatedConv = updatedConversations.find(c => c.conversationId === data.conversationId);
      if (updatedConv) {
        const otherConversations = updatedConversations.filter(c => c.conversationId !== data.conversationId);
        return [updatedConv, ...otherConversations];
      }
      return updatedConversations;
    });

    // If this message is for the currently open conversation and not from current user
    if (currentConversationId === data.conversationId && data.senderId !== currentUserId) {
      const newMessage = {
        senderId: data.senderId,
        content: data.content,
        sentDate: data.timestamp,
        messageType: data.messageType || 'Text'
      };
      setMessages(prev => [...prev, newMessage].sort((a, b) => new Date(a.sentDate) - new Date(b.sentDate)));
    }
  };

  // Main connection handler
  const handleLoadConversations = async () => {
    const userId = parseInt(userIdInput);
    if (!userId) {
      antMessage.error('Please enter a valid User ID');
      return;
    }

    try {
      // Step 1: Validate user first
      setConnectionStatus({ connected: false, message: "Validating user..." });
      const validation = await validateUser(userId);

      if (!validation.isValid) {
        setConnectionStatus({ connected: false, message: "❌ User validation failed" });
        antMessage.error(`User ${userId} is not valid or not found in the system. Please check the user ID and make sure the user exists in the Users module.`);
        return;
      }

      console.log('✅ User validated:', validation);

      // Step 2: Initialize SignalR connection
      setConnectionStatus({ connected: false, message: `Initializing connection for ${validation.displayName || `User ${userId}`}...` });

      const newConnection = await initializeSignalR();

      // Step 3: Connect to hub
      setConnectionStatus({ connected: false, message: `Connecting to messaging hub...` });
      await newConnection.start();

      // Step 4: Join user group and load conversations
      setCurrentUserId(userId);
      setConnection(newConnection);
      await joinUserGroup(userId);
      await loadConversations(userId);

      // Step 5: Update status to connected
      setConnectionStatus({ connected: true, message: `Connected as ${validation.displayName || `User ${userId}`}` });

    } catch (error) {
      console.error('❌ Connection failed:', error);
      setConnectionStatus({ connected: false, message: "❌ Connection failed" });
      antMessage.error(`Failed to connect: ${error.message}`);
    }
  };

  // Select conversation
  const selectConversation = async (conversationId) => {
    setCurrentConversationId(conversationId);
    await loadMessages(conversationId);
  };

  // Utility functions
  const getUserInitials = (displayName) => {
    if (!displayName) return '?';
    return displayName.split(' ')
      .map(name => name.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('');
  };

  const getOtherUserInfo = (conversation) => {
    const isInitiator = conversation.initiatorId === currentUserId;
    return isInitiator ? conversation.recipientInfo : conversation.initiatorInfo;
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Cleanup connection on unmount
  useEffect(() => {
    return () => {
      if (connection) {
        connection.stop();
      }
    };
  }, [connection]);

  return (
    <PageWrapper className={"!p-0"}>
      {!allProfiles?.length > 0 ? (
        <div className="flex w-full h-full">
          {/* Left Sidebar - Conversations / Profiles */}
          <div className="flex flex-col gap-4 w-[26%] m-6 mb-4">
            <h1 className="!m-0 !text-[#343333] !text-2xl !font-semibold leading-5">
              Inbox
            </h1>
            <div className="flex flex-col gap-3 w-full">
              <div className="hidden md:block">
                <Input
                  placeholder="Search "
                  size="medium"
                  bordered={false}
                  className=" !border !border-[#F3F3F3] !bg-[#F3F1FD] !h-11 placeholder:text-[#878787] ![--ant-input-padding-inline:12px] [&_.ant-input-affix-wrapper]:!p-[3px_3px_3px_16px] [&_.ant-input-prefix]:!border-0 [&_.ant-input-prefix]:!mr-0 hover:!border-[#8E81F5]"
                  rootClassName="
                      [&_.ant-input]:!ml-[6px]"
                  prefix={<SearchIcon />}
                />
              </div>

              {/* Dummy Profiles for UI */}
              <div className="flex flex-col gap-2 w-full h-[calc(100vh-198px)] overflow-y-auto">
                {[1, 2, 3, 4, 5].map((item, index) => (
                  <div
                    key={index}
                    className="flex w-full gap-4 !p-3 border-[0.5px] border-[#EAE5FC] !rounded-2xl justify-between items-center pointer"
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex gap-4 items-center">
                        <div className="md:!w-11 md:!h-11 !h-10 !w-10 !rounded-[50%]">
                          <img
                            className="md:!w-11 md:!h-11 !h-10 !w-10 !rounded-[50%]"
                            src={AvatarIcon}
                            alt={"Logo"}
                          />
                        </div>
                        <div className="flex flex-col gap-2">
                          <label className="text-[var(--dark)] text-sm font-medium leading-[100%]">
                            Adeel Abid
                          </label>
                          <p className="text-[var(--gray-3)] text-xs font-normal leading-[100%]">
                            React JS Frontend Developer
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Sidebar - Messages */}
          <div className="relative flex items-center justify-center w-[74%] border-l-[0.5px] border-[#EAE5FC]">
            {messages?.length > 0 ? (
              <div className="flex flex-col gap-2 p-4 w-full">
                {messages.map((msg, idx) => (
                  <div
                    key={idx}
                    className="bg-[#F3F1FD] text-sm p-2 rounded-xl w-fit"
                  >
                    {msg}
                  </div>
                ))}
              </div>
            ) : (
              <EmptyView
                icon={<EmptyInboxIcon />}
                title={"Your inbox is empty! 📭"}
                description={
                  "Once you accept a job invitation, your conversations will appear here. Time to get chatting!"
                }
              />
            )}
          </div>
        </div>
      ) : (
        <div className="flex w-full h-full items-center justify-center">
          <EmptyView
            icon={<EmptyMainIcon />}
            title={"Nothing to see here... yet! 👀"}
            description={
              "Your inbox is as quiet as a nap 💤. But don’t worry—once a job invitation is accepted, you’ll be able to start chatting away!"
            }
          />
        </div>
      )}
    </PageWrapper>
  );
};

export default Messages;
