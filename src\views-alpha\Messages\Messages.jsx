import { useEffect, useState } from "react";
import PageWrapper from "../../components-alpha/PageWrapper/PageWrapper";
import EmptyView from "../../common-alpha/EmptyViewUpdated/EmptyView";
import EmptyMainIcon from "../../assets-alpha/images/view/messages-empty-main.svg?react";
import EmptyInboxIcon from "../../assets-alpha/images/view/empty-inbox.svg?react";
import AvatarIcon from "../../assets-alpha/images/svg/workplace-avatar.svg";
import { Input } from "antd";
import { SearchIcon } from "lucide-react";
import MessagesTemplate from "./Components/MessagesTemplate";
import { HubConnectionBuilder, LogLevel } from "@microsoft/signalr";
const Messages = () => {
  const API_BASE_URL =
    "https://prodoov2-gqg9esa9g9b3d9bd.canadacentral-01.azurewebsites.net";
  const HUB_URL = `${API_BASE_URL}`;
  const [connection, setConnection] = useState(null);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [allProfiles, setAllProfiles] = useState([]);
  const [messages, setMessages] = useState([]);

  useEffect(() => {
    const connection = new HubConnectionBuilder()
      .withUrl(`${HUB_URL}/hubs/messages`)
      .withAutomaticReconnect()
      .configureLogging(LogLevel.Information)
      .build();

    connection
      .start()
      .then(() => {
        console.log("✅ SignalR Connected!");
      })
      .catch(err => {
        console.error("❌ Connection error: ", err);
      });

    return () => {
      connection.stop();
    };
  }, [HUB_URL]);

  return (
    <PageWrapper className={"!p-0"}>
      {!allProfiles?.length > 0 ? (
        <div className="flex w-full h-full">
          {/* Left Sidebar - Conversations / Profiles */}
          <div className="flex flex-col gap-4 w-[26%] m-6 mb-4">
            <h1 className="!m-0 !text-[#343333] !text-2xl !font-semibold leading-5">
              Inbox
            </h1>
            <div className="flex flex-col gap-3 w-full">
              <div className="hidden md:block">
                <Input
                  placeholder="Search "
                  size="medium"
                  bordered={false}
                  className=" !border !border-[#F3F3F3] !bg-[#F3F1FD] !h-11 placeholder:text-[#878787] ![--ant-input-padding-inline:12px] [&_.ant-input-affix-wrapper]:!p-[3px_3px_3px_16px] [&_.ant-input-prefix]:!border-0 [&_.ant-input-prefix]:!mr-0 hover:!border-[#8E81F5]"
                  rootClassName="
                      [&_.ant-input]:!ml-[6px]"
                  prefix={<SearchIcon />}
                />
              </div>

              {/* Dummy Profiles for UI */}
              <div className="flex flex-col gap-2 w-full h-[calc(100vh-198px)] overflow-y-auto">
                {[1, 2, 3, 4, 5].map((item, index) => (
                  <div
                    key={index}
                    className="flex w-full gap-4 !p-3 border-[0.5px] border-[#EAE5FC] !rounded-2xl justify-between items-center pointer"
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex gap-4 items-center">
                        <div className="md:!w-11 md:!h-11 !h-10 !w-10 !rounded-[50%]">
                          <img
                            className="md:!w-11 md:!h-11 !h-10 !w-10 !rounded-[50%]"
                            src={AvatarIcon}
                            alt={"Logo"}
                          />
                        </div>
                        <div className="flex flex-col gap-2">
                          <label className="text-[var(--dark)] text-sm font-medium leading-[100%]">
                            Adeel Abid
                          </label>
                          <p className="text-[var(--gray-3)] text-xs font-normal leading-[100%]">
                            React JS Frontend Developer
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Sidebar - Messages */}
          <div className="relative flex items-center justify-center w-[74%] border-l-[0.5px] border-[#EAE5FC]">
            {messages?.length > 0 ? (
              <div className="flex flex-col gap-2 p-4 w-full">
                {messages.map((msg, idx) => (
                  <div
                    key={idx}
                    className="bg-[#F3F1FD] text-sm p-2 rounded-xl w-fit"
                  >
                    {msg}
                  </div>
                ))}
              </div>
            ) : (
              <EmptyView
                icon={<EmptyInboxIcon />}
                title={"Your inbox is empty! 📭"}
                description={
                  "Once you accept a job invitation, your conversations will appear here. Time to get chatting!"
                }
              />
            )}
          </div>
        </div>
      ) : (
        <div className="flex w-full h-full items-center justify-center">
          <EmptyView
            icon={<EmptyMainIcon />}
            title={"Nothing to see here... yet! 👀"}
            description={
              "Your inbox is as quiet as a nap 💤. But don’t worry—once a job invitation is accepted, you’ll be able to start chatting away!"
            }
          />
        </div>
      )}
    </PageWrapper>
  );
};

export default Messages;
