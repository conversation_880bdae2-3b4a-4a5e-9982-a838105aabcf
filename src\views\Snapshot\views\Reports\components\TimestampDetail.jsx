import React from "react";
import moment from "moment";
import { CustomInputNew } from "../../../../Tictell/views/components/CustomInput";
import ScreenshotGallery from "../../../../Tictell/views/Timelog/ScreenshotGallery";
import DatePickerField from "../../../../../common/DatePicker/DatePicker";

class TimestampDetail extends React.Component {
  render() {
    const {
      snapshotDate,
      currentTimelog,
      ReviewedBylabel,
      ReviewedDatelabel,
      StartTimeLabel,
      EndTimeLabel,
      DurationLabel,
      MemoLabel,
      CommentLabel,
      AmountLabel,
      Dateformatelabel,
      labels,
      Status,
      Screenshots
    } = this.props;
    const {
      TimeLogId,
      Date,
      Memo = "",
      Amount,
      StartTime = "",
      EndTime = "",
      Duration = "",
      Currency,
      ReviewedBy,
      ReviewedDate
    } = currentTimelog;

    const screenshotsToShow = Screenshots || currentTimelog.Screenshots || [];
    return (
      <div className="timelog-detail">
        <CustomInputNew label={snapshotDate}>
          <label className="datepicker-wrapper">
            <DatePickerField
              selected={Date && moment(Date)}
              data-testid={"datepicker"}
              name="expenseDate"
              dateFormat="DD-MM-YYYY"
              placeholderText={Dateformatelabel}
              disabled
            />
          </label>
        </CustomInputNew>
        <div className="start-end">
          <CustomInputNew label={labels.Timelog_StartTime}>
            <label className="datepicker-wrapper">
              <DatePickerField
                selected={StartTime && moment("2017-03-13" + " " + StartTime)}
                showTimeSelect
                showTimeSelectOnly
                timeIntervals={15}
                timeCaption="End Time"
                dateFormat={"HH:mm"}
                disabled
                timeFormat={"HH:mm"}
                placeholderText={labels.Timelog_Time_Placeholder}
              />
            </label>
          </CustomInputNew>
          <label>to</label>
          <CustomInputNew label={labels.Timelog_EndTime}>
            <label className="datepicker-wrapper">
              <DatePickerField
                selected={EndTime && moment("2017-03-13" + " " + EndTime)}
                showTimeSelect
                showTimeSelectOnly
                timeIntervals={15}
                timeCaption="End Time"
                dateFormat={"HH:mm"}
                timeFormat={"HH:mm"}
                disabled
                placeholderText={labels.Timelog_Time_Placeholder}
              />
            </label>
          </CustomInputNew>
        </div>
        <CustomInputNew label={DurationLabel}>
          <input
            type="text"
            autoComplete="off"
            value={Duration}
            name="Duration"
            className={`input-text `}
            placeholder={DurationLabel}
            data-testid="input-text-opportunity-title"
            disabled
          />
        </CustomInputNew>
        <CustomInputNew label={AmountLabel}>
          <input
            type="text"
            autoComplete="off"
            value={!Amount || !Currency ? "" : `${Amount} ${Currency}`}
            name="Amount"
            className={`input-text `}
            placeholder={AmountLabel}
            data-testid="input-text-opportunity-title"
            disabled
          />
        </CustomInputNew>
        <CustomInputNew label={MemoLabel}>
          <textarea
            type="text"
            value={Memo || ""}
            name="Memo"
            className="textarea-field"
            rows="9"
            placeholder={CommentLabel}
            data-testid="input-text-opportunity-description"
            disabled
          />
        </CustomInputNew>
        {(Status === 2 || Status === 3) && ReviewedBy !== null && (
          <div>
            <CustomInputNew label={ReviewedBylabel}>
              <input
                type="text"
                autoComplete="off"
                value={ReviewedBy || ""}
                name="ReviewedBy"
                className={`input-text `}
                placeholder={ReviewedBylabel}
                data-testid="input-text-opportunity-title"
                disabled
              />
            </CustomInputNew>
            <CustomInputNew label={ReviewedDatelabel}>
              <input
                type="text"
                autoComplete="off"
                value={moment(ReviewedDate).format("DD-MM-YYYY") || ""}
                name="ReviewedDate"
                className={`input-text `}
                placeholder={ReviewedDatelabel}
                data-testid="input-text-opportunity-title"
                disabled
              />
            </CustomInputNew>
          </div>
        )}
        {screenshotsToShow && screenshotsToShow.length > 0 && (
          <ScreenshotGallery screenshots={screenshotsToShow} />
        )}
      </div>
    );
  }
}

export default TimestampDetail;
