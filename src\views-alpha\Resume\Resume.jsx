import { useQueryClient } from "@tanstack/react-query";
import { <PERSON><PERSON>, Grid, Upload } from "antd";
import { useState } from "react";
import { ApiUrl } from "../../api-alpha/apiUrls";
import UploadOutlined from "../../assets-alpha/images/svg/upload.svg?react";
import PageWrapper from "../../components-alpha/PageWrapper/PageWrapper";
import { getFileData } from "../../utilities-alpha/helper";
import AchievementSection from "./Component/AchievementSection";
import CertificationsSection from "./Component/CertificationsSection";
import EditInfoModal from "./Component/EditInfoModal";
import EducationSection from "./Component/EducationSection";
import ExperienceSection from "./Component/ExperienceSection";
import IndustrySection from "./Component/IndustrySection";
import KeywordsSection from "./Component/KeywordsSection";
import LanguagesSection from "./Component/LanguagesSection";
import ProfileInfo from "./Component/ProfileInfo";
import RoleDrawerContent from "./Component/RoleDrawerContent";
import RolesSection from "./Component/RolesSection";
import SkillsSection from "./Component/SkillsSection";
import SuggestionsDrawerContent from "./Component/SuggestionsDrawerContent";
import TargetRegionsSection from "./Component/TargetRegionsSection";
import { useGetResume, useSaveParseResume, useUploadResume } from "./resumeApi";
import AvailabilitySection from "./Component/AvailabilitySection";
import { useSelector } from "react-redux";
const ResumePage = () => {
  const { lg } = Grid.useBreakpoint();
  // State for modals and drawers
  const resume = useSelector(state => state.systemLabel.labels?.resume);
  const [isEditInfoModalOpen, setIsEditInfoModalOpen] = useState(false);
  const [isRoleDrawerOpen, setIsRoleDrawerOpen] = useState(false);
  const [isSuggestionsDrawerOpen, setIsSuggestionsDrawerOpen] = useState(false);
  const queryClient = useQueryClient();
  // Handle opening the appropriate drawer
  const openDrawer = option => {
    setIsRoleDrawerOpen(option);
  };

  const { data: resumeData, isFetching: loadingResume, error } = useGetResume();
  const {
    mutate: parseResumeApi,
    isPending: loadingParsing
  } = useSaveParseResume();

  const {
    mutate: uploadResumeApi,
    isPending: loadingUploadResume
  } = useUploadResume();

  const onUploadResume = e => {
    const file = e.file;
    getFileData(file, base64 => {
      uploadResumeApi(
        { fileData: base64 },
        {
          onSuccess: data => {
            parseResumeApi(
              {
                parsedData: data?.items
              },
              {
                onSuccess: () => {
                  queryClient.invalidateQueries({
                    queryKey: [ApiUrl.ResumeEdit.GetMyResume]
                  });
                }
              }
            );
          }
        }
      );
    });
  };
  return (
    <PageWrapper
      title={resume?.mainTitle}
      contentClassName="xxl:px-[120px] h-[124px]"
      className={`relative 
        md:before:hidden before:absolute before:inset-0 before:bg-[url('/assets/images/resume-banner.svg')] before:bg-no-repeat before:bg-top before:bg-cover before:h-[124px] before:w-full
        `}
      titleClassName="z-10 max-md:!text-white"
    >
      {/* Resume Actions */}
      {lg && (
        <div className="flex justify-end mb-4 gap-2">
          <Upload
            beforeUpload={() => false}
            onChange={onUploadResume}
            accept="application/pdf"
            className="[&_.ant-upload]:w-full [&_.ant-upload-list]:hidden "
          >
            <Button
              icon={<UploadOutlined />}
              type="default"
              disabled={loadingParsing || loadingUploadResume || loadingResume}
              loading={loadingParsing || loadingUploadResume || loadingResume}
              className=" !border-[#EAE5FC]"
            >
              {resume?.uploadPdf}
            </Button>
          </Upload>

          {/* <Button
          type="primary"
          icon={<DownloadOutlined />}
          className="bg-purple-500 rounded-full flex items-center"
        >
          Download resume
        </Button> */}
        </div>
      )}

      {/* Resume Content */}
      <div
        className="border border-[#EAE5FC] max-md:border-0 rounded-lg mb-6 relative md:overflow-hidden
        max-md:before:hidden 
        before:absolute before:inset-0 before:bg-[url('/assets/images/resume-banner-web.svg')] before:bg-no-repeat before:bg-top before:bg-cover before:h-[200px] before:w-full
        md:pt-[150px] table
      "
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-md:gap-0">
          {/* Left Column */}
          <div className="md:col-span-1 border-r border-[#EAE5FC] max-md:border-0 max-md:px-0 max-md:pb-0">
            <ProfileInfo setIsEditInfoModalOpen={setIsEditInfoModalOpen} />
            <AvailabilitySection userData={resumeData} />
            <LanguagesSection openDrawer={openDrawer} userData={resumeData} />
            <TargetRegionsSection
              openDrawer={openDrawer}
              userData={resumeData}
            />
            <KeywordsSection openDrawer={openDrawer} userData={resumeData} />
          </div>

          {/* Right Column */}
          <div className="md:col-span-2 md:-ml-6 md:mt-[50px]">
            <RolesSection openDrawer={openDrawer} userData={resumeData} />
            <SkillsSection openDrawer={openDrawer} userData={resumeData} />
            <IndustrySection openDrawer={openDrawer} userData={resumeData} />
            <CertificationsSection
              openDrawer={openDrawer}
              userData={resumeData}
            />
            <EducationSection openDrawer={openDrawer} userData={resumeData} />
            <ExperienceSection openDrawer={openDrawer} userData={resumeData} />
            <AchievementSection openDrawer={openDrawer} userData={resumeData} />
          </div>
        </div>
      </div>

      {isEditInfoModalOpen && (
        <EditInfoModal
          isEditInfoModalOpen={isEditInfoModalOpen}
          setIsEditInfoModalOpen={setIsEditInfoModalOpen}
        />
      )}

      {/* Drawers */}

      {isRoleDrawerOpen && (
        <RoleDrawerContent
          setIsSuggestionsDrawerOpen={setIsSuggestionsDrawerOpen}
          info={isRoleDrawerOpen}
          setOpenModal={setIsRoleDrawerOpen}
        />
      )}

      {isSuggestionsDrawerOpen && (
        <SuggestionsDrawerContent
          open={isSuggestionsDrawerOpen}
          setOpenModal={setIsSuggestionsDrawerOpen}
        />
      )}
    </PageWrapper>
  );
};

export default ResumePage;
