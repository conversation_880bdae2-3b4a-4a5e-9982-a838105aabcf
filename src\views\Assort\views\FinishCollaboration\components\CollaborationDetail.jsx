import moment from "moment";
import { CustomInputNew } from "../../../../Tictell/views/components/CustomInput";
import Button from "../../../../../common/Button/Button";
import DatePickerField from "../../../../../common/DatePicker/DatePicker";

const CollaborationDetail = ({
  selectedCollaboration,
  labels,
  handleCollaborationEndDateChange,
  handleCollaborationNameEdit,
  isEndDateCalendarOpen,
  handleDateOnBlur,
  handleCollborationNameBlur,
  handleCollborationNameChange,
  onCollaborationClose,
  isHelpActive
}) => {
  const {
    Dateformatelabel,
    SNAPSHOT_COLLABORATION_OWNER,
    SNAPSHOT_COLLABORATION_START,
    SNAPSHOT_COLLABORATION_END,
    SNAPSHOT_COLLABORATION_COMPENSATION,
    SNAPSHOT_COLLABORATION_WORKPLACE,
    SNAPSHOT_COLLABORATION_DESC,
    CollaborationNameLabel
  } = labels;
  const {
    CollaborationOwner,
    StartDate,
    EndDate,
    HourlyRate,
    HourlyRateType,
    CompanyName,
    Decription,
    Title,
    isNameEdit
  } = selectedCollaboration;

  return (
    <div className="snapshot-collaboration-detail">
      <div className="start-end">
        <CustomInputNew label={CollaborationNameLabel}>
          <input
            type="text"
            autoComplete="off"
            value={Title || ""}
            name="Amount"
            className={`input-text `}
            placeholder={SNAPSHOT_COLLABORATION_OWNER}
            data-testid="input-collab-title"
            onBlur={handleCollborationNameBlur}
            onChange={handleCollborationNameChange}
            disabled={isNameEdit && isNameEdit ? false : true}
          />
        </CustomInputNew>
        <Button
          testId={`collabUpdateBtn`}
          className="add-btn"
          onClick={handleCollaborationNameEdit}
          isHelpActive={isHelpActive}
          testIdHelp={"add-newLog-btn-help"}
          tooltipHelp={"Click on this button to edit the collaboration name"}
          tooltipButton={"Edit collaboration name"}
        ></Button>
      </div>
      <CustomInputNew label={SNAPSHOT_COLLABORATION_OWNER}>
        <input
          type="text"
          autoComplete="off"
          value={CollaborationOwner || ""}
          name="Amount"
          className={`input-text `}
          placeholder={SNAPSHOT_COLLABORATION_OWNER}
          data-testid="input-expanse-amount"
          disabled
        />
      </CustomInputNew>
      <div className="start-end">
        <CustomInputNew label={SNAPSHOT_COLLABORATION_START}>
          <label className="datepicker-wrapper">
            <DatePickerField
              selected={StartDate && moment(StartDate)}
              name="expenseDate"
              dateFormat="MMM Do YYYY"
              placeholderText={Dateformatelabel}
              disabled
            />
          </label>
        </CustomInputNew>
        <span>-</span>
        <CustomInputNew label={SNAPSHOT_COLLABORATION_END}>
          <label className="datepicker-wrapper">
            <DatePickerField
              selected={EndDate && moment(EndDate)}
              name="expenseDate"
              dateFormat="MMM Do YYYY"
              placeholderText={Dateformatelabel}
              onChange={handleCollaborationEndDateChange}
              minDate={moment()}
              onBlur={handleDateOnBlur}
              open={isEndDateCalendarOpen}
              disabled
            />
          </label>
        </CustomInputNew>
      </div>
      <CustomInputNew label={SNAPSHOT_COLLABORATION_COMPENSATION}>
        <input
          type="text"
          autoComplete="off"
          value={`${HourlyRate || ""} ${HourlyRateType || ""} per Hour`}
          name="Amount"
          className={`input-text `}
          placeholder={SNAPSHOT_COLLABORATION_COMPENSATION}
          data-testid="input-expanse-amount"
          disabled
        />
      </CustomInputNew>
      <CustomInputNew label={SNAPSHOT_COLLABORATION_WORKPLACE}>
        <input
          type="text"
          autoComplete="off"
          value={CompanyName || ""}
          name="Amount"
          className={`input-text `}
          placeholder={SNAPSHOT_COLLABORATION_WORKPLACE}
          data-testid="input-expanse-amount"
          disabled
        />
      </CustomInputNew>
      <CustomInputNew label={SNAPSHOT_COLLABORATION_DESC}>
        <textarea
          type="text"
          value={Decription || ""}
          name={"Description"}
          className="textarea-field"
          rows="6"
          placeholder={SNAPSHOT_COLLABORATION_DESC}
          data-testid="input-comment"
          disabled
        />
      </CustomInputNew>
      <div className="invRequest">
        <button
          className="invDecline SendButton"
          onClick={onCollaborationClose}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default CollaborationDetail;
