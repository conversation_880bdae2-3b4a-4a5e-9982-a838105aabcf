import { useSelector } from "react-redux";
import { ApiUrl } from "../../../api-alpha/apiUrls";
import { Profiles } from "../constant";
import { useResumeLookup } from "../resumeApi";
import { InfoItemCard } from "./ExperienceSection";
import { ShowMoreSection } from "./LanguagesSection";
// AchievementSection Section
const AchievementSection = ({ userData, openDrawer }) => {
  const { data: lookupData, isFetching: isLoadingData } = useResumeLookup({
    urls: [ApiUrl.ResumeEdit.AllResumeAchivementLookup]
  });
  const resume = useSelector(state => state.systemLabel.labels?.resume);

  return (
    <ShowMoreSection
      title={resume?.achievements}
      items={userData?.items?.ResumeOtherAchivenments || []}
      openDrawer={openDrawer}
      addEditAction={Profiles.achievement}
      itemKeyId="ResumeOtherAchivenmentId"
      itemValueKey="Title"
      viewMore
      emptyMessage={resume?.achievementsEmptyMessage}
      addButtonAction={Profiles.achievement}
      renderItem={ach => {
        const name = lookupData?.[0]?.find(
          item => item.AchivenmentId === ach.AchivenmentId
        )?.AchivenmentName;
        const achievementFields = [
          { label: resume?.achievementName, value: name },
          { label: resume?.achievementYear, value: ach.Year },
          { label: resume?.achievementType, value: ach.Title }
        ];

        return (
          <InfoItemCard
            fields={achievementFields}
            description={ach.Description}
            itemKey={ach.ResumeOtherAchivenmentId}
          />
        );
      }}
    />
  );
};

export default AchievementSection;
