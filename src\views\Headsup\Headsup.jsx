import React, { useState, useEffect } from "react";
import { connect } from "react-redux";
import PageWrapper from "../../components/PageWrapper/PageWrapper";
import { notificationAction } from "../../actions/notification";
import "./headsup.scss";
import {
  tictellLogin,
  getCurrenciesApi,
  tictellAppLoginApi,
} from "../Tictell/tictellApi";
import { privateRoutes } from "../../Routes/routing";
import {
  GetAcceptedCollaborationCount,
  getChildFeatureByName,
} from "../Snapshot/snapshotApi";
import { StorageService } from "../../api/storage";
import SnapshotNavigation from "./Navigation/Navigation";
import Shortlist from "./views/Shortlist/Shortlist";
import ConfirmDialog from "../../common/ConfirmDialog/ConfirmDialog";
import Opportunity from "./views/Opportunity/Opportunity";
import Dashboard from "./views/Dashboard/Dashboard";
import SavedSearch from "./views/SavedSearch/SavedSearch";
import { useNavigate } from "react-router-dom";

const Headsup = ({ labels, isHelpActive, location, User, dispatch }) => {
  const [state, setState] = useState({
    UserFeatures: [],
    token: "",
    ExpenseCategories: [],
    Currencies: [],
    isLoading: true,
    UserId: -1,
    dialogMessage: "",
  });

  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getChildFeatureByName({ featureName: "headsup" });
        setState((prevState) => ({
          ...prevState,
          UserFeatures: res.items,
        }));
        const { items } = res;
        const firstUrl = items.find((a) => a.Url);
        let queryString = window.location.href
          .toLowerCase()
          .split("headsup")[1];
        if (queryString === "" && firstUrl) {
          const isApp = window.location.hash.toLowerCase().indexOf("apps") > -1;

          navigate((isApp ? "/apps" : "") + "/headsup/" + firstUrl.Url);
        }
      } catch (err) {
        console.log("Err ", err);
      }

      const storedUser = StorageService.getUser();
      let Email = User.UserEmail || User.userEmail || User.Email || 
                  storedUser?.Email || storedUser?.UserEmail || storedUser?.userEmail;
      setState((prevState) => ({
        ...prevState,
        UserId: User.UserId,
      }));

      if (Email && Email !== 'undefined') {
        try {
          const res = await tictellAppLoginApi(Email);
        if (res.success) {
          const loginRes = await tictellLogin(Email);
          if (loginRes.success) {
            const { access_token } = loginRes.items;
            getCurrencies(access_token);
            getAllactivecollaborations(access_token);
            setState((prevState) => ({
              ...prevState,
              token: access_token,
            }));
          }
        }
        } catch (err) {
          console.log("Error ", err);
        }
      } else {
        console.warn("No valid email found for Headsup Tictell login");
      }
    };

    fetchData();
  }, [User, navigate]);

  const getAllactivecollaborations = async (access_token) => {
    try {
      const res = await GetAcceptedCollaborationCount(access_token);
      if (res.success) {
        setState((prevState) => ({
          ...prevState,
          dialogMessage:
            res.items.length === 0
              ? labels.Snapshot_Collaboration_Dialog_Msg
              : "",
          isLoading: res.items.length === 0,
        }));
      }
    } catch (err) {
      console.log("Error ", err);
    }
  };

  const getCurrencies = async (access_token) => {
    try {
      const res = await getCurrenciesApi();
      if (res.success) {
        setState((prevState) => ({
          ...prevState,
          Currencies: res.items.map((item) => ({
            ...item,
            value: item.CurrencyId,
            label: item.Name,
          })),
        }));
      }
    } catch (err) {
      console.log("Err ", err);
    }
  };

  const handleOkClick = () => {
    navigate(privateRoutes.dashboard.path);
  };

  const {
    UserFeatures,
    token,
    Currencies,
    dialogMessage,
    ExpenseCategories,
    isLoading,
    UserId,
    pendingtimesheet,
    Rejectedtimesheet,
    Rejectedexpense,
    Pendingexpense,
  } = state;

  const currentViewHash =
    window.location.hash.toLowerCase().split("/")[1] === "apps"
      ? window.location.hash.toLocaleLowerCase().split("/")[3]
      : window.location.hash.toLocaleLowerCase().split("/")[2];

  return (
    <PageWrapper className={`headsup-page`}>
      {isLoading && (
        <div id="loader-wrapper">
          <div className="loader-container">
            <div className="loader">
              <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
                <defs>
                  <filter id="goo">
                    <feGaussianBlur
                      in="SourceGraphic"
                      stdDeviation="6"
                      result="blur"
                    />
                    <feColorMatrix
                      in="blur"
                      mode="matrix"
                      values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                      result="goo"
                    />
                    <feBlend in="SourceGraphic" in2="goo" />
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </div>
      )}
      <SnapshotNavigation
        disabled={`${isLoading ? "disabled" : ""}`}
        labels={labels}
        isHelpActive={isHelpActive}
        UserFeatures={UserFeatures}
      />
      {dialogMessage && (
        <ConfirmDialog testId="confirm-diloag">
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              onClick={handleOkClick}
            >
              {"Ok"}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      {!isLoading && (
        <React.Fragment>
          {currentViewHash === "home" &&
            UserFeatures.findIndex(
              (a) =>
                a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
            ) > -1 && (
              <Dashboard
                token={token}
                Readyforapproval={pendingtimesheet}
                Rejected={Rejectedtimesheet}
                asOwnerCount={"12"}
                Rejectedexpense={Rejectedexpense}
                Pendingexpense={Pendingexpense}
                UserFeatures={UserFeatures}
              />
            )}
          {currentViewHash === "shortlist" &&
            UserFeatures.findIndex(
              (a) =>
                a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
            ) > -1 && (
              <Shortlist
                token={token}
                Currencies={Currencies}
                ExpenseCategories={ExpenseCategories}
              />
            )}
          {currentViewHash === "opportunity" &&
            UserFeatures.findIndex(
              (a) =>
                a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
            ) > -1 && (
              <Opportunity
                token={token}
                Currencies={Currencies}
                ExpenseCategories={ExpenseCategories}
                UserId={UserId}
                locationProp={location}
              />
            )}
          {currentViewHash === "savedsearch" &&
            UserFeatures.findIndex(
              (a) =>
                a.Url && a.Url.toLocaleLowerCase().trim() === currentViewHash
            ) > -1 && (
              <SavedSearch
                token={token}
                Currencies={Currencies}
                ExpenseCategories={ExpenseCategories}
                UserId={UserId}
              />
            )}
        </React.Fragment>
      )}
    </PageWrapper>
  );
};

const mapStateToProps = ({ systemLabel, navigation, userInfo }) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  const User = user ? user : StorageService.getUser();
  const { isHelpActive } = navigation;
  return { labels, isHelpActive, User };
};

export default connect(mapStateToProps, { notificationAction })(Headsup);
