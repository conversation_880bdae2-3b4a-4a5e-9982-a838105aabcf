import { SyncOutlined } from "@ant-design/icons";
import { useQueryClient } from "@tanstack/react-query";
import {
  Avatar,
  Col,
  Divider,
  Form,
  Grid,
  message,
  Row,
  Space,
  Upload
} from "antd";
import clsx from "clsx";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { twMerge } from "tailwind-merge";
import { ApiUrl } from "../../api/apiUrls";
import RightIcon from "../../assets-alpha/images/svg/arrow-right-1.svg?react";
import AvatarIcon from "../../assets-alpha/images/svg/avatar-white.svg";
import LockIcon from "../../assets-alpha/images/svg/lock.svg?react";
import NotificationIcon from "../../assets-alpha/images/svg/notification.svg?react";
import ProfileIcon from "../../assets-alpha/images/svg/profile.svg?react";
import Button from "../../common/Button/Button";
import Modal from "../../components-alpha/Modal/Modal";
import { loadImageOrientation } from "../../utilities/helpers";
import ChangePassword from "./components/ChangePassword";
import ProfileInformation from "./components/ProfileInformation";
import SettingHeading from "./components/SettingHeading";
import SettingTitle from "./components/SettingTitle";
import UserNotifications from "./components/UserNotifications";
import {
  useChangePasswordApi,
  useUpdateUserNotificationsApi,
  useUpdateUserProfileApi,
  useUserNotificationsApi
} from "./settingsApi";
const { useBreakpoint } = Grid;

export const NavLinks = labels => [
  {
    icon: <ProfileIcon />,
    text: labels?.profileInformation
  },
  { icon: <LockIcon />, text: labels?.changePassword },
  {
    icon: <NotificationIcon />,
    text: labels?.notifications
  }
];
const Settings = ({ open, onClose }) => {
  const [isValid, setIsValid] = useState(true);
  const [messageApi, contextHolder] = message.useMessage();
  const [selectedTab, setSelectedTab] = useState(0);
  const queryClient = useQueryClient();
  const labels = useSelector(state => state.systemLabel.labels?.setting);
  const screens = useBreakpoint();
  const {
    mutateAsync: updateProfileApi,
    isPending: loadingProfile
  } = useUpdateUserProfileApi();

  const {
    mutateAsync: updatePassword,
    isPending: loadingUpdatePassword
  } = useChangePasswordApi();
  const {
    mutateAsync: updateNotification,
    isPending: loadingUpdateNotification
  } = useUpdateUserNotificationsApi();

  const {
    notifications: userNotification,
    data: notifications,
    isLoading: loadingNotification
  } = useUserNotificationsApi();

  const [form] = Form.useForm();
  const profile = Form.useWatch("ProfilePicture", form);
  useEffect(() => {
    if (screens.md) {
      setSelectedTab(0);
    } else {
      setSelectedTab(-1);
    }
  }, [screens.md]);
  const { setFieldValue } = form;
  const onFinish = async () => {
    try {
      const values = await form.validateFields();
      switch (selectedTab) {
        case -1:
        case 0:
          {
            const profilePic = form.getFieldValue("ProfilePicture");
            const profilePromise = updateProfileApi({
              UserFirstname: values.UserFirstname,
              UserLastname: values.UserLastname,
              ProfilePicture: profilePic,
              CountryId: values.CountryName?.value,
              PhoneNumberList: values.PhoneList || [],
              EmailList: values.EmailList || [],
              SocialMediaLinks: values.SocialLinks || []
            });

            await Promise.all([profilePromise]);

            queryClient.invalidateQueries({
              queryKey: [ApiUrl.Settings.GetUserProfile]
            });
            messageApi.open({
              type: "success",
              content: labels?.updatedSuccessfully
            });
          }
          break;

        case 1:
          {
            const { NewPassword, OldPassword, RepeatPassword } = values;
            const password = updatePassword({
              NewPassword,
              OldPassword,
              RepeatPassword
            }).then(data => {
              if (data.success) {
                messageApi.open({
                  type: "success",
                  content: labels?.passwordUpdatedSuccessfully
                });
              } else {
                messageApi.open({
                  type: "error",
                  content: data?.message
                });
              }
            });
            await Promise.all([password]);
          }
          break;

        case 2:
          {
            const userNotification = values.userNotification;
            const notifyValues = userNotification.reduce((acc, item) => {
              acc[item.name] = item.checked;
              return acc;
            }, {});

            const updateNoti = updateNotification({
              ...notifications.items,
              ...notifyValues
            });

            await Promise.all([updateNoti]);
            messageApi.open({
              type: "success",
              content: labels?.notificationUpdatedSuccessfully
            });
          }
          break;

        default:
          break;
      }
    } catch (errorInfo) {
      console.log("Failed:", errorInfo);
      const errorMessages = errorInfo?.errorFields
        ?.flatMap(field => field.errors)
        .filter(Boolean)
        .join("\n");

      messageApi.open({
        type: "error",
        content: errorMessages ? (
          <span className="whitespace-pre block text-left">
            {errorMessages}
          </span>
        ) : (
          labels?.updateFailedTryAgain
        )
      });
    }
  };
  const onFinishFailed = errorInfo => {
    console.log("Failed:", errorInfo);
  };

  const changeFileUpload = e => {
    const file = e?.file;
    if (file) {
      const previewImage = URL.createObjectURL(file.originFileObj);
      loadImageOrientation(previewImage, image => {
        setFieldValue("ProfilePicture", image);
      });
    }
  };
  const loading =
    loadingProfile || loadingUpdatePassword || loadingUpdateNotification;
  return (
    <>
      {contextHolder}
      <Modal width={750} open={open} onCloseModal={onClose}>
        <div className="font-[inter] relative w-full bg-white">
          <Form
            name="profile"
            form={form}
            layout="vertical"
            requiredMark={false}
            onFinishFailed={onFinishFailed}
            // autoComplete="off"
            preserve
          >
            <Row className="min-h-[750px] max-md:min-h-auto">
              <Col
                span={24}
                md={{ span: 8 }}
                className={clsx("md:bg-[#f3f1fd] py-6 max-md:pb-0")}
              >
                <Space
                  direction="vertical"
                  size="small"
                  className="w-full !gap-y-0"
                >
                  {!screens.md && (
                    <>
                      <SettingHeading
                        onClose={onClose}
                        isValid={isValid}
                        loading={loading}
                        onFinish={onFinish}
                      />
                      <Divider
                        className={clsx("!mt-0", {
                          "max-md:!mb-0": selectedTab >= 0
                        })}
                      />
                    </>
                  )}
                  <div
                    className={clsx({
                      hidden: selectedTab >= 0 && !screens.md
                    })}
                  >
                    <div className="px-6 max-md:flex max-md:flex-row max-md:items-center max-md:gap-4">
                      <Form.Item name="ProfilePicture" noStyle>
                        <Avatar
                          src={profile || AvatarIcon}
                          className="!w-25 !h-25 !m-auto !block min-w-25"
                        />
                        <div className="flex flex-col sm:flex-row gap-[8px] justify-center sm:justify-start sm:mt-[16px] w-full">
                          <Upload
                            onChange={changeFileUpload}
                            accept="image/*"
                            type="button"
                            className="flex-1 [&_.ant-upload]:w-full [&_.ant-upload-list]:hidden"
                          >
                            <Button
                              mainClassName="block"
                              className="bg-white !text-sm !font-normal !leading-[100%] max-md:bg-[#F3F1FD] !text-[var(--purple)] !rounded-lg !px-[18px] !py-[7.5px] w-full !h-8"
                            >
                              {profile ? labels?.change : labels?.uploadImage}
                            </Button>
                          </Upload>
                          {profile && (
                            <Button
                              mainClassName="block flex-1"
                              onClick={() =>
                                setFieldValue("ProfilePicture", null)
                              }
                              className="border !text-sm !font-normal !leading-[100%] border-[var(--light-gray)] !rounded-lg !px-[18px] !py-[7.5px] w-full !h-8"
                            >
                              {labels?.removeImage}
                            </Button>
                          )}
                        </div>
                      </Form.Item>

                      {/* <Text type="secondary" className="text-xs !text-[#878787]">
                      JPG, PNG, 1.5 MB Max
                    </Text> */}
                    </div>

                    {screens.md && <Divider />}
                    <div className="pl-6 max-md:pl-0 max-md:mt-8">
                      <Space
                        direction="vertical"
                        size="small"
                        className="w-full"
                      >
                        {NavLinks(labels).map((item, index) => (
                          <Button
                            key={item.text}
                            mainClassName="max-md:border-b-1 max-md:border-b-[var(--light-gray)]"
                            className={twMerge(
                              clsx(
                                "flex items-center gap-2 w-full px-4 max-md:px-6 !py-[11px] rounded-l-[8px] !text-[var(--gray-3)] hover:bg-white hover:!text-[var(--dark)] !h-10",
                                {
                                  "bg-white !text-[var(--dark)]":
                                    selectedTab === index
                                }
                              )
                            )}
                            onClick={() => {
                              setSelectedTab(index);
                              // onValuesChange();
                            }}
                          >
                            {item.icon}
                            {item.text}
                            {!screens.md && <RightIcon className="ml-auto" />}
                          </Button>
                        ))}
                      </Space>
                    </div>
                  </div>
                </Space>
              </Col>

              <Col
                span={24}
                md={{ span: 16 }}
                className={clsx("py-4", {
                  "!hidden": selectedTab < 0 && !screens.md,
                  "!min-h-[calc(100vh-170px)] pb-[72px]": !screens.md
                })}
              >
                {screens.md && (
                  <SettingHeading
                    onClose={onClose}
                    isValid={isValid}
                    loading={loading}
                    onFinish={onFinish}
                  />
                )}

                {!screens.md && selectedTab >= 0 && (
                  <SettingTitle
                    onSelectedTab={setSelectedTab}
                    selectedTab={selectedTab}
                  />
                )}
                {screens.md && <Divider className="!my-0 " />}

                <div className="space-y-6 px-6 overflow-auto max-h-[625px] max-md:max-h-none  ">
                  {(selectedTab === 0 || selectedTab === -1) && (
                    <ProfileInformation
                      selectedTab={selectedTab}
                      loading={loading}
                    />
                  )}
                  {selectedTab === 1 && <ChangePassword loading={loading} />}
                  {selectedTab === 2 && (
                    <UserNotifications
                      userNotification={userNotification}
                      loading={loading}
                    />
                  )}
                </div>
              </Col>
              <Col
                span={24}
                md={{ span: 16 }}
                className={clsx(
                  "py-4 space-y-6 px-6 overflow-auto max-h-[625px] max-md:max-h-none border-t-1 border-t-[#EAE5FC] !fixed bottom-0 left-0 right-0 bg-white z-10",
                  // {
                  //   "!hidden": selectedTab < 0 || screens.md,
                  // }
                  {
                    "!hidden": screens.md
                  }
                )}
              >
                <Button
                  onClick={onFinish}
                  disabled={!isValid || loading}
                  className="bg-[var(--purple)] py-[6px] px-6 font-medium !text-white !rounded-lg w-full h-10 "
                >
                  {labels?.save}
                  {loading && (
                    <>
                      &nbsp; <SyncOutlined spin />
                    </>
                  )}
                </Button>
              </Col>
            </Row>
          </Form>
        </div>
      </Modal>
    </>
  );
};

export default Settings;
