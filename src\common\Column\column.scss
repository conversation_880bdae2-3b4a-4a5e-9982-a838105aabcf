@use "../../assets/sass/importFiles" as *;
.page-column {
  flex: 1;
  display: flex;
  flex-flow: column;
  padding-top: 10px;
  padding-left: 5px;
  height: 100%;
  @include breakpoint(screen767) {
    height: auto;
  }
  .column-head {
    display: flex;
    flex-flow: row;
  }
  .column-body {
    flex: 1;
    margin-top: 10px;
    background: $white;
    border-radius: 3px;
    overflow: auto;
  }
  &.col-1 {
    @include breakpoint(screen767) {
      flex: auto;
    }
  }
  &.col-2 {
    @include breakpoint(screen767) {
      flex: auto;
    }
  }
  &.col-3 {
    @include breakpoint(screen991) {
      flex: auto;
      width: 100%;
    }
  }
  .column-collapsed {
    display: none;
    flex: 1;
    flex-flow: column;
    position: relative;
  }
  .expand-heading {
    cursor: pointer;
    background: $offwhite2;
    color: $purple4;
    border-radius: 4px;
    text-align: center;
    font-size: 15px;
    flex: 1;
    margin: 5px 0 0;
    display: flex $imp;
    align-items: center;
    justify-content: center;
    transform: rotateZ(180deg);
    writing-mode: vertical-rl;
    outline: none;

    @include breakpoint(screen767) {
      writing-mode: horizontal-tb;
      transform: none;
      margin: 0 10px 0 0;
      order: -1;
    }
  }
  &.page-column-collapse {
    flex: none;
    width: auto;
    @include breakpoint(screen767) {
      align-self: flex-start;
      width: 100%;
      height: auto;
      min-height: 0;
    }
    .column-head,
    .column-body {
      display: none;
    }
    .column-collapsed {
      display: flex;
      @include breakpoint(screen767) {
        flex-flow: row;
      }
    }
  }
}
