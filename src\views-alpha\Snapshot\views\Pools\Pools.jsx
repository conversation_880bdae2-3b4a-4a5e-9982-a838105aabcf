import { MoreOutlined, PlusOutlined } from "@ant-design/icons";
import { But<PERSON>, Dropdown, Grid } from "antd";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { notificationAction } from "../../../../actions/notification";
import { StorageService } from "../../../../api/storage";
import DeleteIcon from "../../../../assets-alpha/images/svg/delete.svg?react";
import EmptyWorkplacePreview from "../../../../assets-alpha/images/view/empty-workplace-preview.svg?react";
import EmptyView from "../../../../common-alpha/EmptyView/EmptyView";
import {
  AddPoolApi,
  DeletePoolApi,
  GetPoolCollaborationsApi,
  GetPoolsApi,
  GetSharedPoolInfo,
  UpdatePoolApi
} from "../../snapshotApi";

import { List, ListItem } from "../Reports/Reports";
import PoolDetail from "./components/PoolDetail";

const Pools = ({ token, UserId }) => {
  const dispatch = useDispatch();
  const { md } = Grid.useBreakpoint();
  const labels = useSelector(s => s.systemLabel?.labels || {});
  const userFromStore = useSelector(s => s.userInfo?.user);
  const User = userFromStore ? userFromStore : StorageService.getUser();

  // pools & collaborations
  const [allPools, setAllPools] = useState([]);
  const [poolDetails, setPoolDetails] = useState({
    PoolId: 0,
    Title: "",
    Collaborations: []
  });
  useEffect(() => {
    GetPoolsApi(token)
      .then(res => {
        if (res.items && res.items.length > 0) {
          const pools = res.items.map(pool => ({
            ...pool,
            IsShared: pool.UserId != (User?.UserId || UserId)
          }));
          setAllPools(pools);
        }
        // setIsPoolsLoading(false);
      })
      .catch(err => {
        console.error("GetPoolsApi error:", err);
        // setIsPoolsLoading(false);
      });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handlePoolClick = item => {
    if (!item) {
      setPoolDetails({});
      return;
    }

    if (!item.IsShared) {
      GetSharedPoolInfo(item.PoolId, token)
        .then(res => {
          const shared = {
            ...item,
            SharedInfo: (res.items || []).map(a => ({
              ...a,
              PoolId: item.PoolId
            }))
          };
        })
        .catch(err => console.log("GetSharedPoolInfo err", err));
    }

    GetPoolCollaborationsApi(item.PoolId, token)
      .then(res => {
        if (res.success) {
          setPoolDetails({
            Title: item.Title,
            PoolId: item.PoolId,
            Collaborations: res.items
          });
        }
      })
      .catch(err => console.log("GetPoolCollaborationsApi err", err));
  };

  const onDelete = item => {
    DeletePoolApi(item.PoolId, token)
      .then(res => {
        const poolDetailsIndex = allPools.findIndex(
          a => a.PoolId === item.PoolId
        );
        const updatedPools = allPools.filter(a => a.PoolId !== item.PoolId);

        let index = 0;
        if (poolDetails?.PoolId === item.PoolId) {
          index =
            poolDetailsIndex === updatedPools.length ? 0 : poolDetailsIndex + 1;
        } else {
          index = updatedPools.findIndex(a => a.PoolId === poolDetails?.PoolId);
        }

        if (res.success) {
          setAllPools(updatedPools);
          handlePoolClick(updatedPools[index]);
          const info = {
            status: "success",
            message: labels.SNAPSHOT_POOL_DELETE_SUCCESS
          };
          dispatch(notificationAction(info));
        } else {
          const info = {
            status: "error",
            message: labels.SNAPSHOT_POOL_DELETE_ERROR
          };
          dispatch(notificationAction(info));
        }
      })
      .catch(err => console.log("DeletePoolApi err", err));
  };

  const savePool = updatePoolDetail => {
    let pd = poolDetails;
    if (updatePoolDetail) pd = updatePoolDetail;

    if (pd?.Title) {
      const pool = {
        PoolId: pd.PoolId,
        Title: pd.Title,
        CollaborationIds: (pd.Collaborations || []).map(a => a.CollaborationId)
      };

      if (pd.PoolId > 1) {
        UpdatePoolApi(token, pool)
          .then(() => {
            GetPools(pd.PoolId);
            const info = {
              status: "success",
              message: labels.Pool_Updated_Succesfully
            };
            dispatch(notificationAction(info));
          })
          .catch(err => console.log("UpdatePoolApi err", err));
      } else {
        AddPoolApi(token, pool)
          .then(res => {
            if (res.success) {
              GetPools(res.items.PoolId);
              const info = {
                status: "success",
                message: labels.Pool_Added_Succesfully
              };
              dispatch(notificationAction(info));
            } else {
              const info = {
                status: "error",
                message: labels.Pool_Already_Exist
              };
              dispatch(notificationAction(info));
            }
          })
          .catch(err => console.log("AddPoolApi err", err));
      }
    }
  };

  const GetPools = currentPoolId => {
    // setIsPoolsLoading(true);
    GetPoolsApi(token)
      .then(res => {
        if (res.items && res.items.length > 0) {
          setAllPools(res.items);
          // setIsPoolsLoading(false);
          handlePoolClick(res.items.find(a => a.PoolId === currentPoolId));
        } else {
          // setIsPoolsLoading(false);
        }
      })
      .catch(err => {
        console.log("GetPools err", err);
        // setIsPoolsLoading(false);
      });
  };

  const handleAddNewPool = () => {
    setPoolDetails({ PoolId: -1, Title: "", Collaborations: [] });
  };

  return (
    <>
      <div className="flex">
        {allPools?.length > 0 && (
          <div className="flex-1/3 max-xl:flex-2/3 h-[calc(100vh-272px)] max-md:h-auto overflow-y-auto border-r-1 border-r-[#C6C6C6] pr-4 max-md:pr-0 max-md:border-r-0">
            <List>
              {allPools &&
                allPools.map(item => (
                  <ListItem
                    key={item.PoolId}
                    text={item.Title}
                    onClick={() => handlePoolClick(item)}
                    isSelected={item.PoolId === poolDetails?.PoolId}
                    rightClassName="p-0 bg-[transparent]"
                    right={
                      <Dropdown
                        overlayClassName="[&_.ant-dropdown-menu]:!py-[6px] [&_.ant-dropdown-menu-item]:!py-[6px]"
                        menu={{
                          ...DropdownMenu,
                          onClick: e => {
                            e.domEvent.stopPropagation();
                            if (e.key === "delete") {
                              onDelete(item);
                            }
                          }
                        }}
                        onClick={e => e.stopPropagation()}
                        trigger={["click"]}
                      >
                        <Button
                          type="text"
                          icon={<MoreOutlined className="!text-[20px]" />}
                        />
                      </Dropdown>
                    }
                  />
                ))}
            </List>
          </div>
        )}
        {(md || allPools?.length <= 0) && (
          <div className="flex-2/3 h-[calc(100vh-272px)] overflow-y-auto">
            <EmptyView
              icon={<EmptyWorkplacePreview />}
              title={"Nothing selected yet"}
              noBorder
              detail={
                <>
                  <div>
                    Click on an item from the list on the left to see full
                    details and take action.
                  </div>
                  <Button
                    type="primary"
                    className="mt-5"
                    onClick={handleAddNewPool}
                  >
                    <PlusOutlined />
                    Create New
                  </Button>
                </>
              }
            />
          </div>
        )}
      </div>

      {/* Pool details & collaborations */}
      {!!poolDetails?.PoolId && (
        <PoolDetail
          onClose={() => {
            setPoolDetails({ PoolId: 0, Title: "", Collaborations: [] });
          }}
          savePool={savePool}
          UserId={UserId}
          token={token}
          poolDetails={poolDetails}
          setPoolDetails={setPoolDetails}
        />
      )}

      <div></div>
    </>
  );
};

export default Pools;

export const DropdownMenu = {
  items: [
    {
      key: "delete",
      label: (
        <div className="flex items-center justify-between gap-2">
          Delete <DeleteIcon />
        </div>
      )
    }
  ]
};
