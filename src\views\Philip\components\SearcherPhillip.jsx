import { Col, Row, Steps } from "antd";
import { filter, isEmpty, map } from "lodash";
import moment from "moment";
import Slider from "rc-slider";
import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { notificationAction } from "../../../actions/notification";
import CapsuleList from "../../../common/CapsuleList/CapsuleList";
import LoadingMask from "../../../common/LoadingMask/LoadingMask";
import Select from "../../../common/Select/Select";
import {
  getSearcherPhillipInfoApi,
  resumeSearchesApi,
  saveResumeSearchApi
} from "../../../components/Phillip/phillipApi";
import { dateFormate } from "../../../utilities/helpers";
import { getCurrenciesApi } from "../../../views-alpha/Collaboration/common/collaborationApi";
import "./phillip.scss";
import { getCompaniesApi } from "../../../views-alpha/Workplace/WorkplaceApi";
import { getAllLocationsApi } from "../../../views-alpha/Opportunity/opportunityApi";
const LOOKUPTYPES = {
  PROFILE: 1,
  SKILL: 2,
  KEYWORD: 3,
  CERTIFICATION: 4,
  COUNTRY: 5,
  LANGUAGE: 6,
  INDUSTRY: 7
};

const Handle = Slider.Handle;

const SearcherPhillip = props => {
  const durationTypeList = [
    { value: "Days", label: "Days" },
    { value: "Weeks", label: "Weeks" },
    { value: "Months", label: "Months" },
    { value: "Years", label: "Years" }
  ];

  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentStep, setCurrentStep] = useState(0);
  const [isJobParsed, setIsJobParsed] = useState(false);
  const [isLookupFiltered, setIsLookupFiltered] = useState(false);
  const [isOpportunitySent, setIsOpportunitySent] = useState(false);
  const [extractedSearchSettings, setExtractedSearchSettings] = useState({
    Certifications: [],
    Countries: [],
    Industries: [],
    Keywords: [],
    Languages: [],
    Profiles: [],
    Skills: []
  });
  const [allWorkplaces, setAllWorkplaces] = useState([]);
  const [allCurrencies, setAllCurrencies] = useState([]);
  const [allLocationsList, setAllLocationsList] = useState([]);
  const [currentSearch, setCurrentSearch] = useState({
    OpportunityName: "",
    HourlyRate: "",
    Countries: [],
    selectedHourlyRateType: {},
    selectedWorkplace: {}
  });
  const [keywords, setKeywords] = useState("");
  const [validation, setValidation] = useState({});

  useEffect(() => {
    getCompaniesApi().then(data => {
      const { success, items } = data;
      if (success) {
        const workplaces = items.map(item => ({
          ...item,
          value: item.UserCompanyId,
          label: item.CompanyName
        }));
        setAllWorkplaces(workplaces);
        setCurrentSearch({
          ...currentSearch,
          selectedWorkplace: workplaces[0]
        });
      }
    });

    getCurrenciesApi().then(data => {
      const { success, items } = data;
      if (success) {
        const currencies = items.map(item => ({
          ...item,
          value: item.CurrencyId,
          label: item.Name
        }));
        setAllCurrencies(currencies);
        setCurrentSearch({
          ...currentSearch,
          selectedHourlyRateType: currencies[0],
          Duration: 4,
          DurationType: durationTypeList[1]
        });
      }
    });

    getAllLocationsApi().then(response => {
      if (response.success) {
        const locationsList = response.items.map(item => ({
          ...item,
          value: item.CountryId,
          label: item.CountryName
        }));
        setAllLocationsList(locationsList);
      }
    });
  }, []);

  const handleJobParse = () => {
    const { labels } = props;
    if (!isEmpty(keywords)) {
      setIsLoading(true);
      getSearcherPhillipInfoApi({ keywords })
        .then(response => {
          if (response.success) {
            let {
              Certifications = [],
              Countries = [],
              Industries = [],
              Keywords = [],
              Languages = [],
              Profiles = [],
              Skills = []
            } = response.items;

            Certifications = map(Certifications, certification => ({
              Id: certification.CertificationId,
              ExpLevel: 0,
              LookupValue: certification.CertificationValue
            }));
            Countries = map(Countries, country => ({
              Id: country.CountryId,
              ExpLevel: 0,
              LookupValue: country.CountryName
            }));
            Industries = map(Industries, industry => ({
              Id: industry.IndustryId,
              ExpLevel: 0,
              LookupValue: industry.IndustryValue
            }));
            Keywords = map(Keywords, keyword => ({
              Id: keyword.KeywordId,
              ExpLevel: 0,
              LookupValue: keyword.KeywordValue
            }));
            Languages = map(Languages, language => ({
              Id: language.LanguageId,
              ExpLevel: 0,
              LookupValue: language.LanguageValue
            }));
            Profiles = map(Profiles, profile => ({
              Id: profile.ProfileId,
              ExpLevel: 0,
              LookupValue: profile.ProfileValue
            }));
            Skills = map(Skills, skill => ({
              Id: skill.SkillId,
              ExpLevel: 0,
              LookupValue: skill.SkillValue
            }));

            if (
              isEmpty(Certifications) &&
              isEmpty(Countries) &&
              isEmpty(Industries) &&
              isEmpty(Keywords) &&
              isEmpty(Languages) &&
              isEmpty(Profiles) &&
              isEmpty(Skills)
            ) {
              const info = {
                message: labels.searcherOnboardingInvalidDescription,
                status: "success"
              };
              notificationAction(info);
              setIsLoading(false);
              return;
            }
            const saveResumeParams = {
              Certifications,
              Countries,
              Industries,
              Keywords,
              Languages,
              Profiles,
              Skills,
              SavedSearchId: 0,
              SearchName: `Philip SavedSearch at ${dateFormate(new Date())}`
            };
            setExtractedSearchSettings(saveResumeParams);
            setIsLoading(false);
            setIsJobParsed(true);
            setCurrentStep(1);
            const element = document.getElementById("step2");
            element.scrollIntoView();
          } else {
            setIsLoading(false);
          }
        })
        .catch(() => setIsLoading(false));
    } else {
      setValidation({
        ...validation,
        textareaError: true
      });
    }
  };

  const saveResumeSettings = () => {
    setIsLoading(true);
    const {
      Certifications,
      Industries,
      Keywords,
      Profiles,
      Skills
    } = extractedSearchSettings;
    saveResumeSearchApi(extractedSearchSettings)
      .then(response => {
        setIsSavedSearchCreated(true);
        setOnBoardSavedSearchId(response.items.SavedSearchId);
        setIsLookupFiltered(true);
        setIsLoading(false);
        let CertificationIds = [],
          Countries = [],
          IndusteryIds = [],
          KeywordIds = [],
          Languages = [],
          ProfileIds = [],
          SkillIds = [];

        CertificationIds = map(
          Certifications,
          certification => certification.Id
        );
        Countries = map(Countries, country => country.Id);
        IndusteryIds = map(Industries, industry => industry.Id);
        KeywordIds = map(Keywords, keyword => keyword.Id);
        Languages = map(Languages, language => language.Id);
        ProfileIds = map(Profiles, profile => profile.Id);
        SkillIds = map(Skills, skill => skill.Id);
        const params = {
          CertificationIds,
          Countries,
          IndusteryIds,
          KeywordIds,
          Languages,
          ProfileIds,
          SkillIds,
          Type: "Only20",
          AvailabilityType: 1,
          AvailabilityDate: "05/09/2018",
          page: 1,
          limit: 10,
          OpportunityStartDate: moment()
            .add(1, "m")
            .startOf("month"),
          prodooStartDateTime: `${moment(new Date()).format(
            "D MMMM YYYY - HH:mm"
          )}`
        };
        setOpportunityParams(params);
        setCurrentStep(2);
        const element = document.getElementById("step3");
        element.scrollIntoView();
      })
      .catch(() => setIsLoading(false));
  };

  const saveResumeSearch = () => {
    setIsLoading(true);
    const { labels } = props;
    let {
      OpportunityName,
      HourlyRate,
      selectedHourlyRateType,
      selectedWorkplace,
      CompanyName
    } = currentSearch;
    if (!OpportunityName) {
      const info = {
        message: labels?.searcher_phillip_automate_no_oppertunity_alert,
        status: "error"
      };
      props.notificationAction(info);
      setIsLoading(false);
      setValidation({
        title: true
      });
      return;
    }
    if (!HourlyRate) {
      const info = {
        message: labels?.searcher_phillip_automate_oppertunity_hourlyrate_alert,
        status: "error"
      };
      props.notificationAction(info);
      setIsLoading(false);
      setValidation({
        hourelyRate: true
      });
      return;
    }
    if (allWorkplaces.length < 1 && !CompanyName) {
      const info = {
        message:
          labels?.searcher_phillip_automate_oppertunity_no_workplace_alert,
        status: "error"
      };
      props.notificationAction(info);
      setIsLoading(false);
      setValidation({
        companyName: true
      });
      return;
    }
    const params = {
      ...opportunityParams,
      HourlyRate,
      OpportunityName,
      CompanyID: CompanyName && 0,
      CompanyName,
      CurrencyId: selectedHourlyRateType?.value,
      CompanyId: selectedWorkplace?.value,
      IsPhilipApp: true
    };
    resumeSearchesApi(params)
      .then(response => {
        if (response.success) {
          setIsLoading(false);
          setIsJobParsed(false);
          setIsLookupFiltered(false);
          setIsOpportunitySent(false);
          setCurrentStep(0);
          setExtractedSearchSettings({
            Certifications: [],
            Countries: [],
            Industries: [],
            Keywords: [],
            Languages: [],
            Profiles: [],
            Skills: []
          });
          setOpportunityParams({});
          setAllWorkplaces([]);
          setAllCurrencies([]);
          setCurrentSearch({
            OpportunityName: "",
            HourlyRate: "",
            selectedHourlyRateType: {},
            selectedWorkplace: {}
          });
          setKeywords("");
          const info = {
            message: "Opportunity sent successfully",
            status: "success"
          };
          getCompaniesApi();
          props.notificationAction(info);
          return;
        } else {
          const info = {
            message: response?.message,
            status: "error"
          };
          props.notificationAction(info);
          setIsLoading(false);
        }
        props.onResumeShortlistUpdate();
      })
      .catch(() => setIsLoading(false));
  };

  const extractedSettingUpdate = (type, item) => {
    let {
      Certifications,
      Countries,
      Industries,
      Keywords,
      Languages,
      Profiles,
      Skills
    } = extractedSearchSettings;
    switch (type) {
      case LOOKUPTYPES.PROFILE: {
        Profiles = filter(Profiles, a => a.Id != item.Id);
        break;
      }
      case LOOKUPTYPES.SKILL: {
        Skills = filter(Skills, a => a.Id != item.Id);
        break;
      }
      case LOOKUPTYPES.CERTIFICATION: {
        Certifications = filter(Certifications, a => a.Id != item.Id);
        break;
      }
      case LOOKUPTYPES.COUNTRY: {
        Countries = filter(Countries, a => a.Id != item.Id);
        break;
      }
      case LOOKUPTYPES.INDUSTRY: {
        Industries = filter(Industries, a => a.Id != item.Id);
        break;
      }
      case LOOKUPTYPES.KEYWORD: {
        Keywords = filter(Keywords, a => a.Id != item.Id);
        break;
      }
      case LOOKUPTYPES.LANGUAGE: {
        Languages = filter(Languages, a => a.Id != item.Id);
        break;
      }
    }
    setExtractedSearchSettings({
      ...extractedSearchSettings,
      Profiles,
      Skills,
      Certifications,
      Keywords,
      Languages,
      Countries
    });
  };

  const handleDateChange = date => {
    setDate(moment(date));
    setIsDatePickerOpen(false);
  };

  const onActionButtonClick = currentPage => {
    setCurrentPage(currentPage + 1);
  };

  const onFormFieldChange = e => {
    const { name, value } = e.target;
    setValidation({
      title: name == "OpportunityName" && false,
      hourelyRate: name == "HourlyRate" && false,
      CompanyName: name == "CompanyName" && false
    });
    setCurrentSearch({
      ...currentSearch,
      [name]: value
    });
  };

  const onFormSelectChange = (name, value) => {
    setCurrentSearch({
      ...currentSearch,
      [name]: value
    });
  };

  const handleSliderChange = value => {
    if (value) {
      setCurrentSearch({
        ...currentSearch,
        Duration: value
      });
    }
  };

  const handle = props => {
    const { value, dragging, index, ...restProps } = props;
    return (
      <Handle value={value} key={index} {...restProps}>
        {value}
      </Handle>
    );
  };

  const handleLocationSelect = ({ label, value }) => {
    const alreadySelcted = filter(
      currentSearch.Countries,
      country => country.CountryId === value
    );
    if (!isEmpty(alreadySelcted)) return;
    setCurrentSearch({
      ...currentSearch,
      Countries: [
        ...currentSearch.Countries,
        { CountryId: value, CountryName: label }
      ]
    });
  };

  const handleCountryDelete = ({ selectedCountry }) => {
    const filterCountries = filter(
      currentSearch.Countries,
      country => country.CountryId !== selectedCountry.CountryId
    );
    setCurrentSearch({
      ...currentSearch,
      Countries: filterCountries
    });
  };

  const { labels } = props;
  const items = [
    {
      title: labels?.searcher_phillip_automate_step1_title,
      description: labels?.searcher_phillip_automate_step1_description
    },
    {
      title: labels?.searcher_phillip_automate_step2_title,
      description: labels?.searcher_phillip_automate_step2_description
    },
    {
      title: labels?.searcher_phillip_automate_step3_title,
      description: labels?.searcher_phillip_automate_step3_description
    }
  ];
  const { Profiles, Skills } = extractedSearchSettings;

  return (
    <React.Fragment>
      {isLoading && <LoadingMask text="" />}
      <div className="phillips-search-steps-">
        <div
          className="phillip-component searcher-phillip marketing-page page-2"
          style={{
            padding: "20px 12px",
            background: "#fff!important",
            height: "-webkit-fill-available"
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              height: "100%"
            }}
          >
            <div>
              <Steps current={currentStep} items={items} />
            </div>
            <div
              style={{
                flex: 1,
                marginTop: "25px"
              }}
            >
              <Row
                gutter={[24, 24]}
                style={{
                  height: "100%"
                }}
              >
                <Col
                  xs={24}
                  sm={8}
                  md={8}
                  className="steps"
                  style={{
                    margin: 0
                  }}
                >
                  <div
                    className="step"
                    style={{
                      background: "#F1EFFB",
                      borderRadius: "12px",
                      padding: "12px",
                      height: "100%",
                      border: validation?.textareaError
                        ? "1px solid #f54949"
                        : null
                    }}
                  >
                    <div
                      className="js_textarea_wrap body"
                      style={{
                        height: "100%",
                        width: "100%"
                      }}
                    >
                      <div className="text-area-wrapper">
                        <textarea
                          autoComplete="off"
                          className="js_textarea"
                          style={{
                            flex: 1
                          }}
                          data-testid="searcher-phillip-keyword-input"
                          placeholder={
                            labels?.searcher_phillip_automate_step1_textarea_placeholder
                          }
                          onChange={e => {
                            setKeywords(e.target.value);
                            setValidation({
                              textareaError: false
                            });
                          }}
                          value={keywords}
                        >
                          {keywords}
                        </textarea>
                        <button onClick={handleJobParse} className="action-btn">
                          {labels?.searcher_phillip_automate_step1_button_text}
                        </button>
                      </div>
                    </div>
                  </div>
                </Col>
                <Col
                  xs={24}
                  sm={8}
                  md={8}
                  className="steps"
                  style={{ margin: 0 }}
                >
                  {
                    <div
                      id="step2"
                      className={`step ${!isJobParsed ? "disabled" : ""}`}
                      style={{
                        background: "#F1EFFB",
                        borderRadius: "12px",
                        padding: "12px",
                        height: "100%",
                        display: "flex",
                        flexDirection: "column"
                      }}
                    >
                      <div
                        className="body"
                        style={{
                          flex: 1
                        }}
                      >
                        <div className="settings-container">
                          <label>
                            {labels?.searcher_phillip_automate_step2_firstpara}
                          </label>
                          {Profiles &&
                            Profiles.map(item => (
                              <label
                                data-testid="resume-edit-suggested-role-item"
                                className="draggable"
                                onClick={() =>
                                  extractedSettingUpdate(
                                    LOOKUPTYPES.PROFILE,
                                    item
                                  )
                                }
                                key={item.Id}
                              >
                                {item.LookupValue}
                              </label>
                            ))}
                        </div>
                        <div className="settings-container">
                          <label>
                            {labels?.searcher_phillip_automate_step2_secondpara}
                          </label>
                          {Skills &&
                            Skills.map(item => (
                              <label
                                data-testid="resume-edit-suggested-role-item"
                                className="draggable"
                                onClick={() =>
                                  extractedSettingUpdate(
                                    LOOKUPTYPES.SKILL,
                                    item
                                  )
                                }
                                key={item.Id}
                              >
                                {item.LookupValue}
                              </label>
                            ))}
                        </div>
                      </div>
                      <button
                        onClick={saveResumeSettings}
                        className="action-btn"
                      >
                        {labels?.searcher_phillip_automate_step2_button_text}{" "}
                      </button>
                    </div>
                  }
                </Col>
                <Col
                  xs={24}
                  sm={8}
                  md={8}
                  className="steps"
                  style={{ margin: 0 }}
                >
                  {
                    <div
                      id="step3"
                      className={`step ${!isLookupFiltered ? "disabled" : ""}`}
                      style={{
                        background: "#F1EFFB",
                        borderRadius: "12px",
                        padding: "12px",
                        height: "100%",
                        display: "flex",
                        flexDirection: "column"
                      }}
                    >
                      <div
                        className="body"
                        style={{
                          flex: 1
                        }}
                      >
                        <label>
                          {labels?.searcher_phillip_automate_step3_firstpara}
                        </label>
                        <input
                          type="text"
                          autoComplete="off"
                          name="OpportunityName"
                          className="input-text opportunity-name"
                          placeholder={
                            labels?.SearcherOpportunityDraftDetailTitlePlaceholder
                          }
                          style={{
                            background: "#fff",
                            borderRadius: "8px",
                            margin: "10px 0px",
                            height: "36px",
                            border: validation?.title
                              ? "1px solid #f54949"
                              : "1px solid #fff"
                          }}
                          value={
                            (currentSearch && currentSearch.OpportunityName) ||
                            ""
                          }
                          onChange={onFormFieldChange}
                          onBlur={onFormFieldChange}
                          data-testid="input-txt-opportunity-hourlyRate"
                        />
                        <label>
                          {labels?.searcher_phillip_automate_step3_secondpara}
                        </label>
                        <Row
                          gutter={[18, 4]}
                          style={{
                            marginTop: "10px",
                            alignItems: "center"
                          }}
                        >
                          <Col span={24}>
                            <label
                              className="form-label"
                              style={{
                                margin: 0,
                                color: "#8d82ab"
                              }}
                            >
                              {labels?.SearcherOpportunityDraftDetailDuration}:
                            </label>
                          </Col>
                          <Col span={12}>
                            <Slider
                              name="durationSlider"
                              min={0}
                              max={12}
                              defaultValue={4}
                              value={currentSearch?.Duration}
                              handle={handle}
                              onChange={handleSliderChange}
                            />
                          </Col>
                          <Col span={12}>
                            <Select
                              name="DurationType"
                              className={`select-input select-Workplace dropdown-phillip-searcher`}
                              placeholder={
                                labels?.SearcherOpportunityDraftDetailDurationPlaceholder
                              }
                              value={currentSearch?.DurationType?.value}
                              onChange={selectedOption => {
                                setCurrentSearch({
                                  ...currentSearch,
                                  DurationType: selectedOption
                                });
                              }}
                              options={durationTypeList}
                              clearable={false}
                              searchable={false}
                            />
                          </Col>
                          <Col span={12}>
                            <input
                              type="text"
                              autoComplete="off"
                              name="HourlyRate"
                              style={{
                                background: "#fff",
                                borderRadius: "8px",
                                height: "36px",
                                border: validation?.hourelyRate
                                  ? "1px solid #f54949"
                                  : "1px solid #fff"
                              }}
                              className="input-text opportunity-rate"
                              placeholder={
                                labels?.searcher_phillip_automate_step3_hourly_placeholder
                              }
                              value={
                                (currentSearch && currentSearch.HourlyRate) ||
                                ""
                              }
                              onChange={onFormFieldChange}
                              onBlur={onFormFieldChange}
                              data-testid="input-txt-opportunity-hourlyRate"
                            />
                          </Col>
                          <Col span={12}>
                            <Select
                              name="FeeCurrencyType"
                              className={`select-input select-hourlyRate dropdown-phillip-searcher ${
                                currentSearch.HourlyRateType ? "inValid" : ""
                              }`}
                              placeholder={
                                labels?.SearcherOpportunityDraftDetailHourlyFeeDropDownPlaceholder
                              }
                              value={
                                (currentSearch &&
                                  currentSearch.selectedHourlyRateType) ||
                                ""
                              }
                              onChange={selectedOption =>
                                onFormSelectChange(
                                  "selectedHourlyRateType",
                                  selectedOption
                                )
                              }
                              clearable={false}
                              searchable={false}
                              options={allCurrencies}
                            />
                          </Col>
                          <Col span={24}>
                            <Select
                              name="LOCATION"
                              placeholder={
                                labels?.SearcherOpportunityDraftDetailLocationPlaceholder
                              }
                              className={`select-input select-Workplace dropdown-phillip-searcher`}
                              onChange={handleLocationSelect}
                              options={allLocationsList}
                              clearable={true}
                              searchable={true}
                            />
                          </Col>
                          {currentSearch?.Countries?.length > 0 && (
                            <Col span={24} className="form-row">
                              <CapsuleList className="selected-locations-list">
                                {map(currentSearch?.Countries, item => (
                                  <label
                                    data-testid="resume-edit-suggested-role-item"
                                    className="draggable"
                                    style={{
                                      marginRight: "8px"
                                    }}
                                    onClick={() =>
                                      handleCountryDelete({
                                        selectedCountry: item
                                      })
                                    }
                                    key={item.CountryId}
                                  >
                                    {item.CountryName}
                                  </label>
                                ))}
                              </CapsuleList>
                            </Col>
                          )}
                          <Col span={24} style={{ marginBottom: "8px" }}>
                            <label>
                              {
                                labels?.searcher_phillip_automate_step3_thirdpara
                              }
                            </label>
                          </Col>
                          <Col span={24}>
                            <Select
                              name="WorkplaceId"
                              className={`select-input select-Workplace dropdown-phillip-searcher`}
                              placeholder={
                                labels?.SearcherOpportunityDraftDetailCompanyPlaceholder
                              }
                              value={
                                (currentSearch &&
                                  currentSearch.selectedWorkplace) ||
                                ""
                              }
                              onChange={selectedOption =>
                                onFormSelectChange(
                                  "selectedWorkplace",
                                  selectedOption
                                )
                              }
                              clearable={false}
                              searchable={false}
                              options={
                                allWorkplaces.length > 0
                                  ? allWorkplaces
                                  : [
                                      {
                                        label: "Add new workplace",
                                        value: 0
                                      }
                                    ]
                              }
                            />
                          </Col>
                        </Row>

                        <div
                          style={{
                            marginTop: "3px"
                          }}
                        >
                          {currentSearch?.selectedWorkplace?.value == 0 && (
                            <input
                              type="text"
                              autoComplete="off"
                              name="CompanyName"
                              className="input-text opportunity-rate"
                              placeholder={labels?.companyNamePlaceholder}
                              style={{
                                background: "#fff",
                                borderRadius: "8px",
                                height: "36px",
                                marginBottom: "10px",
                                border: validation?.companyName
                                  ? "1px solid #f54949"
                                  : "1px solid #fff"
                              }}
                              value={
                                (currentSearch && currentSearch?.CompanyName) ||
                                ""
                              }
                              onChange={onFormFieldChange}
                              onBlur={onFormFieldChange}
                              data-testid="input-txt-opportunity-hourlyRate"
                            />
                          )}
                        </div>
                      </div>
                      <button onClick={saveResumeSearch} className="action-btn">
                        {labels?.searcher_phillip_automate_step3_button_text}{" "}
                      </button>
                    </div>
                  }
                </Col>
              </Row>
            </div>
          </div>
        </div>
      </div>
    </React.Fragment>
  );
};

const mapStateToProps = ({ systemLabel, userInfo }) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  let { FirstName } = user;
  FirstName =
    FirstName !== undefined ? FirstName : sessionStorage.getItem("userName");
  return { labels, FirstName };
};
export default connect(mapStateToProps, { notificationAction })(
  SearcherPhillip
);
