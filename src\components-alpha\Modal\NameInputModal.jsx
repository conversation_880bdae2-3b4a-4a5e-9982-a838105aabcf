import { Modal, Input, Button } from "antd";
import React from "react";
import { twMerge } from "tailwind-merge";

const NameInputModal = ({
  isOpen,
  title,
  placeholder = "",
  confirmText = "Save",
  cancelText = "Cancel",
  initialValue = "",
  error,
  isLoading = false,
  isConfirmDisabled = false,
  onClose,
  onConfirm,
  className
}) => {
  const [value, setValue] = React.useState(initialValue);

  React.useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  const handleOk = () => {
    onConfirm(value.trim());
  };

  return (
    <Modal
      open={isOpen}
      onCancel={!isLoading ? onClose : undefined}
      footer={null}
      className={twMerge(
        `rounded-[25px] [&_.ant-modal-content]:!p-0`,
        className
      )}
      // title={title}
      closable={false}
      centered
      maskClosable={!isLoading}
      // className="rounded-2xl"
    >
      <div className="p-6 pb-0 border-b-1 border-b-[#EAE5FC]">
        <div className="text-lg font-semibold mb-4">{title}</div>
      </div>
      <div className="p-6">
        <Input
          value={value}
          onChange={e => setValue(e.target.value)}
          placeholder={placeholder}
          className="rounded-xl bg-[#f4f2ff] border-none py-2 px-4 mb-3"
          disabled={isLoading}
        />

        {error && (
          <div className="text-red-500 text-sm flex items-center gap-2 mb-3 mt-2 pl-4">
            {error}
          </div>
        )}

        <div className="flex justify-end gap-3 mt-4">
          <Button onClick={onClose} disabled={isLoading}>
            {cancelText}
          </Button>
          <Button
            type="primary"
            className="bg-[var(--purple)] text-white px-6"
            onClick={handleOk}
            disabled={
              isConfirmDisabled || isLoading || !value || value?.length > 50
            }
            loading={isLoading}
          >
            {confirmText}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default NameInputModal;
