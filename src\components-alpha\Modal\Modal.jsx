import { Modal as ModalPopup } from "antd";
import { twMerge } from "tailwind-merge";
import { useHistoryStateHandler } from "../../utilities-alpha/hooks";
import CloseModal from "../../assets-alpha/images/svg/close-modal.svg?react";

const Title = ({ children }) => <div className="modal-title">{children}</div>;
const Body = ({ children }) => <div className="modal-body">{children}</div>;
const Footer = ({ children }) => <div className="modal-footer">{children}</div>;

const Modal = ({
  open,
  onCloseModal,
  children,
  className,
  width = 750,
  footer = null,
  title,
  closable = false,
  closeIcon = null,
  maskClosable = false,
  wrapClassName
}) => {
  // Use the custom hook to handle history state when the drawer opens or closes
  useHistoryStateHandler(!!open, onCloseModal, "modal");

  return (
    <ModalPopup
      open={open}
      onCancel={() => onCloseModal(false)}
      footer={footer}
      className={twMerge(
        `rounded-[25px] max-md:rounded-[25px_25px_0_0] max-md:!max-w-full !m-auto !top-0 overflow-hidden 
        [&_.ant-modal-content]:!p-0 max-md:[&_.ant-modal-content]:!rounded-b-none
        !pb-0 max-md:[&_.ant-modal-body]:min-h-[calc(100vh-8px)]
        [&_.ant-modal-close]:hover:!bg-transparent
        `,
        className
      )}
      closable={closable}
      closeIcon={closeIcon ? closeIcon : <CloseModal />} // Custom close icon
      maskClosable={maskClosable}
      width={width}
      zIndex={100}
      title={title}
      wrapClassName={twMerge(
        "flex items-center p-[1.2rem] max-md:p-0 max-md:pt-[8px]",
        wrapClassName
      )}
    >
      {children}
    </ModalPopup>
  );
};

Modal.defaultProps = {
  className: "",
  onCloseModal: () => {}
};

Modal.Title = Title;
Modal.Body = Body;
Modal.Footer = Footer;

export default Modal;
