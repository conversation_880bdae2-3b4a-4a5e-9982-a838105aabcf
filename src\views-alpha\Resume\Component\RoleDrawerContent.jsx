import { LoadingOutlined, MoreOutlined, PlusOutlined } from "@ant-design/icons";
import { useQueryClient } from "@tanstack/react-query";
import { Button, Checkbox, Dropdown, Grid } from "antd";
import clsx from "clsx";
import { isNull, isObject, isUndefined, pickBy } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { twMerge } from "tailwind-merge";
import { notificationAction } from "../../../actions-alpha/notification";
import { ApiUrl } from "../../../api-alpha/apiUrls";
import ArrowDownIcon from "../../../assets-alpha/images/svg/arrow-down-icon.svg?react";
import CustomInput from "../../../common-alpha/CustomInput/CustomInput";
import DatePicker from "../../../common-alpha/DatePicker/Datepicker";
import DeleteModal from "../../../common-alpha/DeleteModal/DeleteModal";
import CommonDrawer from "../../../common-alpha/Drawer/Drawer";
import CustomSelect from "../../../common-alpha/Select/Select";
import {
  EducationLevelsList,
  RolesLevels
} from "../../../utilities-alpha/constant";
import { getYear } from "../../../utilities-alpha/helper";
import {
  DropdownMenu,
  extractMappedFields,
  findEducationDegree,
  YearOptions
} from "../constant";
import {
  useDeleteResumeApi,
  useGetResumeProfile,
  useResumeLookup,
  useResumeProfile
} from "../resumeApi";
import { InfoItemCard } from "./ExperienceSection";

// Role Drawer Content Component
const RoleDrawerContent = ({
  info,
  setIsSuggestionsDrawerOpen,
  setOpenModal
}) => {
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [addNew, setAddNew] = useState(false);
  const [selectedRole, setSelectedRole] = useState();
  const dispatch = useDispatch();

  const labels = useSelector(state => state.systemLabel.labels);
  const resume = useSelector(state => state.systemLabel.labels?.resume);
  const queryClient = useQueryClient();
  const { md } = Grid.useBreakpoint();
  const [searchKeys, setSearchKeys] = useState([]);
  const handleSearch = index => value => {
    const newSearchKeys = [...searchKeys];
    newSearchKeys[index] = value;
    setSearchKeys(newSearchKeys);
  };

  const {
    data: lookupData,
    isFetching: isLoadingData,
    queries
  } = useResumeLookup({
    urls: info?.resumeLookupUrls,
    keys: info?.selectKeys,
    searchKeys
  });
  const { data: profileData, isFetching: loadingRole } = useGetResumeProfile({
    url: info?.getProfileUrl,
    keys: { ...info?.selectKeys?.[0], ...info?.keys }
  });
  useEffect(() => {
    if (loadingRole) return;
    setAddNew(profileData?.length === 0);
  }, [profileData?.length, loadingRole]);

  const { mutate: resumeProfileApi, isPending: isSaving } = useResumeProfile();
  const { mutate: deleteResume, isPending: isDeleting } = useDeleteResumeApi();
  const formattedDate = date => date?.format();
  const cleanedData = obj =>
    pickBy(obj, val => !isUndefined(val) && !isNull(val) && !isObject(val));

  const onAddRole = () => {
    const payload = cleanedData({
      suffixUrl: info?.addProfileUrl,
      ...cleanedData(selectedRole),
      ...cleanedData(selectedRole?.role),
      ...cleanedData(selectedRole?.ProfileValue),
      [info?.keys?.year]:
        info?.formatDate?.(`${selectedRole?.year?.value}`) ||
        selectedRole?.year?.value,
      [info?.keys?.Title]: selectedRole?.Title,
      [info?.showEducationLevel ? "EducationTypeId" : "ExperienceLevel"]:
        selectedRole?.level?.value ?? 1
    });
    resumeProfileApi(
      {
        suffixUrl: info?.addProfileUrl,
        ...payload
      },
      {
        onSuccess: data => {
          if (data?.success) {
            const notify = {
              message: labels[data?.message] || data?.message,
              status: "success"
            };
            dispatch(notificationAction(notify));
            queryClient.invalidateQueries({
              queryKey: [ApiUrl.ResumeEdit.GetMyResume]
            });
            queryClient.invalidateQueries({ queryKey: [info?.getProfileUrl] });
            setSelectedRole(null);
            setAddNew(false);
          } else {
            const info = {
              message: data?.message,
              status: "error"
            };
            dispatch(notificationAction(info));
          }
        },
        onError: error => {
          const info = {
            message: error?.message,
            status: "error"
          };
          dispatch(notificationAction(info));
        }
      }
    );
  };
  const onDeleteResume = () => {
    const item = deleteDialog;
    deleteResume(
      {
        LookupTypeId: info?.LookupTypeId,
        ResumeId: item?.ResumeId,
        ObjectPK: item?.[info?.ObjectPK] || item?.[info?.keys?.value]
      },
      {
        onSuccess: data => {
          const notify = {
            message: resume?.deletedSuccessfully,
            status: data?.success ? "success" : "info"
          };
          dispatch(notificationAction(notify));
          queryClient.invalidateQueries({
            queryKey: [ApiUrl.ResumeEdit.GetMyResume]
          });
          queryClient.invalidateQueries({ queryKey: [info?.getProfileUrl] });
          setDeleteDialog(false);
        }
      }
    );
  };
  const isExistingRole =
    !selectedRole?.ResumeId &&
    profileData?.find(n => n.value === selectedRole?.role?.value);
  const infoName = useMemo(() => {
    return resume?.[info?.name] || ""; //|| info?.name;
  }, [resume, info?.name]);
  return (
    <CommonDrawer
      title={
        md
          ? infoName
          : addNew
          ? selectedRole?.ResumeId
            ? `${resume?.edit} ${infoName}`
            : `${resume?.add} ${infoName}`
          : infoName
      }
      onClose={() => setOpenModal(false)}
      open={info}
      closeIcon={
        addNew ? (
          <ArrowDownIcon
            onClick={e => {
              e.stopPropagation();
              setAddNew(false);
              setSelectedRole(null);
            }}
          />
        ) : null //show default close icon
      }
      width={400}
    >
      <div>
        <div className={clsx({ "mb-6": md })}>
          {(md || addNew) && (
            <>
              {md && (
                <label className="text-sm text-[#878787] block mb-2">
                  {selectedRole?.ResumeId ? resume?.edit : resume?.newOne}
                </label>
              )}
              {info?.showEducationLevel && (
                <CustomSelect
                  onChange={(value, item) =>
                    setSelectedRole(role => ({ ...role, level: item }))
                  }
                  mainClassName="!mb-2"
                  value={selectedRole?.level}
                  placeholder={resume?.selectDegreePlaceholder}
                  options={EducationLevelsList}
                />
              )}
              {/*  */}
              <CustomSelect
                placeholder={`${resume?.select} ${infoName}`}
                mainClassName="!mb-2"
                layout={"vertical"}
                showSearch
                optionFilterProp="label"
                value={selectedRole?.role}
                onChange={(value, item) =>
                  setSelectedRole(role => ({ ...role, role: item }))
                }
                onSearch={handleSearch(0)}
                options={lookupData?.[0]}
                loading={queries?.[0]?.isLoading}
              />
              {info?.keys?.level && (
                <CustomSelect
                  onChange={(value, item) =>
                    setSelectedRole(role => ({ ...role, level: item }))
                  }
                  value={selectedRole?.level}
                  placeholder={resume?.proficiencyLevelPlaceholder}
                  mainClassName="!mb-2"
                  options={Object.keys(RolesLevels).map(key => ({
                    value: key,
                    label: RolesLevels[key]
                  }))}
                />
              )}

              {info?.keys?.year && (
                <CustomSelect
                  onChange={(value, item) =>
                    setSelectedRole(role => ({ ...role, year: item }))
                  }
                  mainClassName="!mb-2"
                  value={selectedRole?.year}
                  placeholder={resume?.selectYearPlaceholder}
                  options={YearOptions}
                />
              )}

              {info?.showExperience && (
                <>
                  <CustomSelect
                    onChange={(value, item) =>
                      setSelectedRole(role => ({ ...role, ProfileValue: item }))
                    }
                    mainClassName="!mb-2"
                    value={selectedRole?.ProfileValue}
                    onSearch={handleSearch(1)}
                    showSearch
                    placeholder={resume?.selectPositionPlaceholder}
                    options={lookupData?.[1]}
                    loading={queries?.[1]?.isLoading}
                  />

                  <DatePicker
                    onChange={value => {
                      setSelectedRole(role => ({
                        ...role,
                        StartDate: formattedDate(value)
                      }));
                    }}
                    className="!mb-2"
                    format="DD-MM-YYYY"
                    value={selectedRole?.StartDate}
                    placeholder={resume?.fromPlaceholder}
                  />

                  <DatePicker
                    onChange={value =>
                      setSelectedRole(role => ({
                        ...role,
                        EndDate: formattedDate(value)
                      }))
                    }
                    disabled={selectedRole?.isWorking}
                    format="DD-MM-YYYY"
                    className="!mb-2"
                    value={selectedRole?.EndDate}
                    placeholder={resume?.toPlaceholder}
                  />
                  <Checkbox
                    className="!mb-2"
                    onChange={({ target: { checked } }) =>
                      setSelectedRole(role => ({
                        ...role,
                        isWorking: checked
                      }))
                    }
                    checked={selectedRole?.isWorking}
                    disabled={selectedRole?.EndDate}
                  >
                    {resume.ongoing}
                  </Checkbox>
                </>
              )}
              {(info?.showExperience || info?.showAchivenment) && (
                <>
                  {info?.keys.Title && (
                    <CustomInput
                      onChange={e =>
                        setSelectedRole(role => ({
                          ...role,
                          Title: e.target.value
                        }))
                      }
                      className="!mb-2"
                      value={selectedRole?.Title}
                      placeholder={resume?.titlePlaceholder}
                    />
                  )}
                  <CustomInput
                    onChange={e =>
                      setSelectedRole(role => ({
                        ...role,
                        Description: e.target.value
                      }))
                    }
                    type={"textarea"}
                    rows={14}
                    className="!mb-2"
                    value={selectedRole?.Description}
                    placeholder={resume?.descriptionPlaceholder}
                  />
                </>
              )}
            </>
          )}

          <div className="flex justify-between items-center">
            {info?.showSuggestion &&
              selectedRole?.role?.value &&
              (md || addNew) && (
                <Button
                  onClick={() =>
                    setIsSuggestionsDrawerOpen({
                      ...selectedRole?.role,
                      type: info?.type,
                      url: info?.getProfileUrl
                    })
                  }
                  type="link"
                  className="!p-0 !h-auto"
                  disabled={!selectedRole?.role?.value}
                >
                  {resume?.aiSuggestions}
                </Button>
              )}
            {isExistingRole && (
              <span className="text-[var(--red)] text-right">
                {resume?.alreadyExists}
              </span>
            )}
          </div>

          <div
            className={clsx(
              `flex justify-end
              max-md:z-20 max-md:bg-white max-md:fixed max-md:inset-0 max-md:top-auto max-md:px-4 max-md:py-3 
              `,
              {
                "mt-5": !(info?.showSuggestion && selectedRole?.role?.value)
              }
            )}
          >
            {md || addNew ? (
              <Button
                disabled={
                  isExistingRole ||
                  !selectedRole?.role ||
                  (info?.showExperience &&
                    (!selectedRole?.StartDate ||
                      !(selectedRole?.EndDate || selectedRole?.isWorking))) ||
                  (info?.keys?.Title && !selectedRole?.Title) ||
                  (info?.keys?.year && !selectedRole?.year)
                }
                onClick={onAddRole}
                type="primary"
                className="max-md:w-full"
                loading={isSaving}
              >
                {resume?.save}
              </Button>
            ) : (
              <Button
                onClick={() => setAddNew(true)}
                type="primary"
                loading={loadingRole}
                className="max-md:w-full"
              >
                <PlusOutlined /> {resume?.addNew}
              </Button>
            )}
          </div>
        </div>
        <div>
          {(md || !addNew) && (
            <>
              {loadingRole && <LoadingOutlined className="m-auto !table" />}
              {profileData?.length > 0 && (
                <p className="text-sm text-[#878787] !mb-2">
                  {resume?.existing} ({profileData?.length || 0})
                </p>
              )}

              {profileData?.map(item => {
                const educationLevel = info?.showEducationLevel
                  ? findEducationDegree(item)
                  : null;
                return (
                  <div
                    key={item.id}
                    className={twMerge(
                      clsx(
                        "bg-[#F3F1FD] relative p-4 rounded-[16px] mb-2 flex justify-between items-center border-1 border-[#EAE5FC]",
                        {
                          "p-0 border-0":
                            info?.showExperience || info?.showAchivenment
                        }
                      )
                    )}
                  >
                    <div className="flex-1">
                      {!info?.showExperience && !info?.showAchivenment && (
                        <>
                          <p className="font-medium">{item.label}</p>
                          {item.level && (
                            <p className="text-sm text-[#878787]">
                              {RolesLevels[item.level]}
                            </p>
                          )}
                          {info?.showEducationLevel && educationLevel?.label && (
                            <p className="text-sm text-[#878787]">
                              {/* Education: */}
                              {educationLevel?.label}
                            </p>
                          )}
                          {!!item.year && (
                            <p className="text-sm text-[#878787]">
                              {/* Year:  */}
                              {getYear(item.year)}
                            </p>
                          )}
                        </>
                      )}
                      {info?.showExperience && (
                        <>
                          <InfoItemCard
                            fields={[
                              {
                                label: resume?.experienceYear,
                                value: `${getYear(item.StartDate)}-${getYear(
                                  item.EndDate
                                ) || resume?.ongoing}`
                              },
                              {
                                label: resume?.experienceCompany,
                                value: item.IndustryValue
                              },
                              {
                                label: resume?.experiencePosition,
                                value: item.ProfileValue
                              }
                            ]}
                            description={item.Description}
                            itemKey={item.ResumeExperienceId}
                          />
                        </>
                      )}
                      {info?.showAchivenment && (
                        <>
                          <InfoItemCard
                            fields={[
                              {
                                label: resume?.achievementName,
                                value: item.AchivenmentName
                              },
                              {
                                label: resume?.achievementYear,
                                value: item.Year
                              },
                              {
                                label: resume?.achievementType,
                                value: item.Title
                              }
                            ]}
                            description={item.Description}
                            itemKey={item.ResumeOtherAchivenmentId}
                          />
                        </>
                      )}
                    </div>
                    <Dropdown
                      overlayClassName="[&_.ant-dropdown-menu]:!py-[6px] [&_.ant-dropdown-menu-item]:!py-[6px]"
                      menu={{
                        items: DropdownMenu(resume).items,
                        onClick: e => {
                          if (e.key === "edit") {
                            setSelectedRole(() => ({
                              ...item,
                              role: extractMappedFields(
                                item,
                                info?.selectKeys[0]
                              ),
                              ProfileValue: info?.showExperience
                                ? extractMappedFields(item, info?.selectKeys[1])
                                : undefined,
                              isWorking: info?.showExperience
                                ? !item.EndDate
                                : undefined,
                              year: info?.keys?.year
                                ? {
                                    label: getYear(item.year),
                                    value: getYear(item.year)
                                  }
                                : undefined,
                              level: info?.keys?.level
                                ? {
                                    value: item.level,
                                    label: RolesLevels[item.level]
                                  }
                                : info?.showEducationLevel
                                ? {
                                    value: item.EducationTypeId,
                                    label: findEducationDegree(item)?.label
                                  }
                                : undefined
                            }));

                            setAddNew(true);
                          } else if (e.key === "delete") {
                            setDeleteDialog(item);
                          }
                        }
                      }}
                      trigger={["click"]}
                    >
                      <Button
                        type="text"
                        className={twMerge(
                          clsx("!w-auto !h-auto", {
                            "!absolute right-1.5 top-2.5":
                              info?.showExperience || info?.showAchivenment
                          })
                        )}
                        icon={<MoreOutlined className="!text-[20px]" />}
                      />
                    </Dropdown>
                  </div>
                );
              })}
            </>
          )}
        </div>
        <DeleteModal
          open={deleteDialog}
          onCancel={() => setDeleteDialog(false)}
          onConfirm={onDeleteResume}
          isLoading={isDeleting}
          title={resume?.delete}
          description={resume?.deleteConfirmation?.replace(
            "@item",
            deleteDialog?.label || ""
          )}
          cancelText={labels?.ShortlistResumeListCurtainCancelBtnText}
          confirmText={labels?.delete_presentation}
        />
      </div>
    </CommonDrawer>
  );
};

export default RoleDrawerContent;
