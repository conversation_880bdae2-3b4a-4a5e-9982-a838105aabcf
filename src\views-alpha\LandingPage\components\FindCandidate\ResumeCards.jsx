import EnvironmentOutlined from "../../../../assets-alpha/images/svg/location.svg?react";
import RankingIcon from "../../../../assets-alpha/images/svg/ranking.svg?react";
import ProfileIcon from "../../../../assets-alpha/images/svg/profile.svg?react";
import AvatarIcon from "../../../../assets-alpha/images/svg/avatar-white.svg";
import { Avatar, Button } from "antd";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { publicRoutes } from "../../../../Routes/routing";
import SingleResumeDetail from "./SingleResume";
import { useSelector } from "react-redux";

const ResumeCards = ({ resumeDetail }) => {
  const labels = useSelector(state => state.systemLabel.labels);
  const [showResumeDetail, setShowResumeDetail] = useState(false);
  const [selectedResumeId, setSelectedResumeId] = useState();
  const navigate = useNavigate();
  useEffect(() => {
    if (showResumeDetail) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
  }, [showResumeDetail]);

  return (
    <div className="flex flex-col w-full items-start gap-3">
      <>
        {resumeDetail?.Top5Resumes?.slice(0, 4).map((single, id) => (
          <div
            key={id}
            className=" relative pointer w-full !p-4 flex bg-[#EAE5FC] border border-[#EAE5FC] rounded-[10px] h-auto"
            onClick={() => {
              setShowResumeDetail(true);
              setSelectedResumeId(single?.ResumeId);
            }}
          >
            <div>
              <div className="flex flex-col gap-4 md:gap-3">
                <div className="flex !h-auto md:h-[48px] gap-3">
                  <Avatar
                    className="min-w-[52px] !bg-white rounded-[50%]"
                    size={52}
                    src={AvatarIcon}
                  />
                  <div className="flex flex-col w-full h-auto">
                    <h1 className="order-1 md:block hidden !m-0 font-semibold !text-base text-[#2F2F2F]">
                      {(() => {
                        const text = (single?.Profiles || [])
                          .map(item => item?.ProfileValue)
                          .join(", ");
                        const trimmed =
                          text.length > 30
                            ? text.slice(0, 30).replace(/,\s*$/, "") + "..."
                            : text;
                        return trimmed;
                      })()}
                    </h1>
                    <div className="order-2 md:order-1 flex flex-wrap flex-col h-auto md:flex-row gap-2 justify-center md:justify-start md:items-center md:mt-[9px] ">
                      <p className="flex gap-1 items-center">
                        <ProfileIcon className="w-[16px] h-auto text-[#878787]" />
                        <span className="text-[#878787] !text-sm font-normal">
                          {single?.UserFirstName}
                        </span>
                      </p>
                      <p className="flex gap-1 items-center">
                        <EnvironmentOutlined className="w-4 min-w-4 text-[#8E81F5]" />
                        <span className="text-[#8E81F5] !text-sm font-normal">
                          {single?.Region}
                        </span>
                      </p>
                      <p className="flex gap-1 items-center">
                        <RankingIcon className="text-[#34C759]" />
                        <span className="text-[#34C759] !text-sm font-normal">
                          {`Match Score (${
                            single?.CorrelationScore
                              ? (single?.CorrelationScore /
                                  single?.TotalScore) *
                                100
                              : "0"
                          }%)`}
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <h1 className="block md:hidden !m-0 font-semibold !text-base text-[#2F2F2F]">
                  {(() => {
                    const text = (single?.Profiles || [])
                      .map(item => item?.ProfileValue)
                      .join(", ");
                    const trimmed =
                      text.length > 30
                        ? text.slice(0, 30).replace(/,\s*$/, "") + "..."
                        : text;
                    return trimmed;
                  })()}
                </h1>
              </div>

              <div>
                <div className="flex flex-wrap w-full  gap-2 md:mt-6 mt-[14px]">
                  {single?.Skills?.slice(0, 3)?.map((row, index) => (
                    <button className="h-8 !w-auto pl-4 pr-4 bg-white border border-[#EAE5FC] rounded-lg">
                      <span className="!text-[#343333] text-xs font-normal leading-[100%]">
                        {row?.SkillValue}
                      </span>
                    </button>
                  ))}
                  {single?.Skills?.length > 3 && (
                    <button className="h-8 !w-auto pl-4 pr-4 bg-white border border-[#EAE5FC] rounded-lg">
                      <span className="!text-[#343333] text-xs font-normal leading-[100%]">
                        {`+ ${single?.Skills?.length - 3} more`}
                      </span>
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
        <div className="flex w-full justify-center">
          <Button
            type="primary"
            className="!h-10  w-full lg:!h-11"
            onClick={() => {
              navigate(publicRoutes.signup.path);
            }}
          >
            {labels?.Landing_Proceed_Label}
          </Button>
        </div>
      </>

      {/* Custom Drawer */}
      <div
        className={`fixed inset-0 z-[9999] transition-all duration-300 ease-in-out ${
          showResumeDetail ? "visible" : "invisible"
        }`}
      >
        {/* Overlay */}
        <div
          className={`absolute inset-0 bg-[#00000080]  transition-opacity ${
            showResumeDetail ? "opacity-100" : "opacity-0"
          }`}
          onClick={() => setShowResumeDetail(false)}
        />

        {/* Drawer Content */}
        <div
          className={`absolute right-0 top-0 h-full w-full sm:w-[391px] bg-white shadow-lg transition-transform duration-300 ease-in-out ${
            showResumeDetail ? "translate-x-0" : "translate-x-full"
          }`}
        >
          {/* Content */}
          <div className="h-full">
            <SingleResumeDetail
              resumeDetail={resumeDetail}
              selectedResumeId={selectedResumeId}
              setShowResumeDetail={setShowResumeDetail}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResumeCards;
