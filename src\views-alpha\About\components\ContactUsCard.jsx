import { Card, Button, Typography } from "antd";
import { ArrowRightOutlined } from "@ant-design/icons";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { privateRoutes } from "../../../Routes/routing";

const { Title, Paragraph } = Typography;

const ContactUsCard = () => {
  const labels = useSelector(state => state.systemLabel.labels?.about);
  const navigate = useNavigate();
  return (
    <Card className="!bg-[#F3F1FD] !rounded-[16px] [&_.ant-card-body]:!p-[24px_32px] max-md:[&_.ant-card-body]:!p-[20px_16px] !mt-[12px]">
      <Title level={3} className="!font-[500] mb-[12px] !text-[var(--purple)]">
        {labels?.moreQuestion}
      </Title>
      <Paragraph className="!text-[var(--gray-3)] !text-[16px]">
        {labels?.moreAnswer}
      </Paragraph>
      <div className="flex justify-end">
        <Button
          type="primary"
          onClick={() => navigate(privateRoutes.feedback.path)}
        >
          {labels?.contactUs}
          <ArrowRightOutlined />
        </Button>
      </div>
    </Card>
  );
};

export default ContactUsCard;
