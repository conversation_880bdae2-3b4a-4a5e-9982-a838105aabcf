import { Tabs } from "antd";
import TabPane from "antd/es/tabs/TabPane";
import Title from "antd/es/typography/Title";
import { first } from "lodash";
import { useSelector } from "react-redux";
import CollapseUi from "../../common-alpha/CollapseUi/CollapseUi";
import SegmentedUi from "../../common-alpha/SegmentedUi/SegmentedUi";
import PageWrapper from "../../components-alpha/PageWrapper/PageWrapper";
import { htmlParser } from "../../utilities/helpers";
import { useAboutProdooListApi, useFaqsApi } from "./aboutApi";
import ContactUsCard from "./components/ContactUsCard";
import { useLocation, useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import { privateRoutes } from "../../Routes/routing";

const About = () => {
  const { pathname } = useLocation();
  const { data: faqData, isLoading: loadingFaq } = useFaqsApi({ type: 1 });
  const { data: aboutData, isLoading: loadingAbout } = useAboutProdooListApi();
  const labels = useSelector(state => state.systemLabel.labels?.about);
  const selectedTab = pathname.includes("faq")
    ? labels?.faqs
    : labels?.aboutProdoo;

  const navigate = useNavigate();

  const onTabChange = name => {
    navigate(
      `${
        name === labels?.faqs
          ? privateRoutes.faq.path
          : privateRoutes.about.path
      } `
    );
  };

  const faqsList = faqData?.items;
  const aboutProdooList = aboutData?.items;
  return (
    <PageWrapper
      title={pathname.includes("faq") ? labels?.faqs : labels?.title}
    >
      <SegmentedUi
        className="!mt-[24px] !mb-[40px]"
        options={[labels?.aboutProdoo, labels?.faqs]}
        onChange={onTabChange}
        value={selectedTab}
      />

      {selectedTab === labels?.aboutProdoo && (
        <Tabs
          className="[&_.ant-tabs-nav-more]:hidden max-md:[&_.ant-tabs-nav-wrap]:!mx-[calc(-1*var(--page-spacing-lg))] max-md:[&_.ant-tabs-nav-wrap]:px-[var(--page-spacing-lg)]"
          centered
          defaultActiveKey={first(aboutProdooList)?.ConfigurationId}
        >
          {aboutProdooList?.map(about => (
            <TabPane
              className={twMerge(`p-[42px] max-md:p-[22px_24px] bg-[var(--light-purple)] rounded-[25px] text-[var(--dark)]
              [&_h2]:font-[600] [&_h2]:text-[20px] [&_h2]:text-[var(--purple)] [&_h2]:mb-[12px]
              [&_h3]:font-[400] [&_h3]:text-[18px] [&_h3]:text-[var(--purple)] [&_h3]:mb-[12px]
              [&p]:text-[16px] [&_p]:!mb-[12px]
              `)}
              tab={about?.ConfigName}
              key={about?.ConfigurationId}
            >
              {htmlParser(about?.ConfigValue)}
            </TabPane>
          ))}
        </Tabs>
      )}
      {selectedTab === labels?.faqs && (
        <>
          <div className="text-[var(--dark)]">
            <Title
              className="!text-[var(--dark)] text-center !font-[600] !mb-[20px]"
              level={3}
            >
              {labels?.frequentlyAsk}
            </Title>
            <CollapseUi
              items={faqsList?.map(faq => ({
                key: faq.QuestionId,
                label: faq.Title,
                children: htmlParser(faq.Answer)
              }))}
            />
          </div>
          <ContactUsCard />
        </>
      )}
    </PageWrapper>
  );
};

export default About;
