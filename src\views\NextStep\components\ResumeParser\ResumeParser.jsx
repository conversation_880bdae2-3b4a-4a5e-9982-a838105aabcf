import { useState, useEffect, Fragment } from "react";
import { connect } from "react-redux";
import { notificationAction } from "../../../../actions/notification";
import { isEmpty, map, filter } from "lodash";
import "./resume-parser.scss";
import { useNavigate } from "react-router-dom";
import {
  UpdateResumeApi,
  UploadAndGetRolesAndSkillsFromResumeApi
} from "../../nextstepApi";

const LOOKUPTYPES = {
  PROFILE: 1,
  SKILL: 2,
  KEYWORD: 3,
  CERTIFICATION: 4,
  COUNTRY: 5,
  LANGUAGE: 6,
  INDUSTRY: 7
};

const ResumeParser = ({ labels, notificationAction }) => {
  const [state, setState] = useState({
    currentSlide: 0,
    keywords: "",
    isLoading: false,
    extractedSearchSettings: {
      Profiles: [],
      Skills: []
    },
    resumeUploaded: false
  });

  const navigate = useNavigate();

  useEffect(() => {
    window.addEventListener("keydown", handleTabPress);
    return () => {
      window.removeEventListener("keydown", handleTabPress);
    };
  }, []);

  const handleFinishClick = () => {
    const { extractedSearchSettings } = state;
    const { Profiles, Skills } = extractedSearchSettings;
    const resume = {
      ResumeProfiles: Profiles.map(p => ({
        ProfileId: p.Id,
        ExperienceLevel: 2
      })),
      ResumeSkills: Skills.map(s => ({
        SkillId: s.Id,
        ExperienceLevel: 2
      }))
    };
    setState(prevState => ({
      ...prevState,
      isLoading: true
    }));
    UpdateResumeApi(resume).then(res => {
      const info = {
        message: "Resume Updated Successfully",
        status: "success"
      };
      notificationAction(info);
      setState({
        isLoading: false,
        resumeUploaded: false,
        extractedSearchSettings: {
          Profiles: [],
          Skills: []
        }
      });
    });
  };

  const uploadLinkedinPdf = (e, successName) => {
    var fileData = e.target.files[0];
    var types = "pdf";
    var fileType = fileData.name.split(".");
    if (types === fileType[1].toLowerCase()) {
      var reader = new FileReader();
      reader.onload = function(e) {
        var fileDataInBytes = e.target.result.split(",")[1];
        setState(prevState => ({ ...prevState, isLoading: true }));
        UploadAndGetRolesAndSkillsFromResumeApi(fileDataInBytes)
          .then(response => {
            if (response.success) {
              let {
                Certifications = [],
                Countries = [],
                Industries = [],
                Keywords = [],
                Languages = [],
                Profiles = [],
                Skills = []
              } = response.items;

              Certifications = map(Certifications, certification => ({
                Id: certification.CertificationId,
                ExpLevel: 0,
                LookupValue: certification.CertificationValue
              }));
              Countries = map(Countries, country => ({
                Id: country.CountryId,
                ExpLevel: 0,
                LookupValue: country.CountryName
              }));
              Industries = map(Industries, industry => ({
                Id: industry.IndustryId,
                ExpLevel: 0,
                LookupValue: industry.IndustryValue
              }));
              Keywords = map(Keywords, keyword => ({
                Id: keyword.KeywordId,
                ExpLevel: 0,
                LookupValue: keyword.KeywordValue
              }));
              Languages = map(Languages, language => ({
                Id: language.LanguageId,
                ExpLevel: 0,
                LookupValue: language.LanguageValue
              }));
              Profiles = map(Profiles, profile => ({
                Id: profile.ProfileId,
                ExpLevel: 0,
                LookupValue: profile.ProfileValue
              }));
              Skills = map(Skills, skill => ({
                Id: skill.SkillId,
                ExpLevel: 0,
                LookupValue: skill.SkillValue
              }));

              if (
                isEmpty(Certifications) &&
                isEmpty(Countries) &&
                isEmpty(Industries) &&
                isEmpty(Keywords) &&
                isEmpty(Languages) &&
                isEmpty(Profiles) &&
                isEmpty(Skills)
              ) {
                const info = {
                  message: labels.searcherOnboardingInvalidDescription,
                  status: "success"
                };
                notificationAction(info);
                setState(prevState => ({
                  ...prevState,
                  isLoading: false
                }));
                return;
              }
              const saveResumeParams = {
                Certifications,
                Countries,
                Industries,
                Keywords,
                Languages,
                Profiles,
                Skills
              };
              setState(prevState => ({
                ...prevState,
                extractedSearchSettings: saveResumeParams,
                isLoading: false,
                resumeUploaded: true
              }));
            } else {
              setState(prevState => ({
                ...prevState,
                isLoading: false
              }));
            }
          })
          .catch(err => {
            const info = {
              message: "Unable to upload to file.",
              status: "error"
            };
            notificationAction(info);
            setState(prevState => ({ ...prevState, isLoading: false }));
          });
      };
      reader.readAsDataURL(fileData);
    } else {
      const info = {
        message: "Please select pdf file.",
        status: "error"
      };
      notificationAction(info);
    }
  };

  const extractedSettingUpdate = (type, item) => {
    const { extractedSearchSettings } = state;
    let {
      Certifications,
      Countries,
      Industries,
      Keywords,
      Languages,
      Profiles,
      Skills
    } = extractedSearchSettings;
    switch (type) {
      case LOOKUPTYPES.PROFILE: {
        Profiles = filter(Profiles, a => a.Id != item.Id);
        break;
      }
      case LOOKUPTYPES.SKILL: {
        Skills = filter(Skills, a => a.Id != item.Id);
        break;
      }
      case LOOKUPTYPES.CERTIFICATION: {
        Certifications = filter(Certifications, a => a.Id != item.Id);
        break;
      }
      case LOOKUPTYPES.COUNTRY: {
        Countries = filter(Countries, a => a.Id != item.Id);
        break;
      }
      case LOOKUPTYPES.INDUSTRY: {
        Industries = filter(Industries, a => a.Id != item.Id);
        break;
      }
      case LOOKUPTYPES.KEYWORD: {
        Keywords = filter(Keywords, a => a.Id != item.Id);
        break;
      }
      case LOOKUPTYPES.LANGUAGE: {
        Languages = filter(Languages, a => a.Id != item.Id);
        break;
      }
    }
    setState(prevState => ({
      ...prevState,
      extractedSearchSettings: {
        ...extractedSearchSettings,
        Profiles,
        Skills,
        Certifications,
        Keywords,
        Languages,
        Countries
      }
    }));
  };

  const {
    otherResumePdfName,
    linkedinPdfName,
    extractedSearchSettings,
    resumeUploaded
  } = state;
  const { Profiles, Skills } = extractedSearchSettings;

  return (
    <div
      className="phillip-component ipro-phillip resume-parser"
      data-testid="ipro-phillip-popup"
    >
      <div className="upload-div">
        <b>{labels?.iProOnboardingResumeTitle}</b>
        <br />
        {labels?.iProOnboardingResumeSubTitle}
        <br />
        <div
          onChange={e => uploadLinkedinPdf(e, "LinkedInResumeSuccessMessage")}
          className="js_upload_btn"
        >
          {labels.iProOnboardingResumeLinkedInBtnText}
          <input className="linkedInPdf" type="file" title=" " />
        </div>
        {linkedinPdfName && (
          <Fragment>
            <br />
            {linkedinPdfName}
          </Fragment>
        )}
        <br></br>
        {"If you have some other resume we can parse that as well"}
        <br></br>
        <div
          onChange={e => uploadLinkedinPdf(e, "OtherResumeSuccessMessage")}
          className="js_upload_btn"
        >
          {labels.iProOnboardingResumeOtherBtnText}
          <input className="linkedInPdf" type="file" title=" " />
        </div>
        <br></br>
        {otherResumePdfName && (
          <Fragment>
            <br />
            {otherResumePdfName}
          </Fragment>
        )}
      </div>
      {resumeUploaded && (
        <div className={`step`}>
          <div className="body">
            <div className="lookup-head">
              <h3>Roles</h3>
              <label> we have got following roles from your resume</label>
            </div>
            <div className="settings-container">
              {Profiles &&
                Profiles.map(item => (
                  <label
                    data-testid="resume-edit-suggested-role-item"
                    className="draggable"
                    onClick={() =>
                      extractedSettingUpdate(LOOKUPTYPES.PROFILE, item)
                    }
                    key={item.Id}
                  >
                    {item.LookupValue}
                  </label>
                ))}
            </div>
            <div className="lookup-head">
              <h3>Skills</h3>
              <label> we have got following skills from your resume</label>
            </div>
            <div className="settings-container">
              {Skills &&
                Skills.map(item => (
                  <label
                    data-testid="resume-edit-suggested-role-item"
                    className="draggable"
                    onClick={() =>
                      extractedSettingUpdate(LOOKUPTYPES.SKILL, item)
                    }
                    key={item.Id}
                  >
                    {item.LookupValue}
                  </label>
                ))}
            </div>
          </div>
        </div>
      )}
      {resumeUploaded && (
        <button
          type="button"
          data-testid="ipro-phillip-finish-button"
          className="js_upload_btn"
          onClick={handleFinishClick}
        >
          {"Save Resume"}
        </button>
      )}
    </div>
  );
};

const mapStateToProps = ({ systemLabel, userInfo }) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  let { FirstName } = user;
  FirstName =
    FirstName !== undefined ? FirstName : sessionStorage.getItem("userName");
  return { labels, FirstName };
};

export default connect(mapStateToProps, { notificationAction })(ResumeParser);
