import CloseModal from "../../../assets-alpha/images/svg/close-modal.svg";
import { Button, Typography } from "antd";
import AvatarIcon from "../../../assets-alpha/images/svg/avatar-workplace.svg";
import EditIcon from "../../../assets-alpha/images/svg/edit.svg?react";
import DeleteModal from "../../../common-alpha/DeleteModal/DeleteModal";
const Showcase = ({
  labels,
  setIsEditDrawerOpen,
  setIsDrawerOpen,
  handleDelete,
  companyForm,
  countries,
  industries,
  userPhoneNumbers,
  deleteDialog,
  setDeleteDialog,
  handleYesClick,
  isLoading,
  setAvatar
}) => {
  const Industry = industries?.find(item => {
    return item?.IndustryId === companyForm?.IndustryId;
  });
  const Location = countries?.find(item => {
    return item?.CountryId === companyForm?.CountryId;
  });
  const PhoneNo = userPhoneNumbers?.find(item => {
    return item?.UserPhonenumberId === companyForm?.UserPhonenumberId;
  });
  return (
    <div>
      <DeleteModal
        open={deleteDialog}
        onCancel={() => setDeleteDialog(false)}
        onConfirm={handleYesClick}
        isLoading={isLoading}
        title={labels?.Delete_Workplace_Title}
        description={labels?.Delete_Workplace_Description}
        cancelText="Cancel"
        confirmText="Delete"
      />

      <div className="flex flex-col">
        <div className="relative w-[100%] md:w-[497px] h-[64px] ">
          <div className="fixed md:w-[497px] w-[100%] h-[64px] border-b border-[#EAE5FC] z-[999] bg-white rounded-tl-2xl rounded-tr-2xl">
            <div className="flex w-full h-[64px] justify-between items-center p-6">
              <div className="flex justify-between md:gap-5 w-full md:h-10 items-center h-[64px] border-b border-[#EAE5FC] md:border-b-0 md:pl-0 md:pr-0">
                <img
                  src={CloseModal}
                  alt=""
                  className="pointer md:order-1 order-2"
                  onClick={() => {
                    setIsDrawerOpen(false);
                  }}
                />
                <Typography.Title
                  level={3}
                  className="!m-0 flex-1 order-1 md:order-2 !text-[18px] !font-semibold md:!text-2xl md:font-medium"
                >
                  {labels?.Workplace_View_Label}
                </Typography.Title>
              </div>
              <div className="hidden md:block">
                <div className="flex gap-3">
                  <Button
                    type="filled"
                    className="!m-0 !bg-[#FEEDED] !w-[68px] !h-9 !text-[#FF3B30] pointer"
                    onClick={() => handleDelete({ company: companyForm })}
                  >
                    {labels?.delete_presentation}
                  </Button>
                  <Button
                    type="filled"
                    className="!m-0 !bg-[#F3F1FD] !w-[68px] !h-9 !text-[#8E81F5] pointer"
                    onClick={() => {
                      setIsDrawerOpen(false);
                      setIsEditDrawerOpen(true);
                    }}
                    icon={<EditIcon />}
                  >
                    {labels?.IPRO_Edit_Button_Text}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="md:m-6 m-4 flex flex-col md:gap-6 gap-4 ">
          <div className="flex gap-4 h-[72px]">
            <div className="!w-[72px] !h-[72px] rounded-[11.52px]">
              <img
                className="!w-[72px] !h-[72px] rounded-[11.52px]"
                src={companyForm?.Logo ? companyForm?.Logo : AvatarIcon}
                alt="Logo"
              />
            </div>
            <div className="flex flex-col justify-center gap-[7px] h-[72px]">
              <h1 className="!m-0 !font-medium !text-[#343333] !text-[16px]">
                {companyForm?.CompanyName?.length > 20
                  ? `${companyForm?.CompanyName.slice(0, 30)}...`
                  : companyForm?.CompanyName}
              </h1>
              {companyForm?.IsDefaultCompany && (
                <span className="flex justify-center items-center w-[58px] h-6 bg-[#F3F1FD] text-[#8E81F5] text-xs font-medium rounded-[99px]">
                  Default
                </span>
              )}
            </div>
          </div>
          <div className="flex flex-col bg-[#F3F1FD] rounded-xl mb-[78px] ">
            <div className="flex pl-4 pr-4 h-auto min-h-10 !border-b !border-[#EAE5FC]">
              <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
                {labels?.TITLE_LABEL}
              </label>
              <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
              <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
                {companyForm?.CompanyName ? companyForm?.CompanyName : "N/A"}
              </p>
            </div>
            <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
              <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
                {labels?.PHONE_LABEL}
              </label>
              <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
              <p className="!ml-[12.5px] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
                {PhoneNo?.UserPhonenumberValue
                  ? PhoneNo?.UserPhonenumberValue
                  : "N/A"}
              </p>
            </div>
            <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
              <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
                {labels?.COUNTRY_LABEL}
              </label>
              <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
              <p className="!ml-[12.5px] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
                {Location?.CountryName ? Location?.CountryName : "N/A"}
              </p>
            </div>
            <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
              <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
                {labels?.INDUSTRY_LABEL}
              </label>
              <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
              <p className="!ml-[12.5px] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
                {Industry?.IndustryValue ? Industry?.IndustryValue : "N/A"}
              </p>
            </div>
            <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
              <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
                {labels?.ADDRESS_LABEL}
              </label>
              <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
              <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
                {companyForm?.CompanyAddress
                  ? companyForm?.CompanyAddress
                  : "N/A"}
              </p>
            </div>
            <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
              <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
                {labels?.WEB_URL_LABEL}
              </label>
              <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
              <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
                {companyForm?.WebUrl ? companyForm?.WebUrl : "N/A"}
              </p>
            </div>
            <div className="flex pl-4 pr-4  h-auto min-h-10  !border-b !border-[#EAE5FC]">
              <label className="w-[70px] text-[#878787] text-[13px] font-normal pt-3 pb-3">
                {labels?.IproOpportunityNewCompanyVatLabel}
              </label>
              <span className="h-[22.5px] border border-[#EAE5FC] !mt-[9px] pb-[9x] ml-[21.5px]"></span>
              <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal pt-3 pb-3 text-[#343333]">
                {companyForm?.Vat ? companyForm?.Vat : "N/A"}
              </p>
            </div>
            <div className="flex pl-4 pr-4 pt-[14px] min-h-10 h-auto pb-3">
              <label className="flex  !w-[72px] h-full text-[#878787] text-[13px] font-normal ">
                {labels?.collDescription}
              </label>
              <span className="h-[22.5px] border border-[#EAE5FC]  ml-[21.5px]"></span>
              <p className="!ml-[12.5px] !max-w-[calc(100%-110px)] text-[13px] font-normal  text-[#343333]">
                {companyForm?.Detail ? companyForm?.Detail : "N/A"}
              </p>
            </div>
          </div>
        </div>
        <div className="md:hidden flex w-[100%] h-[64px] bg-white fixed bottom-0 justify-center items-center  border-t-[0.5px] border-[#EAE5FC] gap-[9px] pl-4 pr-4">
          <Button
            type="filled"
            className="!m-0 !bg-[#FEEDED] !w-full !h-10 !text-[#FF3B30] pointer"
            onClick={() => handleDelete({ company: companyForm })}
          >
            {labels?.delete_presentation}
          </Button>
          <Button
            type="filled"
            className="!m-0 !bg-[#F3F1FD] !w-full !h-10 !text-[#8E81F5] pointer"
            onClick={() => {
              setIsDrawerOpen(false);
              setIsEditDrawerOpen(true);
            }}
            icon={<EditIcon />}
          >
            {labels?.IPRO_Edit_Button_Text}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Showcase;
