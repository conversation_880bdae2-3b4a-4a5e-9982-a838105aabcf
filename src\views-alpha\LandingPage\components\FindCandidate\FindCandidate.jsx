import { Search } from "lucide-react";
import ArrowRight from "../../../../assets-alpha/images/svg/arrow-right.svg?react";
import EditIcon from "../../../../assets-alpha/images/svg/edit.svg?react";
import LiveIcon from "../../../../assets-alpha/images/svg/live-result.svg?react";
import { useScrollAnimation } from "../../hooks/useScrollAnimation";
import { Button, Input } from "antd";
import { GetTopResume } from "./FindCandidateApi";
import { useState } from "react";
import ResumeCards from "./ResumeCards";
import { useSelector } from "react-redux";

const FindCandidate = () => {
  const labels = useSelector(state => state.systemLabel.labels);
  const [description, setDescription] = useState("");
  const [resumeDetail, setResumeDetail] = useState();
  const [showDetail, setShowDetails] = useState(false);
  const [loading, setLoading] = useState(false);
  const {
    elementRef: sectionRef,
    isVisible: sectionVisible
  } = useScrollAnimation({ delay: 200 });
  const {
    elementRef: searchRef,
    isVisible: searchVisible
  } = useScrollAnimation({ delay: 400 });
  const {
    elementRef: resultsRef,
    isVisible: resultsVisible
  } = useScrollAnimation({ delay: 600 });
  const handleResumeSearch = async () => {
    setLoading(true);
    const dataToSend = {
      keywords: description
    };
    const res = await GetTopResume(dataToSend);
    if (res?.success) {
      setShowDetails(true);
      setResumeDetail(res?.items);
    }
    setLoading(false);
  };
  return (
    <section className=" w-full bg-[#fcfcff] relative overflow-hidden pb-6 lg:pb-24">
      <div className="absolute inset-0 opacity-[0.03]">
        <div
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(to right, var(--company) 1px, transparent 1px),
              linear-gradient(to bottom, var(--company) 1px, transparent 1px),
              linear-gradient(to right, var(--primary) 1px, transparent 1px),
              linear-gradient(to bottom, var(--primary) 1px, transparent 1px)
            `,
            backgroundSize: "40px 40px, 40px 40px, 80px 80px, 80px 80px",
            backgroundPosition: "0 0, 0 0, 20px 20px, 20px 20px"
          }}
        ></div>
      </div>
      <div className="max-w-6xl mx-auto relative z-10 mt-5 lg:mt-[64px]">
        <div
          ref={sectionRef}
          className={`flex justify-center items-center flex-col gap-4 text-center w-full mb-8 lg:mb-20 relative animate-on-scroll px-4 lg:px-0 ${
            sectionVisible ? "animate-visible" : ""
          }`}
        >
          <h2 className="text-2xl lg:text-[40px] font-medium text-[#343333] leading-[100%] lg:leading-[58px]">
            {labels?.Find_Candidate_h1}{" "}
            <span className="text-[#8E81F5]"> {labels?.Find_Candidate_h2}</span>
          </h2>

          <p className="flex w-full justify-center lg:max-w-[748px] text-center text-[#878787] text-xs lg:text-[16px] font-normal">
            {labels?.Find_Candidate_Desc}
          </p>
        </div>
      </div>
      <div className="flex lg:flex-row flex-col w-full justify-center gap-4 lg:gap-[53px] lg:px-0 px-4 lg:max-h-[730px]">
        {/* Left div */}
        {showDetail ? (
          <div className="order-2 lg:order-1 w-full lg:w-[37%] flex gap-3 flex-col items-center bg-white border border-[#EAE5FC] p-4 lg:p-6 rounded-[25px]">
            <div className="flex flex-col w-full bg-[#F4F2FE] h-[216px] p-6 rounded-lg">
              <h1 className="!m-0 !text-sm lg:text-2xl text-[#343333] font-medium">
                {labels?.Profiles}
              </h1>
              <div className="flex flex-wrap gap-2 w-full !mt-2">
                {resumeDetail?.MatchedLookups?.Profiles?.length > 0 ? (
                  resumeDetail?.MatchedLookups?.Profiles?.map((p, idx) => (
                    <div
                      key={idx}
                      className="bg-white flex items-center h-[25px] lg:h-[37px] border border-[#EAE5FC] px-4 !rounded-[5.2px] lg:rounded-lg whitespace-nowrap text-[#343333] text-[10px] lg:text-sm"
                    >
                      {p.ProfileValue}
                    </div>
                  ))
                ) : (
                  <p className="!m-0 !text-xs lg:!text-sm text-[#878787] font-normal">
                    {labels?.LANDING_LOOKING_FOR_RESOURCES_NO_DATA_LBL}
                  </p>
                )}
              </div>
            </div>
            <div className="flex flex-col w-full bg-[#F4F2FE] h-[216px] p-6 rounded-lg">
              <h1 className="!m-0 !text-sm lg:text-2xl text-[#343333] font-medium">
                {labels?.Skills_Label}
              </h1>
              <div className="flex flex-wrap gap-2 w-full !mt-2">
                {resumeDetail?.MatchedLookups?.Skills?.length > 0 ? (
                  resumeDetail?.MatchedLookups?.Skills?.map((s, idx) => (
                    <div
                      key={idx}
                      className="bg-white flex items-center h-[25px] lg:h-[37px] border border-[#EAE5FC] px-4 !rounded-[5.2px] lg:rounded-lg whitespace-nowrap text-[#343333] text-[10px] lg:text-sm"
                    >
                      {s.SkillValue}
                    </div>
                  ))
                ) : (
                  <p className="!m-0 !text-xs lg:!text-sm text-[#878787] font-normal">
                    {labels?.LANDING_LOOKING_FOR_RESOURCES_NO_DATA_LBL}
                  </p>
                )}
              </div>
            </div>
            <div className="flex flex-col w-full bg-[#F4F2FE] h-[216px] p-6 rounded-lg">
              <h1 className="!m-0 !text-sm lg:text-2xl text-[#343333] font-medium">
                {labels?.SHORTLIST_RESUME_INDUSTRY}
              </h1>
              <div className="flex flex-wrap gap-2 w-full !mt-2">
                {resumeDetail?.MatchedLookups?.Industries?.length > 0 ? (
                  resumeDetail?.MatchedLookups?.Industries?.map((s, idx) => (
                    <div
                      key={idx}
                      className="bg-white flex items-center h-[25px] lg:h-[37px] border border-[#EAE5FC] px-4 !rounded-[5.2px] lg:rounded-lg whitespace-nowrap text-[#343333] text-[10px] lg:text-sm"
                    >
                      {s.IndustryValue}
                    </div>
                  ))
                ) : (
                  <p className="!m-0 !text-xs lg:!text-sm text-[#878787] font-normal">
                    {labels?.LANDING_LOOKING_FOR_RESOURCES_NO_DATA_LBL}
                  </p>
                )}
              </div>
            </div>
            <div className="flex w-full justify-center bg-transparent h-11 rounded-lg">
              <Button
                type="default"
                className="text-[#8E81F5] w-full !h-10 lg:!h-11 !text-sm lg:!text-[16px] font-medium bg-[#F4F2FE]"
                onClick={() => {
                  setShowDetails(false);
                }}
              >
                {labels?.Edit_Command_Lbl} <EditIcon />
              </Button>
            </div>
          </div>
        ) : (
          <div className="order-2 lg:order-1 w-full lg:w-[37%] flex flex-col items-center bg-transparent border border-[#EAE5FC] p-4 lg:p-6 rounded-[25px]">
            <div className="relative mx-auto mb-4 lg:mb-8 lg:w-24 lg:h-24  w-[60px] h-[60px] ">
              <div className="absolute w-[60px] h-[60px] lg:w-24 lg:h-24 bg-gradient-to-br from-teal-500 to-teal-400 rounded-2xl flex items-center justify-center text-white shadow-xl shadow-teal-200/50 animate-[search-glow_3s_ease-in-out_infinite]">
                <Search className="w-8 h-8 lg:w-10 lg:h-10" />
              </div>
              <div className="absolute -inset-1 bg-gradient-to-br from-teal-500 to-purple-500 rounded-2xl -z-10 animate-[search-rotate_8s_linear_infinite]"></div>
            </div>
            <div className="flex flex-col items-center gap-3 w-[72%]">
              <h1 className="!m-0 !text-[16px] lg:!text-[28px] text-[#343333] font-medium">
                {labels?.Job_Matching_p1}
              </h1>
              <p className="flex w-full justify-center text-center !text-xs lg:!text-sm font-normal text-[#878787]">
                {labels?.job_Matching_h1}
              </p>
            </div>
            {/* <div className="flex items-center justify-center flex-wrap sm:flex-nowrap gap-x-2 text-center mt-2 lg:mt-3">
                <span className="text-[#8E81F5] text-xs lg:!text-[16px] leading-6 font-semibold whitespace-nowrap">
                  {labels?.AI_Powered}
                </span>
                <span className="text-[#8E81F5] text-xl">•</span>
                <span className="text-[#8E81F5] text-xs lg:!text-[16px] leading-6 font-semibold whitespace-nowrap">
                  {labels?.Real_Time}
                </span>
                <span className="text-[#8E81F5] text-xl">•</span>
                <span className="text-[#8E81F5] text-xs lg:!text-[16px] leading-6 font-semibold whitespace-nowrap">
                  {labels?.Verified_Lbl}
                </span>
              </div>
              <h1 className="!m-0 !text-xs lg:!text-[16px] text-[#343333] font-medium mt-[3px] lg:!mt-[37px]">
                {labels?.Job_Req_Desc}
              </h1> */}
            <div className="flex w-full mt-3 lg:!mt-5">
              <Input.TextArea
                name="description"
                value={description}
                onChange={e => setDescription(e.target.value)}
                placeholder={labels?.Candidate_Placeholder}
                className="!h-[320px] !p-4 placeholder:text-[12px] lg:placeholder:text-[14px] lg:text-sm text-xs !rounded-[8px] lg:!rounded-[12px]"
                rootClassName="[&_.ant-input]:!p-[16px]"
              />
            </div>
            <div className="w-full ">
              <Button
                type="primary"
                className="!h-10 lg:!h-11 w-full mt-4"
                loading={loading}
                onClick={() => {
                  handleResumeSearch();
                }}
              >
                {labels?.Find_Candidate_Btn} <ArrowRight />
              </Button>
            </div>
          </div>
        )}
        {/* Right div show data from api here */}
        <div className="order-1 lg:order-2 w-full lg:w-[35%] flex flex-col gap-5 items-start bg-transparent lg:border border-none border-[#EAE5FC] rounded-[25px]">
          <div className="flex items-center gap-3 lg:gap-5 w-full">
            <div className="!w-8 !h-8 lg:w-[56px] lg:h-[56px] ">
              <div className="w-full h-full bg-[#8E81F5] !rounded-[6.7px] lg:rounded-2xl flex items-center justify-center text-white shadow-xl animate-[search-glow_3s_ease-in-out_infinite]">
                <LiveIcon className="w-3 h-3 lg:w-6 lg:h-6" />
              </div>
              <div className="absolute -inset-1 bg-gradient-to-br from-teal-500 to-purple-500 rounded-2xl -z-10 animate-[search-rotate_8s_linear_infinite]"></div>
            </div>
            <div className="h-6 lg:h-[45px] flex flex-col gap-1">
              <h1 className="!m-0 font-medium !text-xs lg:!text-[20px] text-[#343333] leading-[13px] lg:leading-6">
                {labels?.Live_Results_Lbl}
              </h1>
              <p className="!m-0 text-[8px] lg:text-sm font-normal text-[#878787]">
                {labels?.Real_Time_Lbl}
              </p>
            </div>
          </div>
          {showDetail ? (
            <>
              <ResumeCards resumeDetail={resumeDetail} />
            </>
          ) : (
            <img
              src="/assets/images/LiveResults.webp"
              alt="Live Results"
              className=" lg:h-[92%]"
            />
          )}
        </div>
      </div>
    </section>
  );
};
export default FindCandidate;
