import ReactQuill from "react-quill-new";
import "./richtexteditor.scss";

const RichTextEditor = props => {
  const modules = {
    toolbar: [
      ["bold", "italic", "underline"],
      [{ size: ["small", false, "large", "huge"] }],
      [
        { list: "ordered" },
        { list: "bullet" },
        { indent: "-1" },
        { indent: "+1" }
      ],
      [{ color: [] }, { background: [] }],
      ["link", "clean"]
    ]
  };

  const { handleChange, value, isInvalidValue, placeholder, readOnly } = props;

  return (
    <ReactQuill
      className={`rich-text-editor ${isInvalidValue ? "inValid" : ""}`}
      value={value ? value : ""}
      onChange={handleChange}
      modules={modules}
      bounds=".rich-text-editor"
      placeholder={placeholder}
      readOnly={readOnly}
    />
  );
};

export default RichTextEditor;
