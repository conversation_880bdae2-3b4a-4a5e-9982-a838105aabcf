import { filter, map, replace, sortBy } from "lodash";
import { Fragment, memo, useCallback, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { htmlParser } from "../../utilities/helpers";
import NavButton from "./components/NavButton";
import NavLinkButton from "./components/NavLinkButton";
import NavigationSkeleton from "./components/NavigationSkeleton";
import { useChangeProfileApi, useNavigationMenusApi } from "./navigationApi";
import clsx from "clsx";
import { resumeChangeAction } from "../../actions/resumeActions";
import ArrowDown from "../../assets-alpha/images/svg/arrow-down-1.svg?react";
import WorkspaceIcon from "../../assets-alpha/images/svg/building.svg?react";
import GlobalIcon from "../../assets-alpha/images/svg/global-search.svg?react";
import HomeIcon from "../../assets-alpha/images/svg/home.svg?react";
import JobInvitationIcon from "../../assets-alpha/images/svg/lamp-charge.svg?react";
import ContractIcon from "../../assets-alpha/images/svg/receipt-edit.svg?react";
import FindProfessionalIcon from "../../assets-alpha/images/svg/search-normal.svg?react";
import NetworkIcon from "../../assets-alpha/images/svg/user-add.svg?react";
import SwitchUserIcon from "../../assets-alpha/images/svg/user-speak.svg?react";
import Icon from "../../common-alpha/Icon/Icon";
import Switch from "../../common-alpha/Switch/Switch";
import { Tooltip } from "../../common/Tooltip/Tooltip";
import "./navigation.scss";

import WorkspaceFilledIcon from "../../assets-alpha/images/svg/building-filled.svg?react";
import HomeFilledIcon from "../../assets-alpha/images/svg/home-filled.svg?react";
import JobInvitationFilledIcon from "../../assets-alpha/images/svg/lamp-filled.svg?react";
import ContractFilledIcon from "../../assets-alpha/images/svg/receipt-filled.svg?react";
import FindProfessionalFilledIcon from "../../assets-alpha/images/svg/search-filled.svg?react";
import NetworkFilledIcon from "../../assets-alpha/images/svg/user-add-filled.svg?react";
import SmsIcon from "../../assets-alpha/images/svg/sms.svg?react";
import SmsFilledIcon from "../../assets-alpha/images/svg/sms-filled.svg?react";

import AssortIcon from "../../assets-alpha/images/svg/assort.svg?react";
import EmbarakIcon from "../../assets-alpha/images/svg/embark.svg?react";
import HeadsupIcon from "../../assets-alpha/images/svg/headsup.svg?react";
import PhillipIcon from "../../assets-alpha/images/svg/phillip.svg?react";
import SenseIcon from "../../assets-alpha/images/svg/sense.svg?react";
import SnapshotIcon from "../../assets-alpha/images/svg/snapshot.svg?react";

import AssortFillIcon from "../../assets-alpha/images/svg/assort-fill.svg?react";
import EmbarakFillIcon from "../../assets-alpha/images/svg/embark-fill.svg?react";
import HeadsupFillIcon from "../../assets-alpha/images/svg/headsup-fill.svg?react";
import PhillipFillIcon from "../../assets-alpha/images/svg/phillip-fill.svg?react";
import SenseFillIcon from "../../assets-alpha/images/svg/sense-fill.svg?react";
import SnapshotFillIcon from "../../assets-alpha/images/svg/snapshot-fill.svg?react";

import { twMerge } from "tailwind-merge";
import PortfolioIcon from "../../assets-alpha/images/svg/about-us.svg?react";
import ResumeIcon from "../../assets-alpha/images/svg/portfolio.svg?react";
import RecruiterIcon from "../../assets-alpha/images/svg/recruiter.svg?react";
import TicktellIcon from "../../assets-alpha/images/svg/ticktell.svg?react";
import HeaderIcon from "./components/HeaderIcon";
import SubNavSetting from "./components/SubNavSetting";
import { Grid } from "antd";

const LabelMapWithIcon = {
  //searcher icons
  searcher: {
    SEARCHER_NAVIGATION_ROLE_LABEL: {
      icon: HomeIcon,
      active: HomeFilledIcon
    },
    SEARCHER_NAVIGATION_COMPANY_LABEL: {
      icon: WorkspaceIcon,
      active: WorkspaceFilledIcon
    },
    SEARCHER_NAVIGATION_SEARCH_LABEL: {
      icon: FindProfessionalIcon,
      active: FindProfessionalFilledIcon
    },
    SEARCHER_NAVIGATION_OPPORTUNITY_LABEL: {
      icon: JobInvitationIcon,
      active: JobInvitationFilledIcon
    },
    SEARCHER_NAVIGATION_COLLABORATION_LABEL: {
      icon: ContractIcon,
      active: ContractFilledIcon
    },
    NETWORK_LABEL: { icon: NetworkIcon, active: NetworkFilledIcon },
    SEARCHER_NAVIGATION_MESSAGE_LABEL: {
      icon: SmsIcon,
      active: SmsFilledIcon
    },

    SEARCHER_NAVIGATION_SNAPSHOT_LABEL: {
      icon: SnapshotIcon,
      active: SnapshotFillIcon
    },
    SEARCHER_NAVIGATION_ASSORT_LABEL: {
      icon: AssortIcon,
      active: AssortFillIcon
    },
    SEARCHER_NAVIGATION_EMBARK_LABEL: {
      icon: EmbarakIcon,
      active: EmbarakFillIcon
    },
    SEARCHER_NAVIGATION_PHILIP_LABEL: {
      icon: PhillipIcon,
      active: PhillipFillIcon
    },
    SEARCHER_NAVIGATION_HEADSUP_LABEL: {
      icon: HeadsupIcon,
      active: HeadsupFillIcon
    },
    SEARCHER_NAVIGATION_SENSE_LABEL: {
      icon: SenseIcon,
      active: SenseFillIcon
    },
    Recruiter_Toolkit: { icon: RecruiterIcon, active: RecruiterIcon },
    SEARCHER_NAVIGATION_SWITCH_IPRO: {
      icon: SwitchUserIcon,
      active: SwitchUserIcon
    }
  },
  //Ipro Icons
  ipro: {
    IPRO_NAVIGATION_ROLE_LABEL: {
      icon: HomeIcon,
      active: HomeIcon
    },
    IPRO_NAVIGATION_RESUME_LABEL: {
      icon: ResumeIcon,
      active: ResumeIcon
    },
    IPRO_NAVIGATION_PRESENTATION_LABEL: {
      icon: PortfolioIcon,
      active: PortfolioIcon
    },
    IPRO_NAVIGATION_OPPORTUNITY_LABEL: {
      icon: JobInvitationIcon,
      active: JobInvitationIcon
    },
    IPRO_NAVIGATION_COLLABORATION_LABEL: {
      icon: ContractIcon,
      active: ContractIcon
    },
    NETWORK_LABEL: { icon: NetworkIcon, active: NetworkIcon },
    IPRO_NAVIGATION_MESSAGE_LABEL: {
      icon: SmsIcon,
      active: SmsFilledIcon
    },
    "nav-ipro-toolkit": { icon: RecruiterIcon, active: RecruiterIcon },
    IPRO_NAVIGATION_TICTELL_LABEL: { icon: TicktellIcon, active: TicktellIcon },
    IPRO_NAVIGATION_NEXTSTEP_LABEL: { icon: HeadsupIcon, active: HeadsupIcon },
    IPRO_NAVIGATION_PHILLIP_LABEL: { icon: PhillipIcon, active: PhillipIcon },
    IPRO_NAVIGATION_SWITCH_SEARCHER: { icon: GlobalIcon, active: GlobalIcon }
  }
};

export const NavigationToolTip = ({
  labels,
  children,
  Label,
  isHelpActive
}) => (
  <>
    {isHelpActive || window.innerWidth <= 1240 ? (
      <>{children}</>
    ) : (
      <Tooltip
        followCursor={true}
        content={labels[replace(Label, "_LABEL", "_TOOLTIP")]}
      >
        <div>{children}</div>
      </Tooltip>
    )}
  </>
);

export const SubNav = ({ children }) => (
  <div className="sub-nav-wrapper">{children}</div>
);

const Navigation = memo(
  ({
    isNavShrink,
    setIsNavShrink,
    onSubMenuOpen,
    onMobileNavShrink,
    onNavigationToggle
  }) => {
    const dispatch = useDispatch();
    const [openSubNav, setOpenSubNav] = useState([]);
    const labels = useSelector(state => state.systemLabel.labels);
    const user = useSelector(state => state.userInfo.user);
    const isHelpActive = useSelector(state => state.navigation.isHelpActive);
    const isResumeChange = useSelector(state => state.resume.isResumeChange);
    const { lg } = Grid.useBreakpoint();
    const {
      data: navigationMenusData,
      isLoading: isNavigationFetching
    } = useNavigationMenusApi({
      featureTypeId: user?.IsFreelancer ? 2 : 1,
      appTypeId: 1
    });

    const { isPending, mutate: fetchChangeProfile } = useChangeProfileApi({
      IsFreelancer: user?.IsFreelancer
    });
    const navigationMenu = useMemo(() => {
      const response = navigationMenusData;
      if (response?.success) {
        return filter(response.items, item => {
          if (
            item.FeatureType === 1 ||
            item.FeatureType === 2 ||
            item.FeatureType === 4 ||
            item.FeatureType === 5
          )
            return sortBy(item.SubNavigation, subNavItem => subNavItem.Order);
        });
      }
      return [];
    }, [navigationMenusData]);

    const handleForSubNavClick = useCallback(
      selectedItem => {
        setOpenSubNav(nav => {
          if (nav.includes(selectedItem.Id)) {
            return nav.filter(n => n !== selectedItem.Id);
          }
          return [...nav, selectedItem.Id];
        });
        onSubMenuOpen();
        onMobileNavShrink();
      },
      [onSubMenuOpen, onMobileNavShrink]
    );

    const handleSwitchChange = useCallback(
      e => {
        e.preventDefault();
        e.stopPropagation();
        if (isResumeChange) {
          dispatch(
            resumeChangeAction({
              isResumeChange,
              isRoleSwitchTrigger: true,
              message: labels.RESUME_EDIT_UNSAVED_CHANGES_MESSAGE
            })
          );
          return;
        }

        fetchChangeProfile();
      },
      [isResumeChange, labels, dispatch, fetchChangeProfile]
    );
    const handleClick = () => {
      if (!lg) {
        setIsNavShrink(true);
      }
    };
    const customLabel =
      LabelMapWithIcon[user?.IsFreelancer ? "ipro" : "searcher"];

    return (
      <div
        className={clsx(
          `navigation max-lg:fixed max-lg:top-0 max-lg:left-0 max-lg:bottom-0 max-lg:h-auto relative border-r-1 border-r-[#C6C6C6] p-[16px_14px] flex flex-col overflow-y-auto overflow-x-hidden bg-white h-[calc(100vh-var(--header-height))]`,
          {
            "nav-shrink-container": isNavShrink,
            "z-100": !lg
          }
        )}
        data-testid={`${
          user.IsFreelancer
            ? "ipro-navigation-list"
            : "searcher-navigation-list"
        }`}
      >
        {isNavigationFetching ? (
          <NavigationSkeleton />
        ) : (
          <div className="flex flex-col gap-[16px] flex-1">
            <HeaderIcon onNavShrink={onNavigationToggle} />
            {navigationMenu.map(menu => {
              const {
                Id,
                Label,
                Url,
                CssClassIcon,
                CssClass,
                IsActive,
                NotificationBadge,
                NotificationCount,
                testId,
                SubNavigation,
                Disabled
              } = menu;
              const isSubNavActive = openSubNav.find(n => n === menu.Id);

              const navIcon = customLabel[Label];

              if (!SubNavigation?.length) {
                return (
                  IsActive && (
                    <Fragment key={Id}>
                      <NavigationToolTip
                        labels={labels}
                        Label={Label}
                        isHelpActive={isHelpActive}
                      >
                        <NavLinkButton
                          disabled={Disabled}
                          className={CssClass}
                          onClick={handleClick}
                          classIcon={CssClassIcon}
                          to={Url}
                          testId={testId}
                          navIcon={navIcon}
                          Label={Label}
                          isHelpActive={isHelpActive}
                          isNavShrink={isNavShrink}
                          labels={labels}
                          NotificationBadge={NotificationBadge}
                          NotificationCount={NotificationCount}
                          setIsNavShrink={setIsNavShrink}
                        >
                          {htmlParser(labels[Label])}
                        </NavLinkButton>
                      </NavigationToolTip>
                    </Fragment>
                  )
                );
              }

              if (SubNavigation?.length > 0) {
                return (
                  IsActive && (
                    <Fragment key={Id}>
                      <NavigationToolTip
                        labels={labels}
                        Label={Label}
                        isHelpActive={isHelpActive}
                      >
                        <NavButton
                          disabled={Disabled}
                          onClick={() => handleForSubNavClick(menu)}
                          classIcon={CssClassIcon}
                          name={Label}
                          testId={testId}
                          navIcon={navIcon}
                          isSubNavActive={isSubNavActive}
                          hasSubMenus
                          Label={Label}
                          labels={labels}
                          isHelpActive={isHelpActive}
                          isNavShrink={isNavShrink}
                          NotificationBadge={NotificationBadge}
                          NotificationCount={NotificationCount}
                          childClassName="gap-2 flex items-center"
                        >
                          {isNavShrink ? "" : htmlParser(labels[Label])}
                          {!isHelpActive && !isNavShrink && (
                            <CollapseableIcon isSubNavActive={isSubNavActive} />
                          )}
                        </NavButton>
                      </NavigationToolTip>
                      {isSubNavActive && (
                        <SubNav>
                          {map(SubNavigation, subNav => {
                            const navIcon = customLabel[subNav.Label];

                            return (
                              subNav.IsActive && (
                                <NavigationToolTip
                                  key={subNav.Id}
                                  labels={labels}
                                  Label={subNav.Label}
                                  isHelpActive={isHelpActive}
                                >
                                  <NavLinkButton
                                    className={clsx(
                                      subNav.CssClass,
                                      "sub-nav-link"
                                    )}
                                    onClick={handleClick}
                                    isSubNav
                                    classIcon={subNav.CssClassIcon}
                                    to={subNav.Url}
                                    testId={subNav.testId}
                                    iconClassName="w-[16px]"
                                    navIcon={navIcon}
                                    Label={subNav.Label}
                                    labels={labels}
                                    isHelpActive={isHelpActive}
                                    isNavShrink={isNavShrink}
                                    NotificationBadge={subNav.NotificationBadge}
                                    NotificationCount={subNav.NotificationCount}
                                    setIsNavShrink={setIsNavShrink}
                                  >
                                    {htmlParser(labels[subNav.Label])}
                                  </NavLinkButton>
                                </NavigationToolTip>
                              )
                            );
                          })}
                        </SubNav>
                      )}
                    </Fragment>
                  )
                );
              }
            })}
            <SubNavSetting
              isNavShrink={isNavShrink}
              setIsNavShrink={setIsNavShrink}
            />

            <NavLinkButton
              className={clsx("!text-white mt-auto", {
                "!bg-[var(--green)]": !user.IsFreelancer,
                "!bg-[#8E81F5]": user.IsFreelancer
              })}
              childClassName="flex items-center justify-between flex-1"
              inActive
              disabled={isPending}
              testId={"switch-to-ipro-searcher"}
              onClick={handleSwitchChange}
              navIcon={
                user.IsFreelancer
                  ? customLabel["IPRO_NAVIGATION_SWITCH_SEARCHER"]
                  : customLabel["SEARCHER_NAVIGATION_SWITCH_IPRO"]
              }
              labels={labels}
            >
              {user.IsFreelancer
                ? htmlParser(labels.IPRO_NAVIGATION_SWITCH_SEARCHER)
                : htmlParser(labels.SEARCHER_NAVIGATION_SWITCH_IPRO)}

              <Switch
                className={"ml-2"}
                IsFreelancer={user.IsFreelancer}
                onChange={handleSwitchChange}
                disabled={isPending}
                loading={isPending}
              />
            </NavLinkButton>
          </div>
        )}
      </div>
    );
  }
);

export default Navigation;

export const CollapseableIcon = ({ isSubNavActive, isHelpActive }) => (
  <Icon
    className={twMerge(
      clsx("min-w-[14px] w-[14px] transition-all", {
        "rotate-[-180deg]": isSubNavActive,
        "min-w-[18px] mr-[4px]": isHelpActive
      })
    )}
    renderSvg
    src={ArrowDown}
    alt="arrow down"
  />
);
