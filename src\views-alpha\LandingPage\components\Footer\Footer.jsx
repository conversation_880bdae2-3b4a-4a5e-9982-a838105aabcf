import Logo from "../../../../assets-alpha/images/svg/logo-new.svg?react";
import InstaIcon from "../../../../assets-alpha/images/svg/Instagram-icon.svg?react";
import FbIcon from "../../../../assets-alpha/images/svg/facebook-icon.svg?react";
import TwitterIcon from "../../../../assets-alpha/images/svg/twitter-icon.svg?react";
import LinkedInIcon from "../../../../assets-alpha/images/svg/linkedin-icon.svg?react";
import { Globe, Users } from "lucide-react";
import {
  useScrollAnimation,
  useStaggeredAnimation
} from "../../hooks/useScrollAnimation";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

const Footer = ({
  setTermsAndServicesDrawerOpen,
  setPrivacyPolicyDrawerOpen,
  setUserAgreementDrawerOpen
}) => {
  const labels = useSelector(state => state.systemLabel.labels);
  const navigate = useNavigate();
  const {
    elementRef: brandRef,
    isVisible: brandVisible
  } = useScrollAnimation();
  const {
    containerRef: sectionsRef,
    visibleItems: sectionItems
  } = useStaggeredAnimation(4, 400, 150);
  const {
    elementRef: bottomRef,
    isVisible: bottomVisible
  } = useScrollAnimation();

  const scrollToSection = (id, e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    const element = document.getElementById(id);
    if (element) {
      const headerOffset = 100;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition =
        elementPosition + window.pageYOffset - headerOffset;
      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth"
      });
    }
  };

  const footerSections = [
    {
      title: labels?.Footer_Platform_Lbl,
      icon: Globe,
      links: [
        { name: labels?.Footer_Link_1, id: "for-recruiters" },
        { name: labels?.Footer_Link_2, id: "for-ipros" },
        { name: labels?.Footer_Link_3, id: "whyprodoo" }
      ]
    },
    {
      title: labels?.Footer_Company_Lbl,
      icon: Users,
      links: [
        { name: labels?.Footer_Aboutus_lbl, id: "aboutus" },
        { name: labels?.Footer_Contactus_lbl, id: "contactus" }
      ]
    }
  ];

  return (
    <footer className="bg-gradient-to-b from-card to-card/80 border-t border-primary/20 relative overflow-hidden">
      {/* Background Elements - clicks disabled */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-0 left-1/4 w-40 h-40 bg-gradient-to-br from-primary/5 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-32 h-32 bg-gradient-to-br from-primary/5 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute inset-0 opacity-5">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `radial-gradient(circle at 2px 2px, var(--primary) 1px, transparent 0)`,
              backgroundSize: "30px 30px"
            }}
          ></div>
        </div>
      </div>

      {/* Main Footer */}
      <div className="relative w-full flex md:flex-row flex-col md:gap-0 gap-6 justify-between md:px-[70px] md:py-[50px] px-6 py-6 pointer-events-auto">
        {/* Brand Section */}
        <div
          ref={brandRef}
          className={`space-y-8 animate-on-scroll md:w-[25%] w-full ${
            brandVisible ? "animate-visible" : ""
          }`}
        >
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Logo />
            </div>
            <p className="text-muted-foreground text-xs md:text-[16px] font-normal leading-relaxed">
              {labels?.Footee_Desc}
            </p>
          </div>
        </div>

        {/* Links Sections */}
        <div
          ref={sectionsRef}
          className="flex justify-between md:w-[30%] w-full"
        >
          {footerSections.map((section, index) => (
            <div
              key={index}
              className={`space-y-6 animate-on-scroll ${
                sectionItems[index] ? "animate-visible" : ""
              }`}
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                  <section.icon className="w-4 h-4 text-primary" />
                </div>
                <h4 className="font-medium text-foreground text-sm md:text-[16px]">
                  {section.title}
                </h4>
              </div>
              <ul className="space-y-3 min-w-[117px]">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <a
                      onClick={e => scrollToSection(link.id, e)}
                      className="text-sm text-muted-foreground hover:text-primary transition-all duration-200 flex items-center gap-2 group cursor-pointer"
                    >
                      <div className="w-1 h-1 bg-muted rounded-full group-hover:bg-primary group-hover:scale-150 transition-all duration-200"></div>
                      <span className="group-hover:translate-x-1 transition-transform duration-200 text-[13px] md:text-sm font-normal">
                        {link.name}
                      </span>
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Social Links */}
        <div className="flex w-full md:w-[13%] gap-3 min-w-[196px]">
          {[
            {
              href:
                "https://www.instagram.com/prodooinsta?igshid=MzMyNGUyNmU2YQ%3D%3D&utm_source=qr",
              icon: InstaIcon,
              hover: "hover:bg-pink-500"
            },
            {
              href: "https://www.facebook.com/Prodoofacebook",
              icon: FbIcon,
              hover: "hover:bg-blue-600"
            },
            {
              href: "https://twitter.com/Prodoofreelance",
              icon: TwitterIcon,
              hover: "hover:bg-gray-800"
            },
            {
              href: "https://www.linkedin.com/company/prodoofreelancer",
              icon: LinkedInIcon,
              hover: "hover:bg-green-500"
            }
          ].map((item, idx) => (
            <a
              key={idx}
              href={item.href}
              target="_blank"
              rel="noopener noreferrer"
              className={`w-10 h-10 rounded-full border border-[#EAE5FC] bg-[#F4F2FE] 
                flex items-center justify-center transition-all duration-300 
                hover:scale-110 shadow-sm hover:shadow-lg cursor-pointer ${item.hover}`}
            >
              <item.icon className="w-5 h-5 text-muted-foreground transition-colors duration-300 group-hover:text-white" />
            </a>
          ))}
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-primary/10 relative z-10 bg-[#8E81F5] pointer-events-auto">
        <div className="px-9 md:px-[80px] py-6">
          <div className="flex md:flex-row flex-col w-full justify-between items-center gap-4">
            <div className="flex items-center gap-3 max-w-[140px]">
              <div className="text-sm text-white !font-poppins">
                © {new Date().getFullYear()} <span> </span>
                {labels?.ProDoo_Inc_Lbl}
              </div>
            </div>
            <div className="flex justify-between items-center text-sm text-white font-poppins w-full md:max-w-[346px]">
              <span
                className="cursor-pointer"
                onClick={() => {
                  setTermsAndServicesDrawerOpen(true);
                }}
              >
                {labels?.Footer_Terms_Lbl}
              </span>
              <span
                className="cursor-pointer"
                onClick={() => {
                  setPrivacyPolicyDrawerOpen(true);
                }}
              >
                {labels?.Footer_Privacy_Lbl}
              </span>
              <span
                className="cursor-pointer"
                onClick={() => {
                  setUserAgreementDrawerOpen(true);
                }}
              >
                {labels?.Footer_Cookies_Lbl}
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
