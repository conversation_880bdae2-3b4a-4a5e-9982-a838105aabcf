import EmptyInfo from "../../../common/EmptyInfo/EmptyInfo";
import LoadingMask from "../../../common/LoadingMask/LoadingMask";
import ImgSrc from "../../../assets/images/company.svg";
import CloseModal from "../../../assets-alpha/images/svg/close-modal.svg";
import Mail from "../../../assets-alpha/images/svg/mail-icon.svg";
import call from "../../../assets-alpha/images/svg/call.svg";
import { Button, Typography } from "antd";
import { EditOutlined } from "@ant-design/icons";
const Showcase = props => {
  const viewRender = () => {
    const {
      presentForm,
      labels,
      handleDelete,
      setIsModalOpen,
      setIsDrawerOpen,
      setIsEditForm
    } = props;
    if (presentForm && presentForm.UserDetailId) {
      const { UserEmails, Title, Detail, IsDefault, IsChecked } = presentForm;
      return (
        <div className="flex flex-col w-full !p-0">
          <div className="flex flex-col h-full w-full">
            <div className="flex w-full justify-between gap-2 items-center ">
              <div className="relative w-full md:w-[497px] h-[64px]">
                <div className="fixed md:w-[497px] w-full h-[64px] border-b border-[#EAE5FC] z-[999] bg-white rounded-tl-2xl rounded-tr-2xl">
                  <div className="flex w-full h-[64px] justify-between items-center p-6">
                    <div className="flex justify-between md:gap-5 w-full md:h-10 items-center h-[64px] border-b border-[#EAE5FC] md:border-b-0 md:pl-0 md:pr-0">
                      <img
                        src={CloseModal}
                        alt=""
                        className="pointer md:order-1 order-2"
                        onClick={() => {
                          setIsDrawerOpen(false);
                        }}
                      />
                      <Typography.Title
                        level={3}
                        className="!m-0 flex-1 order-1 md:order-2 !text-[18px] !font-semibold md:!text-2xl md:font-medium"
                      >
                        {labels?.IPRO_Portfolio_Showcase_Text}
                      </Typography.Title>
                    </div>
                    <div className="hidden md:block">
                      {presentForm?.UserDetailId != -1 && (
                        <div className="flex gap-3">
                          <Button
                            type="filled"
                            className="!m-0 !bg-[#FEEDED] !w-[68px] !h-9 !text-[#FF3B30] pointer"
                            onClick={() => {
                              handleDelete(presentForm?.UserDetailId);
                              props?.setDeleteDialog(true);
                            }}
                          >
                            {labels?.delete_presentation}
                          </Button>
                          <Button
                            type="filled"
                            className="!m-0 !bg-[#F3F1FD] !w-[68px] !h-9 !text-[#8E81F5] pointer"
                            onClick={() => {
                              setIsDrawerOpen(false);
                              setIsModalOpen(true);
                              setIsEditForm(true);
                            }}
                            icon={<EditOutlined />}
                          >
                            {labels?.IPRO_Edit_Button_Text}
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-center md:p-0 mt-6">
              <div className="flex flex-col rounded-2xl bg-[#F3F1FD] h-auto w-full min-w-[343px] ml-4 mr-4">
                <div className="flex items-center justify-center rounded-tr-2xl rounded-tl-2xl md:!h-11 !h-10">
                  <p className="!text-[#8E81F5] md:!font-semibold !font-medium !text-[14px] md:!text-[16px] !leading-[100%]">
                    {Title?.length > 40 ? `${Title.slice(0, 40)}...` : Title}
                  </p>
                </div>
                <div className="flex items-center border-b border-t border-b-[#EAE5FC] border-t-[#EAE5FC] p-4 md:h-12 h-9 gap-4">
                  <img src={call} alt="" className="w-4 h-4" />
                  <span className="h-[22px] border border-[#EAE5FC]"></span>
                  <p className="!font-normal !text-[13px] !text-[#343333]">
                    {presentForm?.selectedPhoneNumber?.label
                      ? presentForm?.selectedPhoneNumber?.label
                      : "N/A"}
                  </p>
                </div>
                <div className="flex items-center border-b  border-b-[#EAE5FC] p-4 md:h-12 h-9 gap-4">
                  <img src={Mail} alt="" className="w-4 h-4 " />
                  <span className="h-[22px] border border-[#EAE5FC]"></span>
                  <p className="!font-normal !text-[13px] !text-[#343333]">
                    {UserEmails && UserEmails.UserEmailValue
                      ? UserEmails && UserEmails.UserEmailValue
                      : "N/A"}
                  </p>
                </div>
                <div className="flex border-b border-b-[#F3F1FD] rounded-bl-2xl rounded-br-2xl h-auto p-4 md:!min-h-12 !min-h-9">
                  <p className="!text-[#343333] !leading-[15px] !font-normal !text-[13px]  overflow-auto">
                    {Detail ? (
                      <>
                        <span className="text-[#878787] font-normal text-[13px]">
                          {labels?.collDescription}: <span> </span>
                        </span>
                        {Detail}
                      </>
                    ) : (
                      <label className="!text-[#878787] !font-normal !text-[13px]">
                        {labels?.collDescription}
                        <span className="!text-[#343333] !leading-[15px] !font-normal !text-[13px] ">
                          : N/A
                        </span>
                      </label>
                    )}
                  </p>
                </div>
              </div>
            </div>
            <div className="!mt-6 !pl-4 md:!pl-6 hidden md:block">
              {presentForm?.PortfolioImages?.length > 0 && (
                <h1 className="!m-0 !p-0 !text-[14px] !font-normal !text-[#343333]">
                  {labels?.Image_Label}
                </h1>
              )}
            </div>
            <div className="flex md:justify-start justify-start pl-4 pr-4 md:pl-6 md:pr-6 flex-wrap w-full gap-[9px] mt-4 md:mt-[7px] mb-[90px] [&>*:last-child]:mb-[90px] md:[&>*:last-child]:mb-6">
              {presentForm?.PortfolioImages?.map((item, index) => (
                <div
                  key={index}
                  className="flex md:w-[210px] md:h-[210px] w-[160px] h-[160px] border border-[#EAE5FC] rounded-[10px]"
                  onClick={() => {
                    props?.setShowPreviwImages(true);
                  }}
                >
                  <img
                    className="w-full md:!h-full !h-[167px] min-w-[167px] rounded-[10px] object-cover"
                    src={item?.Image ? item?.Image : ImgSrc}
                    alt="company"
                  />
                </div>
              ))}
            </div>
          </div>
          <div className="">
            {presentForm?.UserDetailId != -1 && (
              <div className="md:hidden flex w-full h-[64px] bg-white fixed bottom-0 justify-center items-center  border-t-[0.5px] border-[#EAE5FC] gap-[9px] pl-4 pr-4">
                <Button
                  type="filled"
                  className="!m-0 !bg-[#FEEDED] !w-full !h-10 !text-[#FF3B30] pointer"
                  onClick={() => {
                    handleDelete(presentForm?.UserDetailId);
                    props?.setDeleteDialog(true);
                  }}
                >
                  {labels?.delete_presentation}
                </Button>
                <Button
                  type="filled"
                  className="!m-0 !bg-[#F3F1FD] !w-full !h-10 !text-[#8E81F5] pointer"
                  onClick={() => {
                    setIsDrawerOpen(false);
                    setIsModalOpen(true);
                    setIsEditForm(true);
                  }}
                  icon={<EditOutlined />}
                >
                  {labels?.IPRO_Edit_Button_Text}
                </Button>
              </div>
            )}
          </div>
        </div>
      );
    }
    return (
      <EmptyInfo testId="company-empty-detail">
        {labels?.Freelance_Present}
      </EmptyInfo>
    );
  };

  return (
    <div className="company-form-editor">
      {props.isLoading && <LoadingMask />}
      {viewRender()}
    </div>
  );
};

export default Showcase;
