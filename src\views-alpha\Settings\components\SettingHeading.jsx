import { Grid, Typography } from "antd";
import { useSelector } from "react-redux";
import Button from "../../../common/Button/Button";
import { CloseOutlined, SyncOutlined } from "@ant-design/icons";
import clsx from "clsx";
const { Title, Text } = Typography;

const SettingHeading = ({ onFinish, isValid, loading, onClose }) => {
  const screens = Grid.useBreakpoint();
  const labels = useSelector(state => state.systemLabel.labels?.setting);

  return (
    <div className="">
      <div className="flex justify-between items-center mb-5 px-6">
        <Title level={3} className="text-2xl font-semibold !mb-0">
          {labels?.title}
        </Title>
        <div className="flex items-center gap-4">
          <Button
            onClick={onFinish}
            disabled={!isValid || loading}
            className={clsx(
              "bg-[var(--purple)] py-[6px] px-[12px] font-medium !text-white rounded-lg h-9",
              {
                "!hidden": !screens.md
              }
            )}
          >
            {labels?.save}
            {loading && (
              <>
                &nbsp; <SyncOutlined spin />
              </>
            )}
          </Button>

          <Button
            onClick={onClose}
            className="bg-[var(--light-purple)] w-[28px] h-[28px] text-[var(--dark)] rounded-4xl flex items-center justify-center"
          >
            <CloseOutlined />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SettingHeading;
