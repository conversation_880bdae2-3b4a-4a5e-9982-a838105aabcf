import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { StorageService } from "../../../../../api/storage";
import EditIcon from "../../../../../assets-alpha/images/svg/edit.svg?react";
import CustomInput from "../../../../../common-alpha/CustomInput/CustomInput";
import CommonDrawer from "../../../../../common-alpha/Drawer/Drawer";
import SegmentedUi from "../../../../../common-alpha/SegmentedUi/SegmentedUi";
import CustomSelect from "../../../../../common-alpha/Select/Select";
import { getAllCollaborationsApi } from "../../../snapshotApi";
import { SubHeading } from "../../Reports/components/TimelogList";

import { MoreOutlined } from "@ant-design/icons";
import { But<PERSON>, Dropdown } from "antd";
import { List, ListItem } from "../../Reports/Reports";
import { DropdownMenu } from "../Pools";
import TimeAndExpenses from "./TimeAndExpenses";

const PoolDetail = ({
  onClose,
  savePool,
  token,
  UserId,
  poolDetails,
  setPoolDetails
}) => {
  const labels = useSelector(state => state.systemLabel.labels);
  const [selectedTab, setSelectedTab] = useState("Overview");
  const [allActiveCollaborations, setAllActiveCollaborations] = useState([]);
  const userFromStore = useSelector(s => s.userInfo?.user);
  const [editPoolName, setEditPoolName] = useState(false);
  const User = userFromStore ? userFromStore : StorageService.getUser();

  const onTabChange = name => {
    setSelectedTab(name);
  };
  const [selectedActiveOption, setSelectedActiveOption] = useState(undefined);
  useEffect(() => {
    getAllCollaborationsApi(1, token)
      .then(res => {
        if (res.items && res.items.length > 0) {
          const actives = res.items.map(coll => ({
            ...coll,
            IsShared: coll.UserId != (User?.UserId || UserId),
            value: coll.CollaborationId,
            label: `${coll.UserName}(${coll.Title})`
          }));
          setAllActiveCollaborations(actives);
        }
      })
      .catch(err => {
        console.error("getAllCollaborationsApi error:", err);
      });
  }, [User?.UserId, UserId, token]);
  const handlePoolNameChange = e => {
    const v = e.target?.value;
    setPoolDetails(prev => ({ ...prev, Title: v }));
  };
  const handlePoolNameBlur = () => {
    editPoolName && savePool();
  };
  const handleActiveCollaborationClick = option => {
    setSelectedActiveOption(option);
    const Collaborations = poolDetails?.Collaborations || [];
    const updatePoolDetail = {
      ...poolDetails,
      Collaborations:
        Collaborations.findIndex(
          a => a.CollaborationId === option.CollaborationId
        ) < 0
          ? Collaborations.concat(option)
          : Collaborations
    };
    setPoolDetails(updatePoolDetail);
    savePool(updatePoolDetail);
  };

  const handleCollaborationRemove = option => {
    const Collaborations = poolDetails?.Collaborations || [];
    const updatePoolDetail = {
      ...poolDetails,
      Collaborations: Collaborations.filter(
        a => a.CollaborationId !== option.CollaborationId
      )
    };
    setPoolDetails(updatePoolDetail);
    savePool(updatePoolDetail);
  };
  return (
    <CommonDrawer
      open={true}
      width={450}
      title={"Pools details"}
      onClose={onClose}
    >
      <SegmentedUi
        className="!mt-4 !mb-6 w-full [&_.ant-segmented-item]:!flex-1"
        options={["Overview", "Time and expense"]}
        onChange={onTabChange}
        value={selectedTab}
        // disabled={disabled}
      />
      {selectedTab === "Overview" && (
        <div>
          <div>
            <CustomInput
              label={"Pool name"}
              value={poolDetails?.Title || ""}
              placeholder={"Title"}
              data-testid="input-expanse-amount"
              onChange={handlePoolNameChange}
              onBlur={handlePoolNameBlur}
              readOnly={!editPoolName}
              suffixInput={
                !editPoolName && (
                  <EditIcon onClick={() => setEditPoolName(true)} />
                )
              }
              layout={"vertical"}
              className="!mb-4"
            />

            <CustomSelect
              label={"Add contract"}
              readonly={true}
              searchable={false}
              // name="Active Collaborations"
              value={selectedActiveOption}
              placeholder={"Please select a collaboration to add"}
              onChange={(selectedOption, item) =>
                handleActiveCollaborationClick(item)
              }
              options={allActiveCollaborations}
              clearable={false}
              layout={"vertical"}
              className="!mb-4"
            />
          </div>

          <SubHeading>Exiting contracts</SubHeading>
          <List>
            {poolDetails?.Collaborations?.map(item => (
              <CollaborationItem
                key={item.CollaborationId}
                item={item}
                handleCollaborationRemove={handleCollaborationRemove}
              />
            ))}
          </List>
        </div>
      )}

      {selectedTab === "Time and expense" && (
        <TimeAndExpenses
          labels={labels}
          token={token}
          poolDetails={poolDetails}
          allCollaborations={poolDetails?.Collaborations}
        />
      )}
    </CommonDrawer>
  );
};

export default PoolDetail;

const CollaborationItem = ({ item, handleCollaborationRemove }) => {
  const [showDetail, setShowDetail] = useState(false);
  return (
    <ListItem
      isSelected={true}
      onClick={() => setShowDetail(!showDetail)}
      text={item.Title}
      rightClassName={"p-0 bg-[transparent]"}
      right={
        <Dropdown
          overlayClassName="[&_.ant-dropdown-menu]:!py-[6px] [&_.ant-dropdown-menu-item]:!py-[6px]"
          menu={{
            ...DropdownMenu,
            onClick: e => {
              e.domEvent.stopPropagation();
              if (e.key === "delete") {
                handleCollaborationRemove(item);
              }
            }
          }}
          onClick={e => e.stopPropagation()}
          trigger={["click"]}
        >
          <Button
            type="text"
            icon={<MoreOutlined className="!text-[20px]" />}
          />
        </Dropdown>
      }
      // detail={
      //   showDetail && (
      //     <div className="mt-4 flex flex-col gap-1">
      //       <div>Time and expense</div>
      //       <div className="flex justify-between text-[var(--dark)]">
      //         <div>Wen design team</div>
      //         <div>
      //           <span className="text-[var(--gray-3)]">Time:</span> 10h 35m
      //         </div>
      //         <div>
      //           <span className="text-[var(--gray-3)]">Expense:</span> 250USD
      //         </div>
      //       </div>
      //     </div>
      //   )
      // }
    />
  );
};
