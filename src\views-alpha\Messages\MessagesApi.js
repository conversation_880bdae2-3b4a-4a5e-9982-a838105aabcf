import { useClientMutation, useClientQuery } from "../../api-alpha/api-service";
import { ApiUrl } from "../../api-alpha/apiUrls";
//Conversation Apis
export const useConvetsationApi = () => {
  return useClientMutation({
    url: ApiUrl.Messages.Conversation,
    method: "POST"
  });
};
export const useGetConversationApi = conversationId => {
  return useClientQuery({
    enabled: !!conversationId,
    url: ApiUrl.Messages.GetConversationByConversationId({ conversationId })
  });
};
export const useGetConversationByUserIdApi = userId => {
  return useClientQuery({
    enabled: !!userId,
    url: ApiUrl.Messages.GetConversationByUserId({ userId })
  });
};

// Messages apis
export const useSendMessageApi = () => {
  return useClientMutation({
    url: ApiUrl.Messages.SendMessage,
    method: "POST"
  });
};
export const useGetMessageApi = () => {
  return useClientQuery({
    url: ApiUrl.Messages.GetMessages,
    enabled: false
  });
};
export const useGetMessagesByConversationIdApi = conversationId => {
  return useClientQuery({
    enabled: !!conversationId,
    url: ApiUrl.Messages.GetMessagesByConversationId({ conversationId })
  });
};

// user apis
export const useAllUsersStatusApi = () => {
  return useClientMutation({
    url: ApiUrl.Messages.AllUserStatus,
    method: "POST"
  });
};
export const useGetUserStatusByIdApi = userId => {
  return useClientQuery({
    enabled: !!userId,
    url: ApiUrl.Messages.GetUserStatusById({ userId })
  });
};
export const useGetValidateUserApi = userId => {
  return useClientQuery({
    enabled: !!userId,
    url: ApiUrl.Messages.GetValidateUser({ userId })
  });
};
export const useGetUsersByIdApi = userId => {
  return useClientQuery({
    enabled: !!userId,
    url: ApiUrl.Messages.GetUsersById({ userId })
  });
};
