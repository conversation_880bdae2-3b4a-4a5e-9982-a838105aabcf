import { map } from "lodash";
import { Fragment, memo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { htmlParser } from "../../../utilities/helpers";
import NavButton from "./NavButton";
import NavLinkButton from "./NavLinkButton";
import clsx from "clsx";
import { CollapseableIcon, NavigationToolTip, SubNav } from "../Navigation";

import FaqIcon from "../../../assets-alpha/images/svg/faq.svg?react";
import InformationIcon from "../../../assets-alpha/images/svg/information.svg?react";
import AboutIcon from "../../../assets-alpha/images/svg/about-us.svg?react";
import FeedbackIcon from "../../../assets-alpha/images/svg/feedback.svg?react";
import SettingIcon from "../../../assets-alpha/images/svg/setting.svg?react";
import LoginIcon from "../../../assets-alpha/images/svg/login.svg?react";

import { Grid, Switch } from "antd";
import {
  helpActiveAction,
  settingModalAction
} from "../../../actions/navigationActions";
import { useLogout } from "../../Header/useHeader";

const LabelMapWithIcon = {
  Navigation_Settings_others: {
    icon: SettingIcon,
    active: SettingIcon
  },
  setting_label: {
    icon: SettingIcon,
    active: SettingIcon
  },
  LANDING_NAV_ABOUT_US_LBL: {
    icon: AboutIcon,
    active: AboutIcon
  },
  IPRO_NAVIGATION_FEEDBACK_LABEL: {
    icon: FeedbackIcon,
    active: FeedbackIcon
  },
  IPRO_NAVIGATION_GUIDE_LABEL: {
    icon: InformationIcon,
    active: InformationIcon
  },
  ABOUT_FAQ_HEADING: {
    icon: FaqIcon,
    active: FaqIcon
  },
  HEADER_LOGOUT_ICON_HELP: {
    active: LoginIcon,
    icon: LoginIcon
  }
};

const settingNav = [
  {
    Id: 1,
    Label: "Navigation_Settings_others",
    SubNavigation: [
      {
        Id: 1,
        Label: "setting_label"
      },
      {
        Id: 2,
        Label: "LANDING_NAV_ABOUT_US_LBL",
        Url: "/about"
      },
      {
        Id: 3,
        Label: "ABOUT_FAQ_HEADING",
        Url: "/faq"
      },
      {
        Id: 4,
        Label: "IPRO_NAVIGATION_FEEDBACK_LABEL",
        Url: "/feedback"
      },
      {
        Id: 5,
        Label: "HEADER_LOGOUT_ICON_HELP",
        className: "!text-[#ff3b30]"
      }
    ]
  },
  {
    Id: 2,
    Label: "IPRO_NAVIGATION_GUIDE_LABEL",
    isGuide: true
  }
];

const SubNavSetting = memo(({ isNavShrink, setIsNavShrink }) => {
  const dispatch = useDispatch();
  const { lg } = Grid.useBreakpoint();
  const { handleLogout } = useLogout();
  const [openSubNav, setOpenSubNav] = useState([]);
  const user = useSelector(state => state.userInfo.user);
  const labels = useSelector(state => state.systemLabel.labels);
  const isHelpActive = useSelector(state => state.navigation.isHelpActive);
  const isSettingOpen = useSelector(state => state.navigation.isSettingOpen);
  const handleNavClick = selectedItem => {
    switch (selectedItem?.Label) {
      case "Navigation_Settings_others":
        setOpenSubNav(nav => {
          if (nav.includes(selectedItem.Id)) {
            return nav.filter(n => n !== selectedItem.Id);
          }
          return [...nav, selectedItem.Id];
        });
        return;
      // break;
      case "IPRO_NAVIGATION_GUIDE_LABEL":
        dispatch(helpActiveAction({ isHelpActive: !isHelpActive }));
        break;

      case "setting_label":
        dispatch(settingModalAction(!isSettingOpen));
        break;

      case "HEADER_LOGOUT_ICON_HELP":
        handleLogout();
        break;

      default:
        break;
    }
    handleClick();
  };
  const handleClick = () => {
    if (!lg) {
      setIsNavShrink(true);
    }
  };

  return (
    <div className="hidden max-lg:block">
      <div className="flex flex-col gap-[16px] flex-1">
        {settingNav.map(menu => {
          const { Id, Label, Url, SubNavigation, isGuide } = menu;
          const isSubNavActive = openSubNav.find(n => n === menu.Id);

          const navIcon = LabelMapWithIcon[Label];

          return (
            <Fragment key={Id}>
              <NavigationToolTip
                labels={labels}
                Label={Label}
                isHelpActive={isHelpActive}
              >
                {Url ? (
                  <NavLinkButton
                    to={Url}
                    navIcon={navIcon}
                    Label={Label}
                    isHelpActive={isHelpActive}
                    isNavShrink={isNavShrink}
                    labels={labels}
                  >
                    {htmlParser(labels[Label])}
                  </NavLinkButton>
                ) : (
                  <NavButton
                    onClick={() => handleNavClick(menu)}
                    name={Label}
                    navIcon={navIcon}
                    Label={Label}
                    labels={labels}
                    isHelpActive={!isGuide && isHelpActive}
                    isNavShrink={isNavShrink}
                    hasSubMenus={SubNavigation?.length > 0}
                    childClassName="gap-2 flex items-center"
                  >
                    {isNavShrink ? "" : htmlParser(labels[Label])}
                    {SubNavigation?.length > 0 &&
                      !isHelpActive &&
                      !isNavShrink && (
                        <CollapseableIcon
                          isSubNavActive={isSubNavActive}
                          isHelpActive={isHelpActive}
                        />
                      )}
                    {isGuide && (
                      <Switch className="!ml-auto" checked={isHelpActive} />
                    )}
                  </NavButton>
                )}
              </NavigationToolTip>
              {SubNavigation?.length > 0 && isSubNavActive && (
                <SubNav>
                  {map(SubNavigation, subNav => {
                    const navIcon = LabelMapWithIcon[subNav.Label];
                    return (
                      <NavigationToolTip
                        key={subNav.Id}
                        labels={labels}
                        Label={subNav.Label}
                        isHelpActive={isHelpActive}
                      >
                        {subNav.Url ? (
                          <NavLinkButton
                            className={clsx(subNav.CssClass, "sub-nav-link")}
                            isSubNav
                            to={subNav.Url}
                            iconClassName="w-[16px]"
                            navIcon={navIcon}
                            Label={subNav.Label}
                            labels={labels}
                            isHelpActive={isHelpActive}
                            isNavShrink={isNavShrink}
                            onClick={handleClick}
                          >
                            {htmlParser(labels[subNav.Label])}
                          </NavLinkButton>
                        ) : (
                          <NavButton
                            onClick={() => handleNavClick(subNav)}
                            name={Label}
                            className={clsx(subNav.className, "sub-nav-link")}
                            navIcon={navIcon}
                            iconClassName="w-[16px] min-w-[20px]"
                            Label={subNav.Label}
                            labels={labels}
                            isHelpActive={isHelpActive}
                            isNavShrink={isNavShrink}
                            isSubNav
                          >
                            {isNavShrink
                              ? ""
                              : htmlParser(labels[subNav.Label])}
                          </NavButton>
                        )}
                      </NavigationToolTip>
                    );
                  })}
                </SubNav>
              )}
            </Fragment>
          );
        })}
      </div>
    </div>
  );
});

export default SubNavSetting;
