import {
  DatePicker,
  Dropdown,
  Menu,
  Modal,
  Row,
  Space,
  Typography
} from "antd";
import { useState } from "react";
import {
  PlusOutlined
} from "@ant-design/icons";
import "./style.scss";
import { useDispatch } from "react-redux";
import { notificationAction } from "../../../../actions/notification";
import closeIcon from "../../../../assets/images/Icons/close-new.png";
import CommonSelect from "../../../../common/selectFilter/selectFilter";
import moment from "moment";
const { Text, Title } = Typography;

const MobileFilter = ({
  isModalOpen,
  setIsModalOpen,
  filter,
  handleChange,
  handleSearch,
  globalState,
  loading,
  setCreateSaveSearchModal,
  labels,
  state,
  handleExistingSaveSearchClick,
  handleClear
}) => {
  const dispatch = useDispatch();
  const [showDropdown, setShowDropdown] = useState(false);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const saveSearchDropdown = resume => {
    return (
      <Menu
        style={{
          height: "300px",
          overflow: "auto",
          width: "290px"
        }}
        className="dropdown-shortlists"
      >
        <Menu.Item
          key="Newest"
          onClick={e => {
            setCreateSaveSearchModal(st => ({
              ...st,
              open: true,
              title: "Create",
              id: 0
            }));
            setShowDropdown(false);
          }}
        >
          <span className="span-">Create New Search</span>
          <PlusOutlined />
        </Menu.Item>
        <Menu.Divider />
        {state?.saveSearch?.map((single, index) => (
          <Menu.Item
            key={index}
            style={{
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              width: "100%"
            }}
            onClick={() => {
              handleExistingSaveSearchClick(single?.SavedSearchId);
              setShowDropdown(!showDropdown);
            }}
          >
            {single?.SearchName}
          </Menu.Item>
        ))}
      </Menu>
    );
  };

  return (
    <>
      <Modal
        open={isModalOpen}
        style={{
          maxHeight: "calc(100% - 32px)",
          overflow: "auto",
          height: "100%"
        }}
        centered
        onCancel={handleCancel}
        zIndex={100}
        footer={false}
        closable={false}
        className={`new-design-modal modal-for-save-search`}
      >
        <div className="main-modal">
          <div
            className="text-center header-modal"
            style={{ alignItems: "start" }}
          >
            <div className="w-full text-clamp">
              <Title level={5} className="!m-0 text-clamp flex-1">
                {globalState?.filterSaveSearch?.SearchName}
              </Title>
              {globalState?.filterSaveSearch?.UpdatedOn && (
                <div className=" text-clamp">
                  <span className=" text-clamp" style={{ color: "#00000080" }}>
                    {" "}
                    {`(Created ${moment(
                      globalState?.filterSaveSearch?.UpdatedOn
                    )?.format("DD-MM-YYYY hh:mm a")})`}
                  </span>
                </div>
              )}
            </div>
            <div
              onClick={() => {
                handleCancel();
              }}
              className="pointer"
            >
              <img className="close-icon" src={closeIcon} alt="" />
            </div>
          </div>
          <div
            className="new-search-resume-main form"
            style={{ flex: 1, overflow: "auto" }}
          >
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "16px",
                borderBottom: "1px solid #F3F3F3"
              }}
            >
              <CommonSelect
                loading={loading?.role}
                options={filter?.ProfileIds || []}
                label={"Role"}
                value={globalState?.selected?.ProfileIds || []}
                placeholder={"Select role"}
                handleChange={handleChange}
                handleSearch={handleSearch}
                searchParams={"role"}
                changesParams={"ProfileIds"}
              />
              <CommonSelect
                options={filter?.Countries}
                label={"Location"}
                placeholder={"Select location"}
                value={globalState?.selected?.Countries || []}
                handleChange={handleChange}
                searchParams={"role"}
                changesParams={"Countries"}
                search={false}
              />
              <CommonSelect
                options={filter?.Languages}
                label={"Language"}
                value={globalState?.selected?.Languages || []}
                placeholder={"Select Language"}
                search={false}
                handleChange={handleChange}
                changesParams={"Languages"}
              />

              <CommonSelect
                options={filter?.SkillIds || []}
                label={"Skill"}
                loading={loading?.skill}
                value={globalState?.selected?.SkillIds || []}
                placeholder={"Select skills"}
                handleChange={handleChange}
                changesParams={"SkillIds"}
                handleSearch={handleSearch}
                searchParams={"Skill"}
                onChange={(e, option) => handleChange(e, "SkillIds", option)}
                onSearch={e => {
                  handleSearch(e, "Skill");
                }}
              />

              <CommonSelect
                options={filter?.KeywordIds || []}
                label={"Keyword"}
                loading={loading?.keyword}
                value={globalState?.selected?.KeywordIds || []}
                placeholder={"Select keywords"}
                handleChange={handleChange}
                changesParams={"KeywordIds"}
                handleSearch={handleSearch}
                searchParams={"keyword"}
              />

              <CommonSelect
                options={filter?.IndusteryIds || []}
                label={"Industry"}
                loading={loading?.industry}
                value={globalState?.selected?.IndusteryIds || []}
                placeholder={"Select industry"}
                handleChange={handleChange}
                changesParams={"IndusteryIds"}
                handleSearch={handleSearch}
                searchParams={"industry"}
              />

              <CommonSelect
                options={filter?.CertificationIds || []}
                label={"Certification"}
                loading={loading?.certificate}
                value={globalState?.selected?.CertificationIds || []}
                placeholder={"Select certifications"}
                handleChange={handleChange}
                changesParams={"CertificationIds"}
                handleSearch={handleSearch}
                searchParams={"certificate"}
              />
              <div>
                <Text className="label !m-0">Availability Date</Text>
                <div className="animated">
                  <DatePicker
                    style={{
                      width: "100%",
                      marginTop: "6px",
                      border: "1px solid #f3f3f3"
                    }}
                    value={globalState?.selected?.AvailabilityDate}
                    onChange={e => handleChange(e, "AvailabilityDate")}
                    bordered={false}
                  />
                </div>
              </div>
            </div>
          </div>
          <Row
            className=""
            style={{
              padding: "20px",
              borderTop: "1px solid #F3F3F3"
            }}
          >
            <Space
              size={[12, 8]}
              style={{
                margin: "auto",
                marginTop: "0px",
                marginBottom: "0px"
              }}
            >
              <button
                className="button-last-footer w-full"
                onClick={() => {
                  handleClear();
                }}
              >
                Clear Search
              </button>
              <Dropdown
                overlay={saveSearchDropdown()}
                trigger={["click"]}
                open={showDropdown}
                onOpenChange={() => {
                  if (
                    !(
                      globalState?.selected?.IndusteryIds.length ||
                      globalState?.selected?.KeywordIds.length ||
                      globalState?.selected?.ProfileIds.length ||
                      globalState?.selected?.SkillIds.length ||
                      globalState?.selected?.CertificationIds.length ||
                      globalState?.selected?.Languages.length ||
                      globalState?.selected?.Countries.length
                    )
                  ) {
                    const info = {
                      message: labels.SEARCH_NO_CRITERIA_VALIDATION,
                      status: "error"
                    };
                    dispatch(notificationAction(info));
                    return;
                  }
                  setShowDropdown(!showDropdown);
                }}
              >
                <button className="button-footer-opportunity w-full">
                  Save Search
                </button>
              </Dropdown>
            </Space>
          </Row>
        </div>
      </Modal>
    </>
  );
};
export default MobileFilter;
