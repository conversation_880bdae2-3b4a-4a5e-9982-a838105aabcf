import { useState } from "react";
import CloseIcon from "../../../../../assets-alpha/images/svg/close-modal.svg?react";
import ArrowLeft from "../../../../../assets-alpha/images/svg/arrow-left-1.svg?react";
import ArrowRight from "../../../../../assets-alpha/images/svg/arrow-right-1.svg?react";
const ImageSlider = ({ setPreviewImages }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const imageDescription = [
    {
      id: 1,
      title: "Snapshot",
      description:
        "Get a quick overview of timesheets, expenses, contracts, and pool reports all in one place.",
      imageURL: "/assets/images/snapshot.webp"
    },
    {
      id: 2,
      title: "<PERSON>",
      description:
        "Smart AI assistant to find the right talent faster with advanced filters.",
      imageURL: "/assets/images/phillip-ai.webp"
    },
    {
      id: 3,
      title: "Tictell",
      description:
        "Track your work with timesheets, expenses, and detailed reports.",
      imageURL: "/assets/images/tictell.webp"
    },
    {
      id: 4,
      title: "<PERSON>",
      description:
        "Transform your resume into a standout profile with AI-powered enhancements.",
      imageURL: "/assets/images/phillip-ipro.webp"
    }
  ];

  const goToPrevious = () => {
    const isFirstSlide = currentIndex === 0;
    const newIndex = isFirstSlide
      ? imageDescription.length - 1
      : currentIndex - 1;
    setCurrentIndex(newIndex);
  };

  const goToNext = () => {
    const isLastSlide = currentIndex === imageDescription.length - 1;
    const newIndex = isLastSlide ? 0 : currentIndex + 1;
    setCurrentIndex(newIndex);
  };

  const goToSlide = slideIndex => {
    setCurrentIndex(slideIndex);
  };

  return (
    <div className="flex flex-col items-center w-full mt-[61px]">
      <div className="absolute top-[22px] md:top-[46px] left-[24px] md:left-[52px]">
        <p className="text-white font-medium md:font-semibold text-sm md:!text-[20px]">
          {currentIndex + 1}/{imageDescription?.length}
        </p>
      </div>
      <div
        className="absolute top-[19px] md:top-[44px] right-[24px] md:right-[52px] cursor-pointer"
        onClick={() => {
          setPreviewImages(false);
        }}
      >
        <CloseIcon />
      </div>
      <div className="flex flex-col gap-3 w-full items-center px-4 md:px-0 text-center">
        <h2 className="!m-0 text-2xl md:text-[32px] font-semibold text-[#8E81F5]">
          {imageDescription[currentIndex].title}
        </h2>
        <p className="!m-0 font-medium leading-[18px] md:leading-[30px] text-xs md:text-[20px] text-white">
          {imageDescription[currentIndex].description}
        </p>
      </div>
      <div className="w-full md:w-[25%] mt-6 md:mt-[66px] mb-3">
        <div className="relative flex w-full h-[70vh] justify-center ">
          <img
            src={imageDescription[currentIndex].imageURL}
            alt=""
            className="bg-cover"
          />
          <button
            onClick={goToPrevious}
            className={`absolute top-1/2 left-[15px] md:-left-1/3 transform -translate-y-1/2 bg-white  text-white rounded-full p-3 border-[0.6px] border-[#EAE5FC] shadow ${currentIndex ===
              0 && "hidden"}`}
          >
            <ArrowLeft />
          </button>
          <button
            onClick={goToNext}
            className={`absolute top-1/2 right-[15px] md:-right-1/3 transform -translate-y-1/2 bg-white  text-white rounded-full p-3 border-[0.6px] border-[#EAE5FC] shadow ${currentIndex ===
              3 && "hidden"}`}
          >
            <ArrowRight />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ImageSlider;
0;
