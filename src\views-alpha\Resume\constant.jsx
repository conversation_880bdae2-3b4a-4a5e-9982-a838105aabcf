import { ApiUrl } from "../../api-alpha/apiUrls";
import EditIcon from "../../assets-alpha/images/svg/edit.svg?react";
import DeleteIcon from "../../assets-alpha/images/svg/trash.svg?react";
import { EducationLevelsList } from "../../utilities-alpha/constant";
import { getFullDate } from "../../utilities-alpha/helper";
export const DropdownMenu = label => {
  return {
    items: [
      {
        key: "edit",
        label: (
          <div className="flex items-center justify-between gap-2">
            {label?.edit} <EditIcon />
          </div>
        )
      },
      {
        key: "delete",
        label: (
          <div className="flex items-center justify-between gap-2">
            {label?.delete} <DeleteIcon />
          </div>
        )
      }
    ]
  };
};

export const Profiles = {
  role: {
    name: "roles",
    type: "Profile",
    resumeLookupUrls: [ApiUrl.ResumeEdit.RolesLookup],
    addProfileUrl: ApiUrl.ResumeEdit.resumeAddRole,
    getProfileUrl: ApiUrl.ResumeEdit.GetResumeProfiles,
    LookupTypeId: 1,
    ObjectPK: "ProfileId",
    selectKeys: [
      {
        value: "ProfileId",
        label: "ProfileValue"
      }
    ],
    keys: {
      level: "ExperienceLevel"
    },
    showSuggestion: true
  },
  skill: {
    name: "skills",
    type: "Skill",
    resumeLookupUrls: [ApiUrl.ResumeEdit.SkillsLookup],
    addProfileUrl: ApiUrl.ResumeEdit.resumeAddSkill,
    getProfileUrl: ApiUrl.ResumeEdit.GetResumeSkills,
    LookupTypeId: 2,
    ObjectPK: "SkillId",
    selectKeys: [
      {
        value: "SkillId",
        label: "SkillValue"
      }
    ],
    keys: {
      level: "ExperienceLevel"
    },
    showSuggestion: true
  },
  certification: {
    name: "certification",
    type: "Certification",
    resumeLookupUrls: [ApiUrl.ResumeEdit.CertificationsLookup],
    addProfileUrl: ApiUrl.ResumeEdit.AddCertification,
    getProfileUrl: ApiUrl.ResumeEdit.GetResumeCertifications,
    LookupTypeId: 5,
    ObjectPK: "CertificationId",
    selectKeys: [
      {
        value: "CertificationId",
        label: "CertificationValue"
      }
    ],
    keys: {
      year: "CertificationDate"
    },
    formatDate: getFullDate,
    showSuggestion: true
  },
  education: {
    name: "education",
    resumeLookupUrls: [ApiUrl.ResumeEdit.EductionsLookup],
    addProfileUrl: ApiUrl.ResumeEdit.AddEducation,
    getProfileUrl: ApiUrl.ResumeEdit.GetResumeEducations,
    LookupTypeId: 8,
    ObjectPK: "EducationId",
    selectKeys: [
      {
        value: "EducationId",
        label: "EducationValue"
      }
    ],
    keys: {
      year: "EducationYear"
    },
    showEducationLevel: true
  },
  industry: {
    name: "industry",
    resumeLookupUrls: [ApiUrl.ResumeEdit.IndustriesLookup],
    addProfileUrl: ApiUrl.ResumeEdit.AddIndustry,
    getProfileUrl: ApiUrl.ResumeEdit.GetResumeIndustries,
    LookupTypeId: 4,
    ObjectPK: "IndustryId",
    selectKeys: [
      {
        value: "IndustryId",
        label: "IndustryValue"
      }
    ],
    keys: {
      level: "ExperienceLevel"
    }
  },
  experience: {
    name: "experience",
    resumeLookupUrls: [
      ApiUrl.ResumeEdit.IndustriesLookup,
      ApiUrl.ResumeEdit.RolesLookup
    ],
    addProfileUrl: ApiUrl.ResumeEdit.AddExperience,
    getProfileUrl: ApiUrl.ResumeEdit.GetResumeExperiences,
    LookupTypeId: 9,
    ObjectPK: "ResumeExperienceId",
    selectKeys: [
      {
        value: "IndustryId",
        label: "IndustryValue"
      },
      {
        value: "ProfileId",
        label: "ProfileValue"
      }
    ],
    keys: {
      isWorking: "isWorking"
      // Title: "CompanyWorked"
    },
    showExperience: true,
    editAll: true
  },
  achievement: {
    name: "achievements",
    resumeLookupUrls: [ApiUrl.ResumeEdit.AllResumeAchivementLookup],
    addProfileUrl: ApiUrl.ResumeEdit.AddResumeAchivenment,
    getProfileUrl: ApiUrl.ResumeEdit.GetResumeAchivenments,
    LookupTypeId: 10,
    ObjectPK: "ResumeOtherAchivenmentId",
    selectKeys: [
      {
        value: "AchivenmentId",
        label: "AchivenmentName"
      }
    ],
    keys: {
      year: "Year",
      Title: "Title"
    },
    showAchivenment: true,
    editAll: true
  },
  keyword: {
    name: "keywords",
    resumeLookupUrls: [ApiUrl.ResumeEdit.GetAllKeywords],
    addProfileUrl: ApiUrl.ResumeEdit.AddKeyword,
    getProfileUrl: ApiUrl.ResumeEdit.GetResumeKeywords,
    LookupTypeId: 3,
    ObjectPK: "KeywordId",
    selectKeys: [
      {
        value: "KeywordId",
        label: "KeywordValue"
      }
    ]
  },
  language: {
    name: "languages",
    resumeLookupUrls: [ApiUrl.ResumeEdit.LanguagesLookup],
    addProfileUrl: ApiUrl.ResumeEdit.AddLanguage,
    getProfileUrl: ApiUrl.ResumeEdit.GetResumeLanguages,
    LookupTypeId: 6,
    ObjectPK: "LanguageId",
    selectKeys: [
      {
        value: "LanguageId",
        label: "LanguageValue"
      }
    ],
    keys: {
      level: "ExperienceLevel"
    }
  },
  region: {
    name: "country",
    resumeLookupUrls: [ApiUrl.ResumeEdit.LocationsLookup],
    addProfileUrl: ApiUrl.ResumeEdit.addLocation,
    getProfileUrl: ApiUrl.ResumeEdit.GetResumeLocation,
    LookupTypeId: 7,
    ObjectPK: "ResumeCountryId",
    selectKeys: [
      {
        value: "CountryId",
        label: "CountryName"
      }
    ]
  }
};

const startYear = 2025;
const endYear = 1970;

export const YearOptions = Array.from(
  { length: startYear - endYear + 1 },
  (_, i) => {
    const year = startYear - i;
    return { label: year, value: year };
  }
);

export const findEducationDegree = education => {
  return EducationLevelsList.find(
    item => item.value === education.EducationTypeId
  );
};

export const extractMappedFields = (role, keys) => {
  if (!keys) return;
  const result = {};

  // Map alias keys to values from original keys
  for (const [aliasKey, originalKey] of Object.entries(keys)) {
    result[aliasKey] = role[originalKey];
    result[originalKey] = role[originalKey];
  }

  return result;
};
