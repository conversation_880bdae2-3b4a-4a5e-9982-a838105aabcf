import { Space, Switch, Typography } from "antd";
import { useEffect, useState } from "react";
const { Text } = Typography;
import dayjs from "dayjs";
import DatePicker from "../../../common-alpha/DatePicker/Datepicker";
import { useResumeAvailability } from "../resumeApi";
import { useQueryClient } from "@tanstack/react-query";
import { ApiUrl } from "../../../api-alpha/apiUrls";
import { useSelector } from "react-redux";

// Languages Section
const AvailabilitySection = ({ userData }) => {
  const [availablityType, setAvailabilityType] = useState(null);
  const [busyUntil, setBusyUntil] = useState(null);
  const resume = useSelector(state => state.systemLabel.labels?.resume);
  const queryClient = useQueryClient();
  const { mutateAsync } = useResumeAvailability();
  useEffect(() => {
    if (userData?.items) {
      setAvailabilityType(userData?.items?.AvailablityType);
      setBusyUntil(dayjs(userData?.items?.AvailabilityDate));
    }
  }, [userData?.items]);
  const handleAvailableChange = ({ name, value }) => {
    const isAvailable =
      name === "AvailablityType" ? value === 1 : availablityType === 1;
    mutateAsync({
      AvailabilityDate: busyUntil ? busyUntil.format("MM/DD/YYYY") : null,
      AvailablityType: availablityType,
      IsAvailable: isAvailable,
      [name]: value
    }).then(() => {
      queryClient.invalidateQueries({
        queryKey: [ApiUrl.ResumeEdit.GetMyResume]
      });
    });
  };
  return (
    <div
      className={
        "border-[#EAE5FC] px-8 py-4 max-md:px-0 border-0 border-b-1 rounded-none"
      }
    >
      <Space className="gap-2 !flex flex-col !items-stretch">
        <Space className="!flex items-center justify-between">
          <Text className="!text-[#343333]">{resume?.available}</Text>
          <Switch
            className="!h-5 switch-small !min-w-[33px]"
            checked={availablityType === 1}
            onChange={checked => {
              setAvailabilityType(checked ? 1 : 3);
              handleAvailableChange({
                name: "AvailablityType",
                value: checked ? 1 : 3
              });
            }}
          />
        </Space>

        <Space className="!flex items-center justify-between">
          <Text className="!text-[#343333]">{resume?.monthNotice}</Text>
          <Switch
            className="!h-5 switch-small !min-w-[33px]"
            checked={availablityType === 2}
            onChange={checked => {
              setAvailabilityType(checked ? 2 : 3);
              handleAvailableChange({
                name: "AvailablityType",
                value: checked ? 2 : 3
              });
            }}
          />
        </Space>

        <Space className="!flex items-center justify-between">
          <Text className="!text-[#343333]">{resume?.busyUntil}</Text>
          <DatePicker
            value={busyUntil}
            onChange={date => {
              setBusyUntil(date);
              handleAvailableChange({
                name: "AvailabilityDate",
                value: date ? date.format("MM/DD/YYYY") : null
              });
            }}
            format="YY-MM-DD"
            inputClassName="!h-6 max-md:!h-6 !rounded-[6px] !text-[#8E81F5] !p-[6px_4px] !w-[93px] [&_input]:!text-[13px]"
            className="!w-[93px] [&_.ant-form-item-control-input]:!min-h-[auto]"
            suffixIconClassName="!w-4"
          />
        </Space>
      </Space>
    </div>
  );
};

export default AvailabilitySection;
