
import { Profiles } from "../constant";
import { ShowMoreSection } from "./LanguagesSection";
import { useSelector } from "react-redux";
// Keywords Section
const KeywordsSection = ({ userData, openDrawer }) => {
  const resume = useSelector(state => state.systemLabel.labels?.resume);
  return (
    <ShowMoreSection
      title={resume?.keywords}
      items={userData?.items?.Keywords || []}
      openDrawer={openDrawer}
      addEditAction={Profiles.keyword}
      viewMore
      itemKeyId="KeywordId"
      itemValueKey="KeywordValue"
      emptyMessage={resume?.keywordsEmptyMessage}
      // /"Optimize your profile with relevant keywords that describe your expertise."
      addButtonAction={Profiles.keyword}
      renderItem={keyword => (
        <div
          key={keyword.KeywordId}
          className="bg-[#F3F1FD] border border-[#EAE5FC] rounded-lg px-3.5 py-2 text-sm text-[#343333]"
        >
          {keyword.KeywordValue}
        </div>
      )}
      leftSection
    />
  );
};

export default KeywordsSection;
