import { useMemo } from "react";
import {
  useClientMutation,
  useClientQuery
} from "../../api-alpha/api-service";
import { ApiUrl } from "../../api-alpha/apiUrls";
//Search Api
export const useRolesLookupApi = searchKey => {
  const query = useClientQuery({
    enabled: !!searchKey,
    url: ApiUrl.ResumeEdit.RolesLookup({ searchKey })
  });
  const queryApi = useMemo(() => {
    if (!query.data) return query;
    return {
      ...query,
      data:
        query.data?.items?.map(item => ({
          ...item,
          label: item.ProfileValue,
          value: item.ProfileId
        })) || []
    };
  }, [query]);

  return queryApi;
};

export const useLocationLookupApi = searchKey => {
  const query = useClientQuery({
    enabled: !!searchKey,
    url: ApiUrl.Opportunity.Locations({ searchKey })
  });
  const queryApi = useMemo(() => {
    if (!query.data) return query;
    return {
      ...query,
      data:
        query.data?.items?.map(item => ({
          ...item,
          label: item.CountryName,
          value: item.CountryId
        })) || []
    };
  }, [query]);

  return queryApi;
};

export const useLanguageLookupApi = searchKey => {
  const query = useClientQuery({
    enabled: !!searchKey,
    url: ApiUrl.Opportunity.SearchLanguage({ searchKey })
  });
  const queryApi = useMemo(() => {
    if (!query.data) return query;
    return {
      ...query,
      data:
        query.data?.items?.map(item => ({
          ...item,
          label: item.LanguageValue,
          value: item.LanguageId
        })) || []
    };
  }, [query]);

  return queryApi;
};

export const useSkillsLookupApi = searchKey => {
  const query = useClientQuery({
    enabled: !!searchKey,
    url: ApiUrl.ResumeEdit.SkillsLookup({ searchKey })
  });
  const queryApi = useMemo(() => {
    if (!query.data) return query;
    return {
      ...query,
      data:
        query.data?.items?.map(item => ({
          ...item,
          label: item.SkillValue,
          value: item.SkillId
        })) || []
    };
  }, [query]);

  return queryApi;
};

export const useKeyworkLookupApi = searchKey => {
  const query = useClientQuery({
    enabled: !!searchKey,
    url: ApiUrl.ResumeEdit.KeywordLookup({ searchKey })
  });
  const queryApi = useMemo(() => {
    if (!query.data) return query;
    return {
      ...query,
      data:
        query.data?.items?.map(item => ({
          ...item,
          label: item.KeywordValue,
          value: item.KeywordId
        })) || []
    };
  }, [query]);

  return queryApi;
};

export const useIndustriesLookupApi = searchKey => {
  const query = useClientQuery({
    enabled: !!searchKey,
    url: ApiUrl.ResumeEdit.IndustriesLookup({ searchKey })
  });
  const queryApi = useMemo(() => {
    if (!query.data) return query;
    return {
      ...query,
      data:
        query.data?.items?.map(item => ({
          ...item,
          label: item.IndustryValue,
          value: item.IndustryId
        })) || []
    };
  }, [query]);

  return queryApi;
};
export const useCertificationsLookupApi = searchKey => {
  const query = useClientQuery({
    enabled: !!searchKey,
    url: ApiUrl.ResumeEdit.CertificationsLookup({ searchKey })
  });
  const queryApi = useMemo(() => {
    if (!query.data) return query;
    return {
      ...query,
      data:
        query.data?.items?.map(item => ({
          ...item,
          label: item.CertificationValue,
          value: item.CertificationId
        })) || []
    };
  }, [query]);

  return queryApi;
};

export const useResumeSearchApi = () => {
  return useClientMutation({
    url: ApiUrl.ResumeSearches.SearchResume
  });
};
export const useSaveSearchApi = () => {
  const query = useClientQuery({
    url: ApiUrl.ResumeSearches.SavedSearchesBase
  });
  const queryApi = useMemo(() => {
    if (!query.data) return query;
    return {
      ...query,
      data:
        query.data?.items?.map(item => ({
          ...item,
          label: item.SearchName,
          value: item.SavedSearchId
        })) || []
    };
  }, [query]);

  return queryApi;
};
export const useSaveSearchDetailApi = () => {
  return useClientMutation({
    url: ApiUrl.ResumeSearches.SavedSearchDetail,
    //?savedSearchId=${savedSearchId}`,
    // enabled: !!savedSearchId,
    method: "GET"
  });
};

export const useSaveSearchRenameApi = () => {
  return useClientMutation({
    url: ApiUrl.ResumeSearches.UpdateSavedSearchItemName,
    method: "GET"
  });
};

export const useFilterWithAiApi = () => {
  return useClientMutation({
    url: ApiUrl.Dashboard.GetSearcherPhillipInfo
  });
};
export const useNewSaveSearchApi = () => {
  return useClientMutation({
    url: ApiUrl.Dashboard.SaveResumeSearch,
    method: "POST"
  });
};
export const useDeleteSavedSearch = () => {
  return useClientMutation({
    url: ApiUrl.ResumeSearches.DeleteSavedSearchItem,
    method: "DELETE"
  });
};
export const useDeleteAllSavedSearch = () => {
  return useClientMutation({
    url: ApiUrl.ResumeSearches.DeleteAll,
    method: "DELETE"
  });
};
//Shortlist api
export const useGetShortlists = (searchKey = "") => {
  const query = useClientQuery({
    url: ApiUrl.Shortlists.Get
  });

  // memoize the filtered items array
  const items = useMemo(() => {
    return (
      query.data?.items?.filter(item =>
        item.ShortlistName.toLowerCase().includes(searchKey?.toLowerCase())
      ) ?? []
    );
  }, [query.data?.items, searchKey]);

  // memoize the whole return to keep object identity
  return useMemo(
    () => ({
      ...query,
      data: items
    }),
    [query, items]
  );
};

export const useGetSortlistResumes = ShortlistId => {
  return useClientQuery({
    url: `${ApiUrl.Shortlistresumes.Get}/?shortlistid=${ShortlistId}`,
    enabled: !!ShortlistId
  });
};
export const useDeleteShortlistByIdApi = () => {
  return useClientMutation({
    url: ApiUrl.Shortlists.GetAllRequestByShortlistId,
    method: "DELETE"
  });
};
export const useDeleteResumesFromShortlist = () => {
  return useClientMutation({
    url: ApiUrl.Shortlists.RemoveResumesFromShortlist,
    method: "DELETE"
  });
};
export const useRenameShortlistApi = () => {
  return useClientMutation({
    url: ApiUrl.Shortlists.updateShortListName,
    method: "PUT"
  });
};
export const useCreateUpdateShortlistApi = () => {
  return useClientMutation({
    url: ApiUrl.Shortlists.Post
  });
};
