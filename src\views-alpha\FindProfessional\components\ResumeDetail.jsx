import { UserOutlined } from "@ant-design/icons";
import { Avatar, Button, Tag, Tooltip } from "antd";
import { round } from "lodash";
import moment from "moment";
import { useNavigate } from "react-router-dom";
import AvatarIcon from "../../../assets-alpha/images/svg/avatar.svg";
import EnvironmentOutlined from "../../../assets-alpha/images/svg/location.svg?react";
import ProfileIcon from "../../../assets-alpha/images/svg/profile.svg?react";
import RankingIcon from "../../../assets-alpha/images/svg/ranking.svg?react";
import { RolesLevels } from "../../../utilities-alpha/constant";
import { formatDate } from "../../../utilities/helpers";
import CommonDrawer from "../../../common-alpha/Drawer/Drawer";
import { useSelector } from "react-redux";

const ResumeDrawer = ({ open, onClose, selectedResume }) => {
  const navigate = useNavigate();
  const findProfessional = useSelector(
    state => state.systemLabel.labels?.findProfessional
  );
  return (
    <CommonDrawer
      title={findProfessional?.resumeDetails}
      width={500}
      onClose={onClose}
      open={open}
    >
      <div className="flex flex-col">
        <div className="flex items-center gap-2 mb-4">
          <Avatar
            size={80}
            className="min-w-[80px]"
            icon={<UserOutlined />}
            src={AvatarIcon}
          />
          <div>
            <p className="font-medium text-[14px] text-lg  flex items-center text-[#878787] gap-[3px]">
              <ProfileIcon className="w-[16px]" />
              {selectedResume.UserFirstName} {selectedResume.UserLastName}
            </p>
            {selectedResume.Region && (
              <p className="text-sm text-[14px] !mt-0.5 text-[#7C7C7C]">
                <span className="text-[var(--purple)] flex items-center gap-1">
                  <EnvironmentOutlined className="min-w-4 w-4" />
                  {selectedResume.Region}
                </span>
              </p>
            )}
            <Tooltip title={<>{findProfessional?.matchScoreTooltip}</>}>
              <p className="text-sm text-[14px] text-[var(--green-2)] !mt-1 flex gap-[3px]">
                <RankingIcon />
                {findProfessional?.matchScore} (
                {round(
                  (selectedResume.CorrelationScore /
                    (selectedResume.TotalScore || 1)) *
                    100,
                  2
                )}
                %)
              </p>
            </Tooltip>
          </div>
        </div>

        {/* <p className="text-sm text-gray-600">
          Full Stack Developer, Comprehensive web designer, Versatile tech
          developer, All in one software engineer
        </p> */}

        <div className="flex gap-3 mb-4">
          <Button
            type="primary"
            block
            onClick={() => navigate("/active-collaborations")}
            className="rounded-xl px-6 bg-indigo-500 hover:bg-indigo-600"
          >
            {findProfessional?.sendInvitation}
          </Button>
          <Button
            block
            onClick={() => navigate("/messages")}
            className="bg-[#F3F1FD] text-gray-800 rounded-xl px-6"
          >
            {findProfessional?.message}
          </Button>
        </div>

        <p className="text-[#878787] font-medium !mb-2">
          {findProfessional?.moreInfo}
        </p>

        {/* More Info Section */}
        <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mb-2 max-md:!mb-[6px]">
          <p className="text-gray-500 text-sm !mb-2">
            {findProfessional?.availability}
          </p>
          <p className="font-medium text-sm mt-1">
            {findProfessional?.date}:{" "}
            {formatDate(selectedResume.AvailabilityDate)}
          </p>
        </div>

        {/* Roles Section */}
        {selectedResume.Profiles?.length > 0 && (
          <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mb-2 max-md:!mb-[6px]">
            <p className="font-medium !mb-2">{findProfessional?.roles}</p>
            {selectedResume.Profiles.map(role => (
              <div
                key={role.ProfileId}
                className="flex justify-between items-center text-sm mb-2"
              >
                <span>
                  {renderScore(role.Score)}
                  {role.ProfileValue}
                </span>
                {renderTags(RolesLevels[role.ExperienceLevel])}
              </div>
            ))}
          </div>
        )}

        {/* Skills Section */}
        {selectedResume.Skills?.length > 0 && (
          <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mb-2 max-md:!mb-[6px]">
            <p className="font-medium !mb-2">{findProfessional?.skills}</p>
            {selectedResume.Skills.map(skill => (
              <div
                key={skill.SkillId}
                className="flex justify-between items-center text-sm mb-2"
              >
                <span>
                  {renderScore(skill.Score)}
                  {skill.SkillValue}
                </span>
                {renderTags(RolesLevels[skill.ExperienceLevel])}
              </div>
            ))}
          </div>
        )}
        {/* Certification Section */}
        {selectedResume.Certifications?.length > 0 && (
          <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mb-2 max-md:!mb-[6px]">
            <p className="font-medium !mb-2">
              {findProfessional?.certification}
            </p>
            {selectedResume.Certifications.map(item => (
              <div
                key={item.CertificationId}
                className="flex justify-between items-center text-sm mb-2"
              >
                <span>
                  {renderScore(item.Score)}
                  {item.CertificationValue}
                </span>
                {renderTags(moment(item.CertificationDate).format("YYYY"))}
              </div>
            ))}
          </div>
        )}

        {/* Education Section */}
        {selectedResume.Educations?.length > 0 && (
          <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mb-2 max-md:!mb-[6px]">
            <p className="font-medium !mb-2">{findProfessional?.education}</p>
            {selectedResume.Educations.map(item => (
              <div
                key={item.EducationId}
                className="flex justify-between items-center text-sm mb-2"
              >
                <span>
                  {renderScore(item.Score)}
                  {item.EducationValue}
                </span>
                {renderTags(item.EducationYear)}
              </div>
            ))}
          </div>
        )}

        {/* Industry Section */}
        {selectedResume.Industries?.length > 0 && (
          <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mb-2 max-md:!mb-[6px]">
            <p className="font-medium !mb-2">{findProfessional?.industry}</p>
            {selectedResume.Industries.map(item => (
              <div
                key={item.IndustryId}
                className="flex justify-between text-sm mb-2"
              >
                <span>
                  {renderScore(item.Score)}
                  {item.IndustryValue}
                </span>
                {renderTags(RolesLevels[item.ExperienceLevel])}
              </div>
            ))}
          </div>
        )}

        {/* Language Section */}
        {selectedResume.Languages?.length > 0 && (
          <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mb-2 max-md:!mb-[6px]">
            <p className="font-medium !mb-2">{findProfessional?.languages}</p>
            {selectedResume.Languages.map(lang => (
              <div
                key={lang.LanguageId}
                className="flex justify-between text-sm mb-2"
              >
                <span>{lang.LanguageValue}</span>
                {renderTags(RolesLevels[lang.ExperienceLevel])}
              </div>
            ))}
          </div>
        )}

        {/* Keyword Section */}
        {selectedResume.Keywords?.length > 0 && (
          <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mb-2 max-md:!mb-[6px]">
            <p className="font-medium !mb-2">{findProfessional?.keywords}</p>
            <div className="flex flex-wrap gap-y-2">
              {selectedResume.Keywords.map(lang => (
                <span
                  key={lang.keywordId}
                  // className="flex justify-between text-sm mb-2"
                >
                  {renderTags(lang.KeywordValue)}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Professional Experience Section */}
        {selectedResume.ResumeExperience?.length > 0 && (
          <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mb-2 max-md:!mb-[6px]">
            <p className="font-medium !mb-2">
              {findProfessional?.professionalExperience}
            </p>
            {selectedResume.ResumeExperience.map(item => (
              <div
                key={item.ResumeExperienceId}
                className="flex justify-between text-sm mb-2"
              >
                <span>
                  {/* {renderScore(item.)} */}
                  {item.CompanyWorked}
                </span>
                {renderTags(item.ProfileValue)}
              </div>
            ))}
          </div>
        )}

        {/* Achivenments Section */}
        {selectedResume.ResumeOtherAchivenments?.length > 0 && (
          <div className="w-full bg-[#F6F3FC] rounded-xl p-4 mb-2 max-md:!mb-[6px]">
            <p className="font-medium !mb-2">
              {findProfessional?.achivenments}
            </p>
            {selectedResume.ResumeOtherAchivenments.map(item => (
              <div
                key={item.AchivenmentId}
                className="flex justify-between text-sm mb-2"
              >
                {/* {renderScore(item.Title)} */}
                <span>{item.AchivenmentName}</span>
                {renderTags(item.Title)}
              </div>
            ))}
          </div>
        )}
      </div>
    </CommonDrawer>
  );
};

const renderTags = name => {
  return (
    name && (
      <Tag className="!whitespace-nowrap !bg-white rounded-lg border-none text-xs !px-3 !py-0.5 !text-[#8E81F5]">
        {name}
      </Tag>
    )
  );
};

const renderScore = score => {
  return <span className="text-green-500 font-medium mr-2">{score}</span>;
};

export default ResumeDrawer;
