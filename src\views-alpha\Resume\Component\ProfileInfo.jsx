import { LoadingOutlined } from "@ant-design/icons";
import { Avatar, Button, Grid, Typography, Upload } from "antd";
import { first } from "lodash";
import AvatarIcon from "../../../assets-alpha/images/svg/avatar.svg";
import PhoneIcon from "../../../assets-alpha/images/svg/call.svg?react";
import CameraIcon from "../../../assets-alpha/images/svg/camera.svg?react";
import EditOutlined from "../../../assets-alpha/images/svg/edit.svg?react";
import EmailIcon from "../../../assets-alpha/images/svg/sms.svg?react";
import { useProfileUpdate } from "../useResume";
const { Title } = Typography;
// Profile Info Section
const ProfileInfo = ({ setIsEditInfoModalOpen }) => {
  const { md } = Grid.useBreakpoint();
  const { loadingProfile, userData, onUpload } = useProfileUpdate();

  return (
    <div
      className="flex flex-col pb-4 px-8 border-[#EAE5FC] md:border-b-1
      max-md:px-0 max-md:mt-12
      "
    >
      <div className="max-w-fit relative mb-4 max-md:mb-2">
        <Avatar
          className="!border-3 !border-white"
          src={
            loadingProfile ? (
              <LoadingOutlined />
            ) : (
              userData?.items?.ProfilePicture || AvatarIcon
            )
          }
          size={md ? 130 : 80}
        />
        <Upload
          beforeUpload={onUpload}
          accept="image/*"
          type="button"
          disabled={loadingProfile}
          className="[&_.ant-upload-list]:hidden
          w-8 h-8 bg-[#F3F1FD] rounded-full absolute bottom-0 right-0 flex items-center justify-center cursor-pointer
          "
        >
          <CameraIcon />
        </Upload>
      </div>

      <div className="flex items-center mb-4">
        <Title level={3} className="font-semibold mr-2 !mb-0 max-md:!text-4">
          {userData?.items?.UserFirstname}&nbsp;
          {userData?.items?.UserLastname}
        </Title>
        <Button
          type="text"
          className="!p-0 !bg-transparent"
          onClick={() => setIsEditInfoModalOpen(true)}
        >
          <EditOutlined className="text-[var(--purple)]" />
        </Button>
      </div>

      <div className="w-full space-y-2">
        <div className="flex items-center text-sm">
          <EmailIcon className="text-[#343333] w-5 h-5 mr-3 max-md:mr-2" />

          <span className="text-[#878787]">{userData?.items?.UserEmail}</span>
        </div>

        <div className="flex items-center text-sm">
          <PhoneIcon className="text-[#343333] w-5 h-5 mr-3 max-md:mr-2" />
          <span className="text-[#878787]">
            {first(userData?.items?.PhoneList)?.UserPhonenumberValue}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ProfileInfo;
