import { Button, Grid } from "antd";

import CustomInput from "../../../common-alpha/CustomInput/CustomInput";
import Modal from "../../../components-alpha/Modal/Modal";
import { useProfileUpdate } from "../useResume";
import { CloseOutlined } from "@ant-design/icons";
import { useSelector } from "react-redux";
// Edit Info Modal
const EditInfoModal = ({
  isEditInfoModalOpen,
  setIsEditInfoModalOpen
  // userData
}) => {
  const resume = useSelector(state => state.systemLabel.labels?.resume);
  const { md } = Grid.useBreakpoint();
  const {
    onSave,
    loadingProfile,
    userData,
    phone,
    setName,
    setPhone,
    name
  } = useProfileUpdate();

  return (
    <Modal
      title={resume?.basicInfo}
      open={isEditInfoModalOpen}
      onCloseModal={() => setIsEditInfoModalOpen(false)}
      closable={md ? false : true}
      closeIcon={
        <CloseOutlined className="bg-[#F3F1FD] !text-[#343333] w-[28px] h-[28px] p-[9px] rounded-full" />
      }
      className="[&_.ant-modal-header]:!p-[24px] [&_.ant-modal-header]:!pb-[20px] [&_.ant-modal-header]:!m-0 [&_.ant-modal-header]:!border-b-1 [&_.ant-modal-header]:!border-b-[#EAE5FC]
      [&_.ant-modal-footer]:!p-[24px] [&_.ant-modal-footer]:!pt-[0] [&_.ant-modal-footer]:max-md:!px-[16px] [&_.ant-modal-footer]:max-md:!pb-[12px] max-md:[&_.ant-modal-footer]:!flex
      max-md:[&_.ant-modal-body]:min-h-[calc(100vh-137px)]
      max-md:!w-full [&_.ant-modal-close]:!top-4.5
      "
      width={450}
      maskClosable={true}
      footer={[
        md && (
          <Button
            key="cancel"
            className="rounded-full border px-4 max-md:!flex-1"
            onClick={() => setIsEditInfoModalOpen(false)}
          >
            {resume?.cancel}
          </Button>
        ),
        <Button
          key="save"
          type="primary"
          className="rounded-full px-4 max-md:!flex-1"
          onClick={() =>
            onSave({ onSuccess: () => setIsEditInfoModalOpen(false) })
          }
          disabled={loadingProfile}
          loading={loadingProfile}
        >
          {resume?.save}
        </Button>
      ]}
      centered
    >
      <div className="p-[24px] pb-[10px] space-y-4">
        <div>
          <CustomInput
            placeholder={resume?.userNamePlaceholder}
            label={resume?.userName}
            layout="vertical"
            value={name}
            onChange={e => setName(e.target.value)}
          />
        </div>

        <div>
          <CustomInput
            placeholder={resume?.userPhonePlaceholder}
            label={resume?.userPhone}
            layout="vertical"
            value={phone}
            onChange={e => setPhone(e.target.value)}
          />
        </div>
      </div>
    </Modal>
  );
};

export default EditInfoModal;
