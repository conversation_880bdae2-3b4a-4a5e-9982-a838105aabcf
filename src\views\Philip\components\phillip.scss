@use "../../../assets/sass/importFiles" as *;
@use 'sass:color';

.ipro-phillip {
  margin: 10px;
  .page-column {
    height: 80%;
  }

  // width: 100%;
  // max-width: 100%;
  // height: 560px;
  position: relative;
  overflow: auto;
  background: #f0edfe;
  color: #3e335e;
  border-radius: 5px;
  max-height: 80vh;
  overflow: hidden;
  // margin: 0% 5%;
  .close-button {
    display: none;
  }
  .onboard-logo {
    max-width: 50%;
  }
  .skip-btn {
    float: right;
    background: none;
    padding: 0;
    border: none;
  }
  .phillip-footer {
    display: flex;
    justify-content: flex-end;
    padding: 5px;
    background: $white;
  }
  .resume-upload-row {
    display: flex;
    justify-content: center;
    flex-flow: row-reverse;
    align-self: center;
    @media (max-width: 340px) {
      flex-direction: column-reverse;
    }
  }
  .setting-heading {
    margin-left: 5px;
  }
  .js_upload_btn {
    background: $green;
    padding: 6px 0px 6px 7px;
    margin-top: 10px;
    margin-bottom: 10px;
    border: none;
    border-radius: 3px;
    color: $white;
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
    font-size: 13px;
    min-width: 155px;
    position: relative;
    align-self: center;
    input[type="file"] {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      opacity: 0;
      width: 100%;
    }
  }
  .resume-name {
    align-self: center;
    border: 2px solid #f0edfe;
    vertical-align: middle;
    margin-right: 5px;
    padding: 5px 10px;
    border-radius: 5px;
    background: color.scale(#f0edfe, $lightness: 3%);
    &.blur {
      opacity: 0.5;
    }
  }
  .js_input {
    background: $blue1;
    border: 0;
    width: 70%;
    border-radius: 3px;
    height: 25px;
    font-size: 13px;
    padding: 5px 10px;
    margin: 5px 0;
  }
  .js_textarea_wrap {
    margin-top: 10px;
    .js_textarea {
      background: $blue1;
      border: 0;
      width: 90%;
      border-radius: 3px;
      height: 80px;
      font-size: 13px;
      padding: 8px 10px;
      resize: none;
      display: block;
    }
  }
  .datepicker-input {
    display: inline-block;
  }
  .custom-date-picker {
    color: $white;
    padding: 2px 20px;
    max-width: 100%;
    background-color: $purple;
    border: none;
    cursor: pointer;
    border-radius: 2px;
    font: normal 13px/16px $RubikRegular;
  }
  .js_item {
    padding: 3px 0;
    &:before {
      content: "";
      background: $red;
      width: 13px;
      height: 13px;
      border-radius: 3px;
      display: inline-block;
      margin-right: 10px;
      vertical-align: baseline;
    }
    &.active {
      &::before {
        background: $green;
      }
    }
  }

  .js_sel_item {
    cursor: pointer;
    position: relative;
    width: max-content;
    padding: 3px 0 3px 20px;
    width: 100%;
    &:before {
      content: "";
      background: $white;
      width: 13px;
      height: 13px;
      border-radius: 50%;
      left: 0;
      position: absolute;
      top: 5px;
    }
    &:after {
      background: $white;
      border: 1px solid $darkBlue;
      border-radius: 50%;
      position: absolute;
      left: 2px;
      top: 7px;
      width: 9px;
      height: 9px;
      content: "";
    }
    &.active {
      &:after {
        background: $darkBlue;
      }
    }
    .react-datepicker-wrapper {
      display: inline-block;
      background: 0;
      color: #fff;
      text-align: center;
      cursor: pointer;
      padding: 0 7px;
      @include breakpoint(screen640) {
        text-align: left;
        padding: 0 0 0 5px;
        display: inline-block;
      }
      &::before {
        display: none;
      }
    }
  }
  .js_query {
    margin-top: 10px;
  }

  .js_item_loading {
    padding: 3px 0;
    &:before {
      content: "";
      background: url("../../../assets/images/loader.gif") no-repeat 98%;
      width: 13px;
      height: 13px;
      border-radius: 3px;
      display: inline-block;
      margin-right: 10px;
      vertical-align: baseline;
      background-size: contain;
    }
    &.active {
      &::before {
        background: $green;
      }
    }
  }

  .js_social_wrap {
    @extend %clearfix;
    padding: 10px 0;
    .linkedin-img {
      float: left;
      padding-right: 10px;
      width: 160px;
      max-width: 50%;
      @include breakpoint(screen640) {
        max-width: 100%;
        width: auto;
        float: none;
      }
    }
    img.facebook-img {
      @extend .linkedin-img;
    }
    .social-btn {
      @extend .linkedin-img;
      .js_social_icon,
      .linkedin-img {
        max-width: 100%;
      }
    }
  }
  .carousel {
    z-index: 1;
    height: 74vh;
    background: $white;
    img {
      pointer-events: auto;
      cursor: pointer;
    }
    .slide {
      background: transparent;
      text-align: inherit;
      display: flex;
      flex-flow: column;
      .view-cnt {
        flex-flow: row !important;
      }
    }
    p {
      align-self: center;
      text-align: center;
      margin: 0 10%;
      @include breakpoint(screen640) {
        text-align: left;
        margin-top: 10px;
      }
    }
    .control-dots {
      z-index: 1;
      position: absolute;
      bottom: 0;
      margin: 0;
      padding: 10px;
      text-align: center;
      width: 100%;
      background: #fff;
      .dot {
        width: 10px;
        height: 10px;
        vertical-align: middle;
        background: #9783cf;
        &.selected {
          width: 15px;
          height: 15px;
        }
      }
    }
  }
  .posRelative {
    position: relative;
    margin: auto;
    width: 200px;
  }
  .js_slide {
    min-height: 360px;
    position: relative;
    color: #3e335e;
    background: #ffffff;
    border-radius: 5px;
    width: 100%;
  }
  .slider-wrapper {
    overflow: visible;
    z-index: 1;
  }
  .js_date_btn {
    background: $purple;
    padding: 4px 7px;
    border: none;
    border-radius: 3px;
    color: $white;
    display: inline-block;
    vertical-align: top;
    font-size: 12px;
    position: relative;
    top: -3px;
  }
  .js_sel_last {
    display: inline-block;
    vertical-align: top;
    margin: 0 10px 10px 0;
  }
  .js_slide_1 {
    // padding-right: 210px;
    &:after {
      background: url("../../../assets/images/onboarding/01.png") no-repeat 98%
        center;
      background-size: contain;
      content: "";
      min-height: 100%;
      width: 210px;
      height: 100%;
      display: inline-block;
      position: absolute;
      right: 0;
      top: 0;
    }
    @include breakpoint(screen767) {
      padding-right: 0;
      &:after {
        display: none;
      }
    }
  }
  .js_slide_2 {
    padding: 20px;
  }
  .js_slide_2_not {
    padding-right: 210px;
    &:after {
      background: url("../../../assets/images/onboarding/02.png") no-repeat 98%
        center;
      background-size: contain;
      content: "";
      min-height: 100%;
      width: 210px;
      height: 390px;
      display: inline-block;
      position: absolute;
      right: 0;
      top: 0;
    }
    @include breakpoint(screen767) {
      padding-right: 0;
      &:after {
        display: none;
      }
    }
  }

  .js_slide_3_not {
    padding-right: 0px !important;
    &:after {
      background: url("../../../assets/images/onboarding/03.png") no-repeat 98%
        center;
      background-size: contain;
      content: "";
      min-height: 100%;
      width: 210px;
      height: 390px;
      display: inline-block;
      position: absolute;
      right: 0;
      top: 0;
    }
    @include breakpoint(screen767) {
      padding-right: 0;
      &:after {
        display: none;
      }
    }
  }

  .js_slide_4_not {
    padding-right: 210px;
    &:after {
      background: url("../../../assets/images/onboarding/04.png") no-repeat 98%
        center;
      background-size: contain;
      content: "";
      min-height: 100%;
      width: 210px;
      height: 380px;
      display: inline-block;
      position: absolute;
      right: 0;
      top: 0;
    }
    @include breakpoint(screen767) {
      padding-right: 0;
      &:after {
        display: none;
      }
    }
  }

  .js_slide_5_not {
    padding-right: 210px;
    &:after {
      background: url("../../../assets/images/onboarding/25.png") no-repeat 98%
        center;
      background-size: contain;
      content: "";
      min-height: 100%;
      width: 210px;
      height: 390px;
      display: inline-block;
      position: absolute;
      right: 0;
      top: 0;
    }
    @include breakpoint(screen767) {
      padding-right: 0;
      &:after {
        display: none;
      }
    }
  }

  &.onboard-1 {
    position: relative;
    &:after {
      display: none;
      background: url("../../../assets/images/onboarding/01.png") no-repeat 50px
        top;
      background-size: cover;
      content: "";
      width: 210px;
      height: 182px;
      position: absolute;
      right: 0;
      bottom: 0;
      @include breakpoint(screen767) {
        display: inline-block;
      }
    }
  }

  &.onboard-2 {
    position: relative;
    &:after {
      display: none;
      background: url("../../../assets/images/onboarding/02.png") no-repeat 50px
        top;
      background-size: cover;
      content: "";
      width: 210px;
      height: 170px;
      position: absolute;
      right: 0;
      bottom: 0;
      @include breakpoint(screen767) {
        display: inline-block;
      }
    }
  }

  &.onboard-3 {
    position: relative;
    &:after {
      display: none;
      background: url("../../../assets/images/onboarding/03.png") no-repeat 47px
        top;
      background-size: cover;
      content: "";
      width: 210px;
      height: 188px;
      position: absolute;
      right: 0;
      bottom: 0;
      @include breakpoint(screen767) {
        display: inline-block;
      }
    }
  }

  &.onboard-4 {
    position: relative;
    &:after {
      display: none;
      background: url("../../../assets/images/onboarding/04.png") no-repeat 50px
        top;
      background-size: cover;
      content: "";
      width: 210px;
      height: 180px;
      position: absolute;
      right: 0;
      bottom: 0;
      @include breakpoint(screen767) {
        display: inline-block;
      }
    }
  }

  &.onboard-5 {
    position: relative;
    &:after {
      display: none;
      background: url("../../../assets/images/onboarding/05.png") no-repeat 98%
        top;
      background-size: cover;
      content: "";
      width: 120px;
      height: 140px;
      position: absolute;
      right: 0;
      bottom: 0;
      @include breakpoint(screen767) {
        display: inline-block;
      }
    }
  }
  .continue-button {
    color: $white;
    padding: 5px 10px;
    max-width: 100%;
    background-color: $green;
    border: none;
    cursor: pointer;
    border-radius: 2px;
    font: normal 13px/16px $RubikRegular;
    @include breakpoint(screen767) {
      display: none;
    }
  }
  .finish-button {
    @extend .continue-button;
    float: right;
    display: block;

    @include breakpoint(screen767) {
      float: left;
    }
  }
  .loadingMaskWrapper {
    background: rgba($darkBlue, 0.7);
    .loadingImg {
      width: auto;
    }
  }
  .linkedin-login {
    float: left;
    a {
      height: 35px $imp;
      span {
        height: 35px $imp;
      }
    }
  }
  .loginBtn {
    box-sizing: border-box;
    position: relative;
    /* width: 13em;  - apply for fixed size */
    padding: 0 15px 0 46px;
    border: none;
    text-align: left;
    line-height: 34px;
    white-space: nowrap;
    border-radius: 0.2em;
    font-size: 11px;
    color: $white;
    float: left;
    font-weight: 500;
    min-width: 170px;
  }
  .loginBtn:before {
    content: "";
    box-sizing: border-box;
    position: absolute;
    top: 0;
    left: 0;
    width: 34px;
    height: 100%;
  }
  .loginBtn:focus {
    outline: none;
  }
  .loginBtn:active {
    box-shadow: inset 0 0 0 32px rgba(0, 0, 0, 0.1);
  }

  /* Facebook */
  .social-btn {
    background: none;
    border: 0;
    padding: 0;
    font: inherit;
    cursor: pointer;
  }

  .loginBtn--facebook {
    background-color: #4c69ba;
    background-image: linear-gradient(#4c69ba, #4c69ba);
    text-shadow: 0 -1px 0 #354c8c;
  }
  .loginBtn--facebook:before {
    border-right: #364e92 1px solid;
    background: url("../../../assets/images/onboarding/icon_facebook.png") 6px
      6px no-repeat;
  }
  .loginBtn--facebook:hover,
  .loginBtn--facebook:focus {
    background-color: #4c69ba;
    background-image: linear-gradient(#4c69ba, #4c69ba);
  }

  /* LinkedIn */
  .loginBtn--linkedin {
    background-color: #0077b5;
    background-image: linear-gradient(#0077b5, #0077b5);
    /*font-family: "Helvetica neue", Helvetica Neue, Helvetica, Arial, sans-serif;*/
    text-shadow: 0 -1px 0 #354c8c;
  }
  .loginBtn--linkedin:before {
    border-right: #364e92 1px solid;
    background: url("../../../assets/images/onboarding/icon_linkedin.png") 6px
      6px no-repeat;
  }
  .loginBtn:active {
    box-shadow: unset;
  }
  .loginBtn--linkedin:hover,
  .loginBtn--linkedin:focus {
    background-color: #0077b5;
    background-image: linear-gradient(#0077b5, #0077b5);
  }
}
.dropdown-phillip-searcher {
  background: #fff !important;
  border-radius: 8px;
  .Select-control {
    background: transparent !important;
    height: 36px;
    .Select-value {
      background: transparent !important;
    }
    .Select-placeholder {
      line-height: 36px;
    }
    .Select-value {
      line-height: 36px;
    }
  }
}
.ipro-phillip {
  min-height: 90vh;
  .carousel {
    height: 83vh;
  }

  .slide-heading {
    background: #f0edfe;
    text-align: center;
    padding: 10px;
    border-radius: 5px;
    margin: 2px;
  }
}
.phillips-search-steps- {
  overflow: auto;
  width: 100%;
  .ant-steps-horizontal:not(.ant-steps-label-vertical)
    .ant-steps-item-description {
    max-width: 150px;
    white-space: normal;
  }
  .ant-steps-item-finish .ant-steps-icon {
    top: -3px;
  }
  .rc-slider {
    margin: 0;
    padding: 0;
  }
  .rc-slider-rail,
  .rc-slider-track {
    background: #fff;
  }
}
