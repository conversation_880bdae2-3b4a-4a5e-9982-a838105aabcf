import { LoadingOutlined, MoreOutlined, PlusOutlined } from "@ant-design/icons";
import { useQueryClient } from "@tanstack/react-query";
import { But<PERSON>, Dropdown } from "antd";
import clsx from "clsx";
import moment from "moment";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { ApiUrl } from "../../../api-alpha/apiUrls";
import RightArrow from "../../../assets-alpha/images/svg/arrow-right.svg?react";
import ShortlistIcon from "../../../assets-alpha/images/view/shortlist.svg?react";
import SelectShortlistIcon from "../../../assets-alpha/images/view/shortlist-select.svg?react";
import EmptyView from "../../../common-alpha/EmptyView/EmptyView";
import NameInputModal from "../../../components-alpha/Modal/NameInputModal";
import { formatDate } from "../../../utilities/helpers";
import { DropdownMenu } from "../constant";
import CloseIcon from "../../../assets-alpha/images/svg/close.svg?react";

import {
  useCreateUpdateShortlistApi,
  useDeleteResumesFromShortlist,
  useDeleteShortlistByIdApi,
  useGetShortlists,
  useGetSortlistResumes,
  useRenameShortlistApi
} from "../findProfessionalApi";
import JobCard from "./JobCard";
import ResumeDrawer from "./ResumeDetail";
import { notificationAction } from "../../../actions-alpha/notification";
import { useDispatch, useSelector } from "react-redux";
import { Grid } from "antd";
import { twMerge } from "tailwind-merge";
import Mask from "../../../common-alpha/Mask/Mask";
import DeleteModal from "../../../common-alpha/DeleteModal/DeleteModal";
const ShortlistsResults = ({ searchKey }) => {
  const [openResume, setOpenResume] = useState(false);
  const [isModalOpen, setModalOpen] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const findProfessional = useSelector(
    state => state.systemLabel.labels?.findProfessional
  );
  const { data: shortlists, isLoading: loadingShortlist } = useGetShortlists(
    searchKey
  );
  const [deleteDialog, setDeleteDialog] = useState();
  const screen = Grid.useBreakpoint();
  const queryClient = useQueryClient();
  const [selectedShortlist, setSelectedShortlist] = useState(null);
  const { data: resumes, isLoading: loadingResumes } = useGetSortlistResumes(
    selectedShortlist?.ShortlistId
  );

  const {
    mutate: deleteResumesShortlistApi,
    isPending: loadingDelete
  } = useDeleteResumesFromShortlist();
  const {
    mutate: deleteShortlistById,
    isPending: loadingDeleteShortlist
  } = useDeleteShortlistByIdApi();
  const {
    mutate: updateName,
    isPending: loadingRenaming,
    error: errorRenaming
  } = useRenameShortlistApi();
  const {
    mutate: createShortlist,
    isPending: loadingCreatingShortlist,
    error: errorCreatingShortlist
  } = useCreateUpdateShortlistApi();

  useEffect(() => {
    setSelectedShortlist(selected =>
      screen?.md ? selected ?? shortlists?.[0] : null
    );
  }, [shortlists, screen?.md]);

  const onRemoveResumeShortlist = resumeItem => {
    deleteResumesShortlistApi(
      {
        shortlistId: selectedShortlist?.ShortlistId,
        resumeIds: [resumeItem.ResumeId]
      },
      {
        onSuccess: data => {
          queryClient.invalidateQueries({
            queryKey: [
              `${ApiUrl.Shortlistresumes.Get}/?shortlistid=${selectedShortlist?.ShortlistId}`
            ]
          });
          const info = {
            message: data?.message,
            status: data?.success ? "success" : "info"
          };
          dispatch(notificationAction(info));

          setDeleteDialog(false);
        }
      }
    );
  };
  const onRenameShortlist = name => {
    if (isModalOpen?.ShortlistId) {
      // renaming existing shortlist
      updateName(
        {
          suffixUrl: `?shortlistId=${isModalOpen?.ShortlistId}&shortListName=${name}`
        },
        {
          onSuccess: data => {
            queryClient.invalidateQueries({
              queryKey: [ApiUrl.Shortlists.Get]
            });
            setModalOpen(false);
            const info = {
              message: data?.message,
              status: data?.success ? "success" : "info"
            };
            dispatch(notificationAction(info));
          }
        }
      );
    } else {
      // create new shortlist

      createShortlist(
        {
          ShortlistName: name
        },
        {
          onSuccess: data => {
            queryClient.invalidateQueries({
              queryKey: [ApiUrl.Shortlists.Get]
            });
            setModalOpen(false);
            const info = {
              message: data?.message,
              status: data?.success ? "success" : "info"
            };
            dispatch(notificationAction(info));
          }
        }
      );
    }
  };

  const onDeleteShortlist = item => {
    deleteShortlistById(
      {
        suffixUrl: `?shortlistId=${item?.ShortlistId}`
      },
      {
        onSuccess: data => {
          queryClient.invalidateQueries({ queryKey: [ApiUrl.Shortlists.Get] });
          const info = {
            message: data?.message,
            status: data?.success ? "success" : "info"
          };
          dispatch(notificationAction(info));
          setDeleteDialog(false);
        }
      }
    );
  };

  return (
    <>
      {loadingShortlist ? (
        <LoadingOutlined className="text-3xl !block m-auto mt-[40px]" />
      ) : shortlists?.length > 0 ? (
        <div
          className={clsx("flex pt-[24px] max-md:pt-4 items-start", {
            "flex-col": !screen.md
          })}
        >
          {/* Left Sidebar */}
          <div
            className={twMerge(
              clsx("space-y-3", {
                "border-r border-[#EAE5FC] pr-4 w-1/3 mr-4 h-[calc(100vh-250px)] overflow-auto":
                  screen.md,
                "w-full mb-6 pb-[20px]": !screen.md
              })
            )}
          >
            <div
              className="max-md:z-20 bg-white
              max-md:fixed max-md:inset-0 max-md:top-auto max-md:px-4 max-md:py-3 max-md:!mb-0
              "
            >
              <Button
                icon={<PlusOutlined />}
                onClick={() =>
                  setModalOpen({ title: findProfessional?.newShortlist })
                }
                className="w-full md:!border-dashed !border-1 !border-[#d9d6fe] !text-[var(--purple)] max-md:!text-white !bg-[#f5f3ff] max-md:!bg-[var(--purple)]"
              >
                {findProfessional?.createNewShortlist}
              </Button>
            </div>

            <div className="space-y-1.5 overflow-y-auto">
              {shortlists?.map(item => (
                <div
                  key={item.ShortlistId}
                  className={clsx(
                    "cursor-pointer border-1 border-[#EAE5FC] hover:bg-[#F3F1FD] p-3 rounded-xl flex justify-between items-center",
                    {
                      "bg-[#F3F1FD]":
                        selectedShortlist?.ShortlistId === item.ShortlistId
                    }
                  )}
                  onClick={() => setSelectedShortlist(item)}
                >
                  <div className="truncate">
                    <p className="text-[14px] font-medium truncate">
                      {item.ShortlistName}
                    </p>
                    <p className="text-[12px] text-gray-500 truncate">
                      {findProfessional?.date} {formatDate(item.UpdatedOn)}{" "}
                      {findProfessional?.time}{" "}
                      {moment(item.UpdatedOn).format("hh:mm A")}
                    </p>
                  </div>
                  <Dropdown
                    overlayClassName="[&_.ant-dropdown-menu]:!py-[6px] [&_.ant-dropdown-menu-item]:!py-[6px]"
                    menu={{
                      ...DropdownMenu(findProfessional),
                      onClick: e => {
                        e.domEvent.stopPropagation();
                        if (e.key === "rename") {
                          setModalOpen({
                            ...item,
                            title: findProfessional?.renameShortlist
                          });
                        } else if (e.key === "delete") {
                          setDeleteDialog({
                            ...item,
                            description: findProfessional?.confirmDeleteShortlistDescription?.replace(
                              "@item",
                              item?.ShortlistName || ""
                            ),
                            onConfirm: () => onDeleteShortlist(item)
                          });
                        }
                      }
                    }}
                    onClick={e => e.stopPropagation()}
                    trigger={["click"]}
                  >
                    <Button
                      type="text"
                      icon={<MoreOutlined className="!text-[20px]" />}
                    />
                  </Dropdown>
                </div>
              ))}
            </div>
          </div>

          {/* Right Content */}
          {!screen.md && selectedShortlist && (
            <Mask onClick={() => setSelectedShortlist(null)} />
          )}
          <div
            className={twMerge(
              clsx("flex flex-col items-stretch justify-start text-gray-400", {
                "w-2/3 h-[calc(100vh-250px)] overflow-auto": screen.md,
                "w-full": !screen.md,
                "fixed inset-0 top-2 z-110 overflow-auto bg-white rounded-t-[16px] justify-start":
                  !screen.md && selectedShortlist
              })
            )}
          >
            {!screen.md && selectedShortlist && (
              <p className="flex items-center justify-between text-[16px] max-md:text-[18px] font-medium text-[#343333] p-4 border-b border-[#EAE5FC] !mb-4 w-full">
                {findProfessional?.shortlistDetails}
                <CloseIcon
                  className="bg-[#F3F1FD] rounded-full cursor-pointer"
                  onClick={() => setSelectedShortlist(null)}
                />
              </p>
            )}
            {selectedShortlist && (
              <>
                {loadingResumes ? (
                  <LoadingOutlined className="text-3xl !block m-auto mt-[40px]" />
                ) : resumes?.items?.length > 0 ? (
                  <div className="px-2 w-full max-md:pb-[65px]">
                    <div className="text-[#343333] font-semibold text-sm mb-4 md:hidden">
                      <span className="text-[#878787]">
                        {findProfessional?.title}:{" "}
                      </span>
                      {selectedShortlist?.ShortlistName}
                    </div>
                    <div
                      className="flex justify-end
                      max-md:z-20 bg-white
                      max-md:fixed max-md:inset-0 max-md:top-auto max-md:px-4 max-md:py-3 max-md:!mb-0
                    "
                    >
                      <Button
                        icon={<RightArrow className="!text-white" />}
                        iconPosition="end"
                        className="max-md:!w-full"
                        onClick={() =>
                          navigate("/active-collaborations", {
                            state: { newInvitation: true, selectedShortlist }
                          })
                        }
                        type="primary"
                      >
                        {findProfessional.continue}
                      </Button>
                    </div>
                    {resumes?.items?.map(resume => (
                      <JobCard
                        key={resume.ResumeId}
                        {...resume}
                        onResumeOpen={setOpenResume}
                        onRemove={() =>
                          setDeleteDialog({
                            ...resume,
                            description: findProfessional?.confirmDeleteShortlistedResumeDescription?.replace(
                              "@item",
                              `${resume?.UserFirstName ||
                                ""} ${resume?.UserLastName || ""}`
                            ),
                            onConfirm: () => onRemoveResumeShortlist(resume)
                          })
                        }
                      />
                    ))}
                  </div>
                ) : (
                  <EmptyView
                    className="flex-1 mt-0"
                    noBorder
                    title={findProfessional?.noProfilesInShortlist}
                    detail={findProfessional?.noProfilesInShortlistDetail}
                    icon={<SelectShortlistIcon />}
                  />
                )}
              </>
            )}
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-center h-[calc(100vh-280px)]">
          <EmptyView
            noBorder
            // title={"Effortless Shortlisting, Smarter Decisions"}
            className={"mt-0"}
            detail={
              <>
                {findProfessional?.emptyShortlistDetail}
                <br />
                <div
                  className="max-md:z-20 bg-white
                  border-t-1 border-t-[#EAE5FC]
                  max-md:fixed max-md:inset-0 max-md:top-auto max-md:px-4 max-md:py-3 max-md:!mb-0
                  "
                >
                  <Button
                    onClick={() =>
                      setModalOpen({ title: findProfessional?.newShortlist })
                    }
                    type="primary"
                    className="mt-4 max-md:mt-0 max-md:w-full"
                    icon={<PlusOutlined />}
                  >
                    {findProfessional?.createShortlist}
                  </Button>
                </div>
              </>
            }
            icon={<ShortlistIcon />}
          />
        </div>
      )}
      {isModalOpen && (
        <NameInputModal
          isOpen={isModalOpen}
          title={isModalOpen?.title}
          initialValue={isModalOpen?.ShortlistName}
          placeholder={findProfessional?.shortlistNamePlaceholder}
          confirmText={
            isModalOpen?.ShortlistId
              ? findProfessional?.save
              : findProfessional.create
          }
          onConfirm={onRenameShortlist}
          onClose={() => setModalOpen(false)}
          isLoading={loadingRenaming || loadingCreatingShortlist}
          error={errorRenaming?.message}
        />
      )}

      <ResumeDrawer
        open={openResume}
        onClose={() => setOpenResume(false)}
        selectedResume={openResume}
      />

      <DeleteModal
        open={deleteDialog}
        onCancel={() => setDeleteDialog(false)}
        onConfirm={deleteDialog?.onConfirm}
        isLoading={loadingDelete || loadingDeleteShortlist}
        title={findProfessional.delete}
        description={deleteDialog?.description}
        cancelText={findProfessional?.cancel}
        confirmText={findProfessional?.confirm}
      />
    </>
  );
};

export default ShortlistsResults;
