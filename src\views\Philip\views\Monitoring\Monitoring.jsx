import { Col, Collapse, Row, Select as Select2 } from "antd";
import { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useNavigate } from "react-router-dom";
import { notificationAction } from "../../../../actions/notification";
import { StorageService } from "../../../../api/storage";
import Button from "../../../../common/Button/Button";
import Column from "../../../../common/Column/Column";
import Select from "../../../../common/Select/Select";
import {
  getSavedSearchesApi,
  getSentOpportunitesApi,
  getShortlistApi,
} from "../../philipApi";
import Col2 from "./mobileView/col2";
import Col3 from "./mobileView/col3";
import "./monitoring.scss";
import {
  postMonitorOpportunityApi,
  postMonitorSavedSearchApi,
  postMonitorShortlist<PERSON>pi,
} from "./monitoringApi";
const { Panel } = Collapse;
const { Option } = Select2;

const Monitoring = (props) => {
  const [state, setState] = useState({
    allOpportunites: [],
    isLoading: false,
    value: "shortlist",
    savedSearches: [],
    shortlist: [],
    opportunitiesList: [],
    selectedOpportunity: [],
    onShortlists: false,
    onSavedSearches: false,
    onOpportunities: false,
    shortlistItemClicked: false,
    opportunityItemClicked: false,
    savedSearchItemClicked: false,
    aNewIpro: false,
    topScoreChange: false,
    newAvailibility: false,
    ShortlistEmail: false,
    OpportunityEmail: false,
    WatchPending: false,
    WatchDeclined: false,
    collapse: false,
    currentSearch: {
      sendEmailOnScoreChange: false,
      sendOpportunityToScoreChanger: false,
      sendEmailOnNewIpro: false,
      sendOpportunityToNewIpro: false,
    },
  });

  const navigate = useNavigate();

  useEffect(() => {
    getShortlist();
    getSavedSearches();
    getOpportunities();
    col1ItemClick("shortlists");
  }, []);

  const col1ItemClick = (type) => {
    setState((prevState) => ({
      ...prevState,
      shortlistItemClicked: false,
      opportunityItemClicked: false,
      savedSearchItemClicked: false,
      onShortlists: type === "shortlists",
      onOpportunities: type === "opportunities",
      onSavedSearches: type === "savedSearches",
      newAvailibility: false,
      topScoreChange: false,
      aNewIpro: false,
    }));
  };

  const onFormSelectChange = (type, selectedItem) => {
    setState((prevState) => ({
      ...prevState,
      currentSearch: {
        ...prevState.currentSearch,
        [type]: selectedItem,
      },
      selectedOpportunity:
        type === "" ? selectedItem : prevState.selectedOpportunity,
    }));
  };

  const col2ItemClick = (type, items) => {
    const { opportunitiesList, shortlist, savedSearches } = state;
    if (type === "shortlists") {
      const listItems = shortlist.map((item) => ({
        ...item,
        isSelected: item.ShortlistId === items?.value,
      }));
      setState((prevState) => ({
        ...prevState,
        shortlist: listItems,
        shortlistItemClicked: true,
        selectedShortlist: items?.value,
        opportunityItemClicked: false,
        savedSearchItemClicked: false,
      }));
    } else if (type === "savedSearches") {
      const listItems = savedSearches.map((item) => ({
        ...item,
        isSelected: item.SavedSearchId === items?.value,
      }));
      setState((prevState) => ({
        ...prevState,
        shortlistItemClicked: false,
        opportunityItemClicked: false,
        savedSearchItemClicked: true,
        savedSearches: listItems,
        currentSearch: {
          SavedSearchId: items?.value,
          sendEmailOnScoreChange: false,
          sendOpportunityToScoreChanger: false,
          sendEmailOnNewIpro: false,
          sendOpportunityToNewIpro: false,
        },
      }));
    } else if (type === "opportunities") {
      const listItems = opportunitiesList.map((item) => ({
        ...item,
        isSelected: item.RequestId === items?.value,
      }));
      setState((prevState) => ({
        ...prevState,
        shortlistItemClicked: false,
        opportunityItemClicked: true,
        savedSearchItemClicked: false,
        opportunitiesList: listItems,
        selectedOpportunity: items?.value,
      }));
    }
  };

  const col3ItemClick = (type) => {
    setState((prevState) => ({
      ...prevState,
      newAvailibility: type === "newAvailibility",
      topScoreChange: type === "topScoreChange",
      aNewIpro: type === "aNewIpro",
    }));
  };

  const getShortlist = () => {
    return getShortlistApi()
      .then((data) => {
        if (data.success) {
          const shortlist = data.items.map((item) => ({
            ...item,
            isSelected: false,
            label: item?.ShortlistName,
            value: item?.ShortlistId,
          }));
          setState((prevState) => ({
            ...prevState,
            shortlist: shortlist,
            fetchingShortlist: false,
          }));
        }
      })
      .catch(() => {
        setState((prevState) => ({
          ...prevState,
          fetchingShortlist: false,
        }));
      });
  };

  const getOpportunities = () => {
    let isFreelancer = false;
    getSentOpportunitesApi({ isFreelancer }).then((response) => {
      if (response.success) {
        const opportubitiesList = response.items.Sent.map((item) => ({
          ...item,
          value: item.RequestId,
          label: item.RequestName,
          isSelected: false,
        }));
        onFormSelectChange(opportubitiesList[0]);
        setState((prevState) => ({
          ...prevState,
          opportunitiesList: opportubitiesList,
        }));
      }
    });
  };

  const getSavedSearches = () => {
    return getSavedSearchesApi()
      .then((data) => {
        if (data.success) {
          const savedSearchesList = data.items.map((item) => ({
            ...item,
            isSelected: false,
            label: item?.SearchName,
            value: item?.SavedSearchId,
          }));
          setState((prevState) => ({
            ...prevState,
            savedSearches: savedSearchesList,
          }));
        }
      })
      .catch(() => {});
  };

  const Col3Item = (IconClass, label, description, type) => {
    let isActive = state[type];
    return (
      <div
        onClick={() => col3ItemClick(type)}
        className={`${isActive ? "onBorders " : "transparentBorder"} mo-nav `}
      >
        <div>
          <i className={IconClass} />
          <label className="count-label">{label}</label>
        </div>
        <p>{description}</p>
      </div>
    );
  };

  const onChange2 = (key) => {
    col3ItemClick(key);
  };

  const Col3ItemMobile = (IconClass, label, description, type, key) => {
    let isActive = state[type];
    return (
      <Column collapse={key !== state.collapse} className="col-1 ">
        <Column.Collapsed
          text={label}
          onClick={() => {
            onChange2(type);
            setState((prevState) => ({
              ...prevState,
              collapse: prevState.collapse === key ? "" : key,
            }));
          }}
          tooltipPlace="left"
          testId="expandbtn1"
        />
        <Column.Head>
          <div className="heading">{label}</div>
          <Button
            className="collapseBtn"
            testId="Collapasebtn1"
            onClick={() => {
              onChange2(type);
              setState((prevState) => ({
                ...prevState,
                collapse: prevState.collapse === key ? "" : key,
              }));
            }}
          />
        </Column.Head>
        <Column.Body className="reports-body">
          <div style={{ padding: "10px" }}>
            <p className="paragraph">{description}</p>
            <div className="mo-col-4">
              {state.newAvailibility && state.onShortlists ? (
                <div>
                  <div className="check-items">
                    <input
                      name="ShortlistEmail"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={state.ShortlistEmail}
                    ></input>
                    <label>
                      {
                        props.labels
                          .MONITORING_SHORTLIST_NEWAVAILIBILITY_CHECKBOX_DETAILS
                      }
                    </label>
                  </div>
                  <div className="check-items">
                    <input
                      name="OpportunityEmail"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={state.OpportunityEmail}
                    ></input>
                    <label>
                      {
                        props.labels
                          .MONITORING_SHORTLIST_NEWAVAILIBILITY_CHECKBOX2_DETAILS
                      }
                    </label>
                  </div>
                  <Select
                    name="opportunities"
                    className={`select-input select-Workplace`}
                    placeholder={"select opportunity"}
                    value={
                      (state.currentSearch &&
                        state.currentSearch.selectedOpportunity) ||
                      ""
                    }
                    onChange={(selectedOption) =>
                      onFormSelectChange("", selectedOption)
                    }
                    clearable={false}
                    searchable={false}
                    options={state.opportunitiesList}
                  />
                  <div className="text-center-">
                    <button
                      onClick={() =>
                        handleMonitorClick("ShortlistAvailibility")
                      }
                      className="action-btn"
                    >
                      {props.labels.MONITOR_FOR_ME_LABEL}
                    </button>
                  </div>
                </div>
              ) : (
                ""
              )}

              {state.topScoreChange && state.onSavedSearches ? (
                <div>
                  <label>
                    {props.labels.MONITOR_TOP_TEN_SCORE_SAVEDSEARCH}
                  </label>
                  <ul>
                    <li>263</li>
                    <li>243</li>
                    <li>231</li>
                    <li>212</li>
                    <li>199</li>
                    <li>199</li>
                    <li>193</li>
                    <li>189</li>
                    <li>172</li>
                    <li>169</li>
                  </ul>
                  <div className="check-items">
                    <input
                      onChange={(value) =>
                        onCheckBoxChange("sendEmailOnScoreChange", value)
                      }
                      type={"checkbox"}
                      value={
                        state.currentSearch &&
                        state.currentSearch.sendEmailOnScoreChange
                      }
                    ></input>
                    <label>
                      {
                        props.labels
                          .MONITOR_SAVEDSEARCH_TOPSCORECHANGE_CHECKBOX1_DETAILS
                      }
                    </label>
                  </div>
                  <div className="check-items">
                    <input
                      onChange={(value) =>
                        onCheckBoxChange("sendOpportunityToScoreChanger", value)
                      }
                      type={"checkbox"}
                      value={
                        state.currentSearch &&
                        state.currentSearch.sendOpportunityToScoreChanger
                      }
                    ></input>
                    <label>
                      {
                        props.labels
                          .MONITOR_SAVEDSEARCH_TOPSCORECHANGE_CHECKBOX2_DETAILS
                      }
                    </label>
                  </div>
                  <Select
                    name="WorkplaceId"
                    className={`select-input select-Workplace`}
                    placeholder={"Select Opportunity"}
                    value={
                      (state.currentSearch &&
                        state.currentSearch
                          .topScoreChangeSelectedOpportunity) ||
                      ""
                    }
                    onChange={(selectedOption) =>
                      onFormSelectChange(
                        "topScoreChangeSelectedOpportunity",
                        selectedOption
                      )
                    }
                    clearable={false}
                    searchable={false}
                    options={state.opportunitiesList}
                  />
                  <div className="text-center-">
                    <button
                      onClick={() =>
                        handleSavedSearchMonitoringClick("scoreChangeMonitor")
                      }
                      className="action-btn"
                    >
                      Monitor this for me
                    </button>
                  </div>
                </div>
              ) : (
                ""
              )}

              {state.aNewIpro && state.onSavedSearches ? (
                <div>
                  <label>
                    {props.labels.MONITOR_SAVEDSEARCH_ANEWIPRO_ASSOONAS_LABEL}
                  </label>
                  <ul>
                    <li>{props.labels.MONITOR_TOP_5_LABEL} </li>
                    <li>{props.labels.MONITOR_TOP_10_LABEL}</li>
                    <li>{props.labels.MONITOR_TOP_20_LABEL}</li>
                    <li>{props.labels.MONITOR_TOP_100_LABEL}</li>
                  </ul>
                  <div className="check-items">
                    <input
                      onChange={(value) =>
                        onCheckBoxChange("sendEmailOnNewIpro", value)
                      }
                      type={"checkbox"}
                      value={
                        state.currentSearch &&
                        state.currentSearch.sendEmailOnNewIpro
                      }
                    ></input>
                    <label>
                      {
                        props.labels
                          .MONITOR_SAVEDSEARCH_ANEWIPRO_CHECKBOX1_DETAILS
                      }
                    </label>
                  </div>
                  <div className="check-items">
                    <input
                      onChange={(value) =>
                        onCheckBoxChange("sendOpportunityToNewIpro", value)
                      }
                      type={"checkbox"}
                      value={
                        state.currentSearch &&
                        state.currentSearch.sendOpportunityToNewIpro
                      }
                    ></input>
                    <label>
                      {
                        props.labels
                          .MONITOR_SAVEDSEARCH_ANEWIPRO_CHECKBOX2_DETAILS
                      }
                    </label>
                  </div>
                  <Select
                    name="opportunities"
                    className={`select-input select-Workplace`}
                    placeholder={"select opportunity"}
                    value={
                      (state.currentSearch &&
                        state.currentSearch.newIproSelectedOpportunity) ||
                      ""
                    }
                    onChange={(selectedOption) =>
                      onFormSelectChange(
                        "newIproSelectedOpportunity",
                        selectedOption
                      )
                    }
                    clearable={false}
                    searchable={false}
                    options={state.opportunitiesList}
                  />
                  <div className="text-center-">
                    <button
                      onClick={() =>
                        handleSavedSearchMonitoringClick("newScorerMonitor")
                      }
                      className="action-btn"
                    >
                      {props.labels.MONITOR_FOR_ME_LABEL}
                    </button>
                  </div>
                </div>
              ) : (
                ""
              )}

              {state.newAvailibility && state.onOpportunities ? (
                <div>
                  <div className="check-items">
                    <input
                      name="WatchDeclined"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={state.WatchDeclined}
                    ></input>
                    <label>
                      Send the email if anyone who declined or haven't responded
                      becomes available
                    </label>
                  </div>
                  <div className="check-items">
                    <input
                      name="WatchPending"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={state.WatchPending}
                    ></input>
                    <label>
                      {
                        props.labels
                          .MONITOR_OPPORTUNITY_NEWAVAIL_CHECKBOX1_DETAILS
                      }
                    </label>
                  </div>
                  <Select
                    name="WorkplaceId"
                    className={`select-input select-Workplace`}
                    placeholder={"Select Opportunity"}
                    value={
                      (state.currentSearch &&
                        state.currentSearch.selectedOpportunity) ||
                      ""
                    }
                    onChange={(selectedOption) =>
                      onFormSelectChange("", selectedOption)
                    }
                    clearable={false}
                    searchable={false}
                    options={state.opportunitiesList}
                  />
                  <div className="text-center-">
                    <button
                      onClick={() =>
                        handleMonitorClick("OpportunityAvailibility")
                      }
                      className="action-btn"
                    >
                      {props.labels.MONITOR_FOR_ME_LABEL}
                    </button>
                  </div>
                </div>
              ) : (
                ""
              )}

              {state.topScoreChange && state.onOpportunities ? (
                <div>
                  <label>
                    {props.labels.MONITOR_OPPORTUNITY_TOPSCORE_TOP_TEN_DETAILS}
                  </label>
                  <ul>
                    <li>263</li>
                    <li>243</li>
                    <li>231</li>
                    <li>212</li>
                    <li>199</li>
                    <li>199</li>
                    <li>193</li>
                    <li>189</li>
                    <li>172</li>
                    <li>169</li>
                  </ul>
                  <div className="check-items">
                    <input
                      name="WatchDeclined"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={state.WatchDeclined}
                    ></input>
                    <label>
                      {
                        props.labels
                          .MONITOR_OPPORTUNITY_TOPSCORE_CHECKBOX1_DETAILS
                      }
                    </label>
                  </div>
                  <div className="check-items">
                    <input
                      name="WatchPending"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={state.WatchPending}
                    ></input>
                    <label>
                      {
                        props.labels
                          .MONITOR_OPPORTUNITY_TOPSCORE_CHECKBOX2_DETAILS
                      }
                    </label>
                  </div>
                  <Select
                    name="WorkplaceId"
                    className={`select-input select-Workplace`}
                    placeholder={"Select Opportunity"}
                    value={
                      (state.currentSearch &&
                        state.currentSearch.selectedOpportunity) ||
                      ""
                    }
                    onChange={(selectedOption) =>
                      onFormSelectChange("selectedOpportunity", selectedOption)
                    }
                    clearable={false}
                    searchable={false}
                    options={state.opportunitiesList}
                  />
                  <div className="text-center-">
                    <button
                      onClick={() =>
                        handleMonitorClick("OpportunityAvailibility")
                      }
                      className="action-btn"
                    >
                      {props.labels.MONITOR_FOR_ME_LABEL}
                    </button>
                  </div>
                </div>
              ) : (
                ""
              )}

              {state.aNewIpro && state.onOpportunities ? (
                <div>
                  <label>
                    {props.labels.MONITOR_OPPORTUNITY_ANEWIPRO_ASSOONAS_LABEL}
                  </label>
                  <ul>
                    <li>{props.labels.MONITOR_TOP_5_LABEL}</li>
                    <li>{props.labels.MONITOR_TOP_10_LABEL}</li>
                    <li>{props.labels.MONITOR_TOP_20_LABEL}</li>
                    <li>{props.labels.MONITOR_TOP_100_LABEL}</li>
                  </ul>
                  <label>
                    {
                      props.labels
                        .MONITOR_OPPORTUNITY_ANEWIPRO_FOR_THE_SAVED_SEARCHLABEL
                    }
                  </label>
                  <div className="check-items">
                    <input type={"checkbox"}></input>
                    <label>
                      {
                        props.labels
                          .MONITOR_SAVEDSEARCH_ANEWIPRO_CHECKBOX1_DETAILS
                      }
                    </label>
                  </div>
                  <div className="check-items">
                    <input
                      name="WatchDeclined"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={state.WatchDeclined}
                    ></input>
                    <label>
                      {
                        props.labels
                          .MONITOR_OPPORTUNITY_ANEWIPRO_CHECKBOX1_DETAILS
                      }
                    </label>
                  </div>
                  <div className="check-items">
                    <input
                      name="WatchPending"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={state.WatchPending}
                    ></input>
                    <label>
                      {
                        props.labels
                          .MONITOR_OPPORTUNITY_ANEWIPRO_CHECKBOX2_DETAILS
                      }
                    </label>
                  </div>
                  <Select
                    name="WorkplaceId"
                    className={`select-input select-Workplace`}
                    placeholder={"Select Opportunity"}
                    value={
                      (state.currentSearch &&
                        state.currentSearch.selectedOpportunity) ||
                      ""
                    }
                    onChange={(selectedOption) =>
                      onFormSelectChange("selectedOpportunity", selectedOption)
                    }
                    clearable={false}
                    searchable={false}
                    options={state.opportunitiesList}
                  />
                  <div className="text-center-">
                    <button
                      onClick={() =>
                        handleMonitorClick("OpportunityAvailibility")
                      }
                      className="action-btn"
                    >
                      {props.labels.MONITOR_FOR_ME_LABEL}
                    </button>
                  </div>
                </div>
              ) : (
                ""
              )}
            </div>
          </div>
        </Column.Body>
      </Column>
    );
  };

  const onShortlistItemOptionChange = (e) => {
    const { name, value } = e.target;
    setState((prevState) => ({
      ...prevState,
      [name]: !prevState[name],
    }));
  };

  const handleMonitorClick = (type) => {
    setState((prevState) => ({ ...prevState, isLoading: true }));
    if (type === "ShortlistAvailibility") {
      const {
        OpportunityEmail,
        ShortlistEmail,
        selectedOpportunity,
        selectedShortlist,
      } = state;
      if (!OpportunityEmail && !ShortlistEmail) {
        const info = {
          message: "Please select at least one option to monitor",
          status: "error",
        };
        setState((prevState) => ({ ...prevState, isLoading: false }));
        props.notificationAction(info);
        return;
      }
      if (!selectedOpportunity) {
        const info = {
          message: "Please select at least one option to monitor",
          status: "error",
        };
        setState((prevState) => ({ ...prevState, isLoading: false }));
        props.notificationAction(info);
        return;
      }
      const data = {
        ShortlistId: selectedShortlist.ShortlistId,
        ...(selectedOpportunity &&
          OpportunityEmail && { OpportunityId: selectedOpportunity.RequestId }),
      };
      postMonitorShortlistApi(data).then((res) => {
        if (res.success) {
          const info = {
            message: "Shortlist successfully added into monitoring list",
            status: "success",
          };
          setState((prevState) => ({ ...prevState, isLoading: false }));
          props.notificationAction(info);
          return;
        } else {
          const info = {
            message: res.message,
            status: "error",
          };
          setState((prevState) => ({ ...prevState, isLoading: false }));
          props.notificationAction(info);
          return;
        }
      });
    }
    if (type === "OpportunityAvailibility") {
      const { WatchDeclined, WatchPending, selectedOpportunity } = state;
      if (!WatchDeclined && !WatchPending) {
        const info = {
          message: "Please select at least one option to monitor",
          status: "error",
        };
        setState((prevState) => ({ ...prevState, isLoading: false }));
        props.notificationAction(info);
        return;
      }
      const data = {
        WatchDeclined,
        WatchPending,
        OpportunityId: selectedOpportunity.RequestId,
      };

      postMonitorOpportunityApi(data).then((res) => {
        if (res.success) {
          const info = {
            message: "Shortlist successfully added into monitoring list",
            status: "success",
          };
          setState((prevState) => ({ ...prevState, isLoading: false }));
          props.notificationAction(info);
          return;
        } else {
          const info = {
            message: res.message,
            status: "error",
          };
          setState((prevState) => ({ ...prevState, isLoading: false }));
          props.notificationAction(info);
          return;
        }
      });
    }
  };

  const onCheckBoxChange = (name, value) => {
    setState((prevState) => ({
      ...prevState,
      currentSearch: {
        ...prevState.currentSearch,
        [`${name}`]: !prevState.currentSearch[`${name}`],
      },
    }));
  };

  const handleSavedSearchMonitoringClick = (type) => {
    setState((prevState) => ({ ...prevState, isLoading: true }));
    const { currentSearch } = state;
    if (type === "scoreChangeMonitor") {
      if (
        !currentSearch.sendEmailOnScoreChange &&
        !currentSearch.sendOpportunityToScoreChanger
      ) {
        const info = {
          message: "Please select at least one option to monitor",
          status: "error",
        };
        props.notificationAction(info);
        setState((prevState) => ({ ...prevState, isLoading: false }));
        return;
      }
      if (
        currentSearch.sendOpportunityToScoreChanger &&
        !currentSearch.topScoreChangeSelectedOpportunity
      ) {
        const info = {
          message: "Please select opportunity to send",
          status: "error",
        };
        props.notificationAction(info);
        setState((prevState) => ({ ...prevState, isLoading: false }));
        return;
      }
      const obj = {
        SavedSearchId: currentSearch.SavedSearchId,
        WatchScoreChange: currentSearch.sendEmailOnScoreChange,
        SendOpportunityOnScoreChange:
          currentSearch.sendOpportunityToScoreChanger,
        ...(currentSearch.sendOpportunityToScoreChanger && {
          ScoreChangeOpportunityId:
            currentSearch.topScoreChangeSelectedOpportunity.RequestId,
        }),
      };

      postMonitorSavedSearchApi(obj)
        .then((res) => {
          if (res.success) {
            const info = {
              message: "Saved Search successfully added into monitoring list",
              status: "success",
            };
            setState((prevState) => ({ ...prevState, isLoading: false }));
            props.notificationAction(info);
          }
        })
        .catch((error) => {
          setState((prevState) => ({ ...prevState, isLoading: false }));
          console.log(error);
        });
    }
    if (type === "newScorerMonitor") {
      if (
        !currentSearch.sendEmailOnNewIpro &&
        !currentSearch.sendOpportunityToNewIpro
      ) {
        const info = {
          message: "Please select at least one option to monitor",
          status: "error",
        };
        setState((prevState) => ({ ...prevState, isLoading: false }));
        props.notificationAction(info);
        return;
      }
      if (
        currentSearch.sendOpportunityToNewIpro &&
        !currentSearch.newIproSelectedOpportunity
      ) {
        const info = {
          message: "Please select opportunity to send",
          status: "error",
        };
        setState((prevState) => ({ ...prevState, isLoading: false }));
        props.notificationAction(info);
        return;
      }
      const obj = {
        SavedSearchId: currentSearch.SavedSearchId,
        WatchNewTopScrorer: currentSearch.sendEmailOnNewIpro,
        SendOpportunityToNewTopScorer: currentSearch.sendOpportunityToNewIpro,
        ...(currentSearch.sendOpportunityToNewIpro && {
          NewScorerOpportunityId:
            currentSearch.newIproSelectedOpportunity.RequestId,
        }),
      };

      postMonitorSavedSearchApi(obj)
        .then((res) => {
          if (res.success) {
            const info = {
              message: "Saved Search successfully added into monitoring list",
              status: "success",
            };
            setState((prevState) => ({ ...prevState, isLoading: false }));
            props.notificationAction(info);
          }
        })
        .catch((error) => {
          setState((prevState) => ({ ...prevState, isLoading: false }));
          console.log(error);
        });
    }
  };

  const onChange = (key) => {
    if (key === "shortlist") {
      col1ItemClick("shortlists");
      setState((prevState) => ({
        ...prevState,
        value: key,
        collapse: false,
      }));
    }
    if (key === "saved") {
      col1ItemClick("savedSearches");
      setState((prevState) => ({
        ...prevState,
        value: key,
        collapse: false,
      }));
    }
    if (key === "opportunities") {
      col1ItemClick("opportunities");
      setState((prevState) => ({
        ...prevState,
        value: key,
        collapse: false,
      }));
    }
  };

  const {
    onOpportunities,
    onSavedSearches,
    onShortlists,
    shortlist,
    savedSearches,
    opportunitiesList,
    savedSearchItemClicked,
    opportunityItemClicked,
    shortlistItemClicked,
    selectedOpportunity,
    aNewIpro,
    newAvailibility,
    topScoreChange,
    ShortlistEmail,
    OpportunityEmail,
    WatchPending,
    WatchDeclined,
    currentSearch,
    value,
    isLoading,
  } = state;
  const { widgetTestId, isHelpActive, labels } = props;
  return (
    <>
      <Row className="monitoring-view-mobile  mb-3">
        {isLoading && (
          <div id="loader-wrapper">
            <div className="loader-container">
              <div className="loader">
                <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
                  <defs>
                    <filter id="goo">
                      <feGaussianBlur
                        in="SourceGraphic"
                        stdDeviation="6"
                        result="blur"
                      />
                      <feColorMatrix
                        in="blur"
                        mode="matrix"
                        values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                        result="goo"
                      />
                      <feBlend in="SourceGraphic" in2="goo" />
                    </filter>
                  </defs>
                </svg>
              </div>
            </div>
          </div>
        )}
        <Col xs={24} md={0} className="aant-section-column-mobile ">
          <Select2
            style={{ width: "100%" }}
            placeholder="select"
            defaultValue={"shortlist"}
            onChange={(e) => onChange(e)}
          >
            <Option value="shortlist">
              <div className="product-menus-mobile">
                <p>Shortlist</p>
              </div>
            </Option>
            <Option value="saved">
              <div className="product-menus-mobile">
                <p>Saved Searches</p>
              </div>
            </Option>
            <Option value="opportunities">
              <div className="product-menus-mobile">
                <p>Opportunities</p>
              </div>
            </Option>
          </Select2>

          {value === "shortlist" && (
            <div className="main-inner-mobile">
              <div className="paragraph">
                A shortlist is a list of iPros we have bundeled together for a
                reason we can monitor and see if anything changes within
                shortlist.
              </div>
              <div className="mo-col-2">
                {onShortlists && shortlist && shortlist.length > 0 ? (
                  <Col2
                    data={shortlist}
                    name="shortlists"
                    handleClick={col2ItemClick}
                  />
                ) : (
                  <div className="empty-message-search">
                    You have no shortlist yet, please add atleast one shortlist
                    first
                  </div>
                )}
              </div>
              <Col3
                onShortlists={onShortlists}
                shortlistItemClicked={shortlistItemClicked}
                labels={labels}
                onSavedSearches={onSavedSearches}
                savedSearchItemClicked={savedSearchItemClicked}
                onOpportunities={onOpportunities}
                opportunityItemClicked={opportunityItemClicked}
                Col3ItemMobile={Col3ItemMobile}
                onChange2={onChange2}
              />
            </div>
          )}

          {value === "saved" && (
            <div className="main-inner-mobile">
              <div className="paragraph">
                Saved Search is a set of search criteria we have saved to reuse
                for interesting roles and skills. We can monitor a saved search
                to see if we get new result.
              </div>
              <div className="mo-col-2">
                {onSavedSearches &&
                savedSearches &&
                savedSearches.length > 0 ? (
                  <Col2
                    data={savedSearches}
                    name="savedSearches"
                    handleClick={col2ItemClick}
                  />
                ) : (
                  <div className="empty-message-search">
                    You have no saved searches yet, please save atleast one
                    search first
                  </div>
                )}
              </div>
              <Col3
                onShortlists={onShortlists}
                shortlistItemClicked={shortlistItemClicked}
                labels={labels}
                onSavedSearches={onSavedSearches}
                savedSearchItemClicked={savedSearchItemClicked}
                onOpportunities={onOpportunities}
                opportunityItemClicked={opportunityItemClicked}
                Col3ItemMobile={Col3ItemMobile}
                onChange2={onChange2}
              />
            </div>
          )}
          {value === "opportunities" && (
            <div className="main-inner-mobile">
              <div className="paragraph">
                An opportunity is a description of position or a job we need to
                staff. We can monitor your Opportunities to see if we can find
                new iPros that are a good match.
              </div>
              <div className="mo-col-2">
                {onOpportunities &&
                opportunitiesList &&
                opportunitiesList.length > 0 ? (
                  <Col2
                    data={opportunitiesList}
                    name="opportunities"
                    handleClick={col2ItemClick}
                  />
                ) : (
                  <div className="empty-message-search">
                    You have no opportunities yet, please add atleast one
                    opportunities first
                  </div>
                )}
              </div>
              <Col3
                onShortlists={onShortlists}
                shortlistItemClicked={shortlistItemClicked}
                labels={labels}
                onSavedSearches={onSavedSearches}
                savedSearchItemClicked={savedSearchItemClicked}
                onOpportunities={onOpportunities}
                opportunityItemClicked={opportunityItemClicked}
                Col3ItemMobile={Col3ItemMobile}
                onChange2={onChange2}
              />
            </div>
          )}
        </Col>
        <Col xs={0} md={24}>
          <div className="monitoring-view">
            <div className="mo-col-1">
              <div
                onClick={() => col1ItemClick("shortlists")}
                className={`${onShortlists ? "onBorders " : " "} mo-nav `}
              >
                <div>
                  <i className="timesheet-icon" />
                  <label>{labels.MONITORING_SHORTLISTS_LABEL}</label>
                </div>
                <p>{labels.MONITORING_SHORTLIST_DETAILS}</p>
              </div>
              <div
                onClick={() => col1ItemClick("savedSearches")}
                className={`${onSavedSearches ? "onBorders " : " "} mo-nav `}
              >
                <div>
                  <i className="timesheet-icon" />
                  <label>{labels.MONITORING_SAVEDSEARCH_LABEL}</label>
                </div>
                <p>{labels.MONITORING_SAVEDSEARCHES_DETAILS}</p>
              </div>
              <div
                onClick={() => col1ItemClick("opportunities")}
                className={`${onOpportunities ? "onBorders " : " "} mo-nav `}
              >
                <div>
                  <i className="timesheet-icon" />
                  <label>
                    {labels?.searcher_phillips_monitoring_opportunities_label}
                  </label>
                </div>
                <p>{labels.MONITORING_OPPORTUNITIES_DETAILS}</p>
              </div>
            </div>
            <div className="mo-col-2">
              {onShortlists && shortlist ? (
                <>
                  <ul
                    className={
                      shortlist?.length > 0 ? "mo-list-item" : "no-data"
                    }
                  >
                    {shortlist?.length > 0 ? (
                      shortlist?.map((item) => (
                        <li
                          className={`${item.isSelected ? "active " : " "}`}
                          onClick={() => col2ItemClick("shortlists", item)}
                        >
                          {item.ShortlistName}
                        </li>
                      ))
                    ) : (
                      <div className="empty-message-search">
                        <div>
                          You have no shortlist yet, please add atleast one
                          shortlist first
                        </div>
                        <button
                          className="action-btn"
                          onClick={() => {
                            navigate("/shortlists");
                          }}
                        >
                          Add Shortlist
                        </button>
                      </div>
                    )}
                  </ul>
                </>
              ) : (
                ""
              )}

              {onSavedSearches && savedSearches ? (
                <ul
                  className={
                    savedSearches?.length > 0 ? "mo-list-item" : "no-data"
                  }
                >
                  {savedSearches?.length > 0 ? (
                    savedSearches.map((item) => (
                      <li
                        className={`${item.isSelected ? "active " : " "}`}
                        onClick={() => col2ItemClick("savedSearches", item)}
                      >
                        {item.SearchName}
                      </li>
                    ))
                  ) : (
                    <div className="empty-message-search">
                      <div>
                        You have no shortlist yet, please add atleast one
                        shortlist first
                      </div>
                      <button
                        className="action-btn"
                        onClick={() => {
                          navigate("/saved-searches");
                        }}
                      >
                        Add Shortlist
                      </button>
                    </div>
                  )}
                </ul>
              ) : (
                ""
              )}

              {onOpportunities && opportunitiesList ? (
                <ul
                  className={
                    opportunitiesList?.length > 0 ? "mo-list-item" : "no-data"
                  }
                >
                  {opportunitiesList?.length > 0 ? (
                    opportunitiesList.map((item) => (
                      <li
                        className={`${item.isSelected ? "active " : " "}`}
                        onClick={() => col2ItemClick("opportunities", item)}
                      >
                        {item.RequestName}
                      </li>
                    ))
                  ) : (
                    <div className="empty-message-search">
                      <div>
                        You have no opportunities yet, please add atleast one
                        opportunity first
                      </div>
                      <button
                        className="action-btn"
                        onClick={() => {
                          navigate("/create-opportunity");
                        }}
                      >
                        Add opportunity
                      </button>
                    </div>
                  )}
                </ul>
              ) : (
                ""
              )}
            </div>
            <div className="mo-col-3">
              {onShortlists && shortlistItemClicked ? (
                <div>
                  {Col3Item(
                    "availabilityIcon",
                    labels.MONITORING_NEW_AVAILABILITY_LABEL,
                    labels.MONITORING_SHORTLIS_NEWAVAILABILITY_DETAILS,
                    "newAvailibility"
                  )}
                </div>
              ) : (
                ""
              )}
              {onSavedSearches && savedSearchItemClicked ? (
                <div>
                  {Col3Item(
                    "scorePlusIcon",
                    labels.MONITORING_TOPSCORE_LABEL,
                    labels.MONITORING_SAVEDSEACH_TOPSCORE_DETAILS,
                    "topScoreChange"
                  )}
                  {Col3Item(
                    "newIproIcon",
                    labels.MONITORING_ANEWIPRO_LABEL,
                    labels.MONITORING_SAVEDSEARCH_ANEWIPRO_DETAILS,
                    "aNewIpro"
                  )}
                </div>
              ) : (
                ""
              )}

              {onOpportunities && opportunityItemClicked ? (
                <div>
                  {Col3Item(
                    "availabilityIcon",
                    labels.MONITORING_NEW_AVAILABILITY_LABEL,
                    labels.MONITORING_OPPORTUNITY_NEWAVAILABILITY_DETAILS,
                    "newAvailibility"
                  )}
                  {Col3Item(
                    "scorePlusIcon",
                    labels.MONITORING_TOPSCORE_LABEL,
                    labels.MONITORING_OPPORTUNITY_TOPSCORE_DETAILS,
                    "topScoreChange"
                  )}
                  {Col3Item(
                    "newIproIcon",
                    labels.MONITORING_ANEWIPRO_LABEL,
                    labels.MONITORING_OPPORTUNITY_NEWIPRO_DETAILS,
                    "aNewIpro"
                  )}
                </div>
              ) : (
                ""
              )}
            </div>
            <div className="mo-col-4">
              {newAvailibility && onShortlists ? (
                <div>
                  <div className="check-items">
                    <input
                      name="ShortlistEmail"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={ShortlistEmail}
                    ></input>
                    <label>
                      {
                        labels.MONITORING_SHORTLIST_NEWAVAILIBILITY_CHECKBOX_DETAILS
                      }
                    </label>
                  </div>
                  <div className="check-items">
                    <input
                      name="OpportunityEmail"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={OpportunityEmail}
                    ></input>
                    <label>
                      {
                        labels.MONITORING_SHORTLIST_NEWAVAILIBILITY_CHECKBOX2_DETAILS
                      }
                    </label>
                  </div>
                  <Select
                    name="opportunities"
                    className={`select-input select-Workplace`}
                    placeholder={"select opportunity"}
                    value={
                      (state.currentSearch &&
                        state.currentSearch.selectedOpportunity) ||
                      ""
                    }
                    onChange={(selectedOption) =>
                      onFormSelectChange("", selectedOption)
                    }
                    clearable={false}
                    searchable={false}
                    options={state.opportunitiesList}
                  />
                  <button
                    onClick={() => handleMonitorClick("ShortlistAvailibility")}
                    className="action-btn"
                  >
                    {labels.MONITOR_FOR_ME_LABEL}
                  </button>
                </div>
              ) : (
                ""
              )}

              {topScoreChange && onSavedSearches ? (
                <div>
                  <label>{labels.MONITOR_TOP_TEN_SCORE_SAVEDSEARCH}</label>
                  <ul>
                    <li>263</li>
                    <li>243</li>
                    <li>231</li>
                    <li>212</li>
                    <li>199</li>
                    <li>199</li>
                    <li>193</li>
                    <li>189</li>
                    <li>172</li>
                    <li>169</li>
                  </ul>
                  <div className="check-items">
                    <input
                      onChange={(value) =>
                        onCheckBoxChange("sendEmailOnScoreChange", value)
                      }
                      type={"checkbox"}
                      value={
                        currentSearch && currentSearch.sendEmailOnScoreChange
                      }
                    ></input>
                    <label>
                      {
                        labels.MONITOR_SAVEDSEARCH_TOPSCORECHANGE_CHECKBOX1_DETAILS
                      }
                    </label>
                  </div>
                  <div className="check-items">
                    <input
                      onChange={(value) =>
                        onCheckBoxChange("sendOpportunityToScoreChanger", value)
                      }
                      type={"checkbox"}
                      value={
                        currentSearch &&
                        currentSearch.sendOpportunityToScoreChanger
                      }
                    ></input>
                    <label>
                      {
                        labels.MONITOR_SAVEDSEARCH_TOPSCORECHANGE_CHECKBOX2_DETAILS
                      }
                    </label>
                  </div>
                  <Select
                    name="WorkplaceId"
                    className={`select-input select-Workplace`}
                    placeholder={"Select Opportunity"}
                    value={
                      (currentSearch &&
                        currentSearch.topScoreChangeSelectedOpportunity) ||
                      ""
                    }
                    onChange={(selectedOption) =>
                      onFormSelectChange(
                        "topScoreChangeSelectedOpportunity",
                        selectedOption
                      )
                    }
                    clearable={false}
                    searchable={false}
                    options={opportunitiesList}
                  />
                  <button
                    onClick={() =>
                      handleSavedSearchMonitoringClick("scoreChangeMonitor")
                    }
                    className="action-btn"
                  >
                    Monitor this for me
                  </button>
                </div>
              ) : (
                ""
              )}

              {aNewIpro && onSavedSearches ? (
                <div>
                  <label>
                    {labels.MONITOR_SAVEDSEARCH_ANEWIPRO_ASSOONAS_LABEL}
                  </label>
                  <ul>
                    <li>{labels.MONITOR_TOP_5_LABEL} </li>
                    <li>{labels.MONITOR_TOP_10_LABEL}</li>
                    <li>{labels.MONITOR_TOP_20_LABEL}</li>
                    <li>{labels.MONITOR_TOP_100_LABEL}</li>
                  </ul>
                  <div className="check-items">
                    <input
                      onChange={(value) =>
                        onCheckBoxChange("sendEmailOnNewIpro", value)
                      }
                      type={"checkbox"}
                      value={currentSearch && currentSearch.sendEmailOnNewIpro}
                    ></input>
                    <label>
                      {labels.MONITOR_SAVEDSEARCH_ANEWIPRO_CHECKBOX1_DETAILS}
                    </label>
                  </div>
                  <div className="check-items">
                    <input
                      onChange={(value) =>
                        onCheckBoxChange("sendOpportunityToNewIpro", value)
                      }
                      type={"checkbox"}
                      value={
                        currentSearch && currentSearch.sendOpportunityToNewIpro
                      }
                    ></input>
                    <label>
                      {labels.MONITOR_SAVEDSEARCH_ANEWIPRO_CHECKBOX2_DETAILS}
                    </label>
                  </div>
                  <Select
                    name="opportunities"
                    className={`select-input select-Workplace`}
                    placeholder={"select opportunity"}
                    value={
                      (currentSearch &&
                        currentSearch.newIproSelectedOpportunity) ||
                      ""
                    }
                    onChange={(selectedOption) =>
                      onFormSelectChange(
                        "newIproSelectedOpportunity",
                        selectedOption
                      )
                    }
                    clearable={false}
                    searchable={false}
                    options={state.opportunitiesList}
                  />
                  <button
                    onClick={() =>
                      handleSavedSearchMonitoringClick("newScorerMonitor")
                    }
                    className="action-btn"
                  >
                    {labels.MONITOR_FOR_ME_LABEL}
                  </button>
                </div>
              ) : (
                ""
              )}

              {newAvailibility && onOpportunities ? (
                <div>
                  <div className="check-items">
                    <input
                      name="WatchDeclined"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={WatchDeclined}
                    ></input>
                    <label>
                      Send the email if anyone who declined or haven't responded
                      becomes available
                    </label>
                  </div>
                  <div className="check-items">
                    <input
                      name="WatchPending"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={WatchPending}
                    ></input>
                    <label>
                      {labels.MONITOR_OPPORTUNITY_NEWAVAIL_CHECKBOX1_DETAILS}
                    </label>
                  </div>
                  <Select
                    name="WorkplaceId"
                    className={`select-input select-Workplace`}
                    placeholder={"Select Opportunity"}
                    value={
                      (state.currentSearch &&
                        state.currentSearch.selectedOpportunity) ||
                      ""
                    }
                    onChange={(selectedOption) =>
                      onFormSelectChange("selectedOpportunity", selectedOption)
                    }
                    clearable={false}
                    searchable={false}
                    options={opportunitiesList}
                  />
                  <button
                    onClick={() =>
                      handleMonitorClick("OpportunityAvailibility")
                    }
                    className="action-btn"
                  >
                    {labels.MONITOR_FOR_ME_LABEL}
                  </button>
                </div>
              ) : (
                ""
              )}

              {topScoreChange && onOpportunities ? (
                <div>
                  <label>
                    {labels.MONITOR_OPPORTUNITY_TOPSCORE_TOP_TEN_DETAILS}
                  </label>
                  <ul>
                    <li>263</li>
                    <li>243</li>
                    <li>231</li>
                    <li>212</li>
                    <li>199</li>
                    <li>199</li>
                    <li>193</li>
                    <li>189</li>
                    <li>172</li>
                    <li>169</li>
                  </ul>
                  <div className="check-items">
                    <input
                      name="WatchDeclined"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={WatchDeclined}
                    ></input>
                    <label>
                      {labels.MONITOR_OPPORTUNITY_TOPSCORE_CHECKBOX1_DETAILS}
                    </label>
                  </div>
                  <div className="check-items">
                    <input
                      name="WatchPending"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={WatchPending}
                    ></input>
                    <label>
                      {labels.MONITOR_OPPORTUNITY_TOPSCORE_CHECKBOX2_DETAILS}
                    </label>
                  </div>
                  <Select
                    name="WorkplaceId"
                    className={`select-input select-Workplace`}
                    placeholder={"Select Opportunity"}
                    value={
                      (state.currentSearch &&
                        state.currentSearch.selectedOpportunity) ||
                      ""
                    }
                    onChange={(selectedOption) =>
                      onFormSelectChange("selectedOpportunity", selectedOption)
                    }
                    clearable={false}
                    searchable={false}
                    options={opportunitiesList}
                  />
                  <button
                    onClick={() =>
                      handleMonitorClick("OpportunityAvailibility")
                    }
                    className="action-btn"
                  >
                    {labels.MONITOR_FOR_ME_LABEL}
                  </button>
                </div>
              ) : (
                ""
              )}

              {aNewIpro && onOpportunities ? (
                <div>
                  <label>
                    {labels.MONITOR_OPPORTUNITY_ANEWIPRO_ASSOONAS_LABEL}
                  </label>
                  <ul>
                    <li>{labels.MONITOR_TOP_5_LABEL}</li>
                    <li>{labels.MONITOR_TOP_10_LABEL}</li>
                    <li>{labels.MONITOR_TOP_20_LABEL}</li>
                    <li>{labels.MONITOR_TOP_100_LABEL}</li>
                  </ul>
                  <label>
                    {
                      labels.MONITOR_OPPORTUNITY_ANEWIPRO_FOR_THE_SAVED_SEARCHLABEL
                    }
                  </label>
                  <div className="check-items">
                    <input type={"checkbox"}></input>
                    <label>
                      {labels.MONITOR_SAVEDSEARCH_ANEWIPRO_CHECKBOX1_DETAILS}
                    </label>
                  </div>
                  <div className="check-items">
                    <input
                      name="WatchDeclined"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={WatchDeclined}
                    ></input>
                    <label>
                      {labels.MONITOR_OPPORTUNITY_ANEWIPRO_CHECKBOX1_DETAILS}
                    </label>
                  </div>
                  <div className="check-items">
                    <input
                      name="WatchPending"
                      onChange={onShortlistItemOptionChange}
                      type={"checkbox"}
                      value={WatchPending}
                    ></input>
                    <label>
                      {labels.MONITOR_OPPORTUNITY_ANEWIPRO_CHECKBOX2_DETAILS}
                    </label>
                  </div>
                  <Select
                    name="WorkplaceId"
                    className={`select-input select-Workplace`}
                    placeholder={"Select Opportunity"}
                    value={
                      (state.currentSearch &&
                        state.currentSearch.selectedOpportunity) ||
                      ""
                    }
                    onChange={(selectedOption) =>
                      onFormSelectChange("selectedOpportunity", selectedOption)
                    }
                    clearable={false}
                    searchable={false}
                    options={opportunitiesList}
                  />
                  <button
                    onClick={() =>
                      handleMonitorClick("OpportunityAvailibility")
                    }
                    className="action-btn"
                  >
                    {labels.MONITOR_FOR_ME_LABEL}
                  </button>
                </div>
              ) : (
                ""
              )}
            </div>
          </div>
        </Col>
      </Row>
    </>
  );
};

const mapStateToProps = ({ systemLabel, navigation, userInfo }) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  const User = user ? user : StorageService.getUser();
  const { isHelpActive } = navigation;
  return { labels, isHelpActive, User };
};

export default connect(mapStateToProps, { notificationAction })(Monitoring);
