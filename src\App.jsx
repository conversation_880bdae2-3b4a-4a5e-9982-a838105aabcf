import { StyleProvider } from "@ant-design/cssinjs";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ConfigProvider } from "antd";
import { lazy, Suspense, useEffect } from "react";
import { Provider } from "react-redux";
import { AuthService } from "./api/auth";
import { initialConfig } from "./store/initialConfig";
import { store } from "./store/store";
import LoadingMain from "./common-alpha/LoadingMain/LoadingMain";
const Routes = lazy(() => import("./Routes/Routes"));
const Notification = lazy(() =>
  import("./common-alpha/Notification/Notification")
);
const ErrorBoundary = lazy(() => import("./components/Error/ErrorBoundary"));
const App = () => {
  useEffect(() => {
    AuthService.setDefaults();
    initialConfig();
  }, []);
  const queryClient = new QueryClient();

  return (
    <StyleProvider hashPriority="high" layer>
      <ConfigProvider
        theme={{
          hashed: false,
          cssVar: true,
          token: {
            fontFamily: "Inter, sans-serif", // Replace with your desired font
            colorPrimary: "#8f82f5"
          },
          components: {
            Tabs: {
              colorBorderSecondary: "none"
            },

            Checkbox: {
              colorBgContainer: "transparent",
              lineWidth: 1.5,
              colorBorder: "#343333"
            },
            Collapse: {
              fontSize: 16,
              colorTextHeading: "#878787",
              headerPadding: "12px 32px",
              contentPadding: "12px 32px",
              colorBorder: "transparent"
            },

            Typography: {
              colorText: "#413c3bcc",
              fontSizeHeading2: "24px",
              fontSizeHeading3: "20px",
              fontSizeHeading4: "18px",
              fontSizeHeading5: "14px",
              fontWeightStrong: 500
            },

            Form: {
              labelFontSize: 14,
              labelColor: "#878787",
              fontSize: 13
            },
            Modal: {
              borderRadiusLG: 16
            },

            Input: {
              paddingBlockLG: 8,
              paddingInlineLG: 11,
              paddingBlock: 5,
              paddingInline: 16,
              borderRadius: 24,
              lineHeight: "32.4px", // takes fix 44px height using paddingBlock 5
              colorText: "#343333",
              colorBgContainer: "#F3F1FD",
              colorBorder: "#F3F1FD",
              controlHeight: 44,
              colorErrorBorder: "#F3F1FD",
              colorErrorBorderHover: "#F3F1FD",
              activeShadow: "none",
              colorTextPlaceholder: "#878787"
            },

            DatePicker: {
              paddingBlockLG: 8,
              paddingInlineLG: 11,
              paddingBlock: 5,
              paddingInline: 16,
              borderRadius: 24,
              lineHeight: "32.4px",
              controlHeight: 44,
              colorText: "#343333",
              colorBgContainer: "#F3F1FD",
              colorBorder: "#F3F1FD",
              colorErrorBorder: "#F3F1FD",
              colorErrorBorderHover: "#F3F1FD",
              activeShadow: "none",
              colorTextPlaceholder: "#878787"
            },

            Select: {
              paddingBlock: 5,
              paddingInline: 16,
              borderRadius: 24,
              colorText: "#343333",
              colorBgContainer: "#F3F1FD",
              colorBorder: "#F3F1FD",
              controlHeight: 44,
              activeShadow: "none",
              colorTextPlaceholder: "#878787",
              multipleItemHeight: "auto",
              multipleItemBg: "#F3F1FD",
              multipleItemBorderColor: "#F3F1FD"
            },
            Button: {
              controlHeight: 36,
              borderRadius: 8,
              colorPrimaryText: "white",
              paddingInline: 16,
              paddingBlock: 8,
              paddingInlineSM: 12,

              defaultBg: "#F3F1FD",
              defaultColor: "#8E81F5",
              defaultBorderColor: "transparent",

              colorLink: "#8E81F5",
              colorLinkHover: "#8E81F5aa",
              defaultHoverBg: "#F3F1FDaa",
              defaultHoverColor: "#878787",
              defaultHoverBorderColor: "transparent",

              defaultActiveBg: "#F3F1FD",
              defaultActiveColor: "#878787",
              defaultActiveBorderColor: "transparent",

              colorBgContainerDisabled: "#D7D7D7",
              colorTextDisabled: "#878787"
            },
            Tag: {
              defaultBg: "#F3F1FD",
              colorBorder: "#F3F1FD",
              defaultColor: "#343333"
            },
            Segmented: {
              borderRadiusSM: 40
            },
            Drawer: {
              colorText: "#343333",
              colorSplit: "#EAE5FC",
              padding: 24,
              lineHeightLG: 1.2
            },
            Rate: {
              starBg: "#000000",
              starColor: "#fadb14",
              starHoverScale: "scale(1.3)",
              starSize: 40
            }
          }
        }}
      >
        <Suspense fallback={<LoadingMain />}>
          <ErrorBoundary>
            <Provider store={store}>
              <GoogleOAuthProvider clientId="937962890378-0v1mdfv3bdv70tfutsps8jo4ivino06m.apps.googleusercontent.com">
                <QueryClientProvider client={queryClient}>
                  <Notification />
                  <Routes />
                </QueryClientProvider>
              </GoogleOAuthProvider>
            </Provider>
          </ErrorBoundary>
        </Suspense>
      </ConfigProvider>
    </StyleProvider>
  );
};

export default App;
