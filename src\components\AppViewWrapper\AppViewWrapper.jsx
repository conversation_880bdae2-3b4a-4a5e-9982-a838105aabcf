import { useState } from "react";
import { connect } from "react-redux";
import Header from "../../components-alpha/Header/Header";
import "./app-view-wrapper.scss";

const AppViewWrapper = props => {
  const [isNavShrink, setIsNavShrink] = useState(false);

  const handleNavigationToggle = () => {
    setIsNavShrink(!isNavShrink);
  };

  const removeNavShrink = () => {
    if (isNavShrink && window.innerWidth <= 1240) {
      setIsNavShrink(false);
    }
  };

  const { children } = props;
  return (
    <div className="product-container">
      <div className="product-view-container" onClick={removeNavShrink}>
        <Header
          hideUserSetting={true}
          onNavigationToggle={handleNavigationToggle}
        />
        {children}
      </div>
    </div>
  );
};

const mapStateToProps = ({ notification }) => {
  return { notification };
};

export default connect(mapStateToProps)(AppViewWrapper);
