import { useState } from "react";
import { ChevronLeft, ChevronRight, Edit3, ChevronDown } from "lucide-react";
export const ExtendContractCalender = ({
  selectedDate = new Date(),
  setShowCalendar,
  tempSelectedDate,
  setTempSelectedDate,
  handleExtendCollaboration,
  setRefresh
}) => {
  const [currentDate, setCurrentDate] = useState(new Date(selectedDate));
  const [showMonthYearPicker, setShowMonthYearPicker] = useState(false);

  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
  ];
  const dayNames = ["S", "M", "T", "W", "T", "F", "S"];

  const getDaysInMonth = date => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    for (let i = 0; i < 42; i++) {
      const day = new Date(startDate);
      day.setDate(startDate.getDate() + i);
      days.push(day);
    }
    return days;
  };

  const isSameMonth = (date1, date2) => {
    return (
      date1.getMonth() === date2.getMonth() &&
      date1.getFullYear() === date2.getFullYear()
    );
  };

  const isSameDate = (date1, date2) => {
    return (
      date1.getDate() === date2.getDate() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getFullYear() === date2.getFullYear()
    );
  };

  const handlePrevMonth = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1)
    );
  };

  const handleMonthYearSelect = (month, year) => {
    setCurrentDate(new Date(year, month, 1));
    setShowMonthYearPicker(false);
  };

  const generateYears = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear - 50; i <= currentYear + 50; i++) {
      years.push(i);
    }
    return years;
  };

  const handleDateClick = date => {
    if (isSameMonth(date, currentDate)) {
      setTempSelectedDate(date);
    }
  };

  const handleCancel = () => {
    setTempSelectedDate(new Date());
    setCurrentDate(new Date());
  };

  const formatSelectedDate = date => {
    const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const months = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec"
    ];
    return `${days[date.getDay()]}, ${
      months[date.getMonth()]
    } ${date.getDate()}`;
  };

  const days = getDaysInMonth(currentDate);

  return (
    <div className="bg-[#ECE6F0] rounded-[28px] p-6 w-full max-w-sm shadow-2xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-gray-600 text-sm font-medium">Select date</h3>
      </div>

      {/* Selected Date Display */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-normal text-gray-900">
          {formatSelectedDate(tempSelectedDate)}
        </h2>
        <Edit3 className="w-5 h-5 text-gray-400" />
      </div>

      {/* Month Navigation */}
      <div className="flex items-center justify-between mb-4">
        <div className="relative">
          <button
            onClick={() => setShowMonthYearPicker(!showMonthYearPicker)}
            className="flex items-center gap-1 px-3 py-1 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <span className="text-lg font-medium text-gray-900">
              {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
            </span>
            <ChevronDown className="w-4 h-4 text-gray-600" />
          </button>

          {/* Month/Year Picker Dropdown */}
          {showMonthYearPicker && (
            <div className="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10 w-64">
              <div className="p-4">
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Month
                  </label>
                  <div className="grid grid-cols-3 gap-2">
                    {monthNames.map((month, index) => (
                      <button
                        key={index}
                        onClick={() =>
                          handleMonthYearSelect(
                            index,
                            currentDate.getFullYear()
                          )
                        }
                        className={`p-2 text-sm rounded-md transition-colors ${
                          index === currentDate.getMonth()
                            ? "bg-[#6750A4] text-white"
                            : "hover:bg-gray-100 text-gray-700"
                        }`}
                      >
                        {month.slice(0, 3)}
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Year
                  </label>
                  <div className="max-h-32 overflow-y-auto">
                    <div className="grid grid-cols-4 gap-1">
                      {generateYears().map(year => (
                        <button
                          key={year}
                          onClick={() =>
                            handleMonthYearSelect(currentDate.getMonth(), year)
                          }
                          className={`p-1 text-sm rounded transition-colors ${
                            year === currentDate.getFullYear()
                              ? "bg-[#6750A4] text-white"
                              : "hover:bg-gray-100 text-gray-700"
                          }`}
                        >
                          {year}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
        <button
          onClick={handlePrevMonth}
          className="p-2 hover:bg-gray-100 rounded-full transition-colors"
        >
          <ChevronLeft className="w-5 h-5 text-gray-600" />
        </button>

        <button
          onClick={handleNextMonth}
          className="p-2 hover:bg-gray-100 rounded-full transition-colors"
        >
          <ChevronRight className="w-5 h-5 text-gray-600" />
        </button>
      </div>

      {/* Day Headers */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {dayNames.map((day, index) => (
          <div
            key={index}
            className="text-center text-sm font-medium text-gray-500 py-2"
          >
            {day}
          </div>
        ))}
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-1 mb-6">
        {days.map((day, index) => {
          const isCurrentMonth = isSameMonth(day, currentDate);
          const isSelected = isSameDate(day, tempSelectedDate);
          const isToday = isSameDate(day, new Date());

          return (
            <button
              key={index}
              onClick={() => handleDateClick(day)}
              className={`
                  w-10 h-10 text-sm rounded-full flex items-center justify-center transition-colors
                  ${!isCurrentMonth ? "text-gray-300" : "text-gray-900"}
                  ${isSelected ? "bg-[#6750A4] text-white" : ""}
                  ${
                    isToday && !isSelected
                      ? "bg-purple-100 text-purple-600"
                      : ""
                  }
                  ${
                    isCurrentMonth && !isSelected && !isToday
                      ? "hover:bg-gray-100"
                      : ""
                  }
                `}
            >
              {day.getDate()}
            </button>
          );
        })}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between items-center">
        <button
          onClick={() => {
            setShowCalendar(false);
          }}
          className="text-[#6750A4] font-medium px-4 py-2 hover:bg-purple-50 rounded-lg transition-colors"
        >
          Close
        </button>
        <div className="flex gap-2">
          <button
            onClick={handleCancel}
            className="text-[#6750A4] font-medium px-4 py-2 hover:bg-purple-50 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleExtendCollaboration}
            className="text-[#6750A4] font-medium px-4 py-2 hover:bg-purple-50 rounded-lg transition-colors"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  );
};
