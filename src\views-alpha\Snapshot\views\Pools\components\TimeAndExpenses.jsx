import moment from "moment";
import { LoadingOutlined } from "@ant-design/icons";
import clsx from "clsx";
import dayjs from "dayjs";
import { useCallback, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { twMerge } from "tailwind-merge";
import { notificationAction } from "../../../../../actions-alpha/notification";
import DatePicker from "../../../../../common-alpha/DatePicker/Datepicker";
import CustomSelect from "../../../../../common-alpha/Select/Select";
import { InfoItemCard } from "../../../../Resume/Component/ExperienceSection";
import {
  GetCollaborationExpenseDetailApi,
  GetCollaborationTimeDetailApi,
  GetPoolExpenseDetailApi,
  GetPoolTimeDetailApi
} from "../../../snapshotApi";
import { SubHeading } from "../../Reports/components/TimelogList";

const durationOptions = [
  { label: "Current Month", value: 1 },
  { label: "Last Month", value: 2 },
  { label: "Year to date", value: 3 }
];
const TimeAndExpenses = ({ labels, allCollaborations, poolDetails, token }) => {
  const {
    Dateformatelabel,
    SNAPSHOT_THIS_COLLABORATION,
    SNAPSHOT_AVG_WEEK,
    SNAPSHOT_THIS_POOL
  } = labels || {};
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const [StartDate, setStartDate] = useState(moment().startOf("months"));
  const [EndDate, setEndDate] = useState(moment());

  const [TimeDetail, setTimeDetail] = useState();
  const [ExpenseDetail, setExpenseDetail] = useState();
  const [PoolTimeDetail, setPoolTimeDetail] = useState();
  const [PoolExpenseDetail, setPoolExpenseDetail] = useState();

  const [selectedCollaboration, setSelectedCollaboration] = useState(
    allCollaborations?.[0]?.CollaborationId
  );

  const [openPicker, setOpenPicker] = useState(false);
  const [selectedOption, setSelectedDuration] = useState({
    label: "Current Month",
    value: 1
  });

  const time = [
    {
      label: SNAPSHOT_THIS_COLLABORATION,
      value: `${TimeDetail?.TotalTime ?? ""} ${TimeDetail?.TotalEarning ??
        ""}`.trim()
    },
    {
      label: SNAPSHOT_AVG_WEEK,
      value: `${TimeDetail?.AvgWeekTime ?? ""} ${TimeDetail?.AvgWeekEarning ??
        ""}`.trim()
    },
    {
      label: SNAPSHOT_THIS_POOL,
      value: `${PoolTimeDetail?.TotalTime ??
        ""} ${PoolTimeDetail?.TotalEarning ?? ""}`.trim()
    },
    {
      label: SNAPSHOT_AVG_WEEK,
      value: `${PoolTimeDetail?.AvgWeekTime ??
        ""} ${PoolTimeDetail?.AvgWeekEarning ?? ""}`.trim()
    }
  ];
  const expense = [
    {
      label: SNAPSHOT_THIS_COLLABORATION,
      value: ExpenseDetail?.TotalExpenses
    },
    {
      label: SNAPSHOT_AVG_WEEK,
      value: ExpenseDetail?.AvgWeekExpenses
    },
    {
      label: SNAPSHOT_THIS_POOL,
      value: PoolExpenseDetail?.TotalExpenses
    },
    {
      label: SNAPSHOT_AVG_WEEK,
      value: PoolExpenseDetail?.AvgWeekExpenses
    }
  ];

  const onChangeCollaboration = item => {
    setSelectedCollaboration(item?.CollaborationId);
  };

  const handleDateChange = (d, name) => {
    const date = d.format("DD/MM/YYYY");
    let startDate = null,
      endDate = null;
    if (name === "StartDate") {
      setStartDate(d);
      startDate = d;
      endDate = EndDate;
    } else {
      setEndDate(d);
      startDate = StartDate;
      endDate = d;
    }
    if (moment(date) >= moment(EndDate)) {
      const info = {
        status: "error",
        message:
          labels.Collaboration_Time_And_Expense_Start_Date_Validation_Message
      };
      dispatch(notificationAction(info));
      return;
    }
  };

  const GetCollaborationTimeDetail = useCallback(
    (start, end, CollaborationId) => {
      if (!CollaborationId) return;
      GetCollaborationTimeDetailApi(CollaborationId, start, end, token)
        .then(res => {
          setTimeDetail(res.items);

          setLoading(false);
        })
        .catch(err => console.log("GetCollaborationTimeDetail err", err));
    },
    [token]
  );

  const GetCollaborationExpenseDetail = useCallback(
    (start, end, CollaborationId) => {
      if (!CollaborationId) return;
      GetCollaborationExpenseDetailApi(CollaborationId, start, end, token)
        .then(res => {
          setExpenseDetail(res.items);

          setLoading(false);
        })
        .catch(err => console.log("GetCollaborationExpenseDetail err", err));
    },
    [token]
  );

  const GetPoolTimeDetail = useCallback(
    (start, end, poolDetailParam) => {
      const PoolId = poolDetailParam?.PoolId;
      if (PoolId === undefined || PoolId === null) return;
      GetPoolTimeDetailApi(PoolId, start, end, token)
        .then(res => {
          setPoolTimeDetail(res.items);

          setLoading(false);
        })
        .catch(err => console.log("GetPoolTimeDetail err", err));
    },
    [token]
  );

  const GetPoolExpenseDetail = useCallback(
    (start, end, poolDetailParam) => {
      const PoolId = poolDetailParam?.PoolId;
      if (PoolId === undefined || PoolId === null) return;
      GetPoolExpenseDetailApi(PoolId, start, end, token)
        .then(res => {
          setPoolExpenseDetail(res.items);

          setLoading(false);
        })
        .catch(err => console.log("GetPoolExpenseDetail err", err));
    },
    [token]
  );

  useEffect(() => {
    setLoading(true);
    GetCollaborationTimeDetail(
      moment(StartDate).format("DD/MM/YYYY"),
      moment(EndDate).format("DD/MM/YYYY"),
      selectedCollaboration
    );
    GetCollaborationExpenseDetail(
      moment(StartDate).format("DD/MM/YYYY"),
      moment(EndDate).format("DD/MM/YYYY"),
      selectedCollaboration
    );
  }, [
    StartDate,
    EndDate,
    GetCollaborationExpenseDetail,
    GetCollaborationTimeDetail,
    selectedCollaboration
  ]);

  useEffect(() => {
    setLoading(true);
    GetPoolTimeDetail(
      moment(StartDate).format("DD/MM/YYYY"),
      moment(EndDate).format("DD/MM/YYYY"),
      poolDetails
    );
    GetPoolExpenseDetail(
      moment(StartDate).format("DD/MM/YYYY"),
      moment(EndDate).format("DD/MM/YYYY"),
      poolDetails
    );
  }, [
    StartDate,
    EndDate,
    GetPoolExpenseDetail,
    GetPoolTimeDetail,
    poolDetails
  ]);

  const handleOptionChange = (o1, option) => {
    console.log("option", option);
    setSelectedDuration(option);
    let start, end;
    const { value } = option;
    if (value == 3) {
      start = moment().startOf("year");
      end = moment();
    } else if (value == 2) {
      start = moment()
        .subtract(1, "month")
        .startOf("month");
      end = moment()
        .subtract(1, "month")
        .endOf("month");
    } else {
      start = moment().startOf("month");
      end = moment();
    }
    setStartDate(start);
    setEndDate(end);
  };

  return (
    <div className="time-expense-detail">
      <div
        className={twMerge(
          clsx(
            "flex items-stretch border-1 border-[#EAE5FC] bg-[#F3F1FD] rounded-2xl",
            {
              "rounded-br-none": openPicker
            }
          )
        )}
      >
        <CustomSelect
          mainClassName="flex-1"
          value={selectedOption}
          clearable={false}
          searchable={false}
          options={durationOptions}
          onChange={handleOptionChange}
        />
        <div className="relative flex-1 flex items-center">
          <div
            onClick={() => setOpenPicker(!openPicker)}
            className="cursor-pointer border-l border-l-[#EAE5FC] pl-4"
          >
            {dayjs(StartDate).format("MMM Do")}-
            {dayjs(EndDate).format("MMM Do YYYY")}
          </div>
          {openPicker && (
            <div className="z-10 absolute inset-0 pl-2.5 top-full bottom-auto border-1 border-[#EAE5FC] bg-[#F3F1FD]">
              <DatePicker
                label={"Start date"}
                value={StartDate && dayjs(StartDate)}
                className="[&_label]:!text-[13px] [&_label]:after:!mr-0 [&_input]:!text-right [&_input]:!text-[13px] [&_.ant-picker]:!p-0 [&_.ant-form-item-label]:!pb-0 [&_.ant-form-item-row]:!items-center"
                format="Do MMM YYYY"
                placeholderText={Dateformatelabel}
                allowClear={false}
                onChange={date => handleDateChange(date, "StartDate")}
                suffixIconClassName="text-[var(--purple)]"
                layout={"horizontal"}
                colon={false}
              />
              <DatePicker
                allowClear={false}
                label={"End date"}
                className="[&_label]:!text-[13px] [&_label]:after:!mr-0 [&_input]:!text-right [&_input]:!text-[13px] [&_.ant-picker]:!p-0 [&_.ant-form-item-label]:!pb-0 [&_.ant-form-item-row]:!items-center"
                value={EndDate && dayjs(EndDate)}
                format="Do MMM YYYY"
                placeholderText={Dateformatelabel}
                onChange={date => handleDateChange(date, "EndDate")}
                suffixIconClassName="text-[var(--purple)]"
                layout={"horizontal"}
                colon={false}
              />
            </div>
          )}
        </div>
      </div>

      <CustomSelect
        label={"Select Contract"}
        readonly={true}
        searchable={false}
        value={selectedCollaboration}
        placeholder={"Please select contract"}
        onChange={(selectedOption, item) => onChangeCollaboration(item)}
        options={allCollaborations}
        clearable={false}
        layout={"vertical"}
        className="!mb-2"
        mainClassName={"!mt-2"}
        fieldNames={{
          label: "Title",
          value: "CollaborationId"
        }}
      />

      <SubHeading className="mt-2">Time</SubHeading>
      {loading ? (
        <div className="flex items-center justify-center">
          <LoadingOutlined />
        </div>
      ) : (
        <InfoItemCard fields={time} labelClassName="min-w-[100px]" />
      )}
      <SubHeading className="mt-2">Expense</SubHeading>
      {loading ? (
        <div className="flex items-center justify-center">
          <LoadingOutlined />
        </div>
      ) : (
        <InfoItemCard fields={expense} labelClassName="min-w-[100px]" />
      )}
    </div>
  );
};

export default TimeAndExpenses;
