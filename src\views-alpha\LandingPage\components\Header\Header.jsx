import { useEffect, useState } from "react";
import Logo from "../../../../assets-alpha/images/svg/logo-new.svg?react";
import MenuIcon from "../../../../assets-alpha/images/svg/menu.svg?react";
import Close from "../../../../assets-alpha/images/svg/close.svg?react";
import { Button } from "antd";
import { useNavigate } from "react-router-dom";
import { publicRoutes } from "../../../../Routes/routing";
import { useSelector } from "react-redux";

const Header = ({ openDrawerMenu, setOpenDrawerMenu }) => {
  const labels = useSelector(state => state.systemLabel.labels);
  const [scrolled, setScrolled] = useState(false);
  const navigate = useNavigate();
  useEffect(() => {
    const onScroll = () => {
      setScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", onScroll);
    return () => window.removeEventListener("scroll", onScroll);
  }, []);

  const scrollToSection = (id, e) => {
    if (e) e.preventDefault();
    const element = document.getElementById(id);
    if (element) {
      const offset = 0;
      const offsetTop =
        element.getBoundingClientRect().top + window.pageYOffset - offset;
      window.scrollTo({ top: offsetTop, behavior: "smooth" });
      // window.history.pushState({}, "", `#${id}`);
    }
  };

  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.substring(1);
      if (hash) {
        const element = document.getElementById(hash);
        if (element) {
          const offset = 92;
          const offsetTop =
            element.getBoundingClientRect().top + window.pageYOffset - offset;
          window.scrollTo({ top: offsetTop, behavior: "smooth" });
        }
      }
    };
    window.addEventListener("hashchange", handleHashChange);
    return () => {
      window.removeEventListener("hashchange", handleHashChange);
    };
  }, []);

  const Menu = () => (
    <>
      <a
        // href="#home"
        onClick={e => scrollToSection("home", e)}
        className="font-medium text-[#343333] text-[16px] hover:!text-[#8E81F5] transition-colors duration-200"
      >
        {labels?.LANDING_HOME_LBL}
      </a>
      <a
        // href="#whyprodoo"
        onClick={e => scrollToSection("whyprodoo", e)}
        className="font-medium text-[#343333] text-[16px] hover:!text-[#8E81F5] transition-colors duration-200"
      >
        {labels?.LANDING_WHY_PRODOO_LBL}
      </a>
      <a
        href="https://prodoo.com/Blog/"
        target="_blank"
        rel="noopener noreferrer"
        className="font-medium text-[#343333] text-[16px] hover:!text-[#8E81F5] transition-colors duration-200"
      >
        {labels?.Landing_Blogs_Label}
      </a>
      <a
        // href="#aboutus"
        onClick={e => scrollToSection("aboutus", e)}
        className="font-medium text-[#343333] text-[16px] hover:!text-[#8E81F5] transition-colors duration-200"
      >
        {labels?.LANDING_NAV_ABOUT_US_LBL}
      </a>
      <a
        // href="#contactus"
        onClick={e => scrollToSection("contactus", e)}
        className="font-medium text-[#343333] text-[16px] hover:!text-[#8E81F5] transition-colors duration-200"
      >
        {labels?.LANDING_CONTACTUS_LABEL}
      </a>
    </>
  );

  return (
    <>
      <div
        className={`fixed flex justify-between items-center h-[56px] lg:h-[72px] !px-4 lg:!px-[40px] w-full z-[9999] transition-colors duration-300 ${
          scrolled ? "!bg-white shadow-md" : "bg-transparent"
        } `}
      >
        <div className="cursor-pointer">
          <Logo />
        </div>
        <div className="hidden lg:flex gap-10 font-inter">
          <Menu />
        </div>
        <div className="hidden lg:flex gap-3">
          <Button
            className="w-[110px] h-10 !rounded-[12px]"
            type="primary"
            onClick={() => {
              navigate(publicRoutes.signup.path);
            }}
          >
            {labels?.Get_Started_Lbl}
          </Button>
          <Button
            className="w-[110px] h-10 !rounded-[12px] !bg-[#F4F2FE] !text-[#343333]"
            type="default"
            onClick={() => {
              navigate(publicRoutes.login.path);
            }}
          >
            {labels?.loginLoginButton}
          </Button>
        </div>
        <div
          className="flex lg:hidden cursor-pointer"
          onClick={() => {
            setOpenDrawerMenu(prev => !prev);
          }}
        >
          {openDrawerMenu ? <Close /> : <MenuIcon />}
        </div>
      </div>
      {openDrawerMenu && (
        <>
          {/* user to remove background scrolling */}
          <style>{`
      body {
        overflow: hidden;
        position: fixed;
        width: 100%;
      }
    `}</style>
          <div
            className="fixed inset-0 bg-transparent z-[49]"
            onClick={() => setOpenDrawerMenu(false)}
          />
          <div
            className={` fixed right-0 z-[50] w-full top-[56px] md:top-[72px]   bg-white/40  backdrop-blur-3xl md:!h-[calc(100vh-12px)] !h-[calc(100vh-12px)] `}
          >
            <div className="flex flex-col gap-1 h-full p-4">
              <div className="flex flex-col gap-1">
                <a
                  onClick={e => {
                    e.preventDefault();
                    setOpenDrawerMenu(false);
                    setTimeout(() => {
                      scrollToSection("home");
                    }, 100);
                  }}
                  className="flex w-full justify-center items-center font-normal !text-[#343333] !text-[16px] py-2 !bg-white h-10 rounded-[8px]"
                >
                  {labels?.LANDING_HOME_LBL}
                </a>
                <a
                  onClick={e => {
                    e.preventDefault();
                    setOpenDrawerMenu(false);
                    setTimeout(() => {
                      scrollToSection("whyprodoo");
                    }, 100);
                  }}
                  className="flex w-full justify-center items-center font-normal !text-[#343333] !text-[16px] py-2 !bg-white h-10 rounded-[8px]"
                >
                  {labels?.LANDING_WHY_PRODOO_LBL}
                </a>
                <a
                  href="https://prodoo.com/Blog/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex w-full justify-center items-center font-normal !text-[#343333] !text-[16px] py-2 !bg-white h-10 rounded-[8px]"
                >
                  {labels?.Landing_Blogs_Label}
                </a>
                <a
                  onClick={e => {
                    e.preventDefault();
                    setOpenDrawerMenu(false);
                    setTimeout(() => {
                      scrollToSection("aboutus");
                    }, 100);
                  }}
                  className="flex w-full justify-center items-center font-normal !text-[#343333] !text-[16px] py-2 !bg-white h-10 rounded-[8px]"
                >
                  {labels?.LANDING_NAV_ABOUT_US_LBL}
                </a>
                <a
                  onClick={e => {
                    e.preventDefault();
                    setOpenDrawerMenu(false);
                    setTimeout(() => {
                      scrollToSection("contactus");
                    }, 100);
                  }}
                  className="flex w-full justify-center items-center font-normal !text-[#343333] !text-[16px] py-2 !bg-white h-10 rounded-[8px]"
                >
                  {labels?.LANDING_CONTACTUS_LABEL}
                </a>
              </div>
              <div className="!mt-5 flex flex-col gap-4">
                <Button
                  className="w-full !h-10 lg:!h-12 !rounded-[12px]"
                  type="primary"
                  onClick={() => {
                    navigate(publicRoutes.signup.path);
                    setOpenDrawerMenu(false);
                  }}
                >
                  {labels?.Get_Started_Lbl}
                </Button>
                <Button
                  className="w-full !h-10 lg:!h-12 !rounded-[12px] !bg-[#F4F2FE] !text-[#343333]"
                  type="default"
                  onClick={() => {
                    navigate(publicRoutes.login.path);

                    setOpenDrawerMenu(false);
                  }}
                >
                  {labels?.loginLoginButton}
                </Button>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default Header;
