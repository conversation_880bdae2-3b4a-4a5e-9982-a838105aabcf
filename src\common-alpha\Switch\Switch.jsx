import "./switch.scss";
import clsx from "clsx";
import { Switch as SwitchButton } from "antd";

const Switch = ({ IsFreelancer, onChange, className, disabled, loading }) => {
  return (
    <div className={clsx("switch-wrapper", className)} onClick={onChange}>
      <SwitchButton
        className="[&_.ant-switch-handle]:before:!bg-[var(--purple)] 
        [&_.ant-switch-handle]:before:!transition-all !bg-white
         [&.ant-switch-checked_.ant-switch-handle]:before:!bg-[var(--green)]
         [&_.ant-switch-loading-icon] !min-w-10 
         "
        loading={loading}
        disabled={disabled}
        checked={!IsFreelancer}
      />
      {/* <Button
        disabled={disabled}
        className={`DashboardCompanyToggle ${IsFreelancer &&
          `DashboardFreelanceToggle`}`}
        loading={loading}
      /> */}
    </div>
  );
};

export default Switch;
