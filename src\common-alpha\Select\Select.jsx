import { LoadingOutlined } from "@ant-design/icons";
import { Form, Select, Typography } from "antd";
import { twMerge } from "tailwind-merge";
import ArrowDown from "../../assets-alpha/images/svg/arrow-down-1.svg?react";

const { Text } = Typography;

const CustomSelect = ({
  name,
  label,
  suffixText,
  onChange,
  options,
  rules,
  suffixIcon,
  PrefixIcon,
  prefixIconClass,
  placeholder = "Select a option and change input text above",
  className,
  mainClassName,
  layout,
  showSearch,
  optionFilterProp,
  onSearch,
  loading,
  value,
  disabled,
  allowClear,
  mode,
  showSuffixIcon = true,
  fieldNames
}) => {
  const prefixDefaultIconClass = twMerge(
    "text-gray-400 h-full !flex items-center w-[24px] justify-center",
    prefixIconClass
  );
  return (
    <Form.Item
      className={twMerge(
        "!mb-0 [&_.ant-select-prefix]:border-r-1 [&_.ant-select-prefix]:pr-[12px] [&_.ant-select-prefix]:h-[32.5px]  [&_.ant-select-prefix]:!mr-[12px] [&_.ant-select-prefix]:border-r-[var(--gray-2)] [&_.ant-form-item-label]:!pb-[6px] [&_.ant-select-selector]:!pr-[16px] [&_.ant-select-selector]:!bg-[#F3F1FD]",
        mainClassName
      )}
      label={label}
      name={name}
      rules={rules}
      extra={
        suffixText && (
          <Text className="!text-xs !mt-2 flex !text-[var(--gray-3)]">
            {suffixText}
          </Text>
        )
      }
      layout={layout}
    >
      <Select
        allowClear={allowClear}
        placeholder={placeholder}
        onChange={onChange}
        fieldNames={fieldNames}
        suffixIcon={
          showSuffixIcon ? (
            <>
              {loading ? (
                <LoadingOutlined className="text-[15px]" />
              ) : (
                <ArrowDown className="w-[20px]" />
              )}
            </>
          ) : (
            <>
              {suffixIcon && loading && (
                <LoadingOutlined className="text-[15px]" />
              )}
              {suffixIcon}
            </>
          )
        }
        mode={mode}
        virtual={false}
        disabled={disabled}
        value={value}
        labelInValue
        prefix={PrefixIcon && <PrefixIcon className={prefixDefaultIconClass} />}
        showSearch={showSearch}
        optionFilterProp={optionFilterProp}
        filterSort={(optionA, optionB) => {
          const input =
            document
              .querySelector(".ant-select-selection-search-input")
              ?.value?.toLowerCase() || "";
          const aStarts = optionA?.label?.toLowerCase?.()?.startsWith?.(input);
          const bStarts = optionB?.label?.toLowerCase?.()?.startsWith?.(input);
          if (aStarts && !bStarts) return -1;
          if (!aStarts && bStarts) return 1;
          return optionA?.label?.localeCompare?.(optionB.label);
        }}
        onSearch={onSearch}
        options={options}
        loading={loading}
        notFoundContent={loading ? "Loading..." : undefined}
        className={` max-md:!h-10 [&.ant-select-multiple]:!h-auto ${className}`}
      >
        {/* {options?.map(option => (
          <Option key={option.value} value={option.label}>
            {option.label}
          </Option>
        ))} */}
      </Select>
    </Form.Item>
  );
};

export default CustomSelect;
