import { useState, useEffect } from "react";
import { connect } from "react-redux";
import PageWrapper from "../../components/PageWrapper/PageWrapper";
import { htmlParser } from "../../utilities/helpers";
import Footer from "../LeadPage/components/Footer/Footer";
import { getDashboardMenusApi } from "../LeadPage/leadPageApi";
import "./cms.scss";
import LogoSrc from "../../assets/images/logo.png";
import WhiteLogoSrc from "../../assets/images/logo.png";
import TopbarSrc from "../../assets/images/topbar.png";
import { getFooterMenuDetailApi } from "./cmsApi";
import { RESET_LANDINGPAGE_URL } from "../../utilities/enviroments";
import { StorageService } from "../../api/storage";
import { useParams } from "react-router-dom";
import LoadingMask from "../../common/LoadingMask/LoadingMask";

const Cms = props => {
  const { labels } = props;
  const { pageId } = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState({});
  const [footerMenu, setFooterMenu] = useState({});

  useEffect(() => {
    getDashboardMenusApi().then(res => {
      const items = res.items;
      const footer = items && items.find(x => x.Name === "FooterMenus");
      setFooterMenu(footer);
    });
    getFooterMenuDetailApi(pageId)
      .then(res => {
        setData(res.items);
        setIsLoading(false);
      })
      .catch(err => {
        console.log({ err });
        onError();
      });
  }, [pageId]);

  const onError = () => {
    setData({
      heading: "Not Found",
      content:
        "<p>Content is not available for your country! It will be uploaded soon<p>"
    });
    setIsLoading(false);
  };

  const onBackClick = () => {
    StorageService.clearAll();

    window.location.href = RESET_LANDINGPAGE_URL;
  };

  return (
    <PageWrapper className="cms-page">
      <div className="cms-page mainContent lightBg">
        {isLoading && <LoadingMask />}
        <header className="landing-header">
          <div className="TopBar">
            <img className="topBarImg" src={TopbarSrc} alt="img" />
          </div>
          <div className="Logo">
            <img className="loginScreen" src={WhiteLogoSrc} alt="logo" />
            <img className="otherScreen" src={LogoSrc} alt="logo" />
          </div>
          <div className="btnWrap">
            <a onClick={onBackClick} id="showLogin" className="loginBtnClick">
              {"Home"}
            </a>
          </div>
        </header>
        {data && (
          <section className="contentContainer">
            <h1 className="proHeading">{data.heading}</h1>
            <div className="aboutPanel">
              <h6 className="subHeading">{htmlParser(data.content)}</h6>
            </div>
          </section>
        )}
        <Footer footerMenu={footerMenu} />
      </div>
    </PageWrapper>
  );
};

const mapStateToProps = ({ systemLabel }) => {
  const { labels } = systemLabel;
  return { labels };
};
export default connect(mapStateToProps)(Cms);
