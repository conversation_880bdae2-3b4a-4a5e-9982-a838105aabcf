import { Col, Form, Row, Switch, Typography } from "antd";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import InformationIcon from "../../../assets-alpha/images/svg/information.svg?react";
import Icon from "../../../common-alpha/Icon/Icon";
import { HelpTooltip } from "../../../common-alpha/Tooltip/Tooltip";
import TooltipModal from "./TooltipModal";
const { Text } = Typography;

export const UserNotificationList = [
  {
    checked: false,
    name: "IsOpportunityRecieved",
    IsFreelancer: true,
    label: "EmailOpportunityInvitation",
    disabled: true
  },
  {
    checked: false,
    name: "IsCollaborationRecieved",
    IsFreelancer: true,
    label: "EmailCollaborationInvitation",
    disabled: true
  },
  {
    checked: false,
    name: "IsReceivedDirectMsg",
    label: "headerMenuEmailNotificationsMessage"
  },
  {
    checked: false,
    name: "IsFeedbackRecieved",
    label: "EmailFeedbackReplyAdminNotification"
  },
  {
    checked: false,
    name: "IsOpportunityInvitationAccepted",
    IsSearcher: true,
    label: "EmailAcceptedOpportunityInvitation"
  },
  {
    checked: false,
    name: "IsOpportunityInvitationDeclined",
    IsSearcher: true,
    label: "EmailDeclinedOpportunityInvitation"
  },
  {
    checked: false,
    name: "IsAcceptedCollaborationInvitation",
    IsSearcher: true,
    label: "EmailAcceptedCollaborationInvitation"
  },
  {
    checked: false,
    name: "IsDeclinedCollaborationInvitation",
    IsSearcher: true,
    label: "EmailDeclinedCollaborationInvitation"
  },
  {
    checked: false,
    name: "IsCandidateLookupAccepted",
    IsFreelancer: true,
    label: "headerMenuEmailNotificationsCLAccepted"
  },
  // {
  //   checked: false,
  //   name: "IsCandidateLookupRejected",
  //   IsFreelancer: true,
  //   label: "headerMenuEmailNotificationsCLRejected",
  // },
  {
    checked: false,
    name: "IsSharedCollaboration",
    IsFreelancer: true,
    label: "EmailSharedCollaboration"
  }
];

const UserNotifications = ({ userNotification }) => {
  const [openTooltipFor, setOpenTooltipFor] = useState(null);
  const form = Form.useFormInstance();
  const labels = useSelector(state => state.systemLabel.labels);
  useEffect(() => {
    const values = form.getFieldsValue();
    form.setFieldsValue({
      ...values,
      userNotification
    });
  }, [form, userNotification]);
  const getTooltipContent = label => {
    switch (label) {
      case "EmailOpportunityInvitation":
        return labels?.setting?.emailOpportunityTooltip;
      case "EmailCollaborationInvitation":
        return labels?.setting?.emailCollaborationTooltip;
      default:
        return "";
    }
  };
  return (
    <div className="userNotification mt-5">
      <div className="flex flex-col gap-4">
        <Text>{labels?.setting?.notificationLabel}</Text>
        <Form.List name={"userNotification"}>
          {fields =>
            fields.map(({ key, name: fieldName, ...restField }) => {
              const notificationItem = userNotification[fieldName];
              return (
                <React.Fragment key={key}>
                  <Form.Item
                    name={[fieldName, "checked"]}
                    label={labels[notificationItem.label]}
                    colon={false}
                    className="!mb-0 [&_.ant-row>.ant-col]:!flex-none [&_.ant-row>.ant-form-item-control]:ml-auto"
                    layout="horizontal"
                    {...restField}
                  >
                    <Row justify="end" align="middle">
                      <Col className="flex items-center">
                        {notificationItem.disabled &&
                        window.innerWidth > 767 ? (
                          <HelpTooltip
                            title={labels?.cannotChangeTooltip}
                            content={getTooltipContent(notificationItem?.label)}
                            placement="top"
                          >
                            <Icon
                              src={InformationIcon}
                              renderSvg
                              className="mr-2 anticon"
                            />
                          </HelpTooltip>
                        ) : notificationItem.disabled ? (
                          <>
                            <Icon
                              onClick={() => setOpenTooltipFor(fieldName)}
                              src={InformationIcon}
                              renderSvg
                              className="mr-2 anticon"
                            />
                            <TooltipModal
                              open={openTooltipFor === fieldName}
                              setOpen={isOpen =>
                                setOpenTooltipFor(isOpen ? fieldName : null)
                              }
                              title={labels?.cannotChangeTooltip}
                              description={getTooltipContent(
                                notificationItem?.label
                              )}
                            />
                          </>
                        ) : null}
                        <Form.Item
                          name={[fieldName, "checked"]}
                          valuePropName="checked"
                          noStyle
                        >
                          <Switch disabled={notificationItem.disabled} />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Form.Item>
                </React.Fragment>
              );
            })
          }
        </Form.List>
      </div>
    </div>
  );
};

export default UserNotifications;
