import { Button, Input } from "antd";
import { isValidEmail } from "../../../utilities/helpers";
import CloseModal from "../../../assets-alpha/images/svg/close-modal.svg";
const NetworkForm = ({
  onFormFieldChange,
  networkData,
  labels,
  handleNetworkInvitationSend,
  setShowAddNetworkModal,
  loading
}) => {
  const { Email } = networkData;
  const validEmail = isValidEmail(Email);
  return (
    <div>
      <div className=" border-b border-[#2f29292f] px-[18px] py-[14px] flex justify-between items-center text-center h-[66px]">
        <h5 className="!m-0 text-[18px] font-semibold">Add network</h5>
        <img
          src={CloseModal}
          alt=""
          className="pointer md:order-1 order-2"
          onClick={() => {
            setShowAddNetworkModal(false);
          }}
        />
      </div>
      <div className="p-6">
        <p>
          Please enter the email address of the person you’d like to add to your
          network.
        </p>
        <Input
          type="text"
          name="Email"
          value={Email || ""}
          className={`!mt-4 ${
            !validEmail && Email?.length > 0
              ? "!border-red-400 !border-[0.5px]"
              : ""
          }`}
          placeholder={labels.NETWORK_EMAIL_PLACEHOLDER_LABEL}
          onChange={onFormFieldChange}
          data-testid="input-text-company-name"
          maxLength="50"
        />
        <div className="w-full flex justify-end">
          <Button
            type="primary"
            className="mt-9"
            loading={loading}
            disabled={!validEmail}
            onClick={handleNetworkInvitationSend}
          >
            Send
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NetworkForm;
