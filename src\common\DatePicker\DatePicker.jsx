import DatePicker from "react-datepicker";
import { PickerContainer } from "./PickerContainer";
import moment from "moment";

const DatePickerField = ({
  name,
  testId,
  todayButton,
  dateFormat,
  selected,
  onChange,
  onBlur,
  className,
  placeholderText,
  minDate,
  maxDate,
  picketId,
  ...rest
}) => {
  return (
    <DatePicker
      data-testid={testId}
      id={picketId}
      name={name}
      todayButton={todayButton}
      dateFormat={dateFormat}
      selected={selected && moment(selected).format()}
      onChange={onChange}
      className={`datepicker-input ${className}`}
      placeholderText={placeholderText}
      minDate={minDate}
      maxDate={maxDate}
      popperContainer={PickerContainer}
      onBlur={onBlur}
      {...rest}
    />
  );
};
export default DatePickerField;
