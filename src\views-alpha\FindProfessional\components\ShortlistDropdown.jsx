import { LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import { <PERSON>ton, Grid } from "antd";
import { useState } from "react";
import LeftIcon from "../../../assets-alpha/images/svg/arrow-left.svg?react";
import NameInputModal from "../../../components-alpha/Modal/NameInputModal";
import {
  useCreateUpdateShortlistApi,
  useGetShortlists
} from "../findProfessionalApi";

import { useQueryClient } from "@tanstack/react-query";
import moment from "moment";
import { useSelector } from "react-redux";
import { ApiUrl } from "../../../api-alpha/apiUrls";
import { formatDate } from "../../../utilities/helpers";

const ShortlistDropdown = ({ selectedResumes, setIsShortlistOpen }) => {
  const [isModalOpen, setModalOpen] = useState(false);
  const queryClient = useQueryClient();
  const screens = Grid.useBreakpoint();
  const findProfessional = useSelector(
    state => state.systemLabel.labels?.findProfessional
  );
  const { data: shortlists, isLoading: loadingShortlist } = useGetShortlists();
  const {
    mutate: createUpdateShortlist,
    isPending: loadingCreatingShortlist,
    error: errorCreatingShortlist
  } = useCreateUpdateShortlistApi();

  const onCreateUpdateShortlist = ({ name, shortlist }) => {
    const shortlistObj = shortlist
      ? {
          ShortlistId: shortlist.ShortlistId,
          ShortlistName: shortlist.ShortlistName,
          ResumeIdsList: selectedResumes?.map(n => n.ResumeId)
        }
      : {
          ShortlistName: name,
          ResumeIdsList: selectedResumes?.map(n => n.ResumeId)
        };
    createUpdateShortlist(shortlistObj, {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: [ApiUrl.Shortlists.Get] });
        queryClient.invalidateQueries({
          queryKey: [
            `${ApiUrl.Shortlistresumes.Get}/?shortlistid=${shortlist?.ShortlistId}`
          ]
        });
        setModalOpen(false);
        setIsShortlistOpen(false);
      }
    });
  };
  return (
    <div className="rounded-[20px] border-1 border-[#EAE5FC] bg-white shadow-xl w-80 max-md:w-full">
      <div className="p-[16px_20px] flex items-center justify-between mb-2 border-b-1 border-b-[#EAE5FC]">
        <span
          onClick={() => {
            if (!screens.md) {
              setIsShortlistOpen(false);
            }
          }}
          className="text-sm font-semibold text-gray-800 text-[16px] flex items-center gap-1.5 max-md:cursor-pointer"
        >
          {!screens.md && <LeftIcon />}
          {findProfessional?.shortlists}
        </span>
        <Button
          type="text"
          icon={<PlusOutlined />}
          onClick={() =>
            setModalOpen({ title: findProfessional?.createShortlist })
          }
          className="!border-dashed !border-1 !border-[#d9d6fe] !text-[var(--purple)] !bg-[#f5f3ff] "
        >
          {findProfessional?.createNew}
        </Button>
      </div>

      <div className="p-[16px_20px] max-h-[300px] max-md:max-h-[calc(100vh-85px)] overflow-auto">
        <div className="text-sm text-gray-500 mb-2">
          {" "}
          {findProfessional?.addInExisting}
        </div>
        <div className="flex flex-col gap-2">
          {loadingCreatingShortlist && <LoadingOutlined />}
          {shortlists.map(s => (
            <button
              key={s.ShortlistId}
              onClick={() => onCreateUpdateShortlist({ shortlist: s })}
              className="w-full text-left bg-[#f4f2ff] rounded-xl p-3 hover:bg-[#ebe6ff] transition"
            >
              <div className="font-medium text-sm text-gray-800">
                {s.ShortlistName}
              </div>
              <div className="text-xs text-gray-500">
                {findProfessional?.date} {formatDate(s.UpdatedOn)}{" "}
                {findProfessional?.time} {moment(s.UpdatedOn).format("hh:mm A")}
              </div>
            </button>
          ))}
        </div>
      </div>

      <NameInputModal
        isOpen={isModalOpen}
        title={isModalOpen?.title}
        initialValue={isModalOpen?.ShortlistName}
        placeholder={findProfessional?.enterShortlistName}
        confirmText={
          isModalOpen?.ShortlistId
            ? findProfessional?.save
            : findProfessional?.create
        }
        onConfirm={name => onCreateUpdateShortlist({ name })}
        onClose={() => setModalOpen(false)}
        isLoading={loadingCreatingShortlist}
        error={errorCreatingShortlist?.message}
      />
    </div>
  );
};

export default ShortlistDropdown;
