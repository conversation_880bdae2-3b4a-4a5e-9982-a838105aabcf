import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";
import svgr from "vite-plugin-svgr";
// import { visualizer } from "rollup-plugin-visualizer";

// https://vite.dev/config/

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");

  return {
    test: {
      globals: true,
      environment: "jsdom",
      dir: "./src"
    },
    plugins: [
      react(),
      tailwindcss(),
      svgr()
      // visualizer({ open: true }) // opens report in browser after build
    ],

    build: {
      outDir: "./build"
      // sourcemap: true,
      // minify: false // ← Key change
    },

    server: {
      port: 3002,
      host: true,
      proxy: {
        "/webapi": `${env.VITE_PROXY}`
      }
    }
  };
});
