import { LoadingOutlined } from "@ant-design/icons";
import { Avatar, Card, Checkbox, Form, Tag, Tooltip } from "antd";
import clsx from "clsx";
import { round } from "lodash";
import { useState } from "react";
import AvatarIcon from "../../../assets-alpha/images/svg/avatar-white.svg";
import EnvironmentOutlined from "../../../assets-alpha/images/svg/location.svg?react";
import ProfileIcon from "../../../assets-alpha/images/svg/profile.svg?react";
import RankingIcon from "../../../assets-alpha/images/svg/ranking.svg?react";
import TrashIcon from "../../../assets-alpha/images/svg/trash.svg?react";
import { useSelector } from "react-redux";

const JobCard = ({
  onRemove,
  onResumeOpen,
  onSelectItem,
  selectedResume,
  ...resumeItem
}) => {
  const {
    ResumeId,
    CorrelationScore,
    Region,
    Profiles = [],
    Skills,
    TotalScore,
    UserFirstName,
    UserLastName
  } = resumeItem;
  const [loading, setLoading] = useState(false);
  const isSelected = selectedResume?.ResumeId === ResumeId;
  const findProfessional = useSelector(
    state => state.systemLabel.labels?.findProfessional
  );
  return (
    <Form.Item className="!m-0 !mt-[8px]">
      <Card
        onClick={() => onResumeOpen(resumeItem)}
        className={clsx(
          `cursor-pointer !bg-[var(--light-purple)] hover:!bg-[var(--light-purple-2)] !border-0 !rounded-[10px] p-[32px] flex flex-col gap-2`,
          {
            "!bg-[var(--light-purple-2)]": isSelected
          }
        )}
      >
        <div className="flex items-start gap-3 max-md:items-center">
          <Avatar className="min-w-[52px]" size={52} src={AvatarIcon} />
          <div className="flex-1 gap-1 flex flex-col">
            <p className="text-gray-900 font-medium max-md:hidden">
              {Profiles?.map(profile => profile.ProfileValue).join(", ")}
            </p>
            <div className="flex flex-wrap items-center gap-x-2 text-[#878787] text-sm max-md:flex-col max-md:items-start">
              <span className="flex items-center gap-1 md:whitespace-nowrap">
                <ProfileIcon className="w-[16px] h-auto" />
                {UserFirstName} {UserLastName}
              </span>
              {Region && (
                <span className="text-[var(--purple)] flex items-center gap-1 md:whitespace-nowrap">
                  <EnvironmentOutlined className="w-4 min-w-4" />

                  <span className="whitespace-normal">{Region}</span>
                </span>
              )}
              <Tooltip title={<>{findProfessional?.matchScoreTooltip}</>}>
                <span
                  onClick={e => e.stopPropagation()}
                  className="text-[var(--green-2)] flex items-center gap-1 md:whitespace-nowrap"
                >
                  <RankingIcon />
                  {findProfessional?.matchScore} (
                  {round((CorrelationScore / (TotalScore || 1)) * 100, 2)}%)
                </span>
              </Tooltip>
            </div>
          </div>
          {onRemove ? (
            loading ? (
              <>
                <LoadingOutlined />
              </>
            ) : (
              <TrashIcon
                className="text-[20px]"
                onClick={e => {
                  e.stopPropagation();
                  onRemove(resumeItem);
                  setLoading(true);
                }}
              />
            )
          ) : (
            // )
            <Checkbox
              onChange={e => {
                e.stopPropagation();
                onSelectItem?.(resumeItem);
              }}
              onClick={e => e.stopPropagation()}
              checked={isSelected}
              name="checked"
              className="mt-1"
            />
          )}
        </div>
        <p className="text-gray-900 !mt-2 font-medium min-md:hidden">
          {Profiles?.map(profile => profile.ProfileValue).join(", ")}
        </p>
        <div className="flex flex-wrap gap-2 mt-3">
          {Skills.filter((_, index) => index < 3).map(skill => (
            <Tag
              key={skill.SkillValue}
              className="!bg-white !border-0 !rounded-[8px] !px-3 !py-1.5 text-gray-700 !mr-0"
            >
              {skill.SkillValue}
            </Tag>
          ))}

          {Skills.length > 3 && (
            <button className="h-8 !w-auto pl-4 pr-4 bg-white border border-[#EAE5FC] rounded-lg">
              <span className="!text-[#343333] text-xs font-normal leading-[100%]">
                {`+ ${Skills.length - 3} ${findProfessional?.more}`}
              </span>
            </button>
          )}
        </div>
      </Card>
    </Form.Item>
  );
};

export default JobCard;
