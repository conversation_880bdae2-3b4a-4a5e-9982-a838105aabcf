import { Modal, Button } from "antd";

const DeleteModal = ({
  open,
  onCancel,
  onConfirm,
  isLoading,
  title,
  description,
  cancelText = "Cancel",
  confirmText = "Delete"
}) => {
  return (
    <Modal
      title={null}
      footer={null}
      closable={false}
      open={open}
      centered={true}
      onCancel={onCancel}
      width={null}
      rootClassName="
        [&_.ant-modal]:!w-[343px]
        md:[&_.ant-modal]:!w-[455px]
        [&_.ant-modal]:!mx-auto
        [&_.ant-modal-content]:!pb-6
        [&_.ant-modal-content]:!h-auto        
      "
    >
      <div className="flex !items-center !h-[22px] ">
        <h1 className="flex !flex-col !font-semibold !text-[#343333] !text-[15px] !leading-[100%] md:!text-[18px] ">
          {title}
        </h1>
      </div>
      <div className="!w-full !border-b !border-gray-300 !mt-4 sm:!mt-[19px]"></div>
      <p className="!w-[299px] md:!w-[395px] !font-normal !text-[13px] md:!text-[16px] !leading-5 !text-[#878787] !mt-[11.3px] md:!mt-5 ml-[26px] !mr-6">
        {description}
      </p>
      <div className="!flex !gap-2 md:!mt-6 !mt-[18.74px]">
        <Button
          className="!w-[50%] !h-[36px]  md:!h-10 !text-[#343333] !bg-[#EAE5FC]"
          type="default"
          onClick={onCancel}
        >
          {cancelText}
        </Button>
        <Button
          className="!w-[50%]  !h-[36px] md:!h-10 !bg-[#FF3B30] !hover:opacity-[90%]"
          type="primary"
          loading={isLoading}
          onClick={onConfirm}
        >
          {confirmText}
        </Button>
      </div>
    </Modal>
  );
};

export default DeleteModal;
