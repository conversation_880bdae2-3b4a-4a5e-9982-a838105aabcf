@use "../../../../assets/sass/importFiles" as *;
// .nextstep-analyzer {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   flex-direction: column;
//   overflow: auto;
//   flex: 1 1;
//   height: 100%;
//   background-color: #f0edfe;
//   color: #3e335e;
//   .settings-container {
//     width: 70%;
//     align-self: center;
//     padding-top: 10px;
//   }
//   .step-div {
//     border: 1px solid #3e335e;
//     min-height: 70px;
//     border-radius: 5px;
//     margin: 0px 25px;
//     justify-content: flex-start;
//     display: flex;
//     width: 70%;
//     align-self: center;
//     @include breakpoint(screen767) {
//       width: 90%;
//       flex-flow: column;
//       height: max-content;
//       min-height: max-content;
//       margin-right: 5%;
//       margin-left: 1%;
//     }
//     .step-circle {
//       display: flex;
//       flex-flow: column;
//       background: #3e335e;
//       color: #f0edfe;
//       font-size: 13px;
//       width: 50px;
//       height: 68px;
//       border-radius: 5px;
//       margin-right: 10px;
//       padding-top: 15px;
//       @include breakpoint(screen767) {
//         flex-flow: row;
//         width: 100%;
//         height: unset;
//         justify-content: center;
//         padding-bottom: 10px;
//         padding-top: 10px;
//         label {
//           margin-right: 10px;
//         }
//         label,
//         span {
//           font-size: 16px;
//         }
//       }
//     }
//     .step-heading {
//       width: 100%;
//       align-self: center;
//       padding: 8px;
//       label {
//         font-size: 17px;
//         font-weight: 900;
//       }
//     }
//   }

//   .mixed-chart {
//     display: flex;
//     justify-content: center;
//     .apexcharts-menu-icon {
//       visibility: hidden;
//     }
//     @include breakpoint(screen767) {
//       flex-flow: column;
//     }
//   }
//   .upload-div {
//     display: flex;
//     flex: 1;
//     flex-direction: column;
//     height: 100%;
//     align-self: center;
//     padding-top: 20px;
//     width: 100%;
//   }
//   .top-salaries {
//     display: flex;
//     flex-flow: column;
//     justify-content: center;
//     .salary-item {
//       margin: 5px;
//       text-align: start;
//       padding: 5px 5px;
//       label {
//         &:first-of-type {
//           font-weight: 900;
//           font-size: 15px;
//         }
//       }
//       &.selected {
//         background: #3e335e;
//         color: #f0edfe;
//         padding: 5px 5px;
//         border-radius: 5px;
//       }
//     }
//     @include breakpoint(screen767) {
//       margin-right: 10px;
//     }
//   }
//   .js_upload_btn {
//     background: #3e335e;
//     padding: 6px 0px 6px 7px;
//     margin-top: 5px;
//     margin-bottom: 5px;
//     border: none;
//     border-radius: 3px;
//     color: #f0edfe;
//     display: inline-block;
//     vertical-align: middle;
//     margin-right: 10px;
//     font-size: 13px;
//     min-width: 155px;
//     position: relative;
//     overflow: hidden;
//     height: 40px;
//   }
//   h3 {
//     margin: 0px;
//   }
//   .lookup-head {
//     margin-bottom: 20px;
//   }

//   #progressInput {
//     margin: 20px auto;
//     width: 30%;
//   }

//   .circle-background,
//   .circle-progress {
//     fill: none;
//   }

//   .circle-background {
//     stroke: #ddd;
//   }

//   .circle-progress {
//     stroke: #3e335e;
//     stroke-linecap: round;
//     stroke-linejoin: round;
//   }

//   .circle-text {
//     font-size: 3em;
//     font-weight: bold;
//     fill: #3e335e;
//   }

//   .progress-bar-container {
//     max-height: 150px;
//     display: flex;
//     align-self: center;
//   }
//   .loader-container {
//     width: 200px;
//     height: 200px;
//     position: absolute;
//     top: 65%;
//     left: 50%;
//     transform: translate(-50%, -50%);
//     margin: auto;
//     filter: url("#goo");
//     animation: rotate-move 2s ease-in-out infinite;
//   }
//   .nextstep-filters {
//     display: flex;
//     align-self: center;
//     margin-top: 20px;
//     @include breakpoint(screen767) {
//       flex-flow: column;
//     }
//   }
//   .select-input {
//     margin: 5px;
//     max-width: 200px;
//     min-width: 200px;
//     border: 1px solid #3e335e;
//     border-radius: 5px;
//     height: 40px;
//     .Select-control {
//       background: #f0edfe !important;
//       color: #3e335e !important;
//       height: 35px;
//       font-size: 15px;
//       text-align: center;
//       box-shadow: 1px 2px #8888888a;
//       border-radius: 5px;
//       // border: 2px solid #f6aa3a !important;
//     }
//     .Select-menu-outer {
//       background: transparent;
//     }
//     .Select-value-label {
//       line-height: 35px;
//       color: #3e335e !important;
//     }
//     .Select-arrow-zone:hover > .Select-arrow {
//       border-top-color: #3e335e !important;
//     }
//     .Select-arrow {
//       border-color: #3e335e transparent transparent;
//     }
//     .Select-option {
//       padding: 10px 6px;
//       font-size: 13px;
//       color: #3e335e;
//       text-align: center;
//       background: #f0edfe;
//       margin-bottom: 2px;
//       border-radius: 5px;
//       border: 2px solid transparent !important;
//       border-bottom: none;
//       margin-top: 2px;
//       margin: 2px;
//       z-index: 12342534534;
//     }
//   }
//   .loader {
//     width: 250px;
//     height: 50px;
//     line-height: 50px;
//     text-align: center;
//     position: absolute;
//     top: 50%;
//     left: 50%;
//     transform: translate(-50%, -50%);
//     font-family: helvetica, arial, sans-serif;
//     text-transform: uppercase;
//     font-weight: 900;
//     color: #3e335e;
//     letter-spacing: 0.2em;

//     &::before,
//     &::after {
//       content: "";
//       display: block;
//       width: 15px;
//       height: 15px;
//       background: #3e335e;
//       position: absolute;
//       animation: load 0.7s infinite alternate ease-in-out;
//     }

//     &::before {
//       top: 0;
//     }

//     &::after {
//       bottom: 0;
//     }
//   }

//   @keyframes load {
//     0% {
//       left: 0;
//       height: 30px;
//       width: 15px;
//     }
//     50% {
//       height: 8px;
//       width: 40px;
//     }
//     100% {
//       left: 235px;
//       height: 30px;
//       width: 15px;
//     }
//   }
// }

.analyzer-root {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: #f3f1fd;
  min-height: 100%;
  width: 100%;
  font-family: Inter !important;

  .nextstep-analyzer {
    border-radius: 12px;
    margin: 50px;
    * {
      margin: 0;
      padding: 0;
    }
    .ant-steps-item-process
      > .ant-steps-item-container
      > .ant-steps-item-tail::after,
    .ant-steps-item-wait
      > .ant-steps-item-container
      > .ant-steps-item-tail::after {
      background: #bbb;
    }

    .txt1 {
      color: #3e345d;
      font-weight: 600;
      font-size: 24px;
      margin-top: 30px;
      margin-bottom: 0;
      line-height: 30px;
    }

    .txt2 {
      font-size: 16px;
      color: #413c3b;
      margin-top: 16px;
      max-width: 80%;
      line-height: 30px;
    }

    .custom-select {
      margin: 0 1rem;
      min-width: 195px;

      p {
        font-size: 16px;
        display: block;
        margin-bottom: 4px;
        font-weight: bolder !important;
        color: #413c3b;
      }

      .ant-select {
        width: 100% !important;
      }

      .ant-select-selection-item {
        color: #413c3b;
        opacity: 0.7;
        font-size: 14px !important;
      }

      .ant-select-selector {
        height: 40px !important;
        padding-top: 0.2rem !important;
        border: none !important;
        width: 100% !important;
      }

      .ant-select-arrow {
        margin-top: -0.4rem !important;
        color: #000;
      }
      .analyze-btn {
        height: 40px;
        margin-top: 1.5rem;
        min-width: 100%;
        font-size: 14px !important;
      }
    }

    .analyze-box {
      margin-top: 50px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .txt3 {
        font-size: 24px;
        color: #3e345d;
        font-weight: 600;
        text-align: center;
        line-height: 30px;
      }

      .chart-warper {
        @media (max-width: 775px) {
          flex-flow: column-reverse;
        }
        .top-salaries {
          margin-top: 1rem;
        }
      }

      .top-salaries {
        display: flex;
        flex-flow: column;
        justify-content: center;
        .salary-item {
          display: flex;
          align-items: center;
          margin: 5px;
          text-align: start;
          padding: 5px 0px;
          cursor: pointer;
          label {
            &:first-of-type {
              font-size: 15px;
            }
            &:nth-child(2) {
              margin-top: 2px;
            }
          }
          &.selected {
            background: #8f82f5;
            color: #f0edfe;
            border-radius: 5px;
            padding: 10px 15px;
          }
        }
        @include breakpoint(screen767) {
          margin-right: 10px;
        }
      }
      @media (max-width: 350px) {
        .top-salaries {
          .salary-item {
            font-size: 10px;
            label {
              &:first-of-type {
                font-size: 10px;
              }
            }
          }
        }
      }

      .bar-chart {
        margin-left: 1rem;
        background: #fff;
        border-radius: 15px;
        padding: 0.2rem;
      }
    }

    @media (max-width: 1180px) {
      .select-warper {
        flex-wrap: wrap;
      }
    }
    @media (max-width: 750px) {
      margin: 30px;
      .select-warper {
        flex-direction: column;
      }
      .top-salaries {
        width: 100%;
      }
      .custom-select {
        min-width: 100%;
        margin: 0;

        .ant-select {
          margin-bottom: 1rem;
        }

        .analyze-btn {
          width: 100%;
        }
      }
      .analyze-box {
        margin-top: 10px;
        p {
          text-align: center;
        }
        .bar-chart {
          margin: 0;
        }
      }
    }
  }
  .add-button {
    background-color: white;
    border-radius: 12px;
    min-width: 160px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.4rem 0.5rem 0.4rem 1rem !important;
    margin-right: 1rem !important;
    margin-bottom: 1rem !important;

    .icon-button-text {
      width: 90%;
      color: #413c3b;
      font-size: 14px;
      font-family: inherit !important;
      line-height: 1rem;
    }
    .plus-button {
      background-color: #8f82f5;
      color: white;
      width: 35px;
      font-size: 1.3rem;
      border-radius: 10px;
      text-align: center;
      cursor: pointer !important;
      margin-left: 1rem !important;
    }

    @media (max-width: 634px) {
      width: 100%;
    }
  }

  .steps-btns-warper {
    .step-btn {
      background: transparent;
      width: 160px;
      height: 50px;
      margin: 2rem;
      color: #8f82f5;
      border-radius: 10px;
    }
    @media (max-width: 585px) {
      flex-direction: column;
      align-items: center;
      justify-content: start;
      margin-bottom: 2rem;
      .step-btn {
        width: 90%;
        margin: 0;
        margin-top: 1rem;
      }
    }
  }

  @media (max-width: 500px) {
    .steps-btns-warper {
      .step-btn {
        height: 35px;
      }
    }
    .nextstep-analyzer {
      .txt1 {
        font-size: 18px;
        line-height: 1.2;
      }
      .txt2 {
        font-size: 14px;
        line-height: 1.2;
        max-width: 100%;
      }
    }
  }
  .nextstep-analyzer {
    .txt1 {
      font-size: 24px !important;
    }
    .ant-steps-item-title {
      font-size: 14px !important;
    }
    .ant-steps-item-description {
      font-size: 14px !important;
    }
    .custom-select label {
      font-size: 18px !important;
    }
    .ant-select-selection-item {
      font-size: 18px !important;
    }
    .analyze-btn {
      font-size: 18px !important;
    }
    .step-btn {
      font-size: 18px !important;
    }
    .analyzer-root {
      .ant-btn {
        font-size: 18px !important;
      }
    }

    .icon-button-text {
      font-size: 18px !important;
    }
  }

  @media (max-width: 491px) {
    .nextstep-analyzer {
      margin: 15px !important;
      .txt1 {
        font-size: 20px !important;
      }
      .text2 {
        font-size: 14px !important;
      }
      .ant-steps-item-title {
        font-size: 12px !important;
      }
      .ant-steps-item-description {
        font-size: 12px !important;
      }
      .custom-select label {
        font-size: 15px !important;
      }
      .ant-select-selection-item {
        font-size: 15px !important;
      }
      .analyze-btn {
        font-size: 14px !important;
      }
      .step-btn {
        font-size: 15px !important;
      }
      .icon-button-text {
        font-size: 14px !important;
      }
    }
  }
  @media (max-width: 300px) {
    .nextstep-analyzer {
      .txt1 {
        font-size: 16px !important;
      }
      .text2 {
        font-size: 12px !important;
      }
      .ant-steps-item-title {
        font-size: 12px !important;
      }
      .ant-steps-item-description {
        font-size: 12px !important;
      }
      .custom-select label {
        font-size: 12px !important;
      }
      .ant-select-selection-item {
        font-size: 12px !important;
      }
      .analyze-btn {
        font-size: 13px !important;
      }
      .step-btn {
        font-size: 15px !important;
      }
      .icon-button-text {
        font-size: 12px !important;
      }
      .add-button {
        margin-right: 0rem !important;
      }
    }
  }
}
