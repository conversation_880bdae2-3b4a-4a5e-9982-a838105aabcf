import { Button, Col, Input, Row, Typography, Upload } from "antd";
import { compact, find, isEmpty, map } from "lodash";
import moment from "moment";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Carousel } from "react-responsive-carousel";
import { useNavigate } from "react-router-dom";
import { notificationAction } from "../../actions/notification";
import Modal from "../../components/Modal/Modal";
import { privateRoutes } from "../../Routes/routing";
import { htmlParser } from "../../utilities/helpers";
import "./phillip.scss";
import {
  getLoginFeaturesApi,
  getSearcherPhillipSocialMediaStatusApi,
  saveParsedResume,
  updateResumeAvailabilityApi,
  uploadLinkedinPDF
} from "./phillipApi";
import DatePickerField from "../../common/DatePicker/DatePicker";
import LoadingMask from "../../common/LoadingMask/LoadingMask";
const { Title, Text } = Typography;
const { TextArea } = Input;
import Img01 from "../../assets/images/onboarding/01.svg";
import Img02 from "../../assets/images/onboarding/02.svg";
import Img03 from "../../assets/images/onboarding/03.svg";
import Img04 from "../../assets/images/onboarding/04.svg";

const Header = ({ labels, onCloseModal }) => (
  <div className="phillip-header">
    {/* <img className="onboard-logo" src={Logo} alt="prodoo-logo" /> */}
    <button onClick={onCloseModal} className="skip-btn">
      <Text className="description-text">
        {labels.iProOnboardingSkipButton}
      </Text>
    </button>
  </div>
);

const Slide = ({ className, children, testId }) => (
  <div className={`js_slide ${className}`} data-testid={testId}>
    {children}
  </div>
);

const CustomInput = ({ onClick, value }) => (
  <button className="custom-date-picker" onClick={onClick}>
    {value}
  </button>
);

const ResumeValues = [
  {
    id: 0,
    name: "Resume",
    notFound: "iProOnboardingResumeCreated",
    found: "iProOnboardingResumeCreated",
    active: true
  },
  {
    id: 1,
    name: "Keywords",
    notFound: "iProOnboardingKeywordNotFound",
    found: "iProOnboardingKeywordFound"
  },
  {
    id: 2,
    name: "Certifications",
    notFound: "iProOnboardingCertificationNotFound",
    found: "iProOnboardingCertificationFound"
  },
  {
    id: 3,
    name: "Skills",
    notFound: "iProOnboardingSkillNotFound",
    found: "iProOnboardingSkillFound"
  },
  {
    id: 4,
    name: "Profiles",
    notFound: "iProOnboardingRoleNotFound",
    found: "iProOnboardingRoleFound"
  },
  {
    id: 5,
    name: "Educations",
    notFound: "iProOnboardingEducationNotFound",
    found: "iProOnboardingEducationFound"
  },
  {
    id: 6,
    name: "Experiences",
    notFound: "iProOnboardingExperienceNotFound",
    found: "iProOnboardingExperienceFound"
  },
  {
    id: 7,
    name: "Countries",
    notFound: "iProOnboardingCountryNotFound",
    found: "iProOnboardingCountryFound"
  },
  {
    id: 8,
    name: "Languages",
    notFound: "iProOnboardingLanguageNotFound",
    found: "iProOnboardingLanguageFound"
  }
  // {
  //   id: 9,
  //   name: "Present",
  //   notFound: "iProOnboardingPresentNotFound",
  //   found: "iProOnboardingPresentFound"
  // }
];

const IProPhillip = ({ open, onSkip, onResumeUpdate }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const labels = useSelector(state => state.systemLabel.labels);

  const FirstName =
    useSelector(state => state.userInfo.user.FirstName) ||
    sessionStorage.getItem("userName");

  const [state, setState] = useState({
    currentSlide: 0,
    date: "",
    companyUrl: "",
    pageUrl: "",
    linkedinUrl: "",
    isFacebookConnected: false,
    isLinkedInConnected: false,
    isCompanyConnected: false,
    isOpportunityCreated: false,
    isShortlistCreated: false,
    isSavedSearchCreated: false,
    keywords: "",
    resumeData: [],
    resumeValues: ResumeValues,
    foundResumeValues: {},
    dateValue: "select a date",
    isDatePickerOpen: false,
    isLoading: false,
    LoginWithFb: false,
    LoginWithLinkedin: false,
    availabilities: [
      {
        id: 1,
        name: "now",
        isActive: false,
        value: labels.iProOnboardingAvailabilityGig
      },
      {
        id: 2,
        name: "notice",
        isActive: false,
        value: labels.iProOnboardingAvailabilityOnNotice
      },
      {
        id: 3,
        name: "date",
        isActive: false,
        value: labels.iProOnboardingAvailabilityUntil
      }
    ],
    selectedAvailability: "notice",
    showDatePicker: false
  });

  useEffect(() => {
    getLoginFeatures();
    getSearcherPhillipSocialMediaStatusApi()
      .then(response => {
        if (response.success) {
          const { FacebookEnabled, LinkedInEnabled } = response.items;
          setState(prevState => ({
            ...prevState,
            isFacebookConnected: FacebookEnabled,
            isLinkedInConnected: LinkedInEnabled
          }));
        }
      })
      .catch(() => {});
    window.addEventListener("keydown", handleTabPress);
    return () => {
      window.removeEventListener("keydown", handleTabPress);
    };
  }, []);

  const handleTabPress = e => {
    if (e.keyCode === 9) {
      e.preventDefault();
      handleContinueClick();
    }
  };

  const handleFinishClick = () => {
    const { availabilities, date = new moment(), resumeData } = state;

    if (resumeData) {
      saveParsedResume(resumeData)
        .then(() => {
          const selectedAvailability = find(availabilities, "isActive");
          if (selectedAvailability) {
            let data = {
              IsAvailable: false,
              AvailabilityDate: moment(date).format("MM/DD/YYYY")
            };
            if (selectedAvailability.name === "notice") {
              data = {
                IsAvailable: false,
                AvailabilityDate: new moment().add(1, "M").format("MM/DD/YYYY"),
                AvailablityType: 2
              };
            } else if (selectedAvailability.name === "date") {
              data = {
                IsAvailable: false,
                AvailabilityDate: moment(date).format("MM/DD/YYYY"),
                AvailablityType: 3
              };
            } else if (selectedAvailability.name === "now") {
              data = {
                IsAvailable: true,
                AvailabilityDate: new moment().format("MM/DD/YYYY"),
                AvailablityType: 1
              };
            }
            updateResumeAvailabilityApi(data).then(({ data }) => {
              const info = {
                message: data.message,
                status: "success"
              };
              dispatch(notificationAction(info));
            });
          }
          const info = {
            message: "Resume saved successfully",
            status: "success"
          };
          dispatch(notificationAction(info));
          onSkip();
          onResumeUpdate();
          if (state.otherResumePdfName || state.linkedinPdfName) {
            navigate(privateRoutes.resumeBuilder.path);
          }
        })
        .catch(err => console.log("Err", err));
    } else {
      onSkip();
    }
  };

  const handleContinueClick = () => {
    if (state.currentSlide === 1 && isEmpty(state.resumeData)) {
      setState(st => ({ ...st, emptyResume: true }));
      return null;
    }
    setState(prevState => ({
      ...prevState,
      currentSlide: prevState.currentSlide + 1
    }));
  };

  const updateCurrentSlide = index => {
    if (state.currentSlide !== index) {
      setState(prevState => ({ ...prevState, currentSlide: index }));
    }
  };

  const handlAvalibalitySelect = availability => {
    const { id, name } = availability;
    const availabilities = map(state.availabilities, item => {
      item.isActive = item.id === id;
      return item;
    });
    setState(prevState => ({
      ...prevState,
      availabilities,
      selectedAvailability: name,
      isDatePickerOpen: name === "date"
    }));
  };

  const getLoginFeatures = () => {
    getLoginFeaturesApi().then(response => {
      setState(prevState => ({
        ...prevState,
        LoginWithFb: response.items.LoginWithFb,
        LoginWithLinkedin: response.items.LoginWithLinkedin
      }));
    });
  };

  const renderResumeValues = item => {
    const { foundResumeValues } = state;
    if (foundResumeValues[item.name] > 0) {
      const value = labels[item.found];
      return value.replace("@count", foundResumeValues[item.name]);
    }
    return labels[item.notFound];
  };

  const uploadLinkedinPdf = (e, successName) => {
    var fileData = e;
    var types = "pdf";
    let { resumeValues } = state;
    var fileType = fileData.name.split(".");
    if (types === fileType[1].toLowerCase()) {
      var reader = new FileReader();
      reader.onload = (function() {
        return function(e) {
          var fileDataInBytes = e.target.result.split(",")[1];
          setState(prevState => ({ ...prevState, isLoading: true }));
          uploadLinkedinPDF(fileDataInBytes)
            .then(res => {
              const info = {
                message: htmlParser(labels[successName]),
                status: "success"
              };
              dispatch(notificationAction(info));

              const foundResumeValues = {
                Profiles: res.items.Profiles && res.items.Profiles.length,
                Skills: res.items.Skills && res.items.Skills.length,
                Certifications:
                  res.items.Certifications && res.items.Certifications.length,
                Keywords: res.items.Keywords && res.items.Keywords.length,
                Countries: res.items.Countries && res.items.Countries.length,
                Languages: res.items.Languages && res.items.Languages.length,
                Industries: res.items.Industries && res.items.Industries.length
              };
              resumeValues = resumeValues.map(item => {
                return {
                  ...item,
                  active:
                    (foundResumeValues[item.name] &&
                      foundResumeValues[item.name] > 0) ||
                    item.name === "Resume"
                };
              });
              setState(prevState => ({
                ...prevState,
                foundResumeValues,
                resumeData: res.items,
                emptyResume: false,
                resumeValues,
                isLoading: false,
                otherResumePdfName:
                  successName === "OtherResumeSuccessMessage"
                    ? fileData.name
                    : prevState.otherResumePdfName,
                linkedinPdfName:
                  successName === "LinkedInResumeSuccessMessage"
                    ? fileData.name
                    : prevState.linkedinPdfName
              }));
              handleContinueClick();
            })
            .catch(() => {
              const info = {
                message: "Unable to upload to file.",
                status: "error"
              };
              dispatch(notificationAction(info));
              setState(prevState => ({ ...prevState, isLoading: false }));
            });
        };
      })(fileData);

      reader.readAsDataURL(fileData);
    } else {
      const info = {
        message: "Please select pdf file.",
        status: "error"
      };
      dispatch(notificationAction(info));
      console.log("else");
    }
  };

  const generateCards = () => {
    const {
      date,
      resumeValues,
      dateValue,
      isDatePickerOpen,
      availabilities,
      otherResumePdfName,
      currentSlide
    } = state;
    const uploadProps = {
      beforeUpload: file => {
        uploadLinkedinPdf(file, "OtherResumeSuccessMessage");
        return false;
      },
      fileList: [],
      maxCount: 1
    };
    const isMobileView = window.innerWidth < 767;
    const slideHtml = [
      <Slide key={1} className="js_slide_1" testId="ipro-phillip-popup-slide-1">
        {(currentSlide === 0 || !isMobileView) && (
          <>
            <Title level={3}>
              {htmlParser(labels.SEARCHER_PHILLIP_WELCOME_LABEL)}&nbsp;
              {FirstName}!
            </Title>
            <Text className="description-text bold-primary only-first">
              {htmlParser(labels.iProOnboardingWelcomeMessage)}
            </Text>
          </>
        )}
      </Slide>,
      <Slide key={2} className="js_slide_3 slide-2">
        {(currentSlide === 1 || !isMobileView) && (
          <>
            <Title level={3}>
              {htmlParser(labels.iProOnboardingResumeUploadTitle)}
            </Title>
            <Text className="description-text">
              {htmlParser(labels.iProOnboardingResumeUploadMessage)}
            </Text>
            <br />
            <Upload {...uploadProps}>
              <Button
                className="upload-btn"
                size="middle"
                type="primary"
                style={
                  state?.emptyResume
                    ? {
                        border: "1px solid red",
                        background: "red"
                      }
                    : null
                }
              >
                Upload PDF CV/Resume
              </Button>
            </Upload>
            {otherResumePdfName && (
              <>
                <br />
                <Text className="description-text">{otherResumePdfName}</Text>
              </>
            )}
          </>
        )}
      </Slide>,
      <Slide key={3} className="js_slide_4 slide-3">
        {(currentSlide === 2 || !isMobileView) && (
          <>
            <Row gutter={4} className="posRelative">
              {resumeValues.map(item => (
                <Col
                  key={item.id}
                  md={12}
                  xs={24}
                  className={`js_item ${item.active ? "active" : ""}`}
                >
                  <Text className="description-text">
                    {renderResumeValues(item)}
                  </Text>
                </Col>
              ))}
            </Row>
            <div className="js_query mt-2">
              <Text className="description-text" style={{ fontWeight: 600 }}>
                {labels.iProOnboardingAvailabilityQuestion}
              </Text>
              <br />
              <div className="js_selection">
                {map(availabilities, item => (
                  <div
                    key={item.id}
                    className={`js_sel_item ${item.isActive && "active"}`}
                    onClick={() => handlAvalibalitySelect(item)}
                    data-testid={`ipro-phillip-avalibility-${item.name}`}
                  >
                    <Text className="description-text">
                      {htmlParser(item.value)}
                    </Text>
                  </div>
                ))}
                <DatePickerField
                  name="DatePicker"
                  minDate={moment()}
                  todayButton="Today"
                  dateFormat="MM/DD/YYYY"
                  selected={date}
                  value={dateValue ? dateValue : date.format("L").toString()}
                  onChange={(date, e) => {
                    e.stopPropagation();
                    setState(prevState => ({
                      ...prevState,
                      date,
                      dateValue: "",
                      isDatePickerOpen: false
                    }));
                  }}
                  onBlur={() => {
                    setState(prevState => ({
                      ...prevState,
                      isDatePickerOpen: false
                    }));
                  }}
                  onInputClick={() => {
                    setState(prevState => ({
                      ...prevState,
                      isDatePickerOpen: true
                    }));
                  }}
                  customInput={<CustomInput />}
                  open={isDatePickerOpen}
                  stop
                />
              </div>
            </div>
          </>
        )}
      </Slide>
    ];
    return compact(slideHtml);
  };

  const getSlideImage = () => {
    switch (state.currentSlide) {
      case 0:
        return <img className="phillip-img" src={Img01} />;
      case 1:
        return <img className="phillip-img" src={Img02} />;
      case 2:
        return <img className="phillip-img" src={Img03} />;
      case 3:
        return <img className="phillip-img" src={Img04} />;
      default:
        return <img className="phillip-img" src={Img01} />;
    }
  };

  const { currentSlide, isLoading } = state;

  return (
    <div className="phillip-component ipro-phillip">
      <Modal
        className={`phillip-popup onboard-${currentSlide + 1}`}
        open={open}
      >
        {isLoading && <LoadingMask text="" />}
        <Header onCloseModal={onSkip} labels={labels} />
        {getSlideImage()}
        <Modal.Body>
          <Carousel
            swipeable
            showStatus={false}
            showArrows={false}
            showThumbs={false}
            selectedItem={currentSlide}
            onChange={updateCurrentSlide}
          >
            {generateCards()}
          </Carousel>
          <div className="phillip-footer">
            {currentSlide !== 2 && (
              <button
                type="primary"
                data-testid="ipro-phillip-continue-button"
                className="continue-button"
                onClick={handleContinueClick}
              >
                {labels.searcherOnboardingContinue}
              </button>
            )}
            {currentSlide === 2 && (
              <button
                type="primary"
                data-testid="ipro-phillip-finish-button"
                className="finish-button"
                onClick={handleFinishClick}
              >
                {labels.searcherOnboardingFinished}
              </button>
            )}
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default IProPhillip;
