import { useState, useEffect } from "react";
import { connect } from "react-redux";
import { useNavigate } from "react-router-dom";
import { Checkbox } from "antd";
import { EyeInvisibleOutlined, EyeTwoTone } from "@ant-design/icons";
import Input from "antd/lib/input";
import { loginAuth } from "./loginAction";
import { publicRoutes, appsRoutes } from "../../../../Routes/routing";
import { isValidEmail } from "../../../../utilities/helpers";
import { notificationAction } from "../../../../actions/notification";
import {
  RegisterThirdPartyApi,
  genetrateTokenApi,
  getForgotPasswordApi,
} from "../../leadPageApi";
import classes from "./mobile-login.module.scss";
import Notification from "../../../../common/Notification/Notification";
import Button from "../../../../common/Button/Button";

import Img6 from "../../../../assets/images/products/6.png";
import tictellImg from "../../../../assets/images/tictell/tictell.png";
import NextStepImg from "../../../../assets/images/apps-icons/NextStep.png";
import assortImg from "../../../../assets/images/apps-icons/assort.png";
import embarkImg from "../../../../assets/images/apps-icons/embark.png";
import headsupImg from "../../../../assets/images/apps-icons/headsup.png";
import phillipSearcherImg from "../../../../assets/images/apps-icons/phillipSearcher.png";
export function CustomInput({
  suffix,
  placeholder,
  Password,
  type,
  invalid,
  ...rest
}) {
  return (
    <div className={`${classes.customInput} tictell-input`}>
      {!Password ? (
        <Input
          placeholder={placeholder}
          suffix={suffix}
          type={type}
          bordered={false}
          className={`${classes.input} ${invalid ? classes.inValid : ""}`}
          {...rest}
        />
      ) : (
        <Input.Password
          placeholder="Password"
          iconRender={(visible) =>
            visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
          }
          bordered={false}
          className={`${classes.input} ${invalid ? classes.inValid : ""}`}
          {...rest}
        />
      )}
    </div>
  );
}

const MobileLoginForm = ({
  labels,
  notification,
  loginAuth,
  notificationAction,
  showResendPopUp,
}) => {
  const [state, setState] = useState({
    email: "",
    password: "",
    appType: "snapshot",
    isLoading: false,
    isFacebookConnected: false,
    IgeDomains: [],
    dialogMessage: "",
    showSignup: false,
    isRemembered: false,
    errorMessage: "",
    onForgot: false,
    invalidemail: false,
    invalidpassword: false,
  });

  const navigate = useNavigate();

  useEffect(() => {
    let appType = JSON.parse(sessionStorage.getItem("appType"));
    let loginCreds = JSON.parse(window.sessionStorage.getItem("loginCreds"));
    if (loginCreds !== null) {
      setState((prevState) => ({
        ...prevState,
        email: loginCreds.email,
        password: loginCreds.password,
        isRemembered: true,
      }));
    }

    setState((prevState) => ({
      ...prevState,
      appType: appType,
    }));
  }, []);

  const onRememberChange = () => {
    setState((prevState) => ({
      ...prevState,
      isRemembered: !prevState.isRemembered,
    }));
  };

  const handleFacebookLoginSuccess = (response) => {
    const { id, name, email, first_name, last_name } = response.profile;
    const user = {
      Name: name,
      FirstName: first_name,
      LastName: last_name,
      ExternalId: id,
      Email: email,
      RegistrationTypeId: 2,
      OAuthProviderId: id,
      IsLoginByMobile: false,
    };
    RegisterThirdPartyApi(user)
      .then((response) => {
        const info = {
          message: response.message,
          status: response.success ? "success" : "error",
        };
        notificationAction(info);
      })
      .catch((error) => console.log("Error in third party api ", error));
  };

  const handleFacebookFailure = (err) => {};

  const handleKeyPress = (event) => {
    if (event.key === "Enter") {
      handleSubmit();
    }
  };

  const handleIgeActionClicks = (domain) => {
    const { email, password } = state;
    if (domain === "Dismiss") {
      setState((prevState) => ({
        ...prevState,
        dialogMessage: "",
        IgeDomains: [],
      }));
      return;
    }
    if (domain === "Current") {
      setState((prevState) => ({
        ...prevState,
        dialogMessage: "",
        IgeDomains: [],
      }));
      loginToCore(email, password);
      return;
    }
    genetrateTokenApi(domain, email).then((res) => {
      if (res.success) {
        var token = res.items;
        if (token) {
          window.location.replace(
            domain +
              "/#" +
              publicRoutes.redirectedfrom.path +
              `?redirectedFrom=${token}`
          );
        }
      }
    });
  };

  const handleForgetPasswordChange = (value) => {
    setState((prevState) => ({
      ...prevState,
      onForgot: value,
    }));
  };

  const handleSubmit = () => {
    const { email, password } = state;
    let isError = false;
    if (!isValidEmail(email)) {
      isError = true;
      setState((prevState) => ({
        ...prevState,
        invalidemail: true,
      }));
    }
    if (!password) {
      isError = true;
      setState((prevState) => ({
        ...prevState,
        invalidpassword: true,
      }));
    }
    if (isError) {
      const info = {
        message: labels.LOGIN_VALIDATION_ERROR_MESSAGE,
        status: "error",
      };
      notificationAction(info);
      return;
    }
    loginToCore(email, password);
  };

  const handleFormFieldChange = (e) => {
    const { name, value } = e.target;
    if (!value || (name === "email" && !isValidEmail(value))) {
      setState((prevState) => ({
        ...prevState,
        [`invalid${name}`]: true,
        [name]: value,
      }));
      return;
    } else {
      setState((prevState) => ({
        ...prevState,
        [`invalid${name}`]: false,
        [name]: value,
      }));
    }
  };

  const handleFacebookClick = () => {
    setState((prevState) => ({
      ...prevState,
      isFacebookConnected: !prevState.isFacebookConnected,
    }));
  };

  const loginToCore = (email, password) => {
    const loginModel = {
      email,
      password,
    };
    setState((prevState) => ({ ...prevState, isLoading: true }));
    loginAuth(loginModel).then((response) => {
      if (response.success) {
        const { appType, isRemembered } = state;
        let redirectAppPath = "";

        switch (appType) {
          case "snapshot":
            redirectAppPath = appsRoutes.snapshot.path;
            break;
          case "tictell":
            redirectAppPath = appsRoutes.tictell.path;
            break;
          case "searcherPhilip":
            redirectAppPath = appsRoutes.philip.path;
            break;
          case "iproPhilip":
            redirectAppPath = appsRoutes.iproPhilip.path;
            break;
          case "sense":
            redirectAppPath = appsRoutes.sense.path;
            break;
          case "headsup":
            redirectAppPath = appsRoutes.headsup.path;
            break;
          case "embark":
            redirectAppPath = appsRoutes.embark.path;
            break;
          case "assort":
            redirectAppPath = appsRoutes.assort.path;
            break;
          case "nextStep":
            redirectAppPath = appsRoutes.nextstep.path;
            break;
          default:
            redirectAppPath = publicRoutes.default.path;
            break;
        }
        setState((prevState) => ({ ...prevState, isLoading: false }));
        if (isRemembered) {
          sessionStorage.setItem("loginCreds", JSON.stringify(loginModel));
        } else {
          sessionStorage.removeItem("loginCreds");
        }

        navigate(redirectAppPath);
      } else {
        setState((prevState) => ({ ...prevState, isLoading: false }));
        if (response.items && response.items.IsEmailAvailable) {
          setState((prevState) => ({
            ...prevState,
            errorMessage: response.message,
          }));
          showResendPopUp(response.message);
        } else {
          const info = {
            message: response.message,
            status: "error",
          };
          setState((prevState) => ({
            ...prevState,
            errorMessage: response.message,
          }));
          notificationAction(info);
        }
      }
    });
  };

  const handleForgotPassword = () => {
    const { email } = state;
    if (!email || !isValidEmail(email)) {
      const info = {
        message: labels.FORGOT_PASSWORD_VALIDATION_ERROR_MESSAGE,
        status: "error",
      };
      notificationAction(info);
      setState((prevState) => ({
        ...prevState,
        invalidemail: true,
      }));
      return;
    }
    getForgotPasswordApi(email)
      .then((response) => {
        const status = response.success ? "success" : "error";
        const info = {
          message: response.message,
          status,
        };
        notificationAction(info);
      })
      .catch((error) => console.log(error));
  };

  const onSignupClick = () => {
    setState((prevState) => ({
      ...prevState,
      showSignup: !prevState.showSignup,
    }));
  };

  const renderImage = () => {
    const { appType } = state;
    let imgData = null;
    switch (appType) {
      case "snapshot":
        imgData = <img src={Img6} alt={appType} className="TickTel" />;
        break;
      case "tictell":
        imgData = <img src={tictellImg} alt={appType} className="TickTel" />;
        break;
      case "nextStep":
        imgData = <img src={NextStepImg} alt={appType} className="TickTel" />;
        break;
      case "assort":
        imgData = <img src={assortImg} alt={appType} className="TickTel" />;
        break;
      case "embark":
        imgData = <img src={embarkImg} alt={appType} className="TickTel" />;
        break;
      case "headsup":
        imgData = <img src={headsupImg} alt={appType} className="TickTel" />;
        break;
      case "sense":
      case "iproPhilip":
        imgData = (
          <img src={phillipSearcherImg} alt={appType} className="TickTel" />
        );
        break;
      case "searcherPhilip":
        imgData = (
          <img src={phillipSearcherImg} alt={appType} className="TickTel" />
        );
        break;
      default:
        break;
    }
    return imgData;
  };

  const {
    email,
    password,
    invalidemail,
    invalidpassword,
    onForgot,
    isLoading,
    isRemembered,
    errorMessage,
    showSignup,
  } = state;

  return (
    <div className={classes.tictellWrapper}>
      {isLoading && (
        <div id="loader-wrapper">
          <div className="loader-container">
            <div className="loader">
              <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
                <defs>
                  <filter id="goo">
                    <feGaussianBlur
                      in="SourceGraphic"
                      stdDeviation="6"
                      result="blur"
                    />
                    <feColorMatrix
                      in="blur"
                      mode="matrix"
                      values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                      result="goo"
                    />
                    <feBlend in="SourceGraphic" in2="goo" />
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </div>
      )}
      {notification.message && (
        <Notification
          status={notification.status}
          className={`${notification.className}`}
        >
          {notification.message}
        </Notification>
      )}
      <div className={classes.wrapper}>
        <div className={classes.logo}>{renderImage()}</div>
        {onForgot ? (
          <>
            <h1>{labels.FORGOT_PASS_FORM_HEAD}</h1>
            {errorMessage && (
              <div className={`text-danger ${classes.errorBadge}`}>
                <span>{errorMessage}</span>
              </div>
            )}
            <CustomInput
              type="email"
              suffix={<img src={`../../../../assets/images/svg/Message.svg`} />}
              name="email"
              invalid={invalidemail}
              id="Email"
              placeholder={labels.loginEmail}
              value={email}
              onChange={handleFormFieldChange}
              onKeyPress={handleKeyPress}
              autoComplete="new-email"
            />
            <p></p>
            <Button onClick={handleForgotPassword}>Submit</Button>
            <p></p>
            <Button onClick={() => handleForgetPasswordChange(false)}>
              {labels.loginLoginButton}
            </Button>
          </>
        ) : (
          <>
            <h1>Log in</h1>
            {errorMessage && (
              <div className={`text-danger ${classes.errorBadge}`}>
                <span>{errorMessage}</span>
              </div>
            )}
            <CustomInput
              type="email"
              suffix={<img src={`../../../../assets/images/svg/Message.svg`} />}
              name="email"
              invalid={invalidemail}
              id="Email"
              placeholder={labels.loginEmail}
              value={email}
              onChange={handleFormFieldChange}
              onKeyPress={handleKeyPress}
              autoComplete="new-email"
            />

            <div className={classes.inputPass}>
              <CustomInput
                Password={true}
                id="Password"
                name="password"
                type="password"
                placeholder={labels.loginPassword}
                value={password}
                onChange={handleFormFieldChange}
                onKeyPress={handleKeyPress}
                autoComplete="new-password"
                invalid={invalidpassword}
              />
            </div>
            <div className={classes.forget}>
              <Checkbox
                onChange={onRememberChange}
                checked={isRemembered}
                className="tictell-login-checkbox"
              >
                <span className={classes.rememberMeTxt}>Remember Me</span>
              </Checkbox>
              <div>
                <a onClick={() => handleForgetPasswordChange(true)}>
                  Forget Password?
                </a>
              </div>
            </div>
            <Button onClick={handleSubmit}>Log in</Button>
            <p></p>
            {/* <p>OR</p>
            <Button onClick={onSignupClick}>Sign Up</Button> */}
            {showSignup && (
              <div className={`${classes.infoBadge}`}>
                <span>
                  This app requires a ProDoo account, please visit
                  <a href="https://prodoo.com/" target="_blank">
                    prodoo.com
                  </a>
                  to register.
                </span>
              </div>
            )}
          </>
        )}
      </div>
      <div>
        <div className={classes.powered}>Powered By</div>
        <div className={classes.footer}>
          <img className="TickTel" src={`../../../../assets/images/logo.png`} />
        </div>
      </div>
    </div>
  );
};

const mapStateToProps = ({ systemLabel, notification }) => {
  const { labels } = systemLabel;
  return { labels, notification };
};

const mapActionToProps = { loginAuth, notificationAction };
export default connect(mapStateToProps, mapActionToProps)(MobileLoginForm);
