import { useSelector } from "react-redux";
import { RolesLevels } from "../../../utilities-alpha/constant";
import { Profiles } from "../constant";
import { ShowMoreSection } from "./LanguagesSection";
// Industry Section
const IndustrySection = ({ userData, openDrawer }) => {
  const resume = useSelector(state => state.systemLabel.labels?.resume);
  return (
    <ShowMoreSection
      title={resume?.industry}
      items={userData?.items?.Industries || []}
      openDrawer={openDrawer}
      addEditAction={Profiles.industry}
      itemKeyId="IndustryId"
      itemValueKey="IndustryValue"
      viewMore
      emptyMessage={resume?.industryEmptyMessage}
      addButtonAction={Profiles.industry}
      renderItem={ind => (
        <div
          key={ind.IndustryId}
          className="bg-[#F3F1FD] border border-[#EAE5FC] rounded-lg px-4 py-3 text-sm text-[#343333]"
        >
          <div className="text-[13px]">{ind.IndustryValue}</div>
          <div className="text-[#878787] text-xs">
            {RolesLevels[ind.ExperienceLevel]}
          </div>
        </div>
      )}
    />
  );
};
export default IndustrySection;
