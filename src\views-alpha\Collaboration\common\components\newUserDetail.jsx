import { Button } from "antd";
import CloseIcon from "../../../../assets-alpha/images/svg/close-modal.svg?react";
import ArrowDown from "../../../../assets-alpha/images/svg/arrow-down-icon.svg?react";
import moment from "moment";
const NewUserDetail = ({
  labels,
  onCollaborationSave,
  setCreateNewCollaboration,
  onClearSelectedIpro,
  setActiveShowMore,
  setShowUserDetail,
  jobInviteData,
  selectedCurrency
}) => {
  const Data = jobInviteData[0];
  return (
    <div className="flex flex-col !w-full md:pl-10 md:gap-7 gap-4">
      <div className="hidden md:flex !w-full justify-between">
        <h1 className="!m-0 !text-[20px] text-[#343333] font-semibold">
          {labels?.Preview_Label}
        </h1>

        <CloseIcon
          className="pointer"
          onClick={() => {
            setCreateNewCollaboration(false);
            setShowUserDetail(false);
            onClearSelectedIpro();
            setActiveShowMore(false);
          }}
        />
      </div>
      <div className="flex md:hidden h-[64px] border-b border-[#EAE5FC] items-center pl-4 pr-4 !w-full justify-between">
        <h1 className="!m-0 !text-[20px] text-[#343333] font-semibold">
          {labels?.Preview_Label}
        </h1>

        <ArrowDown
          className="pointer"
          onClick={() => {
            setCreateNewCollaboration(false);
            setShowUserDetail(false);
            onClearSelectedIpro();
            setActiveShowMore(false);
          }}
        />
      </div>
      <div className="flex flex-col rounded-xl border border-[#BDDDF5] [box-shadow:1px_1px_38px_0px_#BDDDF526_inset,-1px_-1px_38px_0px_#BDDDF526_inset] ml-4 mr-4 md:ml-0 md:mr-0">
        <h1 className="flex w-full justify-center !m-0 !text-[20px] text-[#343333] font-semibold !mt-6">
          {labels?.Section2HiringCollaborationTitle}
        </h1>
        <div className="h-full mt-2 md:!mt-7 ml-3 mr-3 md:!ml-[22px] md:!mr-[22px]">
          <div className="flex items-center h-10 border-b border-[#EAE5FC] p-4 gap-[12.5px]">
            <label className="w-[83px] font-normal text-[13px] text-[#878787]">
              {labels?.TITLE_LABEL}
            </label>
            <span className="h-[22.5px] border-l border-[#EAE5FC]"></span>
            <p className="text-[13px] text-[#343333] font-normal">
              {Data?.RequestName ? Data?.RequestName : "N/A"}
            </p>
          </div>
          <div className="flex items-center h-10 border-b border-[#EAE5FC] p-4 gap-[12.5px]">
            <label className="w-[83px] font-normal text-[13px] text-[#878787]">
              {labels?.IproCollaborationStartDateLabel}
            </label>
            <span className="h-[22.5px] border-l border-[#EAE5FC]"></span>
            <p className="text-[13px] text-[#343333] font-normal">
              {Data?.StartDate
                ? moment(Data?.StartDate).format("DD-MM-YYYY")
                : "N/A"}
            </p>
          </div>
          <div className="flex items-center h-10 border-b border-[#EAE5FC] p-4 gap-[12.5px]">
            <label className="w-[83px] font-normal text-[13px] text-[#878787]">
              {labels?.IproCollaborationDurationLabel}
            </label>
            <span className="h-[22.5px] border-l border-[#EAE5FC]"></span>
            <p className="text-[13px] text-[#343333] font-normal">
              {Data?.Duration ? `${Data.Duration} ${Data.DurationType}` : "N/A"}
            </p>
          </div>
          <div className="flex items-center h-10 border-b border-[#EAE5FC] p-4 gap-[12.5px]">
            <label className="w-[83px] font-normal text-[13px] text-[#878787]">
              {labels?.collHourlyFee}
            </label>
            <span className="h-[22.5px] border-l border-[#EAE5FC]"></span>
            <p className="text-[13px] text-[#343333] font-normal">
              {Data?.HourlyRate
                ? `${Data?.HourlyRate} ${selectedCurrency?.Name}`
                : "N/A"}
            </p>
          </div>
          <div className="flex items-center h-10 border-b border-[#EAE5FC] p-4 gap-[12.5px]">
            <label className="w-[83px] font-normal text-[13px] text-[#878787]">
              {labels?.SEARCHER_NAVIGATION_HEADSUP_OPPORTUNITY_LABEL}
            </label>
            <span className="h-[22.5px] border-l border-[#EAE5FC]"></span>
            <p className="text-[13px] text-[#343333] font-normal">
              {Data?.label ? Data?.label : "N/A"}
            </p>
          </div>
          <div className="flex items-center h-10 border-b border-[#EAE5FC] p-4 gap-[12.5px]">
            <label className="w-[83px] font-normal text-[13px] text-[#878787]">
              {labels?.colliPro}
            </label>
            <span className="h-[22.5px] border-l border-[#EAE5FC]"></span>
            <p className="text-[13px] text-[#343333] font-normal">
              {Data?.IProName ? Data?.IProName : "N/A"}
            </p>
          </div>
          <div className="flex items-start min-h-10 p-4 gap-[12.5px]">
            <label className="!w-[83px] font-normal text-[13px] text-[#878787]">
              {labels?.collDescription}
            </label>
            <span className="h-[22.5px] border-l border-[#EAE5FC]"></span>
            <p className="text-[13px] max-w-[215px] text-[#343333] font-normal">
              {Data?.Decription ? Data?.Decription : "N/A"}
            </p>
          </div>
          <div className="flex justify-center items-center mt-[64px] mb-[66px]">
            <div className="flex justify-between items-cente w-[272px] h-[39px]">
              <div className="flex flex-col items-center justify-between min-w-[90px] h-full border-b-[0.5px] border-[#878787]">
                <p>{labels?.SignUpComboSearcher}</p>
                <p
                  className="text-[#02CAA8] font-bold text-sm leading-[100%]"
                  style={{ fontFamily: "Caveat, cursive" }}
                >
                  {Data?.RecruiterName ? Data?.RecruiterName : ""}
                </p>
              </div>
              <div className="flex flex-col items-center justify-between min-w-[90px] h-full border-b-[0.5px] border-[#878787]">
                <p>{labels?.colliPro}</p>
                <p
                  className="text-[#8E81F5] font-bold text-sm leading-[100%]"
                  style={{ fontFamily: "Caveat, cursive" }}
                >
                  {Data?.IProName ? Data?.IProName : ""}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="hidden md:flex !w-full justify-end md:!mr-0!  !-ml-4">
        <Button
          type="primary"
          className="!w-fill"
          onClick={onCollaborationSave}
        >
          {labels.Send_Contract_Label}
        </Button>
      </div>
      <div className="md:!hidden !pl-4 !pr-4 !flex !items-center !justify-center !w-full fixed bottom-0 h-[70px] bg-white border-t-[0.5px] border-[#EAE5FC]">
        <Button
          type="primary"
          className="!w-full"
          onClick={onCollaborationSave}
        >
          {labels.Send_Contract_Label}
        </Button>
      </div>
    </div>
  );
};

export default NewUserDetail;
