import { formatDate } from "../../../../utilities/helpers";
import { <PERSON><PERSON>, Rate } from "antd";
import <PERSON><PERSON> from "lottie-react";
import SuccessFront from "../../../../assets-alpha/images/animation/SuccessFront.json";
import SuccessBack from "../../../../assets-alpha/images/animation/SuccessBackground.json";
import { StarOutlined } from "@ant-design/icons";
import { useSelector } from "react-redux";
const FeedbackModal = ({
  selectedCollaboration,
  labels,
  IsSearcher,
  handleYesClick,
  closeCollaboration
}) => {
  const { allCurrenciesList } = useSelector(state => ({
    ...state.createNewOpportunity
  }));
  const currType = allCurrenciesList?.find(item => {
    return item?.CurrencyId === Number(selectedCollaboration?.HourlyRateType);
  });
  return (
    <>
      <div className="flex flex-col md:flex-row w-full border  rounded-2xl md:p-0 p-4 gap-4 md:gap-0 pb-0 md:pb-4 border-none md:mb-0 mb-8">
        <div className="order-2 md:order-1 flex flex-col w-full md:w-[46%] pl-6 pr-6 pt-4  md:pt-[36px] pb-[36px] border-[#BDDDF5] md:rounded-bl-2xl md:rounded-tl-2xl rounded-2xl   [box-shadow:1px_1px_38px_0px_#BDDDF526_inset,-1px_-1px_38px_0px_#BDDDF526_inset]">
          <div className="h-[33px] w-full justify-center flex items-center">
            <h1 className="!m-0 !font-semibold !ext-[#343333] !text-sm">
              Contract Summary
            </h1>
          </div>
          <div className="flex flex-col w-full  mt-[11px]">
            <div className="flex w-full items-center h-10 border-b border-[#EAE5FC] p-4 gap-[12.5px]">
              <label className="w-[83px] font-normal text-[13px] text-[#878787] overflow-hidden whitespace-nowrap text-ellipsis">
                {labels?.TITLE_LABEL}
              </label>
              <span className="h-[22.5px] border-l border-[#EAE5FC]"></span>
              <p className="flex w-full text-[13px] text-[#343333] font-normal overflow-hidden whitespace-nowrap text-ellipsis">
                {selectedCollaboration?.company?.CompanyName
                  ? selectedCollaboration?.company?.CompanyName
                  : "N/A"}
              </p>
            </div>
            <div className="flex w-full items-center h-10 border-b border-[#EAE5FC] p-4 gap-[12.5px]">
              <label className="w-[83px] font-normal text-[13px] text-[#878787] overflow-hidden whitespace-nowrap text-ellipsis">
                {labels?.IproCollaborationStartDateLabel}
              </label>
              <span className="h-[22.5px] border-l border-[#EAE5FC]"></span>
              <p className="flex w-full text-[13px] text-[#343333] font-normal overflow-hidden whitespace-nowrap text-ellipsis">
                {selectedCollaboration?.StartDate
                  ? formatDate(selectedCollaboration?.StartDate)
                  : "N/A"}
              </p>
            </div>
            <div className="flex w-full items-center h-10 border-b border-[#EAE5FC] p-4 gap-[12.5px]">
              <label className="w-[83px] font-normal text-[13px] text-[#878787] overflow-hidden whitespace-nowrap text-ellipsis">
                {labels?.IproCollaborationDurationLabel}
              </label>
              <span className="h-[22.5px] border-l border-[#EAE5FC]"></span>
              <p className="flex w-full text-[13px] text-[#343333] font-normal overflow-hidden whitespace-nowrap text-ellipsis">
                {selectedCollaboration?.Duration
                  ? selectedCollaboration?.Duration
                  : "N/A"}
              </p>
            </div>
            <div className="flex w-full items-center h-10 border-b border-[#EAE5FC] p-4 gap-[12.5px]">
              <label className="w-[83px] font-normal text-[13px] text-[#878787] overflow-hidden whitespace-nowrap text-ellipsis">
                {labels?.collHourlyFee}
              </label>
              <span className="h-[22.5px] border-l border-[#EAE5FC]"></span>
              <p className="flex gap-1 w-full text-[13px] text-[#343333] font-normal overflow-hidden whitespace-nowrap text-ellipsis">
                {selectedCollaboration?.HourlyRate}
                <span>{currType?.label}</span>
              </p>
            </div>
            <div className="flex w-full items-center h-10 border-b border-[#EAE5FC] p-4 gap-[12.5px]">
              <label className="w-[83px] font-normal text-[13px] text-[#878787] overflow-hidden whitespace-nowrap text-ellipsis">
                {labels?.SEARCHER_NAVIGATION_HEADSUP_OPPORTUNITY_LABEL}
              </label>
              <span className="h-[22.5px] border-l border-[#EAE5FC]"></span>
              <p className="flex w-full text-[13px] text-[#343333] font-normal overflow-hidden whitespace-nowrap text-ellipsis">
                {selectedCollaboration?.Title
                  ? selectedCollaboration?.Title
                  : "N/A"}
              </p>
            </div>
            <div className="flex w-full items-center h-10 border-b border-[#EAE5FC] p-4 gap-[12.5px]">
              <label className="w-[83px] font-normal text-[13px] text-[#878787] overflow-hidden whitespace-nowrap text-ellipsis">
                {labels?.colliPro}
              </label>
              <span className="h-[22.5px] border-l border-[#EAE5FC]"></span>
              <p className="flex w-full text-[13px] text-[#343333] font-normal overflow-hidden whitespace-nowrap text-ellipsis">
                {selectedCollaboration?.IProName
                  ? selectedCollaboration?.IProName
                  : "N/A"}
              </p>
            </div>
            <div className="flex w-full items-center min-h-10 h-auto p-4 gap-[12.5px]">
              <p className="flex w-full text-[13px] text-[#343333] font-normal overflow-hidden whitespace-nowrap text-ellipsis">
                {selectedCollaboration?.userdetail?.Detail
                  ? selectedCollaboration?.userdetail?.Detail
                  : "N/A"}
              </p>
            </div>
            <div className="flex justify-center items-center mt-[35px] ">
              <div className="flex gap-4 items-center min-w-[199px] h-[39px]">
                <div className="flex flex-col items-center justify-between min-w-[90px] h-full border-b-[0.5px] border-[#878787]">
                  <p>{labels?.SignUpComboSearcher}</p>
                  <p
                    className="text-[#02CAA8] font-bold text-sm leading-[100%]"
                    style={{ fontFamily: "Caveat, cursive" }}
                  >
                    {selectedCollaboration?.RecruiterName
                      ? selectedCollaboration?.RecruiterName
                      : ""}
                  </p>
                </div>
                <div className="flex flex-col items-center justify-between min-w-[90px] h-full border-b-[0.5px] border-[#878787]">
                  <p>{labels?.colliPro}</p>
                  <p
                    className="text-[#8E81F5] font-bold text-sm leading-[100%]"
                    style={{ fontFamily: "Caveat, cursive" }}
                  >
                    {selectedCollaboration?.IProName
                      ? selectedCollaboration?.IProName
                      : "N/A"}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="order-1 md:order-2 flex flex-col w-full md:w-[54%] items-center md:mt-8 mt-0 md:ml-6 md:mr-6 md:mb-6">
          <div className="w-[205px] h-[190px] relative">
            <div className="absolute z-0">
              <Lottie animationData={SuccessBack} loop={true} />
            </div>
            <div className="absolute z-10">
              <Lottie animationData={SuccessFront} loop={true} />
            </div>
          </div>
          <div className="flex w-full items-center flex-col gap-2 text-center mt-[17px]">
            <h1 className="!m-0 !text-[#343333] !text-2xl !font-semibold !leading-9">
              {labels?.Greeting_Message_Contract_End} <span> </span>
              <span className="text-[#8E81F5]">
                {IsSearcher
                  ? selectedCollaboration?.RecruiterName
                  : selectedCollaboration?.IProName}
              </span>
            </h1>
            <p className="text-[#343333] font-medium text-[16px]">
              {labels?.Contract_Complete_Success_Message}
            </p>
          </div>
          <div className="flex flex-col justify-between w-full items-center mt-9 h-[76px]">
            <label className="text-[#878787] text-[16px] font-medium">
              {IsSearcher
                ? labels?.Rate_iPro_Label
                : labels?.Rate_Recruiter_Label}
            </label>
            <Rate character={<StarOutlined />} allowHalf />
          </div>
          <div className="flex flex-col w-full h-[88px] mt-[46px]">
            <Button
              className="w-full !h-10"
              type="primary"
              onClick={() => {
                closeCollaboration();
              }}
            >
              {labels?.USER_SETTING_CHANGE_PASSWORD_SUBMIT_BUTTON}
            </Button>
            <span
              className="flex w-full justify-center mt-5 font-medium text-[#8E81F5] text-sm cursor-pointer"
              onClick={() => {
                handleYesClick();
              }}
            >
              {labels?.Give_Feedback_Label}
            </span>
          </div>
        </div>
      </div>
    </>
  );
};

export default FeedbackModal;
