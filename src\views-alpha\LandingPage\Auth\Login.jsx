import { But<PERSON>, Checkbox, Divider, Input } from "antd";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { notificationAction } from "../../../actions/notification";
import GoogleIcon from "../../../assets-alpha/images/svg/google-icon.svg?react";
import ArrowLeft from "../../../assets-alpha/images/svg/arrow-left.svg?react";
import { privateRoutes, publicRoutes } from "../../../Routes/routing";
import { isValidEmail } from "../../../utilities/helpers";
import Slider from "./components/Slider";
import { loginAuth } from "./loginAction";
import { useGoogleLogin } from "@react-oauth/google";

const Login = () => {
  const dispatch = useDispatch();
  const labels = useSelector(state => state.systemLabel.labels);
  const [state, setState] = useState({
    email: "",
    password: "",
    appType: "snapshot",
    isLoading: false,
    isRemembered: false,
    errorMessage: "",
    invalidemail: false,
    invalidpassword: false
  });

  const navigate = useNavigate();

  useEffect(() => {
    const appType = JSON.parse(sessionStorage.getItem("appType")) || "snapshot";
    const loginCreds = JSON.parse(sessionStorage.getItem("loginCreds"));

    if (loginCreds) {
      setState(prev => ({
        ...prev,
        email: loginCreds.email,
        password: loginCreds.password,
        isRemembered: true,
        appType
      }));
    } else {
      setState(prev => ({ ...prev, appType }));
    }
  }, []);

  const handleFormFieldChange = e => {
    const { name, value } = e.target;
    setState(prev => ({
      ...prev,
      [name]: value,
      [`invalid${name}`]: name === "email" ? !isValidEmail(value) : !value
    }));
  };

  const handleSubmit = () => {
    const { email, password } = state;
    const emailValid = isValidEmail(email);
    const passwordValid = password.length >= 6;

    if (!emailValid || !passwordValid) {
      setState(prev => ({
        ...prev,
        invalidemail: !emailValid,
        invalidpassword: !passwordValid
      }));
      dispatch(
        notificationAction({
          message: !emailValid
            ? labels?.Email_Error_Message
            : labels?.invalidPasswordForRegisteration,
          status: "error"
        })
      );
      return;
    }

    loginToCore(email, password);
  };

  const loginToCore = (
    email,
    password,
    socialLogin = false,
    googleCredentials = {}
  ) => {
    const loginModel = { email, password };
    setState(prev => ({ ...prev, isLoading: true }));

    dispatch(loginAuth(loginModel)).then(response => {
      if (response.success) {
        const { isRemembered } = state;
        const redirectAppPath = privateRoutes.dashboard.path;

        setState(prev => ({ ...prev, isLoading: false }));

        if (isRemembered) {
          sessionStorage.setItem("loginCreds", JSON.stringify(loginModel));
        } else {
          sessionStorage.removeItem("loginCreds");
        }

        navigate(redirectAppPath);
      } else {
        if (socialLogin && response?.message?.includes("incorrect")) {
          navigate(publicRoutes.signup.path, {
            state: { credentials: googleCredentials }
          });
          return;
        }

        setState(prev => ({ ...prev, isLoading: false }));
        dispatch(
          notificationAction({
            message: response.message || "Login failed",
            status: "error"
          })
        );
      }
    });
  };

  const handleGoogleSignIn = useGoogleLogin({
    flow: "implicit",
    // flow: "auth-code",

    onSuccess: tokenResponse => {
      fetch(
        `https://www.googleapis.com/oauth2/v1/userinfo?access_token=${tokenResponse.access_token}`
      )
        .then(res => res.json())
        .then(data => {
          const { email, id } = data;
          // Call your existing login logic
          loginToCore(email, id, true, data);
        });
    },
    onError: () => {
      console.error("Google login failed");
    }
    // flow: "implicit" // or 'auth-code' if you're doing server-side verification
  });

  const {
    email,
    password,
    invalidemail,
    invalidpassword,
    isLoading,
    isRemembered
  } = state;

  return (
    <div className="flex md:flex-row flex-col p-3  w-full min-h-[100vh] gap-3 bg-[url('/assets/images/background.webp')] bg-cover bg-no-repeat bg-center">
      {/* Left Side - Slider */}
      <div className="flex md:flex-col h-[56px] md:h-auto flex-row justify-between md:justify-start items-center md:items-start w-full md:w-[56%] md:bg-[#FFFFFF80] md:rounded-2xl md:border-[1.5px] md:border-[#FFFFFF] relative">
        <span
          className="md:ml-8 md:mt-8 cursor-pointer"
          onClick={() => {
            navigate("/");
          }}
        >
          <img src="/assets/images/logo-prodoo.png" alt="prodoo-logo" />
        </span>
        <div className="hidden md:block absolute w-full max-w-[509px] top-[50%] left-[50%] transform -translate-x-1/2 -translate-y-1/2">
          <Slider />
        </div>
        <div className="block md:hidden">
          <p
            onClick={() => {
              navigate("/");
            }}
            className="text-[#8E81F5] font-medium text-sm"
          >
            {labels?.Goback_Label}
          </p>
        </div>
      </div>
      <div className="relative flex justify-center items-start lg:pt-0 pt-[26px] pb-3 lg:items-center w-full lg:w-[44%] bg-[#FFFFFF80] rounded-2xl border-[1.5px] border-[#FFFFFF]">
        <div
          className="hidden md:block absolute left-[24px] top-[24px] cursor-pointer"
          onClick={() => {
            navigate("/");
          }}
        >
          <ArrowLeft />
        </div>
        <div className="flex flex-col w-full lg:px-0 px-3 lg:w-[77%] text-center">
          <h1 className="!m-0 !font-semibold !text-[20px] lg:!text-2xl text-[#0C1421]">
            {labels?.Welcome_Back_Label}
          </h1>
          <p className="!font-inter !m-0 text-[#878787] text-[13px] lg:!text-[16px] !font-normal mt-3">
            {labels?.Welcome_Description}
          </p>
          <form
            onSubmit={e => {
              e.preventDefault();
              handleSubmit();
            }}
          >
            <div className="mt-11 flex flex-col gap-6 lg:gap-5">
              {/* Email Input */}
              <div className="flex w-full flex-col gap-2">
                <label className="flex w-full text-sm lg:text-[16px] font-normal">
                  {labels?.loginEmail}
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  invalid={invalidemail}
                  placeholder={labels?.LANDING_EMAIL_LBL_PLACEHOLDER}
                  label="Email"
                  value={email}
                  onChange={handleFormFieldChange}
                  layout="vertical"
                  inputClassName="!bg-white !rounded-[12px] !h-12"
                  className="md:!h-12 !h-11 [&_.ant-form-item-label>label]:!text-[#343333] !rounded-[12px]  !bg-white"
                />
                {invalidemail && (
                  <div className="text-red-500 text-sm text-left mt-1">
                    {labels?.Email_Error_Message}
                  </div>
                )}
              </div>

              {/* Password Input */}
              <div className="flex w-full flex-col gap-2">
                <label className="flex w-full text-sm lg:text-[16px] font-normal">
                  {labels?.loginPassword}
                </label>
                <Input.Password
                  id="password"
                  name="password"
                  type="password"
                  isShowPassword={true}
                  value={password}
                  onChange={handleFormFieldChange}
                  placeholder={labels?.Input_Password_Placeholder}
                  label="Password"
                  layout="vertical"
                  inputClassName="!bg-white !rounded-[12px] !h-12"
                  className="md:!h-12 !h-11[&_.ant-form-item-label>label]:!text-[#343333] !rounded-[12px] !bg-white"
                  invalid={invalidpassword}
                />
                {invalidpassword && (
                  <div className="text-red-500 text-sm text-left mt-1">
                    {labels?.invalidPasswordForRegisteration}
                  </div>
                )}
              </div>
              <div className="flex items-center justify-between">
                <Checkbox
                  checked={isRemembered}
                  onChange={() =>
                    setState(prev => ({
                      ...prev,
                      isRemembered: !prev.isRemembered
                    }))
                  }
                  className="text-[13px] lg:text-[16px] [&_.ant-checkbox-inner]:!border-[1.2px] [&_.ant-checkbox-inner]:!border-[#878787]"
                >
                  {labels?.LANDING_SIGNIN_REMEMBER_ME_LBL}
                </Checkbox>
                <p
                  className="font-normal text-[13px] lg:text-[16px] cursor-pointer text-[#3864FD] !m-0"
                  onClick={() => {
                    navigate(publicRoutes.forgetPassword.path);
                  }}
                >
                  {labels?.FORGOT_PASS_FORM_HEAD}
                </p>
              </div>
              <Button
                type="primary"
                htmlType="submit"
                className="rounded-xl !h-11 lg:!h-12"
                loading={isLoading}
              >
                {labels?.loginLoginButton}
              </Button>

              {/* Divider */}
              <div className="flex items-center h-9">
                <Divider plain className="!m-0">
                  {labels?.Or_Label}
                </Divider>
              </div>

              {/* Google Sign In */}
              <div
                onClick={() => handleGoogleSignIn()}
                className="flex gap-2 w-full justify-center items-center bg-white lg:!h-12 !h-11 rounded-xl cursor-pointer"
              >
                <GoogleIcon />
                <span className="text-sm lg:text-[16px] font-normal text-[#878787]">
                  {labels?.LANDING_CONTINUE_WITH_GOOGLE_BTN_LBL}
                </span>
              </div>

              {/* Sign Up Link */}
              <div>
                <p className="!m-0 text-[16px] font-normal text-[#343333]">
                  {labels?.forgotPasswordAlreadylabel}{" "}
                  <span
                    className="text-[#8E81F5] cursor-pointer"
                    onClick={() => {
                      navigate(publicRoutes.signup.path);
                    }}
                  >
                    {labels?.Sign_up_Label}
                  </span>
                </p>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Login;
