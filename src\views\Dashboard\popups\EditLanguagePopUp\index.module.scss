@use "../../../../assets/sass/variables" as *;
@use "../../../../assets/sass/mixins" as *;

.socialLinks {
  display: flex;
  gap: 15px;
  align-items: center;
  .socialLink {
    cursor: pointer;
    font-size: 25px;
    &.activeSocialLink,
    &:hover {
      color: $lightPurple;
    }
  }
}

.editProfileButton {
  @include breakpoint("screen991") {
    position: absolute !important;
    right: 10px;
    top: 10px;
  }
}
.popUpForm {
  h5 {
    font-size: 18px;
    font-weight: 400;
  }
  .customSelect {
    span {
      font-size: 12px;
    }
  }
}
