import React, { useState, useEffect } from "react";
import { connect } from "react-redux";
import PageWrapper from "../../components/PageWrapper/PageWrapper";
import { notificationAction } from "../../actions/notification";

import "./assort.scss";
import { tictellLogin, getCurrenciesApi } from "../Tictell/tictellApi";
import { privateRoutes } from "../../Routes/routing";
import {
  assortAppLoginApi,
  GetAllActiveCollaborationsCount
} from "../Assort/assortApi";
import { getChildFeatureByName } from "../Snapshot/snapshotApi";
import { StorageService } from "../../api/storage";
import AssortNavigation from "./Navigation/Navigation";
import ConfirmDialog from "../../common/ConfirmDialog/ConfirmDialog";
import Dashboard from "./views/Dashboard/Dashboard";
import Collaboration from "./views/FinishCollaboration/FinishCollaboration";
import Opportunity from "./views/Opportunity/Opportunity";
import { useNavigate } from "react-router-dom";

const Snapshot = ({
  labels,
  isHelpActive,
  location,
  User,

  onStateChangeAction
}) => {
  const navigate = useNavigate();
  const [state, setState] = useState({
    UserFeatures: [],
    token: "",
    ExpenseCategories: [],
    Currencies: [],
    isLoading: true,
    UserId: -1,
    dialogMessage: ""
  });

  useEffect(() => {
    getChildFeatureByName({ featureName: "assort" })
      .then(res => {
        setState(prevState => ({
          ...prevState,
          UserFeatures: res.items
        }));
        const { items } = res;
        const firstUrl = items[0];
        var queryString = window.location.href;
        queryString = queryString.toLowerCase().split("assort")[1];
        if (queryString == "") {
          if (firstUrl) {
            const isApp =
              window.location.hash.toLowerCase().indexOf("apps") > -1;

            navigate((isApp ? "/apps" : "") + "/assort/" + firstUrl.Url);
          }
        }
      })
      .catch(err => console.log("Err ", err));

    let { UserEmail } = User;
    let Email = UserEmail;
    if (!Email) {
      const storedUser = StorageService.getUser();
      if (storedUser) {
        Email =
          storedUser.Email || storedUser.UserEmail || storedUser.userEmail;
      }
    }
    setState(prevState => ({
      ...prevState,
      UserId: User.UserId
    }));
    if (Email && Email !== "undefined") {
      assortAppLoginApi(Email)
        .then(res => {
          if (res.success) {
            tictellLogin(Email)
              .then(res => {
                if (res.success) {
                  const { access_token } = res.items;
                  getCurrencies(access_token);
                  getAllactivecollaborations(access_token);
                  setState(prevState => ({
                    ...prevState,
                    token: access_token
                  }));
                }
              })
              .catch(err => console.log(err));
          }
        })
        .catch(Err => console.log("Error ", Err));
    } else {
      console.warn("No valid email found for Assort Tictell login");
    }
  }, []);

  useEffect(() => {
    const onRouteChanged = () => {
      const firstUrl = state.UserFeatures[0];
      var queryString = window.location.href;
      queryString = queryString.toLowerCase().split("assort")[1];
      if (queryString == "") {
        if (firstUrl) {
          const isApp = window.location.hash.toLowerCase().indexOf("apps") > -1;

          navigate((isApp ? "/apps" : "") + "/assort/" + firstUrl.Url);
        }
      }
    };

    onRouteChanged();
  }, [location]);

  const getAllactivecollaborations = access_token => {
    GetAllActiveCollaborationsCount(access_token).then(res => {
      if (res.success) {
        if (res.items.length === 0) {
          setState(prevState => ({
            ...prevState,
            dialogMessage: labels.Snapshot_Collaboration_Dialog_Msg,
            isLoading: false
          }));
        } else {
          setState(prevState => ({
            ...prevState,
            isLoading: false
          }));
        }
      }
    });
  };

  const getCurrencies = access_token => {
    getCurrenciesApi()
      .then(res => {
        if (res.success) {
          setState(prevState => ({
            ...prevState,
            Currencies: res.items.map(item => ({
              ...item,
              value: item.CurrencyId,
              label: item.Name
            }))
          }));
        }
      })
      .catch(err => console.log("Err ", err));
  };

  const handleOkClick = () => {
    navigate(privateRoutes.dashboard.path);
  };

  const {
    UserFeatures,
    token,
    Currencies,
    dialogMessage,
    isLoading,
    UserId
  } = state;
  const hashesList = [
    "#/apps/assort/",
    "#/assort/",
    "#/apps/assort",
    "#/assort"
  ];
  const currentViewHash =
    window.location.hash.toLowerCase().split("/")[1] == "apps"
      ? window.location.hash.toLocaleLowerCase().split("/")[3]
      : window.location.hash.toLocaleLowerCase().split("/")[2];

  return (
    <PageWrapper className={`assort-page`}>
      {isLoading && (
        <div id="loader-wrapper">
          <div className="loader-container">
            <div className="loader">
              <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
                <defs>
                  <filter id="goo">
                    <feGaussianBlur
                      in="SourceGraphic"
                      stdDeviation="6"
                      result="blur"
                    />
                    <feColorMatrix
                      in="blur"
                      mode="matrix"
                      values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
                      result="goo"
                    />
                    <feBlend in="SourceGraphic" in2="goo" />
                  </filter>
                </defs>
              </svg>
            </div>
          </div>
        </div>
      )}
      <AssortNavigation
        disabled={`${isLoading ? "disabled" : ""}`}
        labels={labels}
        isHelpActive={isHelpActive}
        UserFeatures={UserFeatures}
      />
      {dialogMessage && (
        <ConfirmDialog testId="confirm-diloag">
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              onClick={handleOkClick}
            >
              {labels.okLabel}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      {!isLoading && (
        <React.Fragment>
          {currentViewHash == "home" &&
            UserFeatures.findIndex(
              a => a.Url && a.Url.toLocaleLowerCase().trim() == currentViewHash
            ) > -1 && <Dashboard token={token} />}
          {currentViewHash == "opportunites" &&
            UserFeatures.findIndex(
              a => a.Url && a.Url.toLocaleLowerCase().trim() == currentViewHash
            ) > -1 && <Opportunity token={token} Currencies={Currencies} />}
          {currentViewHash == "collaborations" &&
            UserFeatures.findIndex(
              a => a.Url && a.Url.toLocaleLowerCase().trim() == currentViewHash
            ) > -1 && (
              <Collaboration
                token={token}
                Currencies={Currencies}
                locationProp={location}
              />
            )}
          {currentViewHash == "finish-collaboration" &&
            UserFeatures.findIndex(
              a => a.Url && a.Url.toLocaleLowerCase().trim() == currentViewHash
            ) > -1 && (
              <Collaboration
                token={token}
                Currencies={Currencies}
                locationProp={location}
              />
            )}
          {/* {currentViewHash == "start-new-search" &&
            UserFeatures.findIndex(
              (a) =>
                a.Url && a.Url.toLocaleLowerCase().trim() == currentViewHash
            ) > -1 && (
              <StartNewSearch />
            )}
          {currentViewHash == "approve-time-and-expenses" &&
            UserFeatures.findIndex(
              (a) =>
                a.Url && a.Url.toLocaleLowerCase().trim() == currentViewHash
            ) > -1 && (
              <Reports />
            )}           */}
        </React.Fragment>
      )}
    </PageWrapper>
  );
};

const mapStateToProps = ({ systemLabel, navigation, userInfo }) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  const User = user ? user : StorageService.getUser();
  const { isHelpActive } = navigation;
  return { labels, isHelpActive, User };
};

export default connect(mapStateToProps, { notificationAction })(Snapshot);
