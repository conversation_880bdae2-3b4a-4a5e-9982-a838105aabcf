import { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { notificationAction } from "../../../actions/notification";
import { StorageService } from "../../../api/storage";
import { logoutAction } from "../../../store/initialConfig";
import { logoutApi } from "../../../components/Navigation/navigationApi";
import NewOpportunity from "./common/NewOpportunity";

const NewOpportunityIpro = props => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const { labels, isHelpActive } = useSelector(state => ({
    labels: state.systemLabel.labels,
    isHelpActive: state.navigation.isHelpActive
  }));

  useEffect(() => {
    const spliturl = location.pathname.match(/\d+/g);
    if (spliturl) {
      const user = JSON.parse(localStorage.getItem("User"));
      const userId = parseInt(spliturl[1]);
      if (user.UserId !== userId) {
        logoutApi().then(response => {
          navigate("new-opportunity");
          if (response.success) {
            dispatch(logoutAction());
            const info = {
              message: "Please login with your appropriate account.",
              status: "error"
            };
            dispatch(notificationAction(info));
            StorageService.clearAll();

            // window.location.href = RESET_LANDINGPAGE_URL;
            return;
          }
        });
      }
    }
  }, [location.pathname]);

  return (
    <NewOpportunity
      // url={`?type=3`}
      isAccepted={true}
      isNewOpportunityView
      opportunityType="NewRequests"
      IsSearcher={false}
      isHelpActive={isHelpActive}
      notificationAction={notificationAction}
      // list section system label
      Lbltitle={labels?.iProOpportunityNewDetailLblTitle}
      locationsLabel={labels?.iProOpportunityNewDetailLblLocation}
      startDateLabel={labels?.iProOpportunityNewDetailLblStartDate}
      durationLabel={labels?.iProOpportunityNewDetailLblDuration}
      companyLabel={labels?.iProOpportunityNewDetailLblCompany}
      descriptionLabel={labels?.iProOpportunityNewDetailLblDescription}
      countryLabel={labels?.iProOpportunityNewDetailLblCountry}
      SearchParameterHeading={
        labels?.iProOpportunityNewDetailLblSearchParameterHeading
      }
      LblProfile={labels?.iProOpportunityNewDetailLblProfile}
      LblSkills={labels?.iProOpportunityNewDetailLblSkills}
      LblKeyword={labels?.iProOpportunityNewDetailLblKeyword}
      LblIndustry={labels?.iProOpportunityNewDetailLblIndustry}
      LblCertification={labels?.iProOpportunityNewDetailLblCertification}
      LblCountry={labels?.iProOpportunityNewDetailLblCountry}
      LblLanguage={labels?.iProOpportunityNewDetailLblLanguage}
      LblInterest={labels?.iProOpportunityNewDetailLblInterest}
      BtnAccept={labels?.iProOpportunityNewDetailBtnAccept}
      BtnDecline={labels?.iProOpportunityNewDetailBtnDecline}
      BtnDelete={labels?.iProOpportunityDeleteBtn}
      companyAddressLabel={labels?.IproOpportunityNewCompanyAddressLabel}
      companyIndustryLabel={labels?.IproOpportunityNewCompanyIndustryLabel}
      companyCountryLabel={labels?.IproOpportunityNewCompanyCountryLabel}
      companyPhoneLabel={labels?.IproOpportunityNewCompanyPhoneLabel}
      companyWebUrlLabel={labels?.IproOpportunityNewCompanyWebUrlLabel}
      companyVatLabel={labels?.IproOpportunityNewCompanyVatLabel}
      searchInputPlaceholder={labels?.iproOpportunityNewSearchField}
      toolTipExpandList={labels?.ToolTipInvitationExpandList}
      helpTooltipExpandList={labels?.HlpTooltipIproOpportunityNewListExpand}
      toolTipCollapseList={labels?.TooltipIproOpportunityNewListCollapse}
      helpTooltipCollapseList={labels?.HlpTooltipIproOpportunityNewListCollapse}
      listColumnText={labels?.ViewTitleInvitationList}
      // detail section system label
      detailHeadingText={labels?.iproOpportunityNewDetailsHeading}
      toolTipExpandDetail={labels?.ToolTipInvitationExpandDetail}
      helpTooltipExpandDetail={labels?.HlpTooltipIproOpportunityNewDetailExpand}
      toolTipCollapseDetail={labels?.ToolTipInvitationCollaspeDetail}
      helpTooltipCollapseDetail={
        labels?.HlpTooltipIproOpportunityNewDetailCollapse
      }
      detailsColumnText={labels?.ViewTitleInvitationDetail}
      // selected user detail and company section system label
      userDetailHeadingText={labels?.iproOpportunityNewCompanyDetailHeading}
      toolTipExpandUserDetail={labels?.ToolTipCompanyExpandDetail}
      helpTooltipExpandUserDetail={
        labels?.HlpTooltipIproOpportunityNewUserDetailExpand
      }
      toolTipCollapseUserDetail={labels?.ToolTipCompanyCollaspeDetail}
      helpTooltipCollapseUserDetail={
        labels?.HlpTooltipIproOpportunityNewUserDetailCollapse
      }
      userDetailsColumnText={labels?.ViewTitleCompanyDetail}
      emptyOpportunityInfo={labels?.emptyOpportunityInfo}
      listCollapsedTestId={"listCollapsedTestId1"}
      listCollapsedHelpTestId={"listCollapsedTestId2"}
      listExpandTestId={"listCollapsedTestId3"}
      listExpandHelpTestId={"listCollapsedTestId4"}
      detailCollapsedTestId={"listCollapsedTestId5"}
      detailCollapsedHelpTestId={"listCollapsedTestId6"}
      detailExpandTestId={"listCollapsedTestId7"}
      detailExpandHelpTestId={"listCollapsedTestId8"}
      userDetailCollapsedTestId={"listCollapsedTestId9"}
      userDetailCollapsedHelpTestId={"listCollapsedTestId10"}
      userDetailExpandTestId={"listCollapsedTestId11"}
      userDetailExpandHelpTestId={"listCollapsedTestId12"}
      searchInputTestId="opportunity-new-search-input"
      iproOpportunityListItem="iproOpportunityListItem-New"
      iproInvitationSuccessfullyDeleted={
        labels?.iproInvitationSuccessfullyDeleted
      }
      locationProp={location}
    />
  );
};

export default NewOpportunityIpro;
