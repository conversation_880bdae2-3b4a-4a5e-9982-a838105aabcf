
import { useState } from "react";
import moment from "moment";
import "./screenshotgallery.scss";
const ScreenshotGallery = ({ screenshots }) => {
  const [selectedImage, setSelectedImage] = useState(null);

  const timerStatus = JSON.parse(sessionStorage.getItem("tictellTimer") || "{}");
  const isTimerRunning = timerStatus?.status === 1;
  
  // Don't render if timer is active
  if (isTimerRunning) {
    return null;
  }

  if (!screenshots || screenshots.length === 0) {
    return null;
  }

  const handleImageClick = (image) => {
    setSelectedImage(image);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  return (
    <div className="screenshot-gallery">
      <h4>Screenshots</h4>
      <div className="screenshots-container">
        {screenshots.map((screenshot, index) => (
          <div key={index} className="screenshot-item" onClick={() => handleImageClick(screenshot)}>
            <img src={screenshot.BlobUrl} alt={`Screenshot ${index + 1}`} className="thumbnail" />
            <div className="timestamp">
              {moment(screenshot.Timestamp).format("HH:mm:ss")}
            </div>
          </div>
        ))}
      </div>

      {selectedImage && (
        <div className="screenshot-modal" onClick={closeModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <span className="close-button" onClick={closeModal}>&times;</span>
            <img src={selectedImage.BlobUrl} alt="Full-size screenshot" />
            <div className="screenshot-details">
              Taken at: {moment(selectedImage.Timestamp).format("YYYY-MM-DD HH:mm:ss")}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ScreenshotGallery;