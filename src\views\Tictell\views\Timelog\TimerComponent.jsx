import { useState, useEffect, useRef } from "react";
import DisplayComponent from "./Components/DisplayComponent";
import BtnComponent from "./Components/BtnComponent";
import "./timerComponent.scss";
import moment from "moment";

function TimerComponent({ saveTimelog, isTimerOn, seconds, minutes, hours, reset, pause, start, selectedCollaboration, notificationAction }) {
  const [storedTime, setTime] = useState({ s: 0, m: 0, h: 0 });
  const [status, setStatus] = useState(0);
  const [startTime, setStartTime] = useState(0);
  const [screenshots, setScreenshots] = useState([]);
  const [hasScreenCapturePermission, setHasScreenCapturePermission] = useState(false);
  const [showScreenshots, setShowScreenshots] = useState(true);
  const [lastScreenshotTime, setLastScreenshotTime] = useState(0); // Track when last screenshot was taken
  const screenshotIntervalRef = useRef(null);
  const mediaStreamRef = useRef(null);
  
  // Check if screenshots are required based on collaboration settings
  const isScreenshotRequired = () => {
    // TimeTracking = 0 (Auto) and ScreenshotRequirement = 0 (Required)
    return selectedCollaboration?.TimeTracking === 0 && selectedCollaboration?.ScreenshotRequirement === 0;
  };
  
  // Check if screenshots are optional
  const isScreenshotOptional = () => {
    // TimeTracking = 0 (Auto) and ScreenshotRequirement = 1 (Optional)
    return selectedCollaboration?.TimeTracking === 0 && selectedCollaboration?.ScreenshotRequirement === 1;
  };

  const showNotification = (message, status = "info") => {
    if (notificationAction) {
      notificationAction({
        message,
        status
      });
    } else {
      alert(message);
    }
  };
  
  // Toggle screenshots visibility
  const toggleScreenshots = () => {
    setShowScreenshots(!showScreenshots);
  };
  
  // Request permission explicitly for the entire screen
  const requestScreenCapturePermission = async () => {
    try {
      console.log("Requesting full screen capture permission...");
      
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: { 
          cursor: "always",
          displaySurface: "monitor"
        },
        audio: false
      });
      
      const videoTrack = stream.getVideoTracks()[0];
      const trackSettings = videoTrack.getSettings();
      
      console.log("Capture settings:", trackSettings);
      
      if (trackSettings.displaySurface !== "monitor") {
        console.warn("User selected something other than the entire screen.");
        console.log("Selected display surface:", trackSettings.displaySurface);
      }
      
      mediaStreamRef.current = stream;
      setHasScreenCapturePermission(true);
      videoTrack.addEventListener('ended', () => {
        console.log('User ended screen sharing');
        mediaStreamRef.current = null;
        setHasScreenCapturePermission(false);
        
        if (screenshotIntervalRef.current) {
          clearInterval(screenshotIntervalRef.current);
          screenshotIntervalRef.current = null;
        }
      });
      
      return true;
    } catch (err) {
      console.error('Error requesting screen capture permission:', err);
      setHasScreenCapturePermission(false);
      return false;
    }
  };
  
  // Check if a screenshot was recently taken (within the last minute)
  const wasScreenshotRecentlyTaken = () => {
    if (!lastScreenshotTime) return false;
    const now = Date.now();
    const diffInSeconds = (now - lastScreenshotTime) / 1000;
    return diffInSeconds < 60; // Less than a minute
  };
  
  // Capture screenshot using existing stream
  const captureScreenshot = async (forceTake = false) => {
    // Check if a screenshot was recently taken (unless forced)
    if (!forceTake && wasScreenshotRecentlyTaken()) {
      console.log("Screenshot was recently taken, skipping...");
      return;
    }
    
    try {
      if (!mediaStreamRef.current) {
        console.log("No active media stream, attempting to request permission again");
        const permissionGranted = await requestScreenCapturePermission();
        if (!permissionGranted) {
          console.log("Failed to get permission, skipping screenshot");
          return;
        }
      }
      
      console.log("Capturing screenshot...");
      
      // Create a video element to capture a frame
      const video = document.createElement('video');
      video.srcObject = mediaStreamRef.current;
      
      // Wait for the video to load enough data
      await new Promise(resolve => {
        video.onloadedmetadata = () => {
          video.play();
          resolve();
        };
      });
      
      // Create a canvas and draw the video frame
      const canvas = document.createElement('canvas');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const ctx = canvas.getContext('2d');
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      
      // Convert to base64 image (with reduced quality to save bandwidth)
      const screenshot = canvas.toDataURL('image/jpeg', 0.7);
      
      // Add screenshot to state with current timestamp
      const now = new Date();
      const newScreenshot = {
        image: screenshot,
        timestamp: now
      };
      
      setScreenshots(prev => [...prev, newScreenshot]);
      setLastScreenshotTime(now.getTime()); // Update the last screenshot timestamp
      
      console.log("Screenshot captured successfully at", now.toISOString());
    } catch (err) {
      console.error('Error capturing screenshot:', err);
      
      // If there was an error with the stream, clear it and try again next time
      if (mediaStreamRef.current) {
        mediaStreamRef.current.getTracks().forEach(track => track.stop());
        mediaStreamRef.current = null;
        setHasScreenCapturePermission(false);
      }
    }
  };

  // Start the timer with screenshots
  const startTimer = async () => {
    // Check if screenshots are required but user denied permission
    if (isScreenshotRequired()) {
      const consent = window.confirm(
        'This project requires screenshots to be taken periodically. ' +
        'Screen capturing is mandatory for tracking time on this project. ' +
        'Do you consent to screen captures?'
      );
      
      if (!consent) {
        showNotification("Screen capture permission is required for this project. Unable to start the timer.", "info");
        return;
      }
      
      // Request permission for screenshots
      const permissionGranted = await requestScreenCapturePermission();
      
      if (!permissionGranted) {
        showNotification("Screen capture permission is required for this project. Unable to start the timer.", "info");
        return;
      }
      
      // Start timer with screenshots
      startTimerWithScreenshots();
    }
    // Check if screenshots are optional
    else if (isScreenshotOptional()) {
      const consent = window.confirm(
        'This time tracker can take periodic screenshots of your screen while active. ' +
        'Screenshots are optional for this project. ' +
        'Would you like to enable screen captures?'
      );
      
      if (consent) {
        // Request permission for the screen
        const permissionGranted = await requestScreenCapturePermission();
        
        if (permissionGranted) {
          startTimerWithScreenshots();
        } else {
          // Even if permission is denied, we can still start the timer (screenshots are optional)
          startTimerOnly();
        }
      } else {
        // User declined optional screenshots, start timer only
        startTimerOnly();
      }
    } else {
      // For any other case, just start the timer without screenshots
      startTimerOnly();
    }
  };
  
  // Start timer without screenshots
  const startTimerOnly = () => {
    start();
    setStatus(1);
    
    const currentTime = moment().format("YYYY-MM-DD HH:mm:ss");
    setStartTime(currentTime);
    
    let timer = {
      status: 1,
      isTimerOn: true,
      startTime: currentTime,
      screenshotsEnabled: false
    };
    sessionStorage.setItem("tictellTimer", JSON.stringify(timer));
  };
  
  // Start timer with screenshots
  const startTimerWithScreenshots = () => {
    start();
    setStatus(1);
    
    const currentTime = moment().format("YYYY-MM-DD HH:mm:ss");
    setStartTime(currentTime);
    
    // Take initial screenshot (force take even if one was recently taken)
    captureScreenshot(true);
    
    // Set up interval for screenshots (5 minutes = 300000 ms)
    screenshotIntervalRef.current = setInterval(captureScreenshot, 300000);
    
    let timer = {
      status: 1,
      isTimerOn: true,
      startTime: currentTime,
      screenshotsEnabled: true
    };
    sessionStorage.setItem("tictellTimer", JSON.stringify(timer));
  };

  useEffect(() => {
    // Load timer state from session storage
    let tictellTimerLocalStorage = JSON.parse(
      sessionStorage.getItem("tictellTimer")
    );
    
    if (tictellTimerLocalStorage) {
      setStatus(tictellTimerLocalStorage.status);
      
      // Attempt to restore screen capture permission if timer was running with screenshots
      if (tictellTimerLocalStorage.status === 1 && tictellTimerLocalStorage.screenshotsEnabled) {
        // Request permission again but only start the interval if successful
        requestScreenCapturePermission().then(success => {
          if (success) {
            screenshotIntervalRef.current = setInterval(captureScreenshot, 300000);
          } else if (isScreenshotRequired()) {
            // If screenshots are required but permission wasn't granted, stop the timer
            stopTimer();
            showNotification("Screen capture permission is required for this project. Timer has been stopped.", "error");
          }
        });
      }
    }
    
    // Clean up interval and media stream on unmount
    return () => {
      if (screenshotIntervalRef.current) {
        clearInterval(screenshotIntervalRef.current);
      }
      
      if (mediaStreamRef.current) {
        mediaStreamRef.current.getTracks().forEach(track => track.stop());
        mediaStreamRef.current = null;
      }
    };
  }, []);

  const pauseTimer = () => {
    pause();
    setStatus(2);
    
    // Pause screenshot capturing
    if (screenshotIntervalRef.current) {
      clearInterval(screenshotIntervalRef.current);
    }
    
    // Get current timer from session storage to preserve startTime
    let currentTimer = JSON.parse(sessionStorage.getItem("tictellTimer") || "{}");
    
    let timer = {
      ...currentTimer, // This preserves the startTime field
      status: 2,
      isTimerOn: true,
      screenshotsEnabled: hasScreenCapturePermission
    };
    sessionStorage.setItem("tictellTimer", JSON.stringify(timer));
  };

  const stopTimer = () => {
    let tictellTimerLocalStorage = JSON.parse(
      sessionStorage.getItem("tictellTimer") || "{}"
    );
    
    if (!tictellTimerLocalStorage.startTime) {
      console.warn("No start time found in session storage");
      
      // Try to recreate a best-effort time log
      const bestGuessStartTime = moment().subtract(hours, 'hours')
                                        .subtract(minutes, 'minutes')
                                        .subtract(seconds, 'seconds');
      
      tictellTimerLocalStorage = {
        startTime: bestGuessStartTime.format("YYYY-MM-DD HH:mm:ss")
      };
      
      showNotification("Unable to find the exact start time. Using an estimate based on the current timer.", "warning");
    }
    
    // Stop screenshot interval
    if (screenshotIntervalRef.current) {
      clearInterval(screenshotIntervalRef.current);
      screenshotIntervalRef.current = null;
    }
    
    // Stop the media stream
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
      setHasScreenCapturePermission(false);
    }
    
    let startTimeMoment = moment(tictellTimerLocalStorage.startTime, "YYYY-MM-DD HH:mm:ss");
    let endTimeMoment = moment(); // Current time
    
    console.log("Start time:", startTimeMoment.format("YYYY-MM-DD HH:mm:ss"));
    console.log("End time:", endTimeMoment.format("YYYY-MM-DD HH:mm:ss"));
    console.log("Screenshots taken:", screenshots.length);
    
    let timerModel = {
      Date: new Date(),
      StartTime: startTimeMoment,
      EndTime: endTimeMoment,
      Screenshots: screenshots // Add screenshots to the model
    };
    
    saveTimelog(timerModel);
    
    setStatus(0);
    pause();
    reset(null, false);
    setScreenshots([]); // Clear screenshots after saving
    setLastScreenshotTime(0); // Reset last screenshot time
    sessionStorage.removeItem("tictellTimer");
  };

  const resumeTimer = async () => {
    let tictellTimerLocalStorage = JSON.parse(
      sessionStorage.getItem("tictellTimer") || "{}"
    );
    
    // Check if startTime exists, if not log it but continue
    if (!tictellTimerLocalStorage.startTime) {
      console.warn("Warning: No start time found in session storage, using current time");
      tictellTimerLocalStorage.startTime = moment().format("YYYY-MM-DD HH:mm:ss");
    }
    
    // First ensure we always call start() to continue the timer
    start();
    setStatus(1);
    
    // Now handle the screenshot functionality based on requirements
    
    // Check if screenshots are required for this project
    if (isScreenshotRequired() && !hasScreenCapturePermission) {
      const permissionGranted = await requestScreenCapturePermission();
      
      if (!permissionGranted) {
        // Even though permission is required, we'll let the timer run 
        // but show notification to the user
        showNotification("Screen capture permission is required for this project. Timer will continue but screenshots are disabled.", "warning");
        
        let timer = {
          ...tictellTimerLocalStorage,
          status: 1,
          screenshotsEnabled: false
        };
        sessionStorage.setItem("tictellTimer", JSON.stringify(timer));
        return;
      }
      
      // Set up interval for screenshots WITHOUT taking an immediate screenshot
      screenshotIntervalRef.current = setInterval(captureScreenshot, 300000);
      
      let timer = {
        ...tictellTimerLocalStorage,
        status: 1,
        screenshotsEnabled: true
      };
      sessionStorage.setItem("tictellTimer", JSON.stringify(timer));
    }
    // Check if we previously had screenshot permission
    else if (tictellTimerLocalStorage.screenshotsEnabled && !hasScreenCapturePermission) {
      // For optional screenshots, ask again if they want to continue with screenshots
      if (isScreenshotOptional()) {
        const consent = window.confirm(
          'Would you like to continue capturing screenshots for this session? ' +
          'Screenshots are optional for this project.'
        );
        
        if (consent) {
          const permissionGranted = await requestScreenCapturePermission();
          
          if (permissionGranted) {
            // Set up interval WITHOUT taking an immediate screenshot
            screenshotIntervalRef.current = setInterval(captureScreenshot, 300000);
            
            let timer = {
              ...tictellTimerLocalStorage,
              status: 1,
              screenshotsEnabled: true
            };
            sessionStorage.setItem("tictellTimer", JSON.stringify(timer));
          } else {
            // No permission but timer continues
            let timer = {
              ...tictellTimerLocalStorage,
              status: 1,
              screenshotsEnabled: false
            };
            sessionStorage.setItem("tictellTimer", JSON.stringify(timer));
          }
        } else {
          // User declined optional screenshots
          let timer = {
            ...tictellTimerLocalStorage,
            status: 1,
            screenshotsEnabled: false
          };
          sessionStorage.setItem("tictellTimer", JSON.stringify(timer));
        }
      } else {
        // No permission needed
        let timer = {
          ...tictellTimerLocalStorage,
          status: 1,
          screenshotsEnabled: false
        };
        sessionStorage.setItem("tictellTimer", JSON.stringify(timer));
      }
    } else if (hasScreenCapturePermission) {
      // We have permission, but DO NOT take a screenshot on resume
      // Just restart the interval for future screenshots
      screenshotIntervalRef.current = setInterval(captureScreenshot, 300000);
      
      let timer = {
        ...tictellTimerLocalStorage,
        status: 1,
        screenshotsEnabled: true
      };
      sessionStorage.setItem("tictellTimer", JSON.stringify(timer));
    } else {
      // No permission and user didn't want it before, just resume timer
      let timer = {
        ...tictellTimerLocalStorage,
        status: 1,
        screenshotsEnabled: false
      };
      sessionStorage.setItem("tictellTimer", JSON.stringify(timer));
    }
  };

  // Format timestamp for screenshot display
  const formatTime = (timestamp) => {
    return moment(timestamp).format('HH:mm:ss');
  };

  return (
    <div className="main-section">
      <div className="clock-holder">
        <div className="stopwatch">
          <DisplayComponent time={{ h: hours, m: minutes, s: seconds }} />
          <BtnComponent
            status={status}
            resume={resumeTimer}
            stop={stopTimer}
            pause={pauseTimer}
            start={startTimer}
          />
          
          {screenshots.length > 0 && (
            <div className="screenshot-section">
              <div className="screenshot-header">
                <button onClick={toggleScreenshots} className="toggle-btn">
                  {showScreenshots ? 'Hide Screenshots' : 'Show Screenshots'}
                </button>
              </div>
              
              {showScreenshots && (
                <div className="screenshot-container">
                  {screenshots.map((screenshot, index) => (
                    <div key={index} className="screenshot-item">
                      <img 
                        src={screenshot.image} 
                        alt={`Screenshot ${index + 1}`} 
                      />
                      <div className="timestamp">{formatTime(screenshot.timestamp)}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      <style jsx>{`
        .screenshot-section {
          margin-top: 15px;
          padding-top: 10px;
          border-top: 1px solid #ddd;
          background-color: #dfd9f7; /* Light purple background to match your screenshot */
          border-radius: 5px;
          padding: 15px;
        }
        
        .screenshot-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          padding: 0 5px;
        }
        
        .toggle-btn {
          background: #9783cf;
          color: white;
          border: none;
          border-radius: 4px;
          padding: 6px 12px;
          cursor: pointer;
          font-size: 14px;
        }
        
        .toggle-btn:hover {
          background: #9783cf;
        }
        
        .screen-capture-status {
          color: #4CAF50; /* Green text for "active" status */
          font-size: 14px;
        }
        
        .screenshot-container {
          display: grid;
          grid-template-columns: repeat(3, 1fr); /* Three columns instead of two */
          gap: 10px;
          overflow-y: auto;
          max-height: 300px;
          padding: 5px;
        }
        
        .screenshot-item {
          position: relative;
          border-radius: 4px;
          overflow: hidden;
          box-shadow: 0 1px 3px rgba(0,0,0,0.12);
          background-color: white;
        }
        
        .screenshot-item img {
          width: 100%;
          display: block;
          object-fit: cover;
          aspect-ratio: 16/9; /* Maintain consistent aspect ratio */
        }
        
        .timestamp {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background-color: #9370db; /* Purple background to match your screenshot */
          color: white;
          text-align: center;
          padding: 3px 0;
          font-size: 12px;
        }
      `}</style>
    </div>
  );
}

export default TimerComponent;