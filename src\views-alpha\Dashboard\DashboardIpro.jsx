import { <PERSON><PERSON>, Card, Grid } from "antd";
import DashboardProfileIcon from "../../assets-alpha/images/svg/dashboard-profile.svg?react";
import ResumeIcon from "../../assets-alpha/images/svg/dashboard-resume.svg?react";
import ProfileIcon from "../../assets-alpha/images/svg/profile.svg?react";
import ReceiptIcon from "../../assets-alpha/images/svg/receipt-edit.svg?react";
import ContractIcon from "../../assets-alpha/images/view/dashboard-contract.svg?react";
import ResumeImage from "../../assets-alpha/images/view/resume.svg?react";
import EmptyView from "../../common-alpha/EmptyView/EmptyView";

import clsx from "clsx";
import moment from "moment";
import { useMemo } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import BuildingIcon from "../../assets-alpha/images/svg/building.svg?react";
import CalendarIcon from "../../assets-alpha/images/svg/calendar.svg?react";
import MoneysIcon from "../../assets-alpha/images/svg/moneys.svg?react";
import { privateRoutes } from "../../Routes/routing";
import { STATUS_CODES } from "../../utilities-alpha/constant";
import { useGetResume } from "../Resume/resumeApi";
import { useGetCurrencyApi, useGetIproContractsApi } from "./dashboardApi";
import { EmptyCard } from "./DashboardRecruiter";

const DashboardIpro = () => {
  const { md } = Grid.useBreakpoint();
  const navigate = useNavigate();
  const labels = useSelector(state => state.systemLabel.labels?.dashboard);
  const { data: resumeData, isFetching: loadingResume, error } = useGetResume();
  const completed = useMemo(() => {
    if (!resumeData?.items) return 0;
    const items = resumeData?.items;
    return (
      (items?.Profiles?.length > 0) +
      (items?.Skills?.length > 0) +
      (items?.Certifications?.length > 0) +
      (items?.Educations?.length > 0) +
      (items?.ResumeOtherAchivenments?.length > 0) +
      (items?.Keywords?.length > 0) +
      (items?.ResumeExperience?.length > 0) +
      (items?.Languages?.length > 0) +
      (items?.Regions?.length > 0) +
      (items?.Industries?.length > 0)
    );
  }, [resumeData?.items]);
  return (
    <div>
      {/* Main Grid */}
      <Card className="!bg-[#8E81F5] !rounded-[12px] !border-none [&_.ant-card-body]:!p-0 !px-4 max-md:!py-4 !text-white">
        <div className="flex justify-between items-center">
          <div className="flex-1">
            <div className="flex items-center gap-5 mb-7.5 max-md:gap-3">
              <ResumeIcon className="min-w-12" />

              <div>
                <span className="font-semibold text-[20px] max-md:text-base mb-1">
                  {labels?.resume}
                </span>
                <p className="text-sm max-md:text-[13px] font-medium">
                  {labels?.resumeDescription}
                </p>
              </div>
            </div>
            <ChunkedProgressBar completed={completed} total={10} />

            <Button
              type="default"
              className="mt-7 !bg-white"
              onClick={() => navigate(privateRoutes.resumeBuilder.path)}
            >
              {completed >= 10 ? labels?.viewResume : labels?.completeProfile}
            </Button>
          </div>
          {md && (
            <div className="flex-1 flex justify-end">
              <ResumeImage />
            </div>
          )}
        </div>
      </Card>

      {/* Portfolio Section */}
      <Card className="!border-1 !mt-3 !border-[#8E81F5] !bg-[#F4F2FE] rounded-2xl [&_.ant-card-body]:!p-0 !p-2 !pr-5">
        <div className="flex justify-between items-center max-md:flex-col max-md:items-start">
          <div className="flex items-center gap-6 max-md:gap-3">
            <DashboardProfileIcon className="min-w-12" />
            <div>
              <p className="font-semibold text-base">{labels?.portfolio}</p>
              <p className="text-sm text-[#878787] max-md:text-[13px] font-medium">
                {labels?.portfolioAttention}
              </p>
            </div>
          </div>
          <Button
            onClick={() => navigate(privateRoutes.presentations.path)}
            type="default"
            className="!bg-[white] max-md:mt-4"
          >
            {labels?.addPortfolio}
          </Button>
        </div>
      </Card>

      {/* Contracts Section */}
      <Contracts />
    </div>
  );
};

export default DashboardIpro;

const Contracts = () => {
  const { data: contractsData } = useGetIproContractsApi();
  const { data: currencyData } = useGetCurrencyApi();
  const dashboard = useSelector(state => state.systemLabel.labels?.dashboard);
  const filterContracts = useMemo(
    () => contractsData?.items?.filter(c => c.Status === 0 || c.Status === 2),
    [contractsData?.items]
  );

  return (
    <div className="mt-6">
      <h2 className="text-xl font-semibold text-gray-800 ">
        {dashboard?.activeContracts}
      </h2>

      {filterContracts?.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 mt-4 lg:grid-cols-3 gap-6">
          {filterContracts
            ?.filter((c, i) => i < 5)
            ?.map(contract => (
              <TimesheetCard
                key={contract.CollaborationId}
                contract={contract}
                currencyType={
                  currencyData?.items?.find(
                    c => c.CurrencyId === parseInt(contract.HourlyRateType, 10)
                  )?.Name
                }
              />
            ))}
          {filterContracts?.length >= 5 && (
            <EmptyCard link={privateRoutes.iproActiveCollaborations.path} />
          )}
        </div>
      ) : (
        <EmptyView
          detail={dashboard?.noContractsDetail}
          title={dashboard?.noContractsTitle}
          icon={<ContractIcon />}
          noBorder={true}
          className={"[&_.empty-icon]:!h-auto"}
        />
      )}
    </div>
  );
};

const ChunkedProgressBar = ({ total = 10, completed }) => {
  const dashboard = useSelector(state => state.systemLabel.labels?.dashboard);
  return (
    <div>
      <div className="flex space-x-1">
        {Array.from({ length: total }).map((_, index) => (
          <div
            key={index}
            className={clsx(`h-2.5 rounded-full flex-1 bg-white`, {
              "opacity-50": index >= completed
            })}
          />
        ))}
      </div>
      <div className="flex justify-between mt-1">
        <span className="text-xs ">{dashboard?.profileComplete}</span>
        {/* <span className="text-xs text-[#8E81F5]">
          {round((completed / total) * 100, 0)}%
        </span> */}
      </div>
    </div>
  );
};

const TimesheetCard = ({ contract, currencyType }) => {
  const navigate = useNavigate();
  return (
    <Card className="!bg-[#F3F1FD] !rounded-2xl !border-none [&_.ant-card-body]:!p-0">
      {/* Header */}
      <div className="flex items-start space-x-4 gap-4 !px-4 !py-3 border-b-[#EAE5FC] border-b-1">
        <div>
          <h3 className="text-lg mb-1.5 font-semibold text-gray-800 flex items-center gap-3">
            <ReceiptIcon className="w-5 text-[#878787]" /> {contract?.Title}
          </h3>
          <div className="flex items-center gap-3 text-[#878787]">
            <ProfileIcon className="text-[#878787] w-5" />
            <span className="font-medium">{contract?.RecruiterName}</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="py-3 px-4 space-y-3 text-[13px] text-[#343333]">
        <div className="flex ite  ms-center space-x-3">
          <BuildingIcon className="text-[#878787] w-4" />
          <span>{contract?.company?.CompanyName}</span>
        </div>
        <div className="flex items-center space-x-3">
          <CalendarIcon className="text-[#878787] w-4" />
          <span>{moment(contract?.StartDate).format("DD MMMM YYYY")}</span>
        </div>
        <div className="flex items-center space-x-3">
          <MoneysIcon className="text-[#878787]" />
          <span>
            {contract?.HourlyRate} {currencyType}/h
          </span>
        </div>
        <div className="mt-4">
          <Button
            type="primary"
            className="!bg-[white] !shadow-none !rounded-full w-fit text-sm font-medium"
            style={{
              color: `${STATUS_CODES[contract?.Status]?.color}`
            }}
            onClick={() =>
              navigate(privateRoutes.iproActiveCollaborations.path, {
                state: { selectedContract: contract }
              })
            }
          >
            {STATUS_CODES[contract?.Status]?.label}
          </Button>
        </div>
      </div>
    </Card>
  );
};
