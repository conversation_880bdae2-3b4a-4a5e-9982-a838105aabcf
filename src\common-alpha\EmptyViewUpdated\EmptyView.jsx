import { Button } from "antd";

const EmptyView = ({
  icon,
  title,
  description,
  actionText,
  onAction,
  btnIcon,
  isLoading
}) => {
  return (
    <div className="flex flex-col items-center w-[314px] md:w-[420px] !gap-4 md:!gap-5">
      <div>{icon}</div>
      <div className="flex flex-col w-full items-center gap-2 ">
        <h1 className="!m-0 !font-semibold !text-[14px] md:!text-[16px] !text-[#343333] !leading-[20px] text-center">
          {title}
        </h1>
        <p className="font-normal md:!font-medium !text-[13px] text-center md:!text-[14px] !leading-[21px] !text-[#878787] pl-0 pr-0 md:pl-9 md:pr-9">
          {description}
        </p>
      </div>
      <div>
        {actionText && onAction && (
          <Button
            type="primary"
            className="w-auto pl-3 pr-4 !h-10"
            onClick={onAction}
            icon={btnIcon}
          >
            {actionText}
          </Button>
        )}
      </div>
    </div>
  );
};

export default EmptyView;
