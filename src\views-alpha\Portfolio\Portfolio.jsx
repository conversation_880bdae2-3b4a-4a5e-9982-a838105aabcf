import { useState, useEffect } from "react";
import { twMerge } from "tailwind-merge";
import clsx from "clsx";
import { connect } from "react-redux";
import EmptyIcon from "../../assets-alpha/images/svg/portfolio-empty-icon.svg?.react";
import ImgSrc from "../../assets-alpha/images/svg/profile-image.svg";
import find from "lodash/find";
import head from "lodash/head";
import isEmpty from "lodash/isEmpty";
import map from "lodash/map";
import {
  isValidPhoneNumber,
  isValidURL,
  loadImageOrientation
} from "../../utilities/helpers";
import { notificationAction } from "../../actions/notification";
import {
  getResumesApi,
  getUserEmailsApi,
  getUserPhonenumbersApi,
  presentApi,
  presentDeleteApi,
  presentSubmitApi,
  updatePresentApi
} from "./portfolioApi";
import { PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Drawer } from "antd";
import { onStateChangeAction } from "./portfolioAction";
import ConfirmDialog from "../../common/ConfirmDialog/ConfirmDialog";
import PageWrapper from "../../components-alpha/PageWrapper/PageWrapper";
import PreviewImages from "./components/PreviewImages";
import EmptyView from "./components/EmptyView";
import { useLocation } from "react-router-dom";
import Showcase from "./components/Showcase";
import NewPortfolio from "./components/NewPortfolio";
import { BlurBgPortfolio } from "./Utils/Icon";
import DeleteModal from "../../common-alpha/DeleteModal/DeleteModal";
const Portfolio = props => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [updatedAvatar, setupdatedAvatar] = useState([]);
  const [showActionBtns, setShowActionBtns] = useState(true);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [showPreviewImages, setShowPreviwImages] = useState(false);
  const [isEditForm, setIsEditForm] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const location = useLocation();
  useEffect(() => {
    props.isFetching && getPresentList();
    getResumes();
    getUserEmails();
    getUserPhonenumbers();
  }, [refresh]);
  useEffect(() => {
    if (props?.presentForm?.PortfolioImages) {
      const formattedImages = props.presentForm.PortfolioImages.map(img => ({
        PortfolioImageId: img.PortfolioImageId,
        Image: img.Image,
        isThumb: img.IsThumb,
        uid: img.PortfolioImageId?.toString(),
        status: "done"
      }));
      setFileList(formattedImages);
      setupdatedAvatar(formattedImages);
    }
  }, [props?.presentForm?.PortfolioImages]);
  useEffect(() => {
    props.onStateChangeAction({
      presentForm
    });
    props.onStateChangeAction({ profiles });
  }, [location, refresh]);
  const getResumes = () => {
    getResumesApi().then(data => {
      const profiles = data.items.map(item => ({
        ...item,
        value: item.ProfileId,
        label: item.ProfileValue
      }));
      props.onStateChangeAction({ profiles });
    });
  };

  const getUserEmails = () => {
    getUserEmailsApi().then(data => {
      const filterEmail = data.items.filter(item => item.UserEmailValue);
      const userEmails = filterEmail.map(item => ({
        ...item,
        value: item.UserEmailValue,
        label: item.UserEmailValue
      }));
      props.onStateChangeAction({
        userEmails
      });
    });
  };

  const getUserPhonenumbers = () => {
    getUserPhonenumbersApi().then(data => {
      const filterPhone = data.items.filter(item => item.UserPhonenumberValue);
      const userPhonenumbers = filterPhone.map(item => ({
        ...item,
        value: item.UserPhonenumberValue,
        label: item.UserPhonenumberValue
      }));
      props.onStateChangeAction({
        userPhonenumbers
      });
    });
  };

  const getPresentList = () => {
    props.onStateChangeAction({ isFetching: true });
    presentApi()
      .then(data => {
        let presentations = [];
        if (data?.items?.length > 0) {
          presentations = map(data.items, presentation => {
            const newItem = {
              ...presentation,
              selectedPhoneNumber: {
                ...presentation.UserPhonenumbers,
                value:
                  presentation.UserPhonenumbers &&
                  presentation.UserPhonenumbers.UserPhonenumberId,
                label:
                  presentation.UserPhonenumbers &&
                  presentation.UserPhonenumbers.UserPhonenumberValue
              },
              IsDefault: presentation.IsDefault == true ? true : false,
              IsChecked: presentation.IsDefault == true ? true : false
            };
            return newItem;
          });
        } else {
          presentations = [];
        }

        props.onStateChangeAction({
          presentList: presentations,
          filteredList: presentations,
          isLoading: false,
          isFetching: false
        });
      })
      .catch(() => {
        props.onStateChangeAction({
          isFetching: false,
          isLoading: false
        });
      });
  };

  const handleCreateNewClick = () => {
    const emptyPresentForm = { UserDetailId: -1 };
    props.onStateChangeAction({ presentForm: emptyPresentForm });
    props.onStateChangeAction({ profiles });
    setFileList([]);
    setupdatedAvatar([]);
    setIsModalOpen(true);
    var { profiles } = props;
    if (profiles?.length === 0) {
      const info = {
        dialogMessage: labels.Present_ProfileNotFound,
        status: "info"
      };
      props.notificationAction(info);
    } else {
      const presentForm = { UserDetailId: -1 };
      props.onStateChangeAction({ presentForm });
      setIsModalOpen(true);
    }
  };

  const handlePresentClick = id => {
    var { presentList } = props;

    const presentForm = find(presentList, { UserDetailId: id });

    props.onStateChangeAction({
      presentForm
    });
    setIsDrawerOpen(true);
  };

  const handleDeletePresent = id => {
    setShowActionBtns(true);
    props.onStateChangeAction({
      deletedId: id,
      dialogMessage: ""
    });
  };

  const handleYesClick = () => {
    const { deletedId } = props;
    props.onStateChangeAction({
      dialogMessage: "",
      isLoading: true
    });
    props.onStateChangeAction({
      presentForm
    });
    props.onStateChangeAction({
      profiles
    });
    presentDeleteApi({ id: deletedId })
      .then(data => {
        if (data) {
          getPresentList();
          const formattedMessage = data?.message
            .split(/<\/?br\s*\/?>/gi)
            .map((line, id) => (
              <p key={id} className="!p-0 !m-0">
                {line}
              </p>
            ));
          const info = {
            message: data?.message,
            status: data.success ? "success" : "info"
          };
          props.notificationAction(info);
          setShowActionBtns(false);
          props.onStateChangeAction({
            dialogMessage: formattedMessage,
            deletedId: null
          });
          setRefresh(prev => !prev);
          setIsDrawerOpen(false);
          setDeleteDialog(false);
        }
        setTimeout(() => {
          props.onStateChangeAction({ dialogMessage: "" });
        }, 3000);
      })
      .catch(() => {
        props.onStateChangeAction({ isLoading: false });
      });
  };

  const handleNoClick = () => {
    props.onStateChangeAction({
      dialogMessage: "",
      deletedId: null
    });
    setDeleteDialog(false);
  };

  const handleFormSelectChange = (name, childName, selectedOption) => {
    if (!selectedOption) return;
    const { presentForm, userPhonenumbers } = props;
    const { value, UserPhonenumberId } = selectedOption;
    if (name === "UserPhonenumbers") {
      if (UserPhonenumberId) {
        props.onStateChangeAction({
          presentForm: {
            ...presentForm,
            selectedPhoneNumber: selectedOption,
            Phone: UserPhonenumberId,
            UserPhonenumberId
          }
        });
        return;
      }
      const newPhoneNumber = head(userPhonenumbers);
      if (
        newPhoneNumber &&
        newPhoneNumber.value &&
        !isValidPhoneNumber(newPhoneNumber.value)
      ) {
        userPhonenumbers.splice(0, 1);
        props.onStateChangeAction({
          userPhonenumbers: [...userPhonenumbers]
        });
        return;
      } else {
        props.onStateChangeAction({
          presentForm: {
            ...presentForm,
            newPhoneCreated: true,
            Phone: value,
            selectedPhoneNumber: selectedOption
          }
        });
        return;
      }
    }
    props.onStateChangeAction({
      presentForm: {
        ...presentForm,
        [name]: {
          ...presentForm[name],
          ...selectedOption
        },
        invalidRole: name === "Profiles" && !selectedOption.value
      }
    });
  };

  const handleSelectBlur = e => {
    const { presentForm } = props;
    props.onStateChangeAction({
      presentForm: {
        ...presentForm,
        invalidRole: presentForm && !presentForm.Profiles
      }
    });
  };

  const validateUrl = ({ name, value }) => {
    const { presentForm } = props;
    props.onStateChangeAction({
      presentForm: {
        ...presentForm,
        [name]: value,
        [`invalid${name}`]: !(value && isValidURL(value))
      }
    });
  };
  const handleFormFieldChange = e => {
    const { name, value } = e.target;
    const { presentForm } = props;

    if (name === "WebUrl") {
      if (!value.trim()) {
        props.onStateChangeAction({
          presentForm: {
            ...presentForm,
            WebUrl: value,
            invalidWebUrl: false
          }
        });
      } else {
        validateUrl({ name, value });
      }
      return;
    }

    if (name === "IsDefault") {
      props.onStateChangeAction({
        presentForm: {
          ...presentForm,
          [name]: !presentForm.IsDefault
        }
      });
      return;
    }

    props.onStateChangeAction({
      presentForm: {
        ...presentForm,
        [name]: value,
        [`invalid${name}`]: !value
      }
    });
  };
  const handleFormFieldBlur = e => {
    let { name, value } = e.target;
    const { presentForm } = props;

    if (name === "WebUrl") {
      const trimmedValue = value.trim();

      if (
        trimmedValue &&
        trimmedValue.indexOf("http") < 0 &&
        trimmedValue.indexOf("https") < 0 &&
        trimmedValue.indexOf("ftp") < 0
      ) {
        value = "http://" + trimmedValue;
        props.onStateChangeAction({
          presentForm: {
            ...presentForm,
            WebUrl: value
          }
        });
      }
      if (!trimmedValue) {
        props.onStateChangeAction({
          presentForm: {
            ...presentForm,
            WebUrl: "",
            invalidWebUrl: false
          }
        });
      } else {
        validateUrl({ name, value });
      }
      return;
    }
  };
  const setThumbnailIndex = index => {
    setFileList(prev =>
      prev.map((item, i) => ({
        ...item,
        isThumb: i === index
      }))
    );

    setupdatedAvatar(prev =>
      prev.map((item, i) => ({
        PortfolioImageId: item.PortfolioImageId || 0,
        Image: item.Image || item.image || "",
        isThumb: i === index
      }))
    );

    const { presentForm } = props;
    if (presentForm && presentForm.PortfolioImages) {
      const updatedPortfolioImages = presentForm.PortfolioImages.map(
        (img, i) => ({
          ...img,
          IsThumb: i === index
        })
      );

      props.onStateChangeAction({
        presentForm: {
          ...presentForm,
          PortfolioImages: updatedPortfolioImages
        }
      });
    }
  };

  const handleDeleteImage = index => {
    const updatedFiles = [...fileList];
    if (updatedFiles[index]?.PortfolioImageId) {
      updatedFiles[index] = {
        ...updatedFiles[index],
        Delete: true
      };
    } else {
      updatedFiles.splice(index, 1);
    }
    setFileList(updatedFiles);
    setupdatedAvatar(updatedFiles);
  };

  const handleFileChange = file => {
    if (file) {
      loadImageOrientation(file, processedImage => {
        const cleanApiFile = {
          PortfolioImageId: 0,
          Image: processedImage,
          isThumb: false
        };

        const uiFile = {
          ...cleanApiFile,
          uid: Date.now().toString(),
          name: file.name,
          status: "done"
        };

        setFileList(prev => [...prev, uiFile]);
        setupdatedAvatar(prev => [...prev, cleanApiFile]);
      });
    }
  };
  const handleGetImgSrc = () => {
    const { presentForm } = props;
    props.onStateChangeAction({
      presentForm: {
        ...presentForm,
        Logo: null
      }
    });
  };
  const handleSubmitPresent = () => {
    const { presentForm } = props;
    let { WebUrl } = presentForm;
    const {
      Title,
      Detail,
      Profiles,
      UserEmails,
      UserDetailId,
      Logo,
      selectedPhoneNumber,
      IsDefault
    } = presentForm;

    if (!Profiles || !Title) {
      const { presentForm } = props;
      props.onStateChangeAction({
        presentForm: {
          ...presentForm,
          invalidTitle: !Title,
          invalidRole: !Profiles
        }
      });
      const info = {
        message: props.labels.presentFixValidation,
        status: "error"
      };
      props.notificationAction(info);
      return;
    }

    if (
      !isEmpty(WebUrl) &&
      WebUrl.indexOf("http") < 0 &&
      WebUrl.indexOf("https") < 0 &&
      WebUrl.indexOf("ftp") < 0
    ) {
      WebUrl = "http://" + WebUrl;
      props.onStateChangeAction({
        presentForm: {
          ...presentForm,
          WebUrl: WebUrl
        }
      });
    }

    if (!isEmpty(WebUrl) && !isValidURL(WebUrl)) {
      const info = {
        message: props.labels.presentWebUrlInvalid,
        status: "error"
      };
      props.notificationAction(info);
      return;
    }

    props.onStateChangeAction({
      presentForm: {
        ...presentForm,
        isSubmitting: true,
        invalidWebUrl: false
      }
    });
    const PortfolioImages = updatedAvatar.map(item => ({
      PortfolioImageId: item.PortfolioImageId || 0,
      Image: item.Delete === true ? "" : item.Image || item.image || "",
      isThumb:
        item.Delete === true ? false : item.isThumb ?? item.IsThumb ?? false,
      Delete: item.Delete === true
    }));

    const presentObject = {
      Title,
      WebUrl,
      Detail,
      UserDetailId: UserDetailId,
      ProfileId: Profiles && Profiles.ProfileId,
      UserEmailId: UserEmails && UserEmails.UserEmailId,
      Email: UserEmails && UserEmails.UserEmailId,
      UserPhonenumberId:
        selectedPhoneNumber && selectedPhoneNumber.UserPhonenumberId,
      PortfolioImages,
      UserPhonenumberValue: selectedPhoneNumber && selectedPhoneNumber.value,
      IsDefault
    };
    props.onStateChangeAction({ isLoading: true });

    if (UserDetailId === -1) {
      presentSubmitApi(presentObject)
        .then(data => {
          if (data.success) {
            setupdatedAvatar([]);
            getPresentList();
            setIsModalOpen(false);
            const info = {
              message: props.labels.presentSuccessfullySaved,
              status: "success"
            };
            props.notificationAction(info);
          }
          const presentForm = { UserDetailId: -1 };
          props.onStateChangeAction({
            presentForm,
            isLoading: false
          });
        })
        .catch(() => {
          setupdatedAvatar([]);
          setIsModalOpen(false);
          props.onStateChangeAction({ isLoading: false });
        });
    }

    if (UserDetailId !== -1) {
      presentObject.Profiles = presentForm && presentForm.Profiles;
      presentObject.UserPhonenumbers =
        presentForm && presentForm.UserPhonenumbers;
      presentObject.UserEmails = presentForm && presentForm.UserEmails;
      presentObject.Email = presentObject.UserEmailId;
      presentObject.Phone = presentObject.UserPhonenumberId;
      updatePresentApi(presentObject)
        .then(data => {
          setupdatedAvatar([]);
          if (data.success) {
            setIsModalOpen(false);
            getPresentList();
            const info = {
              message: props.labels.presentSuccessfullySaved,
              status: "success"
            };
            props.notificationAction(info);
          }
          props.onStateChangeAction({ isLoading: false });
        })
        .catch(() => {
          props.onStateChangeAction({ isLoading: false });
          setIsModalOpen(false);
        });
    }
  };

  const {
    filteredList,
    presentForm,
    dialogMessage,
    profiles,
    userEmails,
    userPhonenumbers,
    isLoading
  } = props;
  const { labels } = props;
  return (
    <>
      {showPreviewImages ? (
        <PreviewImages
          presentForm={presentForm}
          setShowPreviwImages={setShowPreviwImages}
        />
      ) : (
        <PageWrapper>
          {dialogMessage && (
            <ConfirmDialog testId="present-confirm-diloag">
              <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
              {showActionBtns && (
                <ConfirmDialog.ActionButtons>
                  <ConfirmDialog.Button
                    className="dialog-btn"
                    testId="present-delete-yes"
                    onClick={handleYesClick}
                  >
                    {labels.presentDeleteCurtainYESBtnText}
                  </ConfirmDialog.Button>
                  <ConfirmDialog.Button
                    className="dialog-btn"
                    testId="present-delete-no"
                    onClick={handleNoClick}
                  >
                    {labels.presentDeleteCurtainNOBtnText}
                  </ConfirmDialog.Button>
                </ConfirmDialog.ActionButtons>
              )}
            </ConfirmDialog>
          )}

          <div className="flex flex-col w-full md:gap-5 gap-[9.5px]">
            <div className="flex items-start w-full !h-[22px]  md:!h-9 justify-between md:justify-center">
              <h1 className="flex w-[50%] !m-0 !p-0 !font-semibold !text-[18px] md:!text-2xl !text-[#343333] leading-[100%]">
                {labels?.IPRO_NAVIGATION_PRESENTATION_LABEL}
              </h1>
              <div className="hidden sm:!flex !w-full !justify-end">
                {filteredList?.length > 0 && (
                  <Button
                    type="primary"
                    className="!w-[83px] !h-9 "
                    icon={<PlusOutlined />}
                    onClick={() => {
                      handleCreateNewClick();
                    }}
                  >
                    {labels?.IPRO_Portfolio_Button_Text}
                  </Button>
                )}
              </div>
            </div>
            <div className="flex w-full">
              {filteredList?.length > 0 ? (
                <div
                  className={twMerge(
                    clsx(
                      "flex !w-full flex-wrap lg:justify-start justify-center gap-3 !ml-0 mb-8"
                    )
                  )}
                >
                  {filteredList?.map((item, index) => (
                    <div
                      className="flex  flex-col md:!w-[376px] !w-[343px] md:h-[296.67px] h-[283px] relative pointer"
                      key={index}
                      onClick={() => handlePresentClick(item?.UserDetailId)}
                    >
                      <div
                        key={item?.UserDetailId}
                        className="flex justify-center pointer border border-[#EAE5FC] rounded-2xl"
                      >
                        <div className="flex md:!h-[296.67px] !h-[283px] min-w-[346px]">
                          <div
                            className={`${
                              item?.Logo
                                ? ""
                                : "flex w-full h-full justify-center items-start"
                            }`}
                          >
                            {item?.PortfolioImages?.some(img => img.IsThumb) ? (
                              <img
                                className="md:!w-[376px] !w-[343px]  md:!max-h-[296.67px] !rounded-2xl h-full object-cover"
                                src={
                                  item?.PortfolioImages?.find(
                                    img => img.IsThumb
                                  )?.Image
                                }
                                alt={item?.UserName || "Portfolio Image"}
                              />
                            ) : (
                              <div className="flex !w-full items-start justify-center h-full mt-[47px]">
                                <img
                                  src={ImgSrc}
                                  alt="Portfolio"
                                  className="object-cover"
                                />
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="absolute bottom-0 w-full rounded-bl-2xl rounded-br-2xl overflow-hidden">
                        <div className="relative backdrop-blur-[1px]">
                          <BlurBgPortfolio />
                        </div>
                        <div className="absolute bottom-[17.34px]">
                          <div className="flex justify-between items-center md:w-[376px] w-[343px] pl-[17.36px] pr-[18.03px]">
                            <div className="flex flex-col gap-1">
                              <label className="md:text-[16px] text-[14px] font-semibold text-[#FFFFFF]">
                                {item.Title?.length > 30
                                  ? `${item.Title.slice(0, 30)}...`
                                  : item?.Title}
                              </label>
                              <label className="text-[#E7E7E7] text-[12px] font-medium leading-[100%]">
                                {item?.Profiles?.ProfileValue}
                              </label>
                            </div>
                            <div>
                              {item?.IsDefault && (
                                <button
                                  type="default"
                                  className="w-[66.73px] h-[33px] rounded-[99px] border-[0.5px] border-[#FFFFFF] text-[#FFFFFF] bg-[#FFFFFF33] font-medium text-[14px]"
                                >
                                  Default
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex w-full h-[calc(100vh-300px)] items-center justify-center">
                  <EmptyView
                    icon={<img src={EmptyIcon} alt="Empty" />}
                    title={labels?.IPRO_Portfolio_EmptyScreen_Title}
                    description={labels?.IPRO_Portfolio_EmptyScreen_Description}
                    btnicon={<PlusOutlined />}
                    actionText={labels?.IPRO_Portfolio_Button_Text_Add}
                    onAction={handleCreateNewClick}
                  />
                </div>
              )}
            </div>
            <div className="[&_.ant-drawer-body]:[-ms-overflow-style:none] [&_.ant-drawer-body]:[scrollbar-width:none] [&_.ant-drawer-body::-webkit-scrollbar]:[display:none] [&_.ant-drawer-body::-webkit-scrollbar]:[width:0] [&_.ant-drawer-body::-webkit-scrollbar]:[height:0]">
              <Drawer
                placement="right"
                closable={true}
                open={isModalOpen}
                key="placement"
                headerStyle={{ display: "none" }}
                onClose={() => {
                  setIsModalOpen(false);
                }}
                rootClassName=" 
                  sm:[&_.ant-drawer-content-wrapper]:!m-[8px]
                  [&_.ant-drawer-content-wrapper]:!mt-[8px] 
                  md:[&_.ant-drawer-content]:!rounded-[16px]
                  [&_.ant-drawer-content]:!rounded-tl-[16px]
                  [&_.ant-drawer-content]:!rounded-tr-[16px]
                  sm:[&_.ant-drawer-content-wrapper]:!w-[497px]
                  [&_.ant-drawer-content-wrapper]:!w-[100%]
                  [&_.ant-drawer-content-wrapper]:!rounded-[16px]
                  [&_.ant-drawer-body]:!p-[0px]"
              >
                <div className="w-full h-full">
                  <NewPortfolio
                    labels={props?.labels}
                    isLoading={isLoading}
                    profiles={profiles}
                    userEmails={userEmails}
                    userPhonenumbers={userPhonenumbers}
                    presentForm={presentForm}
                    onFormFieldChange={handleFormFieldChange}
                    onFormFieldBlur={handleFormFieldBlur}
                    onSubmitPresent={handleSubmitPresent}
                    onFileChange={handleFileChange}
                    onSelectBlur={handleSelectBlur}
                    onFormSelectChange={handleFormSelectChange}
                    getImgSrc={handleGetImgSrc}
                    handleSave={handleSubmitPresent}
                    getUserPhonenumbers={getUserPhonenumbers}
                    handleDelete={handleDeletePresent}
                    setIsModalOpen={setIsModalOpen}
                    fileList={fileList}
                    setFileList={setFileList}
                    setThumbnailIndex={setThumbnailIndex}
                    handleDeleteImage={handleDeleteImage}
                    isEditForm={isEditForm}
                    setIsEditForm={setIsEditForm}
                    setRefresh={setRefresh}
                  />
                </div>
              </Drawer>
            </div>
            <div className="[&_.ant-drawer-body]:[-ms-overflow-style:none] [&_.ant-drawer-body]:[scrollbar-width:none] [&_.ant-drawer-body::-webkit-scrollbar]:[display:none] [&_.ant-drawer-body::-webkit-scrollbar]:[width:0] [&_.ant-drawer-body::-webkit-scrollbar]:[height:0]">
              <Drawer
                placement="right"
                closable={true}
                open={isDrawerOpen}
                key="placement"
                headerStyle={{ display: "none" }}
                onClose={() => {
                  setIsDrawerOpen(false);
                }}
                bodyStyle={{
                  padding: 0,
                  maxHeight: "955px"
                }}
                rootClassName=" 
                      sm:[&_.ant-drawer-content-wrapper]:!m-[8px]
                      [&_.ant-drawer-content-wrapper]:!mt-[8px]
                      md:[&_.ant-drawer-content]:!rounded-[16px]
                      [&_.ant-drawer-content]:!rounded-tl-[16px]
                      [&_.ant-drawer-content]:!rounded-tr-[16px]
                      md:[&_.ant-drawer-content-wrapper]:!w-[497px]
                      [&_.ant-drawer-content-wrapper]:!w-[100%]
                      [&_.ant-drawer-content-wrapper]:!rounded-[16px]
                      [&_.ant-drawer-body]:p-[32px_24px]
                      [&_.ant-drawer-body]:!scrollbar-width-none"
              >
                <Showcase
                  labels={props?.labels}
                  isLoading={isLoading}
                  profiles={profiles}
                  userEmails={userEmails}
                  userPhonenumbers={userPhonenumbers}
                  presentForm={presentForm}
                  onFormFieldChange={handleFormFieldChange}
                  onFormFieldBlur={handleFormFieldBlur}
                  onSubmitPresent={handleSubmitPresent}
                  onFileChange={handleFileChange}
                  onSelectBlur={handleSelectBlur}
                  onFormSelectChange={handleFormSelectChange}
                  getImgSrc={handleGetImgSrc}
                  handleSave={handleSubmitPresent}
                  getUserPhonenumbers={getUserPhonenumbers}
                  handleDelete={handleDeletePresent}
                  setIsModalOpen={setIsModalOpen}
                  isDrawerOpen={isDrawerOpen}
                  setIsDrawerOpen={setIsDrawerOpen}
                  fileList={fileList}
                  setFileList={setFileList}
                  setShowPreviwImages={setShowPreviwImages}
                  setDeleteDialog={setDeleteDialog}
                  isEditForm={isEditForm}
                  setIsEditForm={setIsEditForm}
                />
              </Drawer>
            </div>
            <div className="!w-[343px] md:w-[455px]">
              <DeleteModal
                open={deleteDialog}
                onCancel={() => setDeleteDialog(false)}
                onConfirm={handleYesClick}
                isLoading={isLoading}
                title={labels?.IPRO_Portfolio_DeleteModal_Title}
                description={labels?.Portfolio_Modal_Delete_Text}
                cancelText={labels?.ShortlistResumeListCurtainCancelBtnText}
                confirmText={labels?.delete_presentation}
              />
            </div>
          </div>
          {filteredList?.length > 0 && (
            <div className="sm:!hidden !ml-[-16px] !pl-4 !pr-4 !flex !items-center !justify-center !w-full fixed bottom-0 h-[70px] bg-white border-t-[0.5px] border-[#EAE5FC]">
              <Button
                type="primary"
                className="!w-full !h-9 "
                icon={<PlusOutlined />}
                onClick={() => {
                  handleCreateNewClick();
                }}
              >
                {labels?.IPRO_Portfolio_Button_Text}
              </Button>
            </div>
          )}
        </PageWrapper>
      )}
    </>
  );
};

const mapStateToProps = ({ systemLabel, userInfo, navigation, present }) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  const { isHelpActive } = navigation;

  return { labels, user, isHelpActive, ...present };
};
const mapActionToProps = {
  onStateChangeAction,
  notificationAction
};
export default connect(mapStateToProps, mapActionToProps)(Portfolio);
