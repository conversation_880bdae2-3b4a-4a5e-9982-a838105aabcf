import { <PERSON><PERSON>, Drawer, Modal } from "antd";
import filter from "lodash/filter";
import includes from "lodash/includes";
import map from "lodash/map";
import toLower from "lodash/toLower";
import moment from "moment";
import { useEffect, useState } from "react";
import { connect, useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import { unreadCollaborationsAction } from "../../../actions/navigationActions";
import ConfirmDialog from "../../../common/ConfirmDialog/ConfirmDialog";
import LoadingMask from "../../../common/LoadingMask/LoadingMask";
import NewCollaborationDetail from "../NewSearcher/components/common/NewCollaborationDetail";
import ContractEmpty from "../../../assets-alpha/images/view/empty-contract-selected.svg";
import ContractEmptyMain from "../../../assets-alpha/images/view/contract-main-empty.svg";
import ContractEmptyImage from "../../../assets-alpha/images/view/ipro-empty-contract.svg";
import { onStateChangeAction } from "./collaborationAction";
import {
  deleteCollaborationApi,
  // collaborationReviewApi,
  getAllCollaborationApi,
  getCollaborationReasonsApi,
  getCollaborationReviewApi,
  getCurrenciesApi,
  getReviewQuestions,
  readCollaborationApi,
  submitCollaborationReview,
  updateCollaborationApi,
  extendCollaboration
} from "./collaborationApi";
import { notificationAction } from "../../../actions/notification";
import EmptyView from "../../../common-alpha/EmptyViewUpdated/EmptyView";
import { PlusOutlined } from "@ant-design/icons";
import NewCreateCollaboration from "../NewSearcher/createCollobration";
import DeleteModal from "../../../common-alpha/DeleteModal/DeleteModal";
import NewOpportunity from "../../Opportunity/Ipro/common/NewOpportunity";
import CreateNewOpportunity from "../../Opportunity/Searcher/NewCreateOpportunity/NewCreateOpportunity";
import RatingModal from "./components/RatingModal";
import FeedbackModal from "./components/FeedbackModal";
import PageWrapper from "../../../components-alpha/PageWrapper/PageWrapper";
const NewCollaboration = props => {
  const { createContract } = props;
  const dispatch = useDispatch();
  const location = useLocation();

  // ---------- Default states (first time / page reload pe ye hi hon gy) ----------
  const [contractsActive, setContractsActive] = useState(false);
  const [jobInvitationActive, setJobInvitationActive] = useState(true);
  const [sentInviteActive, setSentInviteActive] = useState(true);
  const [draftActive, setDraftActive] = useState(false);
  const [createJobInvitation, setCreateJobInvitation] = useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [createNewCollaboration, setCreateNewCollaboration] = useState(false);
  useEffect(() => {
    if (!location.state) return;
    if (location.state?.newnetwork) {
      setJobInvitationActive(true);
      setCreateJobInvitation(true);
      setContractsActive(false);
      setCreateNewCollaboration(false);
      return;
    }
    if (
      location.state?.newInvitation &&
      location.state?.from === "/resume-search"
    ) {
      setCreateJobInvitation(true);
      setJobInvitationActive(true);
      setContractsActive(false);
      setCreateNewCollaboration(false);
      return;
    }
    if (location.state?.from === "/connected-networks") {
      setContractsActive(true);
      setJobInvitationActive(false);
      setCreateNewCollaboration(true);
      setCreateJobInvitation(false);
      return;
    }
    if (location.state === "/connected-networks") {
      setContractsActive(true);
      setJobInvitationActive(false);
      setCreateNewCollaboration(true);
      setCreateJobInvitation(false);
      return;
    }
  }, [location.state]);

  const [isRatingView, setisRatingView] = useState(false);
  const [availableDetail, setAvailableDetail] = useState();
  const [refresh, setRefresh] = useState(false);
  const [Currencies, setCurrencies] = useState([]);
  const [activeShowMore, setActiveShowMore] = useState(false);
  const [showUserDetail, setShowUserDetail] = useState(false);
  const [endContract, setEndContract] = useState(false);
  const [deleteContract, setDeleteContract] = useState(false);
  const [filteredJobList, setFilteredJobList] = useState(0);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showCalendar, setShowCalendar] = useState(false);
  const [jobInviteData, setJobInviteData] = useState("");
  const [tempSelectedDate, setTempSelectedDate] = useState(
    new Date(selectedDate)
  );
  const [shortlistId, setShortlistId] = useState("");
  const [options, setOptions] = useState([]);
  const [allReviewQuestions, setAllReviewQuestions] = useState([]);
  const [questions, setQuestions] = useState([]);
  const [state, setState] = useState({
    fetchingReviews: false,
    isDeleteCollaboration: false,
    CollborationCloseReasonId: null,
    windowWidth: window.innerWidth,
    isMobileDevice: false,
    active: "",
    mobileModal: false
  });

  const handleResize = () => {
    setState(st => ({
      ...st,
      windowWidth: window.innerWidth,
      isMobileDevice: window.innerWidth <= 768
    }));
  };

  useEffect(() => {
    if (!location?.state?.selectedContract) return;
    setIsDrawerOpen(!!location?.state?.selectedContract);

    setJobInvitationActive(false);
    setContractsActive(true);
    setDraftActive(false);
    setSentInviteActive(true);
    dispatch(
      onStateChangeAction({
        // selectedCollaboration: {},
        actionName: props.isIproActiveCollaboration
          ? "iproActive"
          : "searcherAccepted",
        selectedCollaboration: location?.state?.selectedContract
      })
    );
  }, [
    location?.state?.selectedContract,
    dispatch,
    props.isIproActiveCollaboration
  ]);
  useEffect(() => {
    const {
      isIproActiveCollaboration,
      isIproInActiveCollaboration,
      isSentView,
      isAcceptedView,
      isDeclinedView,
      isInActiveView
    } = props;
    window.actionName = isSentView
      ? "searcherPending"
      : isAcceptedView
      ? "searcherAccepted"
      : isInActiveView
      ? "searcherInActive"
      : isDeclinedView
      ? "searcherDeclined"
      : isIproActiveCollaboration
      ? "iproActive"
      : isIproInActiveCollaboration
      ? "iproInActive"
      : "iproNew";
    // getCurrencies();
    window.addEventListener("resize", handleResize);
    setState(st => ({ ...st, active: location.pathname }));
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);
  const getCurrencies = access_token => {
    dispatch(onStateChangeAction({ isLoading: true }));
    getCurrenciesApi()
      .then(res => {
        if (res.success) {
          const currencies = res.items.map(item => ({
            ...item,
            value: item.CurrencyId,
            label: item.Name
          }));
          dispatch(
            onStateChangeAction({
              currencies
            })
          );
          // getAllCollaboration(currencies);
        } else {
          dispatch(onStateChangeAction({ isLoading: false }));
        }
      })
      .catch(err => {
        dispatch(onStateChangeAction({ isLoading: false }));
      });
  };
  useEffect(() => {
    getAllCollaboration();
  }, [refresh]);
  const getAllCollaboration = CurrenciesParams => {
    const { url } = props;
    let currencies = CurrenciesParams || props.currencies;
    dispatch(onStateChangeAction({ fetchingCollaborations: true }));
    getAllCollaborationApi({ url })
      .then(data => {
        if (data.success) {
          let collaborations = data.items || [];
          collaborations = collaborations.map(collab => {
            const processed = { ...collab };
            if (props.isSentView) {
              processed.IsRead = true;
            }
            if (
              props.isDeclinedView ||
              props.isAcceptedView ||
              props.isInActiveView
            ) {
              processed.IsRead = collab.IsSearcherRead;
            }
            if (!isNaN(collab.HourlyRateType)) {
              processed.HourlyRateType =
                currencies.find(c => c.CurrencyId == collab.HourlyRateType)
                  ?.Name || collab.HourlyRateType;
            }
            return {
              ...processed,
              label: processed.Title,
              value: processed.CollaborationId
            };
          });
          dispatch(
            onStateChangeAction({
              collaborationList: collaborations,
              filteredCollaborations: collaborations,
              fetchingCollaborations: false
            })
          );
          const CollaborationId = location.state
            ? location.state.CollaborationId
            : null;
          const spliturl = location.pathname.match(/\d+/g);
          var CollabIDFromURL = spliturl ? parseInt(spliturl?.[0]) : null;

          if (CollaborationId != null || CollabIDFromURL != null) {
            let filteredCollab = data.items.filter(
              coll =>
                coll.CollaborationId == CollaborationId ||
                coll.CollaborationId == CollabIDFromURL
            );
            if (filteredCollab && filteredCollab.length > 0) {
              if (state?.windowWidth > 767) {
                handleSelectCollaboration(filteredCollab[0], collaborations);
              }
            } else {
              const info = {
                message: "Collaboration has already been deleted by Searcher",
                status: "error"
              };
              dispatch(notificationAction(info));
            }
          }
          filterList(props.searchKey, collaborations);
          dispatch(onStateChangeAction({ isLoading: false }));
        } else {
          dispatch(onStateChangeAction({ isLoading: false }));
        }
      })
      .catch(() => {
        dispatch(onStateChangeAction({ isLoading: false }));
      });
  };
  const filterList = (searchKey, collaborationList) => {
    const filteredCollaborations = filter(collaborationList, collaboration => {
      if (includes(toLower(collaboration.Title), toLower(searchKey))) {
        return collaboration;
      }
    });

    dispatch(onStateChangeAction({ filteredCollaborations }));
  };
  const handleSearchChange = e => {
    const searchKey = e.target.value;
    dispatch(onStateChangeAction({ searchKey }));
    filterList(searchKey, props.collaborationList);
  };
  const handleOnAction = () => {
    dispatch(
      onStateChangeAction({
        shortlistResumes: null,
        selectedResume: null,
        isFetchingShortlistResume: false
      })
    );
    {
      jobInvitationActive
        ? setCreateJobInvitation(true)
        : setCreateNewCollaboration(true);
    }
  };
  const handleSelectCollaboration = (
    selectedCollaboration,
    collaborationList
  ) => {
    const { CollaborationId } = selectedCollaboration;
    const { isInActiveView, isIproInActiveCollaboration } = props;
    if (!selectedCollaboration?.IsRead) {
      if (!selectedCollaboration?.IsRead) {
        const {
          acceptedCollaboration,
          declinedCollaboration,
          isAcceptedView,
          isInActiveView,
          isDeclinedView,
          isIproCollaboration,
          unreadproInactiveCollaborations,
          searcherInActiveCollaboration,
          isIproInActiveCollaboration,
          unreadCollaborations
        } = props;
        readCollaborationApi({
          collaborationId: CollaborationId
        })
          .then(response => {
            const unreadCollabaorations = filter(
              collaborationList,
              item => item.CollaborationId !== CollaborationId && !item.IsRead
            );
            if (isIproCollaboration) {
              dispatch(
                unreadCollaborationsAction({
                  unreadCollaborations: unreadCollabaorations.length,
                  unreadproInactiveCollaborations: unreadproInactiveCollaborations
                })
              );
            }
            if (isIproInActiveCollaboration) {
              dispatch(
                unreadCollaborationsAction({
                  unreadproInactiveCollaborations: unreadCollabaorations.length,
                  unreadCollaborations: unreadCollaborations
                })
              );
            }
            if (isInActiveView) {
              dispatch(
                unreadCollaborationsAction({
                  searcherInActiveCollaboration: unreadCollabaorations.length,
                  declinedCollaboration: declinedCollaboration,
                  acceptedCollaboration: acceptedCollaboration
                })
              );
            }
            if (isAcceptedView) {
              dispatch(
                unreadCollaborationsAction({
                  acceptedCollaboration: unreadCollabaorations.length,
                  declinedCollaboration: declinedCollaboration,
                  searcherInActiveCollaboration: searcherInActiveCollaboration
                })
              );
            }
            if (isDeclinedView) {
              dispatch(
                unreadCollaborationsAction({
                  acceptedCollaboration: acceptedCollaboration,
                  declinedCollaboration: unreadCollabaorations.length,
                  searcherInActiveCollaboration: searcherInActiveCollaboration
                })
              );
            }

            const filteredCollaborations = map(collaborationList, item => {
              if (item.CollaborationId === CollaborationId) {
                item.IsRead = true;
              }
              return item;
            });
            dispatch(
              onStateChangeAction({
                filteredCollaborations,
                selectedCollaboration
              })
            );
          })
          .catch(res => res);
      }
    }

    const selectedUser = {
      ...selectedCollaboration.company,
      ...selectedCollaboration.userdetail
    };
    if (isIproInActiveCollaboration || isInActiveView) {
      getReviewDetails(CollaborationId);
    }
    dispatch(
      onStateChangeAction({
        selectedCollaboration,
        selectedUser,
        selectedCompany: selectedCollaboration.company,
        dialogMessage: "",
        yesClickCount: 0
      })
    );
    if (state.windowWidth < 767) {
      setState(st => ({ ...st, isMobileDevice: true }));
    }
  };

  const handleCollaborationDelete = () => {
    setDeleteContract(true);
    const { collaborationDeleteConfirmation } = props;

    setState({ isDeleteCollaboration: true });
  };

  const handleCollaborationClose = () => {
    setEndContract(true);
    if (props.isSentView) {
      closeCollaboration();
    }
  };
  const handleDateSelect = date => {
    setSelectedDate(date);
  };
  const handleYesClick = () => {
    {
      deleteContract ? deleteCollaboration() : "";
      setEndContract(false);
    }
    const {
      yesClickCount,
      likeToRateCollaborationMessage,
      selectedCollaboration
    } = props;
    const { isDeleteCollaboration } = state;
    if (isDeleteCollaboration) {
      deleteCollaboration();
      return;
    }

    dispatch(onStateChangeAction({ fetchingReviews: true }));
    getReviewDetails(selectedCollaboration.CollaborationId);
    getCollaborationCloseReasons();
    setisRatingView(true);
    if (state.windowWidth < 768) {
      handleListOpenMobile();
    }
  };

  const deleteCollaboration = () => {
    const {
      selectedCollaboration: { CollaborationId },
      IsSearcher,
      collaborationDeletedMsg
    } = props;
    setState({ isLoading: true, dialogMessage: "" });
    const apiParams = `collaborationId=${CollaborationId}&${
      IsSearcher ? "IsSearcher=true" : "IsSearcher=false"
    }`;
    dispatch(onStateChangeAction({ isLoading: true, dialogMessage: "" }));
    deleteCollaborationApi({ apiParams }).then(response => {
      if (response.success) {
        setDeleteContract(false);
        setEndContract(false);
        const info = {
          message: collaborationDeletedMsg,
          status: "success"
        };
        dispatch(notificationAction(info));
        getAllCollaboration();
        dispatch(
          onStateChangeAction({
            isLoading: false,
            selectedCollaboration: {},
            yesClickCount: 0
          })
        );
      } else {
        setDeleteContract(false);
        setEndContract(false);
        const info = {
          message: "Something went wrong",
          status: "error"
        };
        dispatch(notificationAction(info));
        dispatch(
          onStateChangeAction({
            isLoading: false
          })
        );
      }
    });
  };

  const closeCollaboration = () => {
    const {
      selectedCollaboration: { CollaborationId },
      collaborationCloseMsg,
      IsSearcher
    } = props;
    setState({ isLoading: true, dialogMessage: "" });
    const apiParams = `collaborationId=${CollaborationId}&IsDeclined=false&${
      IsSearcher ? "IsSearcher=true" : "IsSearcher=false"
    }&isClosed=true`;
    dispatch(onStateChangeAction({ isLoading: true, dialogMessage: "" }));
    updateCollaborationApi({ apiParams }).then(response => {
      setEndContract(false);
      const info = {
        message: labels?.End_Contract_Message,
        status: "success"
      };
      dispatch(notificationAction(info));
      getAllCollaboration();
      dispatch(
        onStateChangeAction({
          isLoading: false,
          selectedCollaboration: {},
          yesClickCount: 0
        })
      );
    });
  };

  const handleNoClick = () => {
    const { yesClickCount } = props;
    if (yesClickCount === 0) {
      dispatch(
        onStateChangeAction({
          dialogMessage: "",
          selectedCompany: null
        })
      );
      return;
    }
    closeCollaboration();
  };

  const handleCollaborationAccept = () => {
    const {
      selectedCollaboration: { CollaborationId }
    } = props;
    const { collaborationAcceptedMsg } = props;
    const apiParams = `collaborationId=${CollaborationId}&IsDeclined=false`;
    dispatch(onStateChangeAction({ isLoading: true }));
    updateCollaborationApi({ apiParams }).then(response => {
      const info = {
        message: collaborationAcceptedMsg,
        status: "success"
      };
      dispatch(notificationAction(info));
      getAllCollaboration();
      dispatch(
        onStateChangeAction({
          isLoading: false,
          selectedCollaboration: {}
        })
      );
    });
  };

  const handleCollaborationDecline = () => {
    const {
      selectedCollaboration: { CollaborationId }
    } = props;
    const { collaborationDeclinedMsg } = props;
    const apiParams = `collaborationId=${CollaborationId}&IsDeclined=true`;
    dispatch(onStateChangeAction({ isLoading: true }));
    updateCollaborationApi({ apiParams }).then(response => {
      const info = {
        message: collaborationDeclinedMsg,
        status: "success"
      };
      dispatch(notificationAction(info));
      getAllCollaboration();
      dispatch(
        onStateChangeAction({
          isLoading: false,
          selectedCollaboration: {}
        })
      );
    });
  };
  const handleOkClick = () => {
    const {
      selectedCollaboration: { CollaborationId }
    } = props;
    const newEndDate = moment(tempSelectedDate).format("YYYY-MM-DD");
    const apiParams = `collaborationId=${CollaborationId}&newEndDate=${newEndDate}`;
    dispatch(onStateChangeAction({ isLoading: true }));
    extendCollaboration({ apiParams }).then(response => {
      if (response?.success) {
        setRefresh(prev => !prev);
        setShowCalendar(false);
        setIsDrawerOpen(false);
        dispatch(onStateChangeAction({ isLoading: false }));
        const info = {
          message: response?.message,
          status: "success"
        };
        dispatch(notificationAction(info));
      } else {
        setShowCalendar(false);
        setIsDrawerOpen(false);
        dispatch(onStateChangeAction({ isLoading: false }));
        const info = {
          message: response?.message,
          status: "error"
        };
        dispatch(notificationAction(info));
      }
    });
  };

  const handleEndCollaboration = () => {};
  const getReviewDetails = CollaborationId => {
    setState({ fetchingReviews: true });
    const { IsSearcher } = props;
    getReviewQuestions({ questionType: IsSearcher ? 1 : 2 })
      .then(res => {
        const reviewQuestions = res.items.map(r => ({
          ...r,
          ReviewQuestionValue: props.labels[r.Question],
          Answer: r.Question !== "Rating_Question5" ? r.Answer || 0 : "",
          CollaborationId: CollaborationId
        }));
        setAllReviewQuestions(reviewQuestions);
        getCollaborationReviewApi({
          collaborationId: CollaborationId,
          IsIProReview: !IsSearcher
        })
          .then(reviewRes => {
            const reviews = reviewRes.items;
            let currentCollborationCloseReasonId = null;
            dispatch(
              onStateChangeAction({
                reviewQuestions: reviewQuestions.map(a => {
                  const currentReview = reviews
                    ? reviews.find(
                        b => b.ReviewQuestionId == a.ReviewQuestionId
                      )
                    : null;
                  if (
                    currentReview &&
                    a.ReviewQuestionId == currentReview.ReviewQuestionId &&
                    a.Question === "Rating_Question6"
                  ) {
                    currentCollborationCloseReasonId =
                      currentReview.CollborationCloseReasonId;
                  }
                  setState({ currentReview: currentReview });
                  return {
                    ...a,
                    Answer:
                      currentReview != null ? currentReview.Answer : a.Answer,
                    IsIProReview: !IsSearcher
                  };
                }),

                isAlreadyRated: reviews && reviews.length > 0 ? true : false
              })
            );
            setState({ fetchingReviews: false });
            onFormSelectChange("", {
              value: currentCollborationCloseReasonId
            });
          })
          .catch(err => {
            setState({ fetchingReviews: false });
          });
      })
      .catch(err => {
        setState({ fetchingReviews: false });
      });
  };

  const getCollaborationCloseReasons = () => {
    getCollaborationReasonsApi().then(reasons => {
      if (reasons.success) {
        const reasonsList = reasons.items.map(item => ({
          value: item.CollborationCloseReasonId,
          label: props.labels[item.ReasonLabel] || item.ReasonLabel
        }));
        setOptions(reasonsList);
      }
    });
  };

  const handleRatingChange = (rating, value) => {
    const { reviewQuestions } = props;
    dispatch(
      onStateChangeAction({
        reviewQuestions: reviewQuestions.map(a =>
          a.ReviewQuestionId == rating.ReviewQuestionId
            ? {
                ...a,
                Answer:
                  rating.Question === "Rating_Question5"
                    ? value.target.value
                    : value
              }
            : a
        )
      })
    );
  };

  const onFormSelectChange = (answer, selectedOption) => {
    if (!selectedOption) return;
    const { reviewQuestions } = props;
    const { value } = selectedOption;
    setState({
      CollborationCloseReasonId: value
    });
    dispatch(
      onStateChangeAction({
        reviewQuestions: reviewQuestions.map(a =>
          a.Question === "Rating_Question6"
            ? {
                ...a,
                Answer: null,
                CollborationCloseReasonId: value
              }
            : a
        )
      })
    );

    return;
  };

  const handleSubmit = () => {
    const { reviewQuestions } = props;
    submitCollaborationReview({ collaborationReview: questions })
      .then(data => {
        setisRatingView(false);
        setState(st => ({ ...st, mobileModal: false }));
      })
      .catch(err => {});
    closeCollaboration();
  };
  const renderDate = date => {
    return moment(date).format("MM/DD/YYYY");
  };

  const handleMobileModalClose = () => {
    setState(st => ({ ...st, mobileModal: false }));
  };

  const handleListOpenMobile = () => {
    setState(st => ({ ...st, mobileModal: true }));
  };

  const {
    searchInputPlaceholder,
    emptyCollaorationInfo,
    startDateLabel,
    durationLabel,
    compensationLabel,
    companyLabel,
    descriptionLabel,
    acceptedCollaboration,
    selectInterest,
    companyAddressLabel,
    comapnyIndustryLabel,
    companyCountryLabel,
    companyPhoneLabel,
    companyWebUrlLabel,
    companyVatLabel,
    IsSearcher,
    popupNoText,
    popupYesText,
    selectedCollaboration,
    filteredCollaborations,
    selectedUser,
    isLoading,
    selectedCompany,
    dialogMessage,
    yesClickCount = 0,
    isInActiveView,
    isIproInActiveCollaboration,
    isDeclinedView,
    isAcceptedView,
    isSentView,
    isIproActiveCollaboration,
    isIproNewCollaboration,
    iProOpportunityNewDetailBtnAccept,
    iProOpportunityNewDetailBtnDecline,
    labels
  } = props;
  useEffect(() => {
    if (allReviewQuestions?.length) {
      const updated = allReviewQuestions.map(q => ({
        ...q,
        IsIProReview: !IsSearcher
      }));
      setQuestions(updated);
    }
  }, [allReviewQuestions, IsSearcher]);
  const { fetchingReviews, windowWidth } = state;
  const contractsEmptyView =
    filteredCollaborations?.length === 0 && contractsActive ? true : false;
  const jobInvitationEmptyView =
    filteredJobList < 1 && jobInvitationActive ? true : false;

  return (
    <PageWrapper className={"!p-0"}>
      <div
        className={`flex ${
          (contractsEmptyView && contractsActive) ||
          (jobInvitationEmptyView && jobInvitationActive)
            ? "flex-col"
            : "flex-row"
        } w-full h-[calc(100vh_-_var(--header-height))] bg-white `}
      >
        <div
          className={`flex flex-col ${
            (contractsEmptyView && contractsActive) ||
            (jobInvitationEmptyView && jobInvitationActive)
              ? "!w-full"
              : "w-full md:w-[35%]"
          } md:p-6 p-4 pb-16 md:pb-4 flex w-full flex-col bg-white`}
        >
          <div className="sticky top-0 z-10 bg-white ">
            <div>
              <h1 className="!m-0 !text-[18px] md:!text-2xl text-[#343333] leading-[100%] font-semibold">
                {labels?.SEARCHER_NAVIGATION_ASSORT_COLLABORATION_LABEL}
              </h1>
              <div className="flex w-[252px] md:!h-10 !h-9 bg-[#F3F1FD] rounded-[99px] items-center gap-1 !mt-[12px]">
                <button
                  onClick={() => {
                    setJobInvitationActive(true);
                    setContractsActive(false);
                  }}
                  className={`w-[122px] md:!h-10 !h-9 ${
                    jobInvitationActive
                      ? "bg-[#8E81F5] text-white font-medium text-sm rounded-tl-[99px] rounded-bl-[99px]"
                      : "text-[#878787] font-medium text-sm"
                  }`}
                >
                  {labels?.ViewTitleShortlistOpportunityList}
                </button>
                <button
                  onClick={() => {
                    setJobInvitationActive(false);
                    setContractsActive(true);
                    setDraftActive(false);
                    setSentInviteActive(true);
                  }}
                  className={`w-[125px] md:!h-10 !h-9 ${
                    contractsActive
                      ? "bg-[#8E81F5] text-white font-medium text-sm rounded-tr-[99px] rounded-br-[99px] "
                      : "text-[#878787] font-medium text-sm"
                  }`}
                >
                  {labels?.SEARCHER_NAVIGATION_ASSORT_COLLABORATION_LABEL}
                </button>
              </div>
            </div>
          </div>
          {jobInvitationActive && IsSearcher ? (
            <CreateNewOpportunity
              setCreateJobInvitation={setCreateJobInvitation}
              createJobInvitation={createJobInvitation}
              setFilteredJobList={setFilteredJobList}
              filteredJobList={filteredJobList}
              handleOnAction={handleOnAction}
              sentInviteActive={sentInviteActive}
              setSentInviteActive={setSentInviteActive}
              draftActive={draftActive}
              setDraftActive={setDraftActive}
              shortlistId={shortlistId}
              setShortlistId={setShortlistId}
              refresh={refresh}
              setRefresh={setRefresh}
            />
          ) : jobInvitationActive && IsSearcher === undefined ? (
            <NewOpportunity
              draftActive={draftActive}
              setFilteredJobList={setFilteredJobList}
              filteredJobList={filteredJobList}
              setShortlistId={setShortlistId}
            />
          ) : (
            <div className="bg-white !w-full h-full">
              {window?.innerWidth > 767 ? (
                <Modal
                  title={null}
                  footer={null}
                  closeIcon={null}
                  closable={false}
                  open={createNewCollaboration}
                  centered
                  onCancel={() => {
                    if (!availableDetail) {
                      setCreateNewCollaboration(false);
                      setActiveShowMore(false);
                      setJobInviteData("");
                    }
                  }}
                  rootClassName={`
          ${
            availableDetail
              ? "[&_.ant-modal]:!w-[80%]"
              : "[&_.ant-modal]:!w-[41%]"
          }
          [&_.ant-modal-content]:!p-[0px]
          [&_.ant-modal]:!min-w-[580px]`}
                >
                  <NewCreateCollaboration
                    availableDetail={availableDetail}
                    setAvailableDetail={setAvailableDetail}
                    setCreateNewCollaboration={setCreateNewCollaboration}
                    setRefresh={setRefresh}
                    activeShowMore={activeShowMore}
                    setActiveShowMore={setActiveShowMore}
                    Currencies={Currencies}
                    setCurrencies={setCurrencies}
                    showUserDetail={showUserDetail}
                    setShowUserDetail={setShowUserDetail}
                    filteredCollaborations={filteredCollaborations}
                    jobInviteData={jobInviteData}
                    setJobInviteData={setJobInviteData}
                  />
                </Modal>
              ) : (
                <Drawer
                  placement="right"
                  closable={true}
                  open={createNewCollaboration}
                  key="placement"
                  headerStyle={{ display: "none" }}
                  onClose={() => {
                    setCreateNewCollaboration(false);
                    setActiveShowMore(false);
                    setJobInvitationActive("");
                  }}
                  bodyStyle={{
                    padding: 0,
                    maxHeight: "955px"
                  }}
                  rootClassName=" 
            md:[&_.ant-drawer-content-wrapper]:!m-[8px]
            [&_.ant-drawer-content-wrapper]:!mt-[8px]
            md:[&_.ant-drawer-content]:!rounded-[16px]
            [&_.ant-drawer-content]:!rounded-tl-[16px]
            [&_.ant-drawer-content]:!rounded-tr-[16px]
            md:[&_.ant-drawer-content-wrapper]:!w-[497px]
            [&_.ant-drawer-content-wrapper]:!w-[100%]
            [&_.ant-drawer-content-wrapper]:!rounded-[16px]
            [&_.ant-drawer-body]:p-[32px_24px]
            [&_.ant-drawer-body]:!scrollbar-width-none"
                >
                  <NewCreateCollaboration
                    availableDetail={availableDetail}
                    setAvailableDetail={setAvailableDetail}
                    setCreateNewCollaboration={setCreateNewCollaboration}
                    setRefresh={setRefresh}
                    Currencies={Currencies}
                    setCurrencies={setCurrencies}
                    activeShowMore={activeShowMore}
                    setActiveShowMore={setActiveShowMore}
                    showUserDetail={showUserDetail}
                    setShowUserDetail={setShowUserDetail}
                    filteredCollaborations={filteredCollaborations}
                    jobInviteData={jobInviteData}
                    setJobInviteData={setJobInviteData}
                  />
                </Drawer>
              )}
              <div className="h-full">
                {isLoading && <LoadingMask />}
                {dialogMessage && (
                  <ConfirmDialog testId="company-confirm-diloag">
                    <ConfirmDialog.Message>
                      {dialogMessage}
                    </ConfirmDialog.Message>
                    <ConfirmDialog.ActionButtons>
                      <ConfirmDialog.Button
                        className="dialog-btn"
                        testId="company-delete-yes"
                        onClick={handleYesClick}
                      >
                        {popupYesText}
                      </ConfirmDialog.Button>
                      <ConfirmDialog.Button
                        className="dialog-btn"
                        testId="company-delete-no"
                        onClick={handleNoClick}
                      >
                        {popupNoText}
                      </ConfirmDialog.Button>
                    </ConfirmDialog.ActionButtons>
                  </ConfirmDialog>
                )}

                <div className="flex  !w-full h-full">
                  <div className="!w-full md:w-[44%]">
                    <div className="h-full w-full flex flex-col">
                      {filteredCollaborations?.length > 0 ? (
                        // <div className="flex-1 overflow-y-auto  [scrollbar-width:'none'] [-ms-overflow-style:'none'] [&::-webkit-scrollbar]:hidden">
                        <div className="flex-1 overflow-y-auto">
                          <div className="flex flex-col w-full gap-1 pb-4 pt-3 md:h-[calc(100vh_-_164px)] h-[calc(100vh_-_156px)]">
                            {filteredCollaborations?.map(item => (
                              <div
                                key={item.CollaborationId}
                                className="flex justify-between w-full rounded-2xl border-[0.5px] min-h-[60px] border-[#EAE5FC] px-4 pt-[10px] pb-[10px] items-center pointer hover:bg-gray-50"
                                onClick={() => {
                                  setIsDrawerOpen(true);
                                  handleSelectCollaboration(
                                    item,
                                    props.collaborationList
                                  );
                                }}
                              >
                                <div
                                  className={`flex gap-2 justify-between items-center w-full pointer 
                                  `}
                                >
                                  <div className="flex w-full items-center justify-between">
                                    <div className="flex flex-col gap-2 flex-1 min-w-0">
                                      <label className="text-sm text-[#343333] font-medium leading-[20px] truncate w-full">
                                        {item?.Title}
                                      </label>
                                      <p className="flex gap-2 text-xs font-normal text-[#878787]">
                                        <span>
                                          {`Date ${moment(
                                            item?.StartDate
                                          ).format("DD-MM-YYYY")}`}
                                        </span>
                                        <span>
                                          {`Time ${moment(
                                            item?.StartDate
                                          ).format("h:mm A")}`}
                                        </span>
                                      </p>
                                    </div>

                                    <div className="flex-shrink-0 ml-2">
                                      <span
                                        className={`flex items-center pl-4 pr-4 h-[27px] rounded-[99px] font-medium text-xs ${
                                          item?.Status === 0
                                            ? "bg-[#F3F1FD] text-[#8E81F5]"
                                            : item?.Status === 1
                                            ? "bg-[#F3F1FD] text-[#8E81F5]"
                                            : item?.Status === 2
                                            ? "bg-[#E8FEE7] text-[#34C759]"
                                            : item?.Status === 3
                                            ? "bg-[#FEEDED] text-[#FF3B30]"
                                            : item?.Status === 4
                                            ? "bg-[#FAECFE] text-[#D22CFF]"
                                            : item?.Status === 5
                                            ? "bg-[#FEEDED] text-[#FF3B30]"
                                            : ""
                                        }`}
                                      >
                                        {item?.Status === 0
                                          ? "New"
                                          : item?.Status === 1
                                          ? "Sent"
                                          : item?.Status === 2
                                          ? "Accepted"
                                          : item?.Status === 3
                                          ? "Declined"
                                          : item?.Status === 4
                                          ? "Expired"
                                          : item?.Status === 5
                                          ? "Closed"
                                          : ""}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div className="flex flex-col justify-center h-full"></div>
                      )}
                    </div>
                  </div>
                  {selectedCollaboration.company && (
                    <Drawer
                      placement="right"
                      closable={true}
                      open={isDrawerOpen}
                      key="placement"
                      headerStyle={{ display: "none" }}
                      onClose={() => {
                        setIsDrawerOpen(false);
                      }}
                      bodyStyle={{
                        padding: 0,
                        maxHeight: "955px"
                      }}
                      rootClassName=" 
                  md:[&_.ant-drawer-content-wrapper]:!m-[8px]
                  [&_.ant-drawer-content-wrapper]:!mt-[8px]
                  md:[&_.ant-drawer-content]:!rounded-[16px]
                  [&_.ant-drawer-content]:!rounded-tl-[16px]
                  [&_.ant-drawer-content]:!rounded-tr-[16px]
                  md:[&_.ant-drawer-content-wrapper]:!w-[497px]
                  [&_.ant-drawer-content-wrapper]:!w-[100%]
                  [&_.ant-drawer-content-wrapper]:!rounded-[16px]
                  [&_.ant-drawer-body]:p-[32px_24px]
                  [&_.ant-drawer-body]:!scrollbar-width-none"
                    >
                      <NewCollaborationDetail
                        selectedCollaboration={selectedCollaboration}
                        iProOpportunityNewDetailBtnDecline={
                          iProOpportunityNewDetailBtnDecline
                        }
                        iProOpportunityNewDetailBtnAccept={
                          iProOpportunityNewDetailBtnAccept
                        }
                        labels={props.labels}
                        emptyCollaorationInfo={emptyCollaorationInfo}
                        startDateLabel={startDateLabel}
                        durationLabel={durationLabel}
                        isDeclinedView={isDeclinedView}
                        compensationLabel={compensationLabel}
                        companyLabel={companyLabel}
                        descriptionLabel={descriptionLabel}
                        acceptedCollaboration={acceptedCollaboration}
                        selectInterest={selectInterest}
                        onCollaborationAccept={handleCollaborationAccept}
                        onCollaborationDecline={handleCollaborationDecline}
                        onCollaborationDelete={handleCollaborationDelete}
                        onCollaborationClose={handleCollaborationClose}
                        onCollaborationEnd={handleEndCollaboration}
                        IsSearcher={IsSearcher}
                        selectedCompany={selectedCompany}
                        isIproInActiveCollaboration={
                          isIproInActiveCollaboration
                        }
                        isInActiveView={isInActiveView}
                        isAcceptedView={isAcceptedView}
                        isSentView={isSentView}
                        isIproActiveCollaboration={isIproActiveCollaboration}
                        isIproNewCollaboration={isIproNewCollaboration}
                        addressLabel={companyAddressLabel}
                        industryLabel={comapnyIndustryLabel}
                        countryLabel={companyCountryLabel}
                        phoneLabel={companyPhoneLabel}
                        webUrlLabel={companyWebUrlLabel}
                        vatLabel={companyVatLabel}
                        setIsDrawerOpen={setIsDrawerOpen}
                        selectedDate={selectedDate}
                        tempSelectedDate={tempSelectedDate}
                        setTempSelectedDate={setTempSelectedDate}
                        onDateSelect={handleDateSelect}
                        showCalendar={showCalendar}
                        setShowCalendar={setShowCalendar}
                        handleExtendCollaboration={handleOkClick}
                        setRefresh={setRefresh}
                      />
                    </Drawer>
                  )}
                </div>
                {IsSearcher && filteredCollaborations?.length > 0 && (
                  <div className="md:!hidden !-ml-4  !flex !items-center !justify-center !w-full fixed bottom-0 h-[70px] bg-white border-t-[0.5px] border-[#EAE5FC]">
                    <Button
                      type="primary"
                      className="!w-full !h-10 ml-4 mr-4"
                      icon={<PlusOutlined />}
                      onClick={() => {
                        setCreateNewCollaboration(true);
                      }}
                    >
                      {labels?.Create_Contract_Label}
                    </Button>
                  </div>
                )}
              </div>
              <div className="!w-[343px] md:w-[455px]">
                <Modal
                  title={null}
                  footer={null}
                  closeIcon={null}
                  closable={false}
                  open={endContract}
                  centered
                  onCancel={() => setEndContract(false)}
                  rootClassName="
                 [&_.ant-modal-content]:!p-0 
                 [&_.ant-modal-content]:max-h-[100vh] 
                 [&_.ant-modal-content]:overflow-y-auto 
                 [&_.ant-modal]:!min-w-[90%] 
                 md:[&_.ant-modal]:!min-w-[60%] 
                 md:[&_.ant-modal]:!max-w-[840px]              
               "
                >
                  <FeedbackModal
                    selectedCollaboration={selectedCollaboration}
                    IsSearcher={IsSearcher}
                    labels={labels}
                    handleYesClick={handleYesClick}
                    closeCollaboration={closeCollaboration}
                  />
                </Modal>
              </div>
              <div>
                <Modal
                  title={null}
                  footer={null}
                  closeIcon={null}
                  closable={false}
                  open={isRatingView}
                  border={false}
                  centered={window.innerWidth >= 767}
                  onCancel={() => setisRatingView(false)}
                  rootClassName="
                 [&_.ant-modal-content]:!p-0 
                 [&_.ant-modal-content]:max-h-[100vh] 
                 [&_.ant-modal-content]:overflow-y-auto 
                 [&_.ant-modal]:!min-w-[90%] 
                 md:[&_.ant-modal]:!min-w-[45%]          
                 md:[&_.ant-modal]:!max-w-[650px]          
               "
                >
                  <RatingModal
                    selectedCollaboration={selectedCollaboration}
                    labels={labels}
                    onFormSelectChange={onFormSelectChange}
                    options={options}
                    onCloseCollaboration={closeCollaboration}
                    CollborationCloseReasonId={state.CollborationCloseReasonId}
                    reviewQuestions={allReviewQuestions}
                    onSubmit={handleSubmit}
                    onRatingChange={handleRatingChange}
                    isAlreadyRated={props.isAlreadyRated}
                    isInActiveView={isInActiveView}
                    questions={questions}
                    setQuestions={setQuestions}
                  />
                </Modal>
              </div>
              <div className="!w-[343px] md:w-[455px]">
                <DeleteModal
                  open={deleteContract}
                  onCancel={() => setDeleteContract(false)}
                  onConfirm={handleYesClick}
                  isLoading={isLoading}
                  title={labels?.End_Contract_Title}
                  description={labels?.End_Contract_Description}
                  cancelText={labels?.companyDeleteCurtainNOBtnText}
                  confirmText={labels?.delete_presentation}
                />
              </div>
            </div>
          )}
        </div>
        <div
          className={`flex ${
            (contractsEmptyView && contractsActive) ||
            (jobInvitationEmptyView && jobInvitationActive)
              ? "!w-full h-full flex items-center"
              : "hidden md:block w-[65%] border-l-[0.5px] border-[#C6C6C6]"
          } `}
        >
          <div className="flex w-full h-full items-center justify-center">
            <EmptyView
              icon={
                <img
                  src={
                    contractsEmptyView && contractsActive
                      ? ContractEmptyImage
                      : jobInvitationEmptyView && jobInvitationActive
                      ? ContractEmptyMain
                      : ContractEmpty
                  }
                  alt="Empty"
                />
              }
              title={
                IsSearcher
                  ? contractsEmptyView && contractsActive
                    ? labels?.Recruiter_Contract_Empty_Title
                    : jobInvitationEmptyView && jobInvitationActive
                    ? labels?.Contract_Main_Empty_Title
                    : labels?.Contract_Nothing_Selected_Title
                  : contractsEmptyView && contractsActive
                  ? labels?.Ipro_Empty_Title
                  : jobInvitationEmptyView && jobInvitationActive
                  ? labels?.Recruiter_Contracts_EmptyScreen_Title
                  : labels?.Contract_Nothing_Selected_Title
              }
              description={
                IsSearcher
                  ? contractsEmptyView && contractsActive
                    ? labels?.Contract_Empty_Main_Description
                    : jobInvitationEmptyView && jobInvitationActive
                    ? labels?.Job_Invite_Empty_Main_Description
                    : labels?.Contract_Nothing_Selected_Description
                  : contractsEmptyView && contractsActive
                  ? labels?.Ipro_empty_Description
                  : jobInvitationEmptyView && jobInvitationActive
                  ? labels?.Recruiter_Contracts_EmptyScreen_Description
                  : labels?.Contract_Nothing_Selected_Description
              }
              {...(IsSearcher && {
                btnIcon: <PlusOutlined />,
                actionText: jobInvitationActive
                  ? filteredJobList > 0
                    ? labels?.Create_Invitation_Label
                    : labels?.Create_Job_invite_title
                  : labels?.Create_Contract_Label,
                onAction: handleOnAction
              })}
            />
          </div>
        </div>
      </div>
    </PageWrapper>
  );
};

const mapStateToProps = (
  {
    systemLabel,
    navigation,
    iproCollaborationNew,
    iproCollaborationInActive,
    iproCollaborationActive,
    searcherCollaborationPending,
    searcherCollaborationAccepted,
    searcherCollaborationDeclined,
    searcherCollaborationInActive
  },
  {
    isIproActiveCollaboration,
    isIproInActiveCollaboration,
    isSentView,
    isAcceptedView,
    isDeclinedView,
    isInActiveView
  }
) => {
  const {
    acceptedCollaboration,
    declinedCollaboration,
    unreadproInactiveCollaborations,
    searcherInActiveCollaboration,
    unreadCollaborations
  } = navigation;
  const { labels } = systemLabel;
  const collaborationProps = isSentView
    ? searcherCollaborationPending
    : isAcceptedView
    ? searcherCollaborationAccepted
    : isDeclinedView
    ? searcherCollaborationDeclined
    : isInActiveView
    ? searcherCollaborationInActive
    : isIproActiveCollaboration
    ? iproCollaborationActive
    : isIproInActiveCollaboration
    ? iproCollaborationInActive
    : iproCollaborationNew;
  return {
    labels,
    acceptedCollaboration,
    declinedCollaboration,
    searcherInActiveCollaboration,
    unreadproInactiveCollaborations,
    unreadCollaborations,
    ...collaborationProps
  };
};

const actions = {
  unreadCollaborationsAction,
  onStateChangeAction
};
export default connect(mapStateToProps, actions)(NewCollaboration);
