import { useState, useEffect } from "react";
import { isEmpty, map } from "lodash";
import moment from "moment";
import { connect } from "react-redux";
import { useNavigate, useLocation } from "react-router-dom";
import { notificationAction } from "../../../../actions/notification";
import { StorageService } from "../../../../api/storage";
import ImgSrc from "../../../../assets/images/present.svg";
import Button from "../../../../common/Button/Button";
import Column from "../../../../common/Column/Column";
import ConfirmDialog from "../../../../common/ConfirmDialog/ConfirmDialog";
import LoadingMask from "../../../../common/LoadingMask/LoadingMask";
import ReviewForm from "../../../../common/ReviewForm/ReviewForm";
import Select from "../../../../common/Select/Select";
import { isValidEmail } from "../../../../utilities/helpers";
import {
  addCollaborationApi,
  getAllCollaborationApi,
  getAllOpportunitiesApi,
  getCollaborationReasonsApi,
  getCollaborationReviewApi,
  getOpportunityIprosResumeApi,
  getReviewQuestions,
  submitCollaborationReview,
  updateCollaborationApi
} from "../../../../views-alpha/Collaboration/common/collaborationApi";
import {
  GetResumesByResumeId,
  GetSharedCollaborationInfo,
  GetUserDetailByResumeAndRequestId,
  ShareCollaborationApi,
  UpdateCollaborationEndDate,
  renameCollaborationApi
} from "../../assortApi";
import Presentations from "../../components/PresentationDetails";
import ResumeDetail from "../../components/ResumeDetail";
import CollaborationDetail from "./components/CollaborationDetail";
import CreateNewCollaboration from "./components/CreateNewCollaboration";
import "./finish-collaboration.scss";

const Collaborations = ({
  labels,
  isHelpActive,
  notificationAction,
  UserId,
  Currencies,
  ...props
}) => {
  const [state, setState] = useState({
    CollborationCloseReasonId: null,
    reasonOptions: [
      { value: "1", label: "Job completed successfully" },
      { value: "2", label: "Job cancelled due to freelancer performance" },
      { value: "3", label: "Job canceled for other reasons" },
      { value: "4", label: "Another Reason" }
    ],
    options: [],
    acceptedCollaborations: [],
    selectedUser: [],
    selectedResume: [],
    allCollaborations: [],
    formCollapsed: false,
    isCollapsed1: false,
    isCollapsed2: false,
    isCollapsed3: true,
    isCollapsed4: true,
    isCollapsed5: true,
    allTimeSheets: [],
    weekTimelogsList: [],
    weekExpenselogsList: [],
    currentExpenselog: {},
    currentTimeReport: {
      StatusId: 1,
      isEmpty: true,
      Amount: "",
      Time: ""
    },
    currentTimelog: {},
    TimeSheetStatusId: 2,
    ExpenseSheetStatusId: 2,
    isLoading: false,
    selectedCollaboration: {},
    durationOptions: [
      { label: "Current Month", value: 1 },
      { label: "Last Month", value: 2 },
      { label: "Year to date", value: 3 }
    ],
    StartDate: moment().startOf("months"),
    EndDate: moment(),
    TimeDetail: {},
    ExpenseDetail: {},
    selectedDuration: {},
    isEndDateCalendarOpen: false,
    opportunityList: [],
    iprosList: [],
    selectedIpro: {},
    collaborationForm: {
      invalidRequestId: false
    },
    isCreateCollaboration: false
  });

  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const {
      PENDING_COLLABORATIONS,
      DECLINED_COLLABORATIONS,
      ALL_ACTIVE_COLLAB
    } = labels;
    setState(prevState => ({
      ...prevState,
      options: [
        {
          label: ALL_ACTIVE_COLLAB || "Active Collaborations",
          value: 1,
          url: "GetAllCollaborationByUserId?IsAccepted=true"
        },
        {
          label: PENDING_COLLABORATIONS || "Pending Collaborations",
          value: 2,
          url: "GetAllCollaborationByUserId"
        },
        {
          label: DECLINED_COLLABORATIONS || "Declined Collaborations",
          value: 3,
          url: "GetAllCollaborationByUserId?isDeclined=true"
        }
      ],
      selectedOption: {
        value: 1,
        label: ALL_ACTIVE_COLLAB,
        url: "GetAllCollaborationByUserId?IsAccepted=true"
      }
    }));
    getOpportunities();
    GetAllCollaborations("GetAllCollaborationByUserId?IsAccepted=true");
  }, []);

  const getOpportunities = () => {
    getAllOpportunitiesApi(0).then(data => {
      if (data.success) {
        const opportunityList = data.items.Sent.map(item => ({
          ...item,
          value: item.RequestId,
          label: item.RequestName
        }));
        setState(prevState => ({ ...prevState, opportunityList }));
      }
    });
  };

  const getOpportunityIProsResume = requestId => {
    getOpportunityIprosResumeApi(requestId).then(data => {
      if (data.success) {
        const iprosList = map(data.items.user, item => {
          const newItem = {
            ...item,
            value: item.ResumeId,
            label: item.UserName
          };

          return newItem;
        });
        setState(prevState => ({ ...prevState, iprosList }));
      }
    });
  };

  const handleFormSelectChange = (name, selectedOption) => {
    if (!selectedOption) return;
    const { collaborationForm, iprosList } = state;
    const { value, RequestId } = selectedOption;
    if (name === "OpprtunityId") {
      if (RequestId) {
        getOpportunityIProsResume(RequestId);
        setState(prevState => ({
          ...prevState,
          collaborationForm: {
            ...collaborationForm,
            invalidRequestId: false,
            Request: RequestId,
            RequestId
          }
        }));
        GetIproPresentation(collaborationForm.ResumeId, RequestId);
        GetResumeDetail(collaborationForm.ResumeId);
        return;
      }
    }
    const { ResumeId } = selectedOption;
    if (name === "ResumeId") {
      if (ResumeId) {
        setState(prevState => ({
          ...prevState,
          collaborationForm: {
            ...collaborationForm,
            invalidResumeId: false,
            Resume: ResumeId,
            ResumeId
          },
          selectedIpro: iprosList.find(item => item.ResumeId === ResumeId)
        }));
        GetIproPresentation(ResumeId, collaborationForm.RequestId);
        GetResumeDetail(ResumeId);
        return;
      }
    }
    if (name === "HourlyRateType") {
      if (value) {
        setState(prevState => ({
          ...prevState,
          collaborationForm: {
            ...collaborationForm,
            invalidHourlyRateType: false,
            HourlyRateType: value,
            value
          }
        }));
        return;
      }
    }
    if (name === "DurationType") {
      if (value) {
        setState(prevState => ({
          ...prevState,
          collaborationForm: {
            ...collaborationForm,
            invalidDurationType: false,
            DurationType: value,
            value
          }
        }));
        return;
      }
    }
  };

  const handleDateChange = d => {
    const { collaborationForm } = state;
    try {
      setState(prevState => ({
        ...prevState,
        collaborationForm: {
          ...collaborationForm,
          invalidStartDate: false,
          StartDate: d,
          d
        }
      }));
    } catch (e) {
      console.log(e?.message);
    }
  };

  const handleSliderChange = value => {
    const { collaborationForm } = state;
    if (value) {
      setState(prevState => ({
        ...prevState,
        collaborationForm: {
          ...collaborationForm,
          Duration: value,
          value
        }
      }));
    }
  };

  const handleFormFieldChange = e => {
    const { name, value } = e.target;
    if (name === "Title") {
      validateField({ name, value });
      return;
    }
    if (name === "Description") {
      validateField({ name, value });
      return;
    }
    if (name === "HourlyRate") {
      if (isNaN(value)) return;
      validateField({ name, value });
    }
  };

  const validateSelect = name => {
    const { collaborationForm } = state;
    setState(prevState => ({
      ...prevState,
      collaborationForm: {
        ...collaborationForm,
        [`invalid${name}`]: true
      }
    }));
  };

  const validateField = ({ name, value }) => {
    const { collaborationForm } = state;
    setState(prevState => ({
      ...prevState,
      collaborationForm: {
        ...collaborationForm,
        [name]: value,
        [`invalid${name}`]: !value
      }
    }));
  };

  const makeMandatoryFieldRed = () => {
    const { collaborationForm } = state;
    setState(prevState => ({
      ...prevState,
      collaborationForm: {
        ...collaborationForm,
        invalidTitle: !undefined,
        Title: undefined,

        invalidStartDate: !undefined,
        StartDate: undefined,

        invalidDurationType: !undefined,
        DurationType: undefined,

        invalidHourlyRate: !undefined,
        HourlyRate: undefined,

        invalidRequestId: !undefined,
        RequestId: undefined,

        invalidResumeId: !undefined,
        ResumeId: undefined,

        invalidHourlyRateType: !undefined,
        HourlyRateType: undefined
      }
    }));
  };

  const handleCollaborationSave = () => {
    const { collaborationForm } = state;
    const {
      collTitleRequired,
      COLLABORATION_CREATE_REQURIED_FIELD_MESSAGE,
      collStartDateRequired,
      collDurationTypeRequired,
      collCurrancyTypeRequired,
      collOppRequired,
      collIproRequired,
      collSuccessfullySent
    } = labels;
    const {
      Title = "",
      HourlyRate = "",
      RequestId = "",
      ResumeId = "",
      Description = "",
      HourlyRateType = "",
      StartDate = "",
      DurationType = "",
      Duration = "4"
    } = collaborationForm;
    var collaboration = {
      Title,
      HourlyRate,
      HourlyRateType,
      RequestId,
      ResumeId,
      Description,
      StartDate,
      DurationType,
      Duration
    };

    if (
      isEmpty(Title) &&
      !StartDate &&
      !DurationType &&
      isEmpty(HourlyRate) &&
      !HourlyRateType &&
      !RequestId &&
      !ResumeId
    ) {
      const info = {
        message: COLLABORATION_CREATE_REQURIED_FIELD_MESSAGE,
        status: "error"
      };
      makeMandatoryFieldRed();
      notificationAction(info);
      return;
    }

    if (isEmpty(Title)) {
      const info = {
        message: collTitleRequired,
        status: "error"
      };
      validateField({ name: "Title", value: Title });
      notificationAction(info);
      return;
    }

    if (!StartDate) {
      const info = {
        message: collStartDateRequired,
        status: "error"
      };
      validateField({ name: "StartDate", value: StartDate });
      notificationAction(info);
      return;
    }
    if (!DurationType) {
      const info = {
        message: collDurationTypeRequired,
        status: "error"
      };
      validateField({ name: "DurationType", value: DurationType });
      notificationAction(info);
      return;
    }
    if (isEmpty(HourlyRate)) {
      const info = {
        message: "Hourly rate required",
        status: "error"
      };
      validateField({ name: "HourlyRate", value: HourlyRate });
      notificationAction(info);
      return;
    }
    if (!HourlyRateType) {
      const info = {
        message: collCurrancyTypeRequired,
        status: "error"
      };
      validateField({ name: "HourlyRateType", value: HourlyRateType });
      notificationAction(info);
      return;
    }
    if (!RequestId) {
      const info = {
        message: collOppRequired,
        status: "error"
      };
      validateField({ name: "RequestId", value: RequestId });
      notificationAction(info);
      return;
    }
    if (!ResumeId) {
      const info = {
        message: collIproRequired,
        status: "error"
      };
      validateField({ name: "ResumeId", value: ResumeId });
      notificationAction(info);
      return;
    }
    setState(prevState => ({ ...prevState, isLoading: true }));
    const duration = Duration + " " + DurationType;
    collaboration.Duration = duration;
    collaboration.Decription = Description;
    collaboration.StartDate = moment(StartDate).format("MM/DD/YYYY");
    addCollaborationApi({ collaboration })
      .then(() => {
        const info = {
          message: collSuccessfullySent,
          status: "success"
        };
        notificationAction(info);
        setState(prevState => ({
          ...prevState,
          isLoading: false,
          collaborationForm: {},
          selectedIpro: {},
          iprosList: []
        }));
        GetAllCollaborations(state.selectedOption.url);
      })
      .catch(() => {
        setState(prevState => ({ ...prevState, isLoading: false }));
        const info = {
          message: "Error",
          status: "error"
        };
        notificationAction(info);
      });
    return;
  };

  const GetAllCollaborations = url => {
    setState(prevState => ({ ...prevState, isLoading: true }));
    getAllCollaborationApi({ url }).then(res => {
      if (res.items.length > 0) {
        const allCollaborations = res.items.map(coll => {
          return {
            ...coll,
            Description: coll.Decription,
            HourlyRateType: !isNaN(coll.HourlyRateType)
              ? Currencies
                ? Currencies.find(
                    a => a.CurrencyId == parseInt(coll.HourlyRateType)
                  ).Name
                : ""
              : Currencies
              ? Currencies.find(a => a.Name == coll.HourlyRateType).Name
              : ""
          };
        });

        setState(prevState => ({
          ...prevState,
          allCollaborations
        }));
        const spliturl = location.pathname.match(/\d+/g);
        if (spliturl != null) {
          let filteredCollab = allCollaborations.filter(
            coll => coll.CollaborationId == spliturl[0]
          );
          handleCollaborationClick(filteredCollab[0]);
        } else {
          handleCollaborationClick(allCollaborations[0]);
        }
      }
      setState(prevState => ({ ...prevState, isLoading: false }));
    });
  };

  const handleOptionChange = option => {
    setState(prevState => ({
      ...prevState,
      selectedOption: option
    }));
    GetAllCollaborations(option.url);
  };

  const handleStartDateChange = d => {
    if (moment(d) >= moment(state.EndDate)) {
      const info = {
        status: "error",
        message:
          labels.Collaboration_Time_And_Expense_Start_Date_Validation_Message
      };
      notificationAction(info);
      return;
    }
    setState(prevState => ({
      ...prevState,
      StartDate: d
    }));
    // GetCollaborationTimeDetail(
    //   moment(d).format("DD/MM/YYYY"),
    //   moment(state.EndDate).format("DD/MM/YYYY"),
    //   state.selectedCollaboration
    // );
    // GetCollaborationExpenseDetail(
    //   moment(d).format("DD/MM/YYYY"),
    //   moment(state.EndDate).format("DD/MM/YYYY"),
    //   state.selectedCollaboration
    // );
  };

  const handleEndDateChange = d => {
    if (moment(d) <= moment(state.StartDate)) {
      const info = {
        status: "error",
        message: labels.Collaboration_Time_And_Expense_Date_Validation_Message
      };
      notificationAction(info);
      return;
    }
    setState(prevState => ({
      ...prevState,
      EndDate: d
    }));
    GetCollaborationTimeDetail(
      moment(state.StartDate).format("DD/MM/YYYY"),
      moment(d).format("DD/MM/YYYY"),
      state.selectedCollaboration
    );
    GetCollaborationExpenseDetail(
      moment(state.StartDate).format("DD/MM/YYYY"),
      moment(d).format("DD/MM/YYYY"),
      state.selectedCollaboration
    );
  };

  const handleCollaborationClick = (item, collaborations = null) => {
    if (!item) {
      setState(prevState => ({
        ...prevState,
        selectedCollaboration: {}
      }));
      return;
    }
    getReviewDetails(item.CollaborationId);
    getCollaborationCloseReasons();
    if (!item.IsShared) {
      GetSharedCollaborationInfo(item.CollaborationId, props.token)
        .then(res => {
          item = {
            ...item,
            SharedInfo: res.items.map(a => ({
              ...a,
              CollaborationId: item.CollaborationId
            }))
          };
          const updatedCollaborations =
            collaborations != null ? collaborations : state.allCollaborations;
          const allCollaborations = updatedCollaborations.map(a => ({
            ...a,
            isSelected: a.CollaborationId == item.CollaborationId,
            isActive: a.CollaborationId == item.CollaborationId,
            IsShared: a.UserId != UserId
          }));
          setState(prevState => ({
            ...prevState,
            selectedCollaboration: {
              ...item,
              newEmail: "",
              PreviousEndDate: item.EndDate
            },
            allCollaborations: allCollaborations,
            isCollapsed2: false,
            isCollapsed3: false,
            collaborationForm: {},
            IsCloned: false,
            isCreateCollaboration: false,
            isLoading: false
          }));
        })
        .catch(err => console.log("Err ", err));
    } else {
      const updatedCollaborations =
        collaborations != null ? collaborations : state.allCollaborations;
      const allCollaborations = updatedCollaborations.map(a =>
        a.CollaborationId == item.CollaborationId
          ? { ...a, isSelected: true, isActive: true }
          : { ...a, isSelected: false, isActive: false }
      );
      setState(prevState => ({
        ...prevState,
        selectedCollaboration: {
          ...item,
          newEmail: "",
          PreviousEndDate: item.EndDate
        },
        collaborationForm: {},
        allCollaborations: allCollaborations,
        isCollapsed2: false,
        isLoading: false
      }));
    }
    GetIproPresentation(item.ResumeId, item.RequestId);
    GetResumeDetail(item.ResumeId);
  };

  const handleShareCollaborationInputChange = e => {
    const { name, value } = e.target;
    setState(prevState => ({
      ...prevState,
      selectedCollaboration: {
        ...prevState.selectedCollaboration,
        newEmail: value
      }
    }));
  };

  const handleShareCollaboration = () => {
    const {
      CollaborationId,
      newEmail,
      SharedInfo
    } = state.selectedCollaboration;
    if (!isValidEmail(newEmail)) {
      const info = {
        status: "error",
        message: labels.VALID_EMAIL
      };
      notificationAction(info);
      return;
    }
    ShareCollaborationApi(CollaborationId, newEmail, props.token).then(res => {
      if (res.success) {
        const sharedInfo = SharedInfo.concat({
          CollaborationId: CollaborationId,
          UserEmail: newEmail,
          UserId: res.items.UserId
        });
        setState(prevState => ({
          ...prevState,
          selectedCollaboration: {
            ...prevState.selectedCollaboration,
            SharedInfo: sharedInfo,
            newEmail: "",
            PreviousEndDate: sharedInfo.EndDate
          }
        }));
        const info = {
          status: "success",
          message: labels.SNAPSHOT_COLLABORATIONS_SHARE_SUCCESS
        };
        notificationAction(info);
      } else {
        const info = {
          status: "error",
          message: res.message
        };
        notificationAction(info);
      }
    });
  };

  const handleCollaborationEndDateChange = d => {
    setState(prevState => ({
      ...prevState,
      selectedCollaboration: {
        ...prevState.selectedCollaboration,
        EndDate: d
      }
    }));
    const EndDate = moment(d);
    const { CollaborationId } = state.selectedCollaboration;
    UpdateCollaborationEndDate(
      CollaborationId,
      moment(EndDate).format("DD/MM/YYYY")
    ).then(res => {
      if (res.success) {
        const info = {
          status: "success",
          message: labels.Collaboration_Extend_Success_Message
        };
        notificationAction(info);
        let allCollaborations = state.allCollaborations.map(a =>
          a.CollaborationId == CollaborationId
            ? { ...a, EndDate: EndDate, PreviousEndDate: EndDate }
            : a
        );
        if (state.selectedOption.value == "2") {
          allCollaborations = allCollaborations.filter(
            a => a.CollaborationId != CollaborationId
          );
        }
        setState(prevState => ({
          ...prevState,
          allCollaborations,
          selectedCollaboration: {
            ...prevState.selectedCollaboration,
            PreviousEndDate: EndDate
          },
          isEndDateCalendarOpen: false
        }));
        handleCollaborationClick(allCollaborations[0]);
        return;
      } else {
        const info = {
          status: "error",
          message: labels.Collaboration_Extend_Error_Message
        };
        notificationAction(info);
        return;
      }
    });
  };

  const handleCollaborationExtend = () => {
    setState(prevState => ({
      ...prevState,
      isEndDateCalendarOpen: true
    }));
  };

  const handleDateOnBlur = () => {
    setState(prevState => ({
      ...prevState,
      isEndDateCalendarOpen: false
    }));
  };

  const handleCollborationNameBlur = () => {
    const { CollaborationId, Title } = state.selectedCollaboration;
    renameCollaborationApi(CollaborationId, Title)
      .then(res => {
        if (res.success) {
          const info = {
            status: "success",
            message: labels.collaborationTitleUpdatedLabel
          };
          notificationAction(info);
          const { allCollaborations, selectedCollaboration } = state;
          setState(prevState => ({
            ...prevState,
            allCollaborations: allCollaborations.map(item =>
              item.CollaborationId == selectedCollaboration.CollaborationId
                ? { ...selectedCollaboration, isActive: true, isSelected: true }
                : item
            )
          }));
        }
      })
      .catch(err => console.log("Err ", err));
  };

  const handleCollborationNameChange = e => {
    const { name, value } = e.target;
    setState(prevState => ({
      ...prevState,
      selectedCollaboration: {
        ...prevState.selectedCollaboration,
        Title: value
      }
    }));
  };

  const handleCollaborationClone = item => {
    const allCollaborations = state.allCollaborations.map(a => ({
      ...a,
      isSelected: false,
      isActive: false
    }));
    getOpportunityIProsResume(item.RequestId);
    setState(prevState => ({
      ...prevState,
      formCollapsed: false,
      collaborationForm: {
        ...item,
        Title: `${labels.copyofLabel} ${item.Title}`,
        IsCloned: true,
        Duration: item.Duration.split(" ")[0],
        DurationType: item.Duration.split(" ")[1]
      },
      selectedCollaboration: {},
      allCollaborations,
      IsCloned: true,
      isCreateCollaboration: false
    }));
  };

  const handleAddNewCollaboration = () => {
    const allCollaborations = state.allCollaborations.map(a => ({
      ...a,
      isSelected: false,
      isActive: false
    }));
    setState(prevState => ({
      ...prevState,
      collaborationForm: {
        Duration: 4
      },
      selectedCollaboration: {},
      formCollapsed: false,
      allCollaborations,
      selectedResume: {},
      selectedUser: {}
    }));
  };

  const GetResumeDetail = ResumeId => {
    GetResumesByResumeId(ResumeId)
      .then(res => {
        if (res.success) {
          setState(prevState => ({
            ...prevState,
            selectedResume: res.items,
            isLoading: false
          }));
        }
      })
      .catch(response => {
        console.log("error in get resume request ");
      });
  };

  const GetIproPresentation = (ResumeId, RequestId) => {
    GetUserDetailByResumeAndRequestId(ResumeId, RequestId).then(res => {
      if (res.success) {
        setState(prevState => ({
          ...prevState,
          selectedUser: res.items
        }));
      }
    });
  };

  const handleCollaborationNameEdit = () => {
    setState(prevState => ({
      ...prevState,
      selectedCollaboration: {
        ...prevState.selectedCollaboration,
        isNameEdit: !prevState.selectedCollaboration.isNameEdit
      }
    }));
  };

  const handleCollaborationClose = () => {
    const { InfoSearcherSentCollaborationCloseConfirmationMsg } = labels;
    setState(prevState => ({
      ...prevState,
      dialogMessage: InfoSearcherSentCollaborationCloseConfirmationMsg
    }));
  };

  const handleYesClick = () => {
    const { yesClickCount } = state;
    const { likeToRateCollaborationMessage } = labels;
    if (!yesClickCount || yesClickCount === 0) {
      setState(prevState => ({
        ...prevState,
        dialogMessage: likeToRateCollaborationMessage,
        yesClickCount: 1
      }));
      return;
    }
    setState(prevState => ({
      ...prevState,
      dialogMessage: "",
      yesClickCount: 2
    }));
  };

  const closeCollaboration = () => {
    const { InfoSearcherCollaborationClosed } = labels;
    const { CollaborationId } = state.selectedCollaboration;
    const apiParams = `collaborationId=${CollaborationId}&IsClosed=true&IsSearcher=true`;
    setState(prevState => ({
      ...prevState,
      isLoading: true,
      dialogMessage: ""
    }));
    updateCollaborationApi({ apiParams }).then(response => {
      const info = {
        message: InfoSearcherCollaborationClosed,
        status: "success"
      };
      notificationAction(info);
      setState(prevState => ({
        ...prevState,
        isLoading: false,
        selectedCollaboration: {},
        yesClickCount: 0
      }));
      GetAllCollaborations(state.selectedOption.url);
    });
  };

  const handleNoClick = () => {
    const { yesClickCount } = state;
    if (yesClickCount === 0) {
      setState(prevState => ({
        ...prevState,
        dialogMessage: "",
        selectedCompany: null
      }));
      return;
    }
    closeCollaboration();
  };

  const getReviewDetails = CollaborationId => {
    getReviewQuestions({ questionType: 1 })
      .then(res => {
        const reviewQuestions = res.items.map(r => ({
          ...r,
          ReviewQuestionValue: labels[r.Question],
          Answer: r.Question !== "Rating_Question5" ? r.Answer || 0 : "",
          CollaborationId: CollaborationId
        }));
        getCollaborationReviewApi({
          collaborationId: CollaborationId,
          IsIProReview: false
        })
          .then(reviewRes => {
            const reviews = reviewRes.items;
            let currentCollborationCloseReasonId = null;
            setState(prevState => ({
              ...prevState,
              reviewQuestions: reviewQuestions.map(a => {
                const currentReview = reviews
                  ? reviews.find(b => b.ReviewQuestionId == a.ReviewQuestionId)
                  : null;
                if (
                  currentReview &&
                  a.ReviewQuestionId == currentReview.ReviewQuestionId
                ) {
                  currentCollborationCloseReasonId =
                    currentReview.CollborationCloseReasonId;
                }
                return {
                  ...a,
                  Answer:
                    currentReview != null ? currentReview.Answer : a.Answer,
                  IsIProReview: false
                };
              }),
              isAlreadyRated: reviews && reviews.length > 0 ? true : false
            }));
            onReasonFormSelectChange("", {
              value: currentCollborationCloseReasonId
            });
          })
          .catch(err => console.log(err));
      })
      .catch(err => console.log(err));
  };

  const getCollaborationCloseReasons = () => {
    getCollaborationReasonsApi()
      .then(reasons => {
        const reasonsList = reasons.items.map(item => ({
          ...item,
          value: item.CollborationCloseReasonId,
          label: labels[item.ReasonLabel]
        }));
        var systemlabels = labels;
        setState(prevState => ({
          ...prevState,
          reasonOptions: reasonsList
        }));
      })
      .catch(err => console.log(err));
  };

  const onReasonFormSelectChange = (answer, selectedOption) => {
    if (!selectedOption) return;
    const { reviewQuestions } = state;
    const { value } = selectedOption;
    setState(prevState => ({
      ...prevState,
      CollborationCloseReasonId: value,
      reviewQuestions: reviewQuestions?.map(a =>
        a.Question === "Rating_Question6"
          ? {
              ...a,
              Answer: null,
              CollborationCloseReasonId: value
            }
          : a
      )
    }));
  };

  const handleRatingChange = (rating, value) => {
    const { reviewQuestions } = state;
    setState(prevState => ({
      ...prevState,
      reviewQuestions: reviewQuestions?.map(a =>
        a.ReviewQuestionId == rating.ReviewQuestionId
          ? {
              ...a,
              Answer:
                rating.Question === "Rating_Question5"
                  ? value.target.value
                  : value
            }
          : a
      )
    }));
  };

  const handleSubmit = () => {
    const { reviewQuestions } = state;
    let ReviewSubmitModel = reviewQuestions;
    submitCollaborationReview({ collaborationReview: ReviewSubmitModel })
      .then(data => {})
      .catch(err => {});
    closeCollaboration();
  };

  const {
    isCollapsed1,
    isCollapsed2,
    isCollapsed3,
    isCollapsed4,
    isCollapsed5,
    selectedOption,
    selectedCollaboration,
    selectedUser,
    selectedResume,
    isEndDateCalendarOpen,
    formCollapsed,
    collaborationForm,
    isLoading,
    opportunityList,
    iprosList,
    dialogMessage,
    yesClickCount,
    isCreateCollaboration,
    IsCloned
  } = state;
  const {
    SNAPSHOT_COLLABORATIONS,
    SNAPSHOT_COLLABORATION_DETAIL,
    SNAPSHOT_COLLABORATION_TIME_AND_EXPENSES,
    SNAPSHOT_COLLABORATION_PRESENT,
    SNAPSHOT_COLLABORATION_RESUME
  } = labels;

  return (
    <div className="collaborations-view">
      {isLoading && <LoadingMask />}
      {dialogMessage && (
        <ConfirmDialog testId="company-confirm-diloag">
          <ConfirmDialog.Message>{dialogMessage}</ConfirmDialog.Message>
          <ConfirmDialog.ActionButtons>
            <ConfirmDialog.Button
              className="dialog-btn"
              testId="company-delete-yes"
              onClick={handleYesClick}
            >
              {labels.popupYesText}
            </ConfirmDialog.Button>
            <ConfirmDialog.Button
              className="dialog-btn"
              testId="company-delete-no"
              onClick={handleNoClick}
            >
              {labels.popupNoText}
            </ConfirmDialog.Button>
          </ConfirmDialog.ActionButtons>
        </ConfirmDialog>
      )}
      <Column collapse={isCollapsed1} className="col-1">
        <Column.Collapsed
          text={SNAPSHOT_COLLABORATIONS}
          onClick={() =>
            setState(prevState => ({ ...prevState, isCollapsed1: false }))
          }
          isHelpActive={isHelpActive}
          tooltipButton={labels.ToolTipSnapshotExpand}
          tooltipHelp={labels.hlptxtSnapshotExpand}
          tooltipPlace="left"
          testId="expandbtn1"
        />
        <Column.Head>
          <div className="heading">{SNAPSHOT_COLLABORATIONS}</div>
          <Button
            className="collapseBtn"
            testId="Collapasebtn1"
            onClick={() =>
              setState(prevState => ({ ...prevState, isCollapsed1: true }))
            }
            tooltipButton={labels.ToolTipSnapshotCollapse}
            tooltipHelp={labels.hlptxtSnapshotCollapse}
            tooltipPlace="left"
            isHelpActive={isHelpActive}
          />
        </Column.Head>
        {
          <Select
            readonly={true}
            searchable={false}
            testId={"collaborationDropdown"}
            name="AllPresentation"
            className="select-input"
            value={selectedOption}
            placeholder={labels.presentPrimaryRolePlaceholder}
            onChange={selectedOption => handleOptionChange(selectedOption)}
            options={state.options}
            clearable={false}
          />
        }
        <Column.Body>
          <div className="tictell-list">
            {state.allCollaborations.map(item => (
              <div
                key={item.CollaborationId}
                data-testid={`collaborations-list-item${
                  item.isActive ? "activeItem" : ""
                }`}
                className={`tictell-list-item-container  ${
                  item.isSelected ? "selected-item" : ""
                } ${item.isActive ? "activeItem" : ""}`}
              >
                <div
                  onClick={() => handleCollaborationClick(item)}
                  className={`dashItemImg ${
                    item.PresenatationLogo ? "" : "no-dp"
                  }`}
                >
                  <img
                    className="roundImg"
                    src={
                      item.PresenatationLogo ? item.PresenatationLogo : ImgSrc
                    }
                    alt={"UserName"}
                  />
                </div>
                <div
                  onClick={() => handleCollaborationClick(item)}
                  className="name-container"
                >
                  <label data-testid={`collabs-list-owner-username`}>
                    {item.UserName}
                  </label>
                  <label data-testid={`collabs-list-owner-title`}>
                    {item.Title}
                  </label>
                </div>
                <Button
                  className={"clone-icon"}
                  onClick={() => handleCollaborationClone(item)}
                  testId={"collab-clone-btn"}
                  tooltipButton={"Clone this collaboration"}
                />
              </div>
            ))}
            <Button
              mainClassName={"action-button-wrapper"}
              testid={`add-newLog-btn`}
              className={`add-btn`}
              onClick={() =>
                setState(prevState => ({
                  ...prevState,
                  isCreateCollaboration: true,
                  IsCloned: false,
                  collaborationForm: {}
                }))
              }
              // isHelpActive={isHelpActive}
              testIdHelp={"add-newLog-btn-help"}
              tooltipHelp={labels.tictell_add_timelog}
              tooltipButton={labels.ToolTipAddTimelog}
            />
          </div>
        </Column.Body>
      </Column>
      {IsCloned || isCreateCollaboration ? (
        <Column collapse={formCollapsed} className="col-2">
          <Column.Collapsed
            collapse={formCollapsed}
            onClick={() =>
              setState(prevState => ({ ...prevState, formCollapsed: false }))
            }
            isHelpActive={isHelpActive}
            tooltipPlace="left"
            text={"Collaboration Details"}
            tooltipButton={
              labels.COLLABORATION_CREATE_SECTION_EXPAND_BUTTON_TOOLTIP
            }
            tooltipHelp={labels.COLLABORATION_CREATE_SECTION_EXPAND_BUTTON_HELP}
            testId="collaboration-form-expand-btn"
            testIdHelp="collaboration-form-expand-help-btn"
          />
          <Column.Head>
            <Column.HeaderTitle isActive={true}>
              {"Collaboration Details"}
            </Column.HeaderTitle>

            <Button
              onClick={() =>
                setState(prevState => ({ ...prevState, formCollapsed: true }))
              }
              className="collapseBtn"
              isHelpActive={isHelpActive}
              tooltipPlace="left"
              tooltipButton={
                labels.COLLABORATION_CREATE_SECTION_COLLAPSE_BUTTON_TOOLTIP
              }
              tooltipHelp={
                labels.COLLABORATION_CREATE_SECTION_COLLAPSE_BUTTON_HELP
              }
              testId="collaboration-form-collapse-btn"
              testIdHelp="collaboration-form-collapse-help-btn"
            />
          </Column.Head>
          <Column.Body>
            <CreateNewCollaboration
              collaborationForm={collaborationForm}
              onCollaborationSave={handleCollaborationSave}
              onFormSelectChange={handleFormSelectChange}
              labels={labels}
              isLoading={isLoading}
              onFormFieldChange={handleFormFieldChange}
              onDateChange={handleDateChange}
              onSliderChange={handleSliderChange}
              opportunityList={opportunityList}
              isCreateCollaboration={isCreateCollaboration}
              iprosList={iprosList}
              Currencies={Currencies}
            />
          </Column.Body>
        </Column>
      ) : (
        <Column collapse={isCollapsed2} className="col-2">
          <Column.Collapsed
            text={SNAPSHOT_COLLABORATION_DETAIL}
            onClick={() =>
              setState(prevState => ({ ...prevState, isCollapsed2: false }))
            }
            isHelpActive={isHelpActive}
            tooltipButton={labels.ToolTipFeedBackExpandCreate}
            tooltipHelp={labels.HlpTooltipFeedbackExpandCreateButton}
            tooltipPlace="left"
            testId="expandbtn2"
          />
          <Column.Head>
            <div className="heading">{SNAPSHOT_COLLABORATION_DETAIL}</div>
            <Button
              className="collapseBtn"
              onClick={() =>
                setState(prevState => ({ ...prevState, isCollapsed2: true }))
              }
              tooltipButton={labels.ToolTipFeedBackCollaspeCreate}
              tooltipHelp={labels.HlpTooltipFeedbackCollapseCreateButton}
              tooltipPlace="left"
              isHelpActive={isHelpActive}
              testId="collapsebtn2"
            />
          </Column.Head>
          {!isCollapsed2 && (
            <div
              className={`tictell-list-item-container activeItem collaboration-heading`}
            >
              <div
                className={`dashItemImg ${
                  selectedCollaboration.PresenatationLogo ? "" : "no-dp"
                }`}
              >
                <img
                  className="roundImg"
                  src={
                    selectedCollaboration.PresenatationLogo
                      ? selectedCollaboration.PresenatationLogo
                      : ImgSrc
                  }
                  alt={"UserName"}
                />
              </div>
              <div className="name-container">
                <label data-testid={`collabs-details-owner-username`}>
                  {selectedCollaboration.UserName}
                </label>
                <label data-testid={`collabs-details-owner-title`}>
                  {selectedCollaboration.Title}
                </label>
              </div>
              <span
                className={`${
                  selectedCollaboration.IsShared ? "shared-icon" : ""
                }`}
              />
            </div>
          )}
          <Column.Body>
            <CollaborationDetail
              labels={labels}
              declinedStatus={props.declinedStatus}
              selectedCollaboration={selectedCollaboration}
              handleShareCollaborationDelete={
                props.handleShareCollaborationDelete
              }
              handleShareCollaborationInputChange={
                handleShareCollaborationInputChange
              }
              handleCollaborationShare={handleShareCollaboration}
              handleCollaborationEndDateChange={
                handleCollaborationEndDateChange
              }
              handleCollaborationNameEdit={handleCollaborationNameEdit}
              isEndDateCalendarOpen={isEndDateCalendarOpen}
              handleDateOnBlur={handleDateOnBlur}
              handleCollborationNameBlur={handleCollborationNameBlur}
              handleCollborationNameChange={handleCollborationNameChange}
              isHelpActive={isHelpActive}
              onCollaborationClose={handleCollaborationClose}
            ></CollaborationDetail>
          </Column.Body>
        </Column>
      )}
      {yesClickCount && yesClickCount == 2 && (
        <Column collapse={isCollapsed5} className="col-4">
          <Column.Collapsed
            text={SNAPSHOT_COLLABORATION_RESUME}
            onClick={() =>
              setState(prevState => ({ ...prevState, isCollapsed5: false }))
            }
            isHelpActive={isHelpActive}
            tooltipButton={labels.ToolTipFeedBackExpandCreate}
            tooltipHelp={labels.HlpTooltipFeedbackExpandCreateButton}
            tooltipPlace="left"
            testId="expandbtn5"
          />
          <Column.Head>
            <div className="heading">{SNAPSHOT_COLLABORATION_RESUME}</div>
            <Button
              className="collapseBtn"
              onClick={() =>
                setState(prevState => ({ ...prevState, isCollapsed5: true }))
              }
              tooltipButton={labels.ToolTipFeedBackCollaspeCreate}
              tooltipHelp={labels.HlpTooltipFeedbackCollapseCreateButton}
              tooltipPlace="left"
              isHelpActive={isHelpActive}
              testId="collapsebtn5"
            />
          </Column.Head>
          <Column.Body className="flex">
            <ReviewForm
              selectedCollaboration={selectedCollaboration}
              onCloseCollaboration={closeCollaboration}
              onSubmit={handleSubmit}
              onRatingChange={handleRatingChange}
              isAlreadyRated={state.isAlreadyRated}
              starRatedColor="#9783cf"
              starHoverColor="#9783cf"
              onFormSelectChange={onReasonFormSelectChange}
              options={state.reasonOptions}
              CollborationCloseReasonId={state.CollborationCloseReasonId}
              reviewQuestions={props.reviewQuestions}
              isInActiveView={props.isInActiveView}
            />
          </Column.Body>
        </Column>
      )}
      {
        <Column collapse={isCollapsed4} className="col-3">
          <Column.Collapsed
            text={SNAPSHOT_COLLABORATION_PRESENT}
            onClick={() =>
              setState(prevState => ({ ...prevState, isCollapsed4: false }))
            }
            isHelpActive={isHelpActive}
            tooltipButton={labels.ToolTipFeedBackExpandCreate}
            tooltipHelp={labels.HlpTooltipFeedbackExpandCreateButton}
            tooltipPlace="left"
            testId="expandbtn4"
          />
          <Column.Head>
            <div className="heading">{SNAPSHOT_COLLABORATION_PRESENT}</div>
            <Button
              className="collapseBtn"
              onClick={() =>
                setState(prevState => ({ ...prevState, isCollapsed4: true }))
              }
              tooltipButton={labels.ToolTipFeedBackCollaspeCreate}
              tooltipHelp={labels.HlpTooltipFeedbackCollapseCreateButton}
              tooltipPlace="left"
              isHelpActive={isHelpActive}
              testId="collapsebtn4"
            />
          </Column.Head>
          <Column.Body>
            <Presentations selectedUser={selectedUser} labels={labels} />
          </Column.Body>
        </Column>
      }
      {
        <Column collapse={isCollapsed5} className="col-4">
          <Column.Collapsed
            text={SNAPSHOT_COLLABORATION_RESUME}
            onClick={() =>
              setState(prevState => ({ ...prevState, isCollapsed5: false }))
            }
            isHelpActive={isHelpActive}
            tooltipButton={labels.ToolTipFeedBackExpandCreate}
            tooltipHelp={labels.HlpTooltipFeedbackExpandCreateButton}
            tooltipPlace="left"
            testId="expandbtn5"
          />
          <Column.Head>
            <div className="heading">{SNAPSHOT_COLLABORATION_RESUME}</div>
            <Button
              className="collapseBtn"
              onClick={() =>
                setState(prevState => ({ ...prevState, isCollapsed5: true }))
              }
              tooltipButton={labels.ToolTipFeedBackCollaspeCreate}
              tooltipHelp={labels.HlpTooltipFeedbackCollapseCreateButton}
              tooltipPlace="left"
              isHelpActive={isHelpActive}
              testId="collapsebtn5"
            />
          </Column.Head>
          <Column.Body>
            <ResumeDetail resume={selectedResume} />
          </Column.Body>
        </Column>
      }
    </div>
  );
};

const mapStateToProps = ({ systemLabel, navigation, userInfo }) => {
  const { labels } = systemLabel;
  const { user } = userInfo;
  const User = user ? user : StorageService.getUser();
  const { isHelpActive } = navigation;
  return { labels, isHelpActive, User };
};
export default connect(mapStateToProps, { notificationAction })(Collaborations);
